.spotlight-thumb .slick-list {
  padding-top: 20px;
}
.spotlight .slick-list,
.spotlight .slick-slide > div,
.spotlight .slick-slider,
.spotlight .slick-track,
.spotlight .spotlight-item {
  height: 100%;
}
.spotlight[data-v-419f4f25] {
  position: relative;
  min-height: 530px;
  height: 530px;
  overflow: hidden;
}
@media only screen and (max-width: 767px) {
  .spotlight[data-v-419f4f25] {
    height: 460px;
    min-height: 460px;
  }
}
.spotlight-promoImg[data-v-419f4f25] {
  margin-bottom: 10px;
}
.spotlight .container[data-v-419f4f25] {
  max-width: 1266px;
  margin: 0 auto;
}
.spotlight .action[data-v-419f4f25] {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}
.spotlight-link[data-v-419f4f25] {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}
.spotlight-promo[data-v-419f4f25] {
  display: inline-block;
}
@media only screen and (max-width: 410px) {
  .spotlight-promo[data-v-419f4f25] {
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
  }
}
.spotlight-promo fieldset[data-v-419f4f25] {
  border: 1px dashed #d8d8d8;
  border-radius: 2px;
  display: inline-block;
  vertical-align: top;
  color: #fff;
  padding: 0 15px;
  line-height: 2.8;
  font-family: du-font-secondary-bold, du-font-primary-b;
  font-size: 1.2rem;
  margin-left: 20px;
}
.spotlight-promo fieldset legend[data-v-419f4f25] {
  display: block;
  height: 0;
  position: relative;
  top: -8px;
  margin: 0;
  line-height: 1.4;
  padding: 0 6px;
  font-size: 0.8667rem;
  font-family: du-font-secondary-regular, du-font-primary-l;
}
[dir="rtl"] .spotlight-promo fieldset legend[data-v-419f4f25] {
  font-family: du-font-secondary-regular-ar, du-font-primary-l;
}
@media only screen and (max-width: 767px) {
  .spotlight-promo fieldset[data-v-419f4f25] {
    line-height: 2.6;
  }
}
@media only screen and (max-width: 410px) {
  .spotlight-promo fieldset[data-v-419f4f25] {
    margin-left: 0;
    margin-top: 30px;
  }
  [dir="rtl"] .spotlight-promo fieldset[data-v-419f4f25] {
    margin-right: 0;
  }
}
[dir="rtl"] .spotlight-promo fieldset[data-v-419f4f25] {
  margin-left: 0;
  margin-right: 20px;
  font-family: du-font-secondary-bold-ar, du-font-primary-b;
}
.spotlight-item[data-v-419f4f25] {
  height: 100%;
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
}
.spotlight-item .grid-container[data-v-419f4f25] {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding-bottom: 80px;
  position: relative;
}
@media only screen and (max-width: 767px) {
  .spotlight-item .grid-container[data-v-419f4f25] {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-align: end;
    -ms-flex-align: end;
    align-items: flex-end;
    max-height: 500px;
    padding: 15px 15px 76px 15px;
  }
}
.spotlight-item .subtitle1[data-v-419f4f25] {
  margin-bottom: 13px;
  font-family: du-font-secondary-regular, du-font-primary-l;
}
[dir="rtl"] .spotlight-item .subtitle1[data-v-419f4f25] {
  font-family: du-font-secondary-regular-ar, du-font-primary-l;
}
@media only screen and (min-width: 767px) {
  .spotlight-item .subtitle1[data-v-419f4f25] {
    margin-bottom: 22px;
  }
}
.spotlight-item-content[data-v-419f4f25] {
  border-radius: 16px;
  color: #fff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  opacity: 0;
  -webkit-transform: translateX(50px);
  transform: translateX(50px);
  -webkit-transition: all 0.5s ease-in-out 0ms;
  transition: all 0.5s ease-in-out 0ms;
  -webkit-transition-delay: 0.3s;
  transition-delay: 0.3s;
  width: 100%;
}
@media only screen and (max-width: 991px) {
  .spotlight-item-content[data-v-419f4f25] {
    padding: 10px 20px 20px;
  }
}
@media only screen and (max-width: 767px) {
  .spotlight-item-content[data-v-419f4f25] {
    padding: 15px;
    height: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }
}
@media only screen and (min-width: 991px) {
  .spotlight-item-content[data-v-419f4f25] {
    border: 3px solid #fff;
    padding: 50px;
    max-width: 1266px;
    height: 380px;
    border-radius: 10px;
  }
}
[dir="rtl"] .spotlight-item-content[data-v-419f4f25] {
  -webkit-transform: translateX(-50px);
  transform: translateX(-50px);
}
@media only screen and (min-width: 991px) {
  .spotlight-item-content-inner[data-v-419f4f25] {
    max-width: 580px;
  }
}
.spotlight .online-exclusive[data-v-419f4f25] {
  -webkit-transform: translateX(50px);
  transform: translateX(50px);
  -webkit-transition: all 0.5s ease-in-out 0ms;
  transition: all 0.5s ease-in-out 0ms;
  -webkit-transition-delay: 0.3s;
  transition-delay: 0.3s;
}
[dir="rtl"] .spotlight .online-exclusive[data-v-419f4f25] {
  -webkit-transform: translateX(-50px);
  transform: translateX(-50px);
}
.spotlight-image[data-v-419f4f25] {
  position: absolute;
  left: 50%;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  top: 0;
  bottom: 0;
  z-index: -1;
  background-size: auto;
  background-position: 50%;
  background-repeat: no-repeat;
  max-width: 1920px;
  width: auto;
  height: auto;
  -webkit-transition: 0.3s ease-in-out;
  transition: 0.3s ease-in-out;
  -o-object-fit: cover;
  object-fit: cover;
  background-color: #777;
  opacity: 1;
  z-index: 0;
}
.spotlight-image.slick-lazyload-error[data-v-419f4f25],
.spotlight-image.slick-loading[data-v-419f4f25] {
  background-color: #ccc;
  width: 100%;
  height: 100%;
}
@media only screen and (max-width: 767px) {
  .spotlight-image[data-v-419f4f25] {
    width: 100%;
    height: 100%;
  }
}
.spotlight-image.slick-loading[data-v-419f4f25] {
  opacity: 0.4;
}
.spotlight-thumb[data-v-419f4f25] {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  cursor: pointer;
  height: 81px;
}
@media only screen and (min-width: 767px) {
  .spotlight-thumb[data-v-419f4f25] {
    height: 91px;
  }
}
.spotlight-thumb[data-v-419f4f25]:before {
  position: absolute;
  content: "";
  left: 0;
  right: 0;
  bottom: 0;
  top: 20px;
  background-color: hsla(0, 0%, 93.3%, 0.7);
}
@media only screen and (min-width: 767px) {
  .spotlight-thumb[data-v-419f4f25]:before {
    border-radius: 8px 8px 0 0;
  }
}
.spotlight-thumb .container[data-v-419f4f25] {
  padding: 0;
}
.spotlight-thumb-inner[data-v-419f4f25] {
  background-color: transparent;
  overflow: visible;
  padding: 0;
}
.spotlight-thumb-item[data-v-419f4f25] {
  padding: 10px 17px;
}
@media only screen and (min-width: 767px) {
  .spotlight-thumb-item[data-v-419f4f25] {
    padding: 17px 30px 13px;
  }
}
.spotlight-thumb-item p[data-v-419f4f25] {
  margin-bottom: 0;
}
.spotlight-thumb-card[data-v-419f4f25] {
  background-color: transparent;
}
.spotlight-thumb-content[data-v-419f4f25] {
  padding: 20px 30px;
}
.spotlight-thumb .swiper-container[data-v-419f4f25] {
  background-color: hsla(0, 0%, 93.3%, 0);
  padding: 0;
}
.spotlight-thumb .swiper-container p[data-v-419f4f25] {
  color: #000;
  margin-bottom: 0;
}
.spotlight-thumb .spotlight-thumb-item[data-v-419f4f25]:before {
  position: absolute;
  content: "";
  top: -15px;
  left: 50%;
  -webkit-transform: translate(-50%);
  transform: translate(-50%);
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
  border-bottom: 15px solid transparent;
  -webkit-transition: 0.3s ease-in-out;
  transition: 0.3s ease-in-out;
}
.spotlight-thumb
  .slick-active.slick-current
  .spotlight-thumb-item[data-v-419f4f25] {
  background-color: #fff;
  -webkit-box-shadow: 0 -3px 20px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 -3px 20px 0 rgba(0, 0, 0, 0.1);
  border-radius: 2px 2px 0 0;
  position: relative;
}
@media only screen and (min-width: 767px) {
  .spotlight-thumb
    .slick-active.slick-current
    .spotlight-thumb-item[data-v-419f4f25] {
    -webkit-box-shadow: 0 -5px 14px 2px rgba(0, 0, 0, 0.15);
    box-shadow: 0 -5px 14px 2px rgba(0, 0, 0, 0.15);
  }
}
.spotlight-thumb
  .slick-active.slick-current
  .spotlight-thumb-item[data-v-419f4f25]:before {
  border-bottom: 15px solid #fff;
}
.spotlight-thumb
  .slick-active.slick-current
  .spotlight-thumb-item
  .spotlight-timer[data-v-419f4f25] {
  height: 2px;
  width: 0;
  position: absolute;
  bottom: 0;
  background-image: linear-gradient(224deg, #753bbd, #c724b1 74%);
}
.spotlight-thumb
  .slick-active.slick-current
  .spotlight-thumb-item
  .spotlight-timer.animation[data-v-419f4f25] {
  -webkit-animation-name: increase-data-v-419f4f25;
  animation-name: increase-data-v-419f4f25;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
}
.spotlight-thumb
  .slick-active.slick-current
  .spotlight-thumb-item
  .spotlight-timer.hover[data-v-419f4f25] {
  -webkit-animation-play-state: paused;
  animation-play-state: paused;
}
[dir="ltr"]
  .spotlight-thumb
  .slick-active.slick-current
  .spotlight-thumb-item
  .spotlight-timer[data-v-419f4f25] {
  left: 0;
}
[dir="rtl"]
  .spotlight-thumb
  .slick-active.slick-current
  .spotlight-thumb-item
  .spotlight-timer[data-v-419f4f25] {
  right: 0;
  -webkit-animation-duration: 8s;
  animation-duration: 8s;
}
@-webkit-keyframes increase-data-v-419f4f25 {
  to {
    width: 100%;
  }
}
@keyframes increase-data-v-419f4f25 {
  to {
    width: 100%;
  }
}
.spotlight .slick-active.slick-current .online-exclusive[data-v-419f4f25],
.spotlight
  .slick-active.slick-current
  .spotlight-item-content[data-v-419f4f25] {
  opacity: 1;
  -webkit-transform: translateX(0);
  transform: translateX(0);
}
.spotlight .carousel-thumb-title[data-v-419f4f25] {
  font-size: 0.937rem;
  font-family: du-font-secondary-bold, du-font-primary-b;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
[dir="rtl"] .spotlight .carousel-thumb-title[data-v-419f4f25] {
  font-family: du-font-secondary-bold-ar, du-font-primary-b;
}
.spotlight .carousel-thumb-subtitle[data-v-419f4f25] {
  font-size: 0.812rem;
  color: #777;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
@font-face {
  font-family: du-font-primary-bold;
  font-display: swap;
  src: url(../fonts/duCo_WHeadline16_Bd.31111de8.eot);
  src: url(../fonts/duCo_WHeadline16_Bd.31111de8.eot)
      format("embedded-opentype"),
    url(../fonts/duCo_WHeadline16_Bd.9a96b6ad.woff2) format("woff2"),
    url(../fonts/duCo_WHeadline16_Bd.45403988.woff) format("woff"),
    url(../fonts/duCo_WHeadline16_Bd.8b6edce9.ttf) format("truetype");
}
@font-face {
  font-family: du-font-primary-b;
  font-display: swap;
  src: url(../fonts/duCo_WHeadline16_Bd.31111de8.eot);
  src: url(../fonts/duCo_WHeadline16_Bd.31111de8.eot)
      format("embedded-opentype"),
    url(../fonts/duCo_WHeadline16_Bd.9a96b6ad.woff2) format("woff2"),
    url(../fonts/duCo_WHeadline16_Bd.45403988.woff) format("woff"),
    url(../fonts/duCo_WHeadline16_Bd.8b6edce9.ttf) format("truetype");
}
@font-face {
  font-family: du-font-secondary-bold;
  font-display: swap;
  src: url(../fonts/ProximaNova-Bold.b07aae54.eot);
  src: url(../fonts/ProximaNova-Bold.b07aae54.eot) format("embedded-opentype"),
    url(../fonts/ProximaNova-Bold.5d99a47a.woff2) format("woff2"),
    url(../fonts/ProximaNova-Bold.6f5a91b6.woff) format("woff"),
    url(../fonts/ProximaNova-Bold.64101876.ttf) format("truetype");
}
@font-face {
  font-family: du-font-secondary-medium;
  font-display: swap;
  src: url(../fonts/ProximaNova-Medium.e793d74f.eot);
  src: url(../fonts/ProximaNova-Medium.e793d74f.eot) format("embedded-opentype"),
    url(../fonts/ProximaNova-Medium.cdb801a3.woff2) format("woff2"),
    url(../fonts/ProximaNova-Medium.8acd8ec9.woff) format("woff"),
    url(../fonts/ProximaNova-Medium.bbd43743.ttf) format("truetype");
}
@font-face {
  font-family: du-font-secondary-regular;
  font-display: swap;
  src: url(../fonts/ProximaNova-Regular.cbd07064.eot);
  src: url(../fonts/ProximaNova-Regular.cbd07064.eot)
      format("embedded-opentype"),
    url(../fonts/ProximaNova-Regular.4f34c672.woff2) format("woff2"),
    url(../fonts/ProximaNova-Regular.e13fc23e.woff) format("woff"),
    url(../fonts/ProximaNova-Regular.486e30d8.ttf) format("truetype");
}
@font-face {
  font-family: du-font-secondary-bold-ar;
  font-display: swap;
  src: url(../fonts/Dubai-Bold.8fbdda63.ttf) format("truetype");
}
@font-face {
  font-family: du-font-secondary-medium-ar;
  font-display: swap;
  src: url(../fonts/Dubai-Medium.1b70cb9a.ttf) format("truetype");
}
@font-face {
  font-family: du-font-secondary-regular-ar;
  font-display: swap;
  src: url(../fonts/Dubai-Regular.24ab51f6.ttf) format("truetype");
}
@media only screen and (min-width: 767px) {
  .spotlight-item[data-text-color="white"] {
    background-color: #777;
  }
}
@media only screen and (max-width: 767px) {
  .spotlight-item[data-text-color-m="white"] {
    background-color: #777;
  }
}
@media only screen and (min-width: 767px) {
  .banner-inner[data-text-color="white"] .description1,
  .banner-inner[data-text-color="white"] .headline2,
  .banner-inner[data-text-color="white"] fieldset,
  .banner-inner[data-text-color="white"] h1,
  .banner-inner[data-text-color="white"] legend,
  .banner-inner[data-text-color="white"] p,
  .spotlight-item[data-text-color="white"] .description1,
  .spotlight-item[data-text-color="white"] .headline2,
  .spotlight-item[data-text-color="white"] fieldset,
  .spotlight-item[data-text-color="white"] h1,
  .spotlight-item[data-text-color="white"] legend,
  .spotlight-item[data-text-color="white"] p {
    color: #fff;
  }
  .banner-inner[data-text-color="black"] .description1,
  .banner-inner[data-text-color="black"] .headline2,
  .banner-inner[data-text-color="black"] fieldset,
  .banner-inner[data-text-color="black"] h1,
  .banner-inner[data-text-color="black"] legend,
  .banner-inner[data-text-color="black"] p,
  .banner-inner[data-text-color="dark"] .description1,
  .banner-inner[data-text-color="dark"] .headline2,
  .banner-inner[data-text-color="dark"] fieldset,
  .banner-inner[data-text-color="dark"] h1,
  .banner-inner[data-text-color="dark"] legend,
  .banner-inner[data-text-color="dark"] p,
  .spotlight-item[data-text-color="black"] .description1,
  .spotlight-item[data-text-color="black"] .headline2,
  .spotlight-item[data-text-color="black"] fieldset,
  .spotlight-item[data-text-color="black"] h1,
  .spotlight-item[data-text-color="black"] legend,
  .spotlight-item[data-text-color="black"] p,
  .spotlight-item[data-text-color="dark"] .description1,
  .spotlight-item[data-text-color="dark"] .headline2,
  .spotlight-item[data-text-color="dark"] fieldset,
  .spotlight-item[data-text-color="dark"] h1,
  .spotlight-item[data-text-color="dark"] legend,
  .spotlight-item[data-text-color="dark"] p {
    color: #333;
  }
}
@media only screen and (max-width: 767px) {
  .banner-inner[data-text-color-m="black"] .description1,
  .banner-inner[data-text-color-m="black"] .headline2,
  .banner-inner[data-text-color-m="black"] fieldset,
  .banner-inner[data-text-color-m="black"] h1,
  .banner-inner[data-text-color-m="black"] legend,
  .banner-inner[data-text-color-m="black"] p,
  .banner-inner[data-text-color-m="dark"] .description1,
  .banner-inner[data-text-color-m="dark"] .headline2,
  .banner-inner[data-text-color-m="dark"] fieldset,
  .banner-inner[data-text-color-m="dark"] h1,
  .banner-inner[data-text-color-m="dark"] legend,
  .banner-inner[data-text-color-m="dark"] p,
  .spotlight-item[data-text-color-m="black"] .description1,
  .spotlight-item[data-text-color-m="black"] .headline2,
  .spotlight-item[data-text-color-m="black"] fieldset,
  .spotlight-item[data-text-color-m="black"] h1,
  .spotlight-item[data-text-color-m="black"] legend,
  .spotlight-item[data-text-color-m="black"] p,
  .spotlight-item[data-text-color-m="dark"] .description1,
  .spotlight-item[data-text-color-m="dark"] .headline2,
  .spotlight-item[data-text-color-m="dark"] fieldset,
  .spotlight-item[data-text-color-m="dark"] h1,
  .spotlight-item[data-text-color-m="dark"] legend,
  .spotlight-item[data-text-color-m="dark"] p {
    color: #333;
  }
  .banner-inner[data-text-color-m="white"] .description1,
  .banner-inner[data-text-color-m="white"] .headline2,
  .banner-inner[data-text-color-m="white"] fieldset,
  .banner-inner[data-text-color-m="white"] h1,
  .banner-inner[data-text-color-m="white"] legend,
  .banner-inner[data-text-color-m="white"] p,
  .spotlight-item[data-text-color-m="white"] .description1,
  .spotlight-item[data-text-color-m="white"] .headline2,
  .spotlight-item[data-text-color-m="white"] fieldset,
  .spotlight-item[data-text-color-m="white"] h1,
  .spotlight-item[data-text-color-m="white"] legend,
  .spotlight-item[data-text-color-m="white"] p {
    color: #fff;
  }
}
@media only screen and (max-width: 767px) {
  .banner-inner[data-position-small="center"],
  .spotlight-item[data-position-small="center"] {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
  }
  .banner-inner[data-position-small="center"] .spotlight-item-content,
  .spotlight-item[data-position-small="center"] .spotlight-item-content {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
  .banner-inner[data-position-small="bottom"],
  .spotlight-item[data-position-small="bottom"] {
    -webkit-box-align: end;
    -ms-flex-align: end;
    align-items: flex-end;
  }
  .banner-inner[data-position-small="bottom"] .spotlight-item-content,
  .spotlight-item[data-position-small="bottom"] .spotlight-item-content {
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
  }
  .banner-inner[data-position-small="top"],
  .spotlight-item[data-position-small="top"] {
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
  }
  .banner-inner[data-position-small="top"] .spotlight-item-content,
  .spotlight-item[data-position-small="top"] .spotlight-item-content {
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }
}
@media only screen and (max-width: 991px) {
  .banner-inner[data-border-mobile=""] .banner-item,
  .banner-inner[data-border-mobile=""] .spotlight-item-content,
  .banner-inner[data-border-mobile="none"] .banner-item,
  .banner-inner[data-border-mobile="none"] .spotlight-item-content,
  .spotlight-item[data-border-mobile=""] .banner-item,
  .spotlight-item[data-border-mobile=""] .spotlight-item-content,
  .spotlight-item[data-border-mobile="none"] .banner-item,
  .spotlight-item[data-border-mobile="none"] .spotlight-item-content {
    border-color: transparent;
  }
  .banner-inner[data-border-mobile="white"] .spotlight-item-content,
  .spotlight-item[data-border-mobile="white"] .spotlight-item-content {
    border: 2px solid #fff !important;
  }
  .banner-inner[data-border-mobile="white"] .banner-item,
  .spotlight-item[data-border-mobile="white"] .banner-item {
    border: 2px solid #fff;
  }
  .banner-inner[data-border-mobile="violet"] .banner-item,
  .banner-inner[data-border-mobile="violet"] .spotlight-item-content,
  .spotlight-item[data-border-mobile="violet"] .banner-item,
  .spotlight-item[data-border-mobile="violet"] .spotlight-item-content {
    border: 2px solid #753bbd;
  }
  .banner-inner[data-border-mobile="blue"] .banner-item,
  .banner-inner[data-border-mobile="blue"] .spotlight-item-content,
  .spotlight-item[data-border-mobile="blue"] .banner-item,
  .spotlight-item[data-border-mobile="blue"] .spotlight-item-content {
    border: 2px solid #0081ab;
  }
  .banner-inner[data-border-mobile="magenta"] .banner-item,
  .banner-inner[data-border-mobile="magenta"] .spotlight-item-content,
  .spotlight-item[data-border-mobile="magenta"] .banner-item,
  .spotlight-item[data-border-mobile="magenta"] .spotlight-item-content {
    border: 2px solid #c724b1;
  }
}
@media only screen and (min-width: 991px) {
  .banner-inner[data-border=""] .banner-item,
  .banner-inner[data-border=""] .spotlight-item-content,
  .banner-inner[data-border="none"] .banner-item,
  .banner-inner[data-border="none"] .spotlight-item-content,
  .spotlight-item[data-border=""] .banner-item,
  .spotlight-item[data-border=""] .spotlight-item-content,
  .spotlight-item[data-border="none"] .banner-item,
  .spotlight-item[data-border="none"] .spotlight-item-content {
    border: 0 solid transparent;
  }
  .banner-inner[data-border="gradient"] .banner-item,
  .banner-inner[data-border="gradient"] .spotlight-item-content,
  .spotlight-item[data-border="gradient"] .banner-item,
  .spotlight-item[data-border="gradient"] .spotlight-item-content {
    background-image: url(../img/border.cd60ef6d.svg);
    border: 0;
    background-size: 100% 100%;
    background-position: 50%;
    background-repeat: no-repeat;
  }
  .banner-inner[data-border="white"] .banner-item,
  .banner-inner[data-border="white"] .spotlight-item-content,
  .spotlight-item[data-border="white"] .banner-item,
  .spotlight-item[data-border="white"] .spotlight-item-content {
    border: 2px solid #fff;
  }
  .banner-inner[data-border="violet"] .banner-item,
  .banner-inner[data-border="violet"] .spotlight-item-content,
  .spotlight-item[data-border="violet"] .banner-item,
  .spotlight-item[data-border="violet"] .spotlight-item-content {
    border: 2px solid #753bbd;
  }
  .banner-inner[data-border="blue"] .banner-item,
  .banner-inner[data-border="blue"] .spotlight-item-content,
  .spotlight-item[data-border="blue"] .banner-item,
  .spotlight-item[data-border="blue"] .spotlight-item-content {
    border: 2px solid #0081ab;
  }
  .banner-inner[data-border="magenta"] .banner-item,
  .banner-inner[data-border="magenta"] .spotlight-item-content,
  .spotlight-item[data-border="magenta"] .banner-item,
  .spotlight-item[data-border="magenta"] .spotlight-item-content {
    border: 2px solid #c724b1;
  }
}
@media only screen and (min-width: 991px) {
  .banner-inner[data-box="full-window-gradient-bg"] .spotlight-item-content,
  .spotlight-item[data-box="full-window-gradient-bg"] .spotlight-item-content {
    background: linear-gradient(
      70deg,
      rgba(117, 59, 189, 0.8) 12%,
      rgba(0, 169, 206, 0.8) 41%,
      rgba(0, 169, 206, 0.08) 80%
    );
  }
  .banner-inner[data-box="full-window-gradient-bg"]
    .spotlight-item-content
    [dir="rtl"],
  .spotlight-item[data-box="full-window-gradient-bg"]
    .spotlight-item-content
    [dir="rtl"] {
    background: linear-gradient(
      -70deg,
      rgba(117, 59, 189, 0.8) 12%,
      rgba(0, 169, 206, 0.8) 41%,
      rgba(0, 169, 206, 0.08) 80%
    );
  }
}
.banner-inner[data-box="full-window-gradient-bg"] .banner-item-content,
.spotlight-item[data-box="full-window-gradient-bg"] .banner-item-content {
  background-image: linear-gradient(
    -65deg,
    transparent 11%,
    rgba(0, 169, 206, 0.6) 40%,
    rgba(0, 32, 91, 0.8) 69%,
    #753bbd
  );
}
[dir="rtl"]
  .banner-inner[data-box="full-window-gradient-bg"]
  .banner-item-content,
[dir="rtl"]
  .spotlight-item[data-box="full-window-gradient-bg"]
  .banner-item-content {
  background-image: linear-gradient(
    -215deg,
    rgba(117, 59, 189, 0) 30%,
    rgba(0, 169, 206, 0.9) 77%,
    #00a9ce
  );
}
@media only screen and (min-width: 991px) {
  .banner-inner[data-box="half-window-gradient-bg"] .spotlight-item-content,
  .spotlight-item[data-box="half-window-gradient-bg"] .spotlight-item-content {
    max-width: 680px;
    border-radius: 15px;
    background: linear-gradient(
      40deg,
      rgba(117, 59, 189, 0.7),
      rgba(0, 169, 206, 0.4) 42%,
      rgba(0, 169, 206, 0.1)
    );
  }
  .banner-inner[data-box="half-window-gradient-bg"]
    .spotlight-item-content
    [dir="rtl"],
  .spotlight-item[data-box="half-window-gradient-bg"]
    .spotlight-item-content
    [dir="rtl"] {
    background: linear-gradient(
      -40deg,
      rgba(117, 59, 189, 0.7),
      rgba(0, 169, 206, 0.4) 42%,
      rgba(0, 169, 206, 0.1)
    );
  }
  .banner-inner[data-box="half-window-gradient-bg"]
    .spotlight-item-content-inner,
  .spotlight-item[data-box="half-window-gradient-bg"]
    .spotlight-item-content-inner {
    width: 100%;
  }
}
.banner-inner[data-box="half-window-gradient-bg"] .banner-item,
.spotlight-item[data-box="half-window-gradient-bg"] .banner-item {
  max-width: 680px;
}
.banner-inner[data-box="half-window-gradient-bg"] .banner-item-content,
.spotlight-item[data-box="half-window-gradient-bg"] .banner-item-content {
  width: 100%;
  background: linear-gradient(
    40deg,
    rgba(117, 59, 189, 0.7),
    rgba(0, 169, 206, 0.4) 42%,
    rgba(0, 169, 206, 0.1)
  );
}
.banner-inner[data-box="half-window-gradient-bg"]
  .banner-item-content
  [dir="rtl"],
.spotlight-item[data-box="half-window-gradient-bg"]
  .banner-item-content
  [dir="rtl"] {
  background: linear-gradient(
    -40deg,
    rgba(117, 59, 189, 0.7),
    rgba(0, 169, 206, 0.4) 42%,
    rgba(0, 169, 206, 0.1)
  );
}
.banner-inner[data-box="blue"] .banner-item-content,
.banner-inner[data-box="blue"] .spotlight-item-content,
.spotlight-item[data-box="blue"] .banner-item-content,
.spotlight-item[data-box="blue"] .spotlight-item-content {
  background-image: linear-gradient(
    215deg,
    rgba(117, 59, 189, 0) 30%,
    rgba(0, 169, 206, 0.9) 77%,
    #00a9ce
  );
}
.banner-inner[data-box="blue"] .banner-item-content [dir="rtl"],
.banner-inner[data-box="blue"] .spotlight-item-content [dir="rtl"],
.spotlight-item[data-box="blue"] .banner-item-content [dir="rtl"],
.spotlight-item[data-box="blue"] .spotlight-item-content [dir="rtl"] {
  background-image: linear-gradient(
    -215deg,
    rgba(117, 59, 189, 0) 30%,
    rgba(0, 169, 206, 0.9) 77%,
    #00a9ce
  );
}
.spotlight-thumb[data-timing="10000"] .spotlight-timer.animation {
  -webkit-animation-duration: 10.5s;
  animation-duration: 10.5s;
}
.spotlight-thumb[data-timing="9000"] .spotlight-timer.animation {
  -webkit-animation-duration: 9.5s;
  animation-duration: 9.5s;
}
.spotlight-thumb[data-timing="8000"] .spotlight-timer.animation {
  -webkit-animation-duration: 8.5s;
  animation-duration: 8.5s;
}
.spotlight-thumb[data-timing="7500"] .spotlight-timer.animation {
  -webkit-animation-duration: 8s;
  animation-duration: 8s;
}
.spotlight-thumb[data-timing="7000"] .spotlight-timer.animation {
  -webkit-animation-duration: 7.5s;
  animation-duration: 7.5s;
}
.spotlight-thumb[data-timing="6000"] .spotlight-timer.animation {
  -webkit-animation-duration: 6.5s;
  animation-duration: 6.5s;
}
.spotlight-thumb[data-timing="5000"] .spotlight-timer.animation {
  -webkit-animation-duration: 5.5s;
  animation-duration: 5.5s;
} /*! normalize.css v8.0.0 | MIT License | github.com/necolas/normalize.css */
html {
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
}
h1 {
  font-size: 2em;
  margin: 0.67em 0;
}
hr {
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
  height: 0;
  overflow: visible;
}
pre {
  font-family: monospace, monospace;
  font-size: 1em;
}
a {
  background-color: transparent;
}
abbr[title] {
  border-bottom: none;
  text-decoration: underline;
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
}
b,
strong {
  font-weight: bolder;
}
code,
kbd,
samp {
  font-family: monospace, monospace;
  font-size: 1em;
}
small {
  font-size: 80%;
}
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sub {
  bottom: -0.25em;
}
sup {
  top: -0.5em;
}
img {
  border-style: none;
}
button,
input,
optgroup,
select,
textarea {
  font-size: 100%;
  line-height: 1.15;
  margin: 0;
}
button,
input {
  overflow: visible;
}
button,
select {
  text-transform: none;
}
[type="button"],
[type="reset"],
[type="submit"],
button {
  -webkit-appearance: button;
}
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner,
button::-moz-focus-inner {
  border-style: none;
  padding: 0;
}
[type="button"]:-moz-focusring,
[type="reset"]:-moz-focusring,
[type="submit"]:-moz-focusring,
button:-moz-focusring {
  outline: 1px dotted ButtonText;
}
fieldset {
  padding: 0.35em 0.75em 0.625em;
}
legend {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  color: inherit;
  display: table;
  max-width: 100%;
  padding: 0;
  white-space: normal;
}
progress {
  vertical-align: baseline;
}
textarea {
  overflow: auto;
}
[type="checkbox"],
[type="radio"] {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0;
}
[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
  height: auto;
}
[type="search"] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}
[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}
::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}
details {
  display: block;
}
summary {
  display: list-item;
}
[hidden],
template {
  display: none;
}
.foundation-mq {
  font-family: "small=0em&s-medium=20em&medium=48em&landscape=64.0625em&large=64em&mlarge=80em&xlarge=85.375em&xxlarge=105em";
}
html {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  font-size: 100%;
}
*,
:after,
:before {
  -webkit-box-sizing: inherit;
  box-sizing: inherit;
}
body {
  background: #fff;
  font-family: Helvetica Neue, Helvetica, Roboto, Arial, sans-serif;
  font-weight: 400;
  line-height: 1.5;
  color: #333;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
img {
  display: inline-block;
  vertical-align: middle;
  max-width: 100%;
  height: auto;
  -ms-interpolation-mode: bicubic;
}
textarea {
  height: auto;
  min-height: 50px;
  border-radius: 0;
}
select {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
  border-radius: 0;
}
.map_canvas embed,
.map_canvas img,
.map_canvas object,
.mqa-display embed,
.mqa-display img,
.mqa-display object {
  max-width: none !important;
}
button {
  padding: 0;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 0;
  border-radius: 0;
  background: transparent;
  line-height: 1;
  cursor: pointer;
}
[data-whatinput="mouse"] button {
  outline: 0;
}
pre {
  overflow: auto;
}
button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
}
.is-visible {
  display: block !important;
}
.is-hidden {
  display: none !important;
}
.grid-container {
  padding-right: 2rem;
  padding-left: 2rem;
  max-width: 1266px;
  margin-left: auto;
  margin-right: auto;
}
@media print, screen and (min-width: 48em) {
  .grid-container {
    padding-right: 1rem;
    padding-left: 1rem;
  }
}
.grid-container.fluid {
  padding-right: 2rem;
  padding-left: 2rem;
  max-width: 100%;
  margin-left: auto;
  margin-right: auto;
}
@media print, screen and (min-width: 48em) {
  .grid-container.fluid {
    padding-right: 1rem;
    padding-left: 1rem;
  }
}
.grid-container.full {
  padding-right: 0;
  padding-left: 0;
  max-width: 100%;
  margin-left: auto;
  margin-right: auto;
}
.grid-x {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-flow: row wrap;
  flex-flow: row wrap;
}
.cell {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  min-height: 0;
  min-width: 0;
  width: 100%;
}
.cell.auto {
  -webkit-box-flex: 1;
  -ms-flex: 1 1 0px;
  flex: 1 1 0px;
}
.cell.shrink {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
}
.grid-x > .auto,
.grid-x > .shrink {
  width: auto;
}
.grid-x > .small-1,
.grid-x > .small-2,
.grid-x > .small-3,
.grid-x > .small-4,
.grid-x > .small-5,
.grid-x > .small-6,
.grid-x > .small-7,
.grid-x > .small-8,
.grid-x > .small-9,
.grid-x > .small-10,
.grid-x > .small-11,
.grid-x > .small-12,
.grid-x > .small-full,
.grid-x > .small-shrink {
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
}
@media print, screen and (min-width: 48em) {
  .grid-x > .medium-1,
  .grid-x > .medium-2,
  .grid-x > .medium-3,
  .grid-x > .medium-4,
  .grid-x > .medium-5,
  .grid-x > .medium-6,
  .grid-x > .medium-7,
  .grid-x > .medium-8,
  .grid-x > .medium-9,
  .grid-x > .medium-10,
  .grid-x > .medium-11,
  .grid-x > .medium-12,
  .grid-x > .medium-full,
  .grid-x > .medium-shrink {
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }
}
@media print, screen and (min-width: 64em) {
  .grid-x > .large-1,
  .grid-x > .large-2,
  .grid-x > .large-3,
  .grid-x > .large-4,
  .grid-x > .large-5,
  .grid-x > .large-6,
  .grid-x > .large-7,
  .grid-x > .large-8,
  .grid-x > .large-9,
  .grid-x > .large-10,
  .grid-x > .large-11,
  .grid-x > .large-12,
  .grid-x > .large-full,
  .grid-x > .large-shrink {
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }
}
.grid-x > .small-1,
.grid-x > .small-2,
.grid-x > .small-3,
.grid-x > .small-4,
.grid-x > .small-5,
.grid-x > .small-6,
.grid-x > .small-7,
.grid-x > .small-8,
.grid-x > .small-9,
.grid-x > .small-10,
.grid-x > .small-11,
.grid-x > .small-12 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
}
.grid-x > .small-1 {
  width: 8.3333333333%;
}
.grid-x > .small-2 {
  width: 16.6666666667%;
}
.grid-x > .small-3 {
  width: 25%;
}
.grid-x > .small-4 {
  width: 33.3333333333%;
}
.grid-x > .small-5 {
  width: 41.6666666667%;
}
.grid-x > .small-6 {
  width: 50%;
}
.grid-x > .small-7 {
  width: 58.3333333333%;
}
.grid-x > .small-8 {
  width: 66.6666666667%;
}
.grid-x > .small-9 {
  width: 75%;
}
.grid-x > .small-10 {
  width: 83.3333333333%;
}
.grid-x > .small-11 {
  width: 91.6666666667%;
}
.grid-x > .small-12 {
  width: 100%;
}
@media print, screen and (min-width: 48em) {
  .grid-x > .medium-auto {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 0px;
    flex: 1 1 0px;
    width: auto;
  }
  .grid-x > .medium-1,
  .grid-x > .medium-2,
  .grid-x > .medium-3,
  .grid-x > .medium-4,
  .grid-x > .medium-5,
  .grid-x > .medium-6,
  .grid-x > .medium-7,
  .grid-x > .medium-8,
  .grid-x > .medium-9,
  .grid-x > .medium-10,
  .grid-x > .medium-11,
  .grid-x > .medium-12,
  .grid-x > .medium-shrink {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
  }
  .grid-x > .medium-shrink {
    width: auto;
  }
  .grid-x > .medium-1 {
    width: 8.3333333333%;
  }
  .grid-x > .medium-2 {
    width: 16.6666666667%;
  }
  .grid-x > .medium-3 {
    width: 25%;
  }
  .grid-x > .medium-4 {
    width: 33.3333333333%;
  }
  .grid-x > .medium-5 {
    width: 41.6666666667%;
  }
  .grid-x > .medium-6 {
    width: 50%;
  }
  .grid-x > .medium-7 {
    width: 58.3333333333%;
  }
  .grid-x > .medium-8 {
    width: 66.6666666667%;
  }
  .grid-x > .medium-9 {
    width: 75%;
  }
  .grid-x > .medium-10 {
    width: 83.3333333333%;
  }
  .grid-x > .medium-11 {
    width: 91.6666666667%;
  }
  .grid-x > .medium-12 {
    width: 100%;
  }
}
@media print, screen and (min-width: 64em) {
  .grid-x > .large-auto {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 0px;
    flex: 1 1 0px;
    width: auto;
  }
  .grid-x > .large-1,
  .grid-x > .large-2,
  .grid-x > .large-3,
  .grid-x > .large-4,
  .grid-x > .large-5,
  .grid-x > .large-6,
  .grid-x > .large-7,
  .grid-x > .large-8,
  .grid-x > .large-9,
  .grid-x > .large-10,
  .grid-x > .large-11,
  .grid-x > .large-12,
  .grid-x > .large-shrink {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
  }
  .grid-x > .large-shrink {
    width: auto;
  }
  .grid-x > .large-1 {
    width: 8.3333333333%;
  }
  .grid-x > .large-2 {
    width: 16.6666666667%;
  }
  .grid-x > .large-3 {
    width: 25%;
  }
  .grid-x > .large-4 {
    width: 33.3333333333%;
  }
  .grid-x > .large-5 {
    width: 41.6666666667%;
  }
  .grid-x > .large-6 {
    width: 50%;
  }
  .grid-x > .large-7 {
    width: 58.3333333333%;
  }
  .grid-x > .large-8 {
    width: 66.6666666667%;
  }
  .grid-x > .large-9 {
    width: 75%;
  }
  .grid-x > .large-10 {
    width: 83.3333333333%;
  }
  .grid-x > .large-11 {
    width: 91.6666666667%;
  }
  .grid-x > .large-12 {
    width: 100%;
  }
}
.grid-margin-x:not(.grid-x) > .cell {
  width: auto;
}
.grid-margin-y:not(.grid-y) > .cell {
  height: auto;
}
.grid-margin-x {
  margin-left: -2rem;
  margin-right: -2rem;
}
@media print, screen and (min-width: 48em) {
  .grid-margin-x {
    margin-left: -1rem;
    margin-right: -1rem;
  }
}
.grid-margin-x > .cell {
  width: calc(100% - 4rem);
  margin-left: 2rem;
  margin-right: 2rem;
}
@media print, screen and (min-width: 48em) {
  .grid-margin-x > .cell {
    width: calc(100% - 2rem);
    margin-left: 1rem;
    margin-right: 1rem;
  }
}
.grid-margin-x > .auto,
.grid-margin-x > .shrink {
  width: auto;
}
.grid-margin-x > .small-1 {
  width: calc(8.33333% - 4rem);
}
.grid-margin-x > .small-2 {
  width: calc(16.66667% - 4rem);
}
.grid-margin-x > .small-3 {
  width: calc(25% - 4rem);
}
.grid-margin-x > .small-4 {
  width: calc(33.33333% - 4rem);
}
.grid-margin-x > .small-5 {
  width: calc(41.66667% - 4rem);
}
.grid-margin-x > .small-6 {
  width: calc(50% - 4rem);
}
.grid-margin-x > .small-7 {
  width: calc(58.33333% - 4rem);
}
.grid-margin-x > .small-8 {
  width: calc(66.66667% - 4rem);
}
.grid-margin-x > .small-9 {
  width: calc(75% - 4rem);
}
.grid-margin-x > .small-10 {
  width: calc(83.33333% - 4rem);
}
.grid-margin-x > .small-11 {
  width: calc(91.66667% - 4rem);
}
.grid-margin-x > .small-12 {
  width: calc(100% - 4rem);
}
@media print, screen and (min-width: 48em) {
  .grid-margin-x > .auto,
  .grid-margin-x > .shrink {
    width: auto;
  }
  .grid-margin-x > .small-1 {
    width: calc(8.33333% - 2rem);
  }
  .grid-margin-x > .small-2 {
    width: calc(16.66667% - 2rem);
  }
  .grid-margin-x > .small-3 {
    width: calc(25% - 2rem);
  }
  .grid-margin-x > .small-4 {
    width: calc(33.33333% - 2rem);
  }
  .grid-margin-x > .small-5 {
    width: calc(41.66667% - 2rem);
  }
  .grid-margin-x > .small-6 {
    width: calc(50% - 2rem);
  }
  .grid-margin-x > .small-7 {
    width: calc(58.33333% - 2rem);
  }
  .grid-margin-x > .small-8 {
    width: calc(66.66667% - 2rem);
  }
  .grid-margin-x > .small-9 {
    width: calc(75% - 2rem);
  }
  .grid-margin-x > .small-10 {
    width: calc(83.33333% - 2rem);
  }
  .grid-margin-x > .small-11 {
    width: calc(91.66667% - 2rem);
  }
  .grid-margin-x > .small-12 {
    width: calc(100% - 2rem);
  }
  .grid-margin-x > .s-medium-auto,
  .grid-margin-x > .s-medium-shrink {
    width: auto;
  }
  .grid-margin-x > .s-medium-1 {
    width: calc(8.33333% - 2rem);
  }
  .grid-margin-x > .s-medium-2 {
    width: calc(16.66667% - 2rem);
  }
  .grid-margin-x > .s-medium-3 {
    width: calc(25% - 2rem);
  }
  .grid-margin-x > .s-medium-4 {
    width: calc(33.33333% - 2rem);
  }
  .grid-margin-x > .s-medium-5 {
    width: calc(41.66667% - 2rem);
  }
  .grid-margin-x > .s-medium-6 {
    width: calc(50% - 2rem);
  }
  .grid-margin-x > .s-medium-7 {
    width: calc(58.33333% - 2rem);
  }
  .grid-margin-x > .s-medium-8 {
    width: calc(66.66667% - 2rem);
  }
  .grid-margin-x > .s-medium-9 {
    width: calc(75% - 2rem);
  }
  .grid-margin-x > .s-medium-10 {
    width: calc(83.33333% - 2rem);
  }
  .grid-margin-x > .s-medium-11 {
    width: calc(91.66667% - 2rem);
  }
  .grid-margin-x > .s-medium-12 {
    width: calc(100% - 2rem);
  }
  .grid-margin-x > .medium-auto,
  .grid-margin-x > .medium-shrink {
    width: auto;
  }
  .grid-margin-x > .medium-1 {
    width: calc(8.33333% - 2rem);
  }
  .grid-margin-x > .medium-2 {
    width: calc(16.66667% - 2rem);
  }
  .grid-margin-x > .medium-3 {
    width: calc(25% - 2rem);
  }
  .grid-margin-x > .medium-4 {
    width: calc(33.33333% - 2rem);
  }
  .grid-margin-x > .medium-5 {
    width: calc(41.66667% - 2rem);
  }
  .grid-margin-x > .medium-6 {
    width: calc(50% - 2rem);
  }
  .grid-margin-x > .medium-7 {
    width: calc(58.33333% - 2rem);
  }
  .grid-margin-x > .medium-8 {
    width: calc(66.66667% - 2rem);
  }
  .grid-margin-x > .medium-9 {
    width: calc(75% - 2rem);
  }
  .grid-margin-x > .medium-10 {
    width: calc(83.33333% - 2rem);
  }
  .grid-margin-x > .medium-11 {
    width: calc(91.66667% - 2rem);
  }
  .grid-margin-x > .medium-12 {
    width: calc(100% - 2rem);
  }
}
@media print, screen and (min-width: 64em) {
  .grid-margin-x > .large-auto,
  .grid-margin-x > .large-shrink {
    width: auto;
  }
  .grid-margin-x > .large-1 {
    width: calc(8.33333% - 2rem);
  }
  .grid-margin-x > .large-2 {
    width: calc(16.66667% - 2rem);
  }
  .grid-margin-x > .large-3 {
    width: calc(25% - 2rem);
  }
  .grid-margin-x > .large-4 {
    width: calc(33.33333% - 2rem);
  }
  .grid-margin-x > .large-5 {
    width: calc(41.66667% - 2rem);
  }
  .grid-margin-x > .large-6 {
    width: calc(50% - 2rem);
  }
  .grid-margin-x > .large-7 {
    width: calc(58.33333% - 2rem);
  }
  .grid-margin-x > .large-8 {
    width: calc(66.66667% - 2rem);
  }
  .grid-margin-x > .large-9 {
    width: calc(75% - 2rem);
  }
  .grid-margin-x > .large-10 {
    width: calc(83.33333% - 2rem);
  }
  .grid-margin-x > .large-11 {
    width: calc(91.66667% - 2rem);
  }
  .grid-margin-x > .large-12 {
    width: calc(100% - 2rem);
  }
}
.grid-padding-x .grid-padding-x {
  margin-right: -2rem;
  margin-left: -2rem;
}
@media print, screen and (min-width: 48em) {
  .grid-padding-x .grid-padding-x {
    margin-right: -1rem;
    margin-left: -1rem;
  }
}
.grid-container:not(.full) > .grid-padding-x {
  margin-right: -2rem;
  margin-left: -2rem;
}
@media print, screen and (min-width: 48em) {
  .grid-container:not(.full) > .grid-padding-x {
    margin-right: -1rem;
    margin-left: -1rem;
  }
}
.grid-padding-x > .cell {
  padding-right: 2rem;
  padding-left: 2rem;
}
@media print, screen and (min-width: 48em) {
  .grid-padding-x > .cell {
    padding-right: 1rem;
    padding-left: 1rem;
  }
}
.small-up-1 > .cell {
  width: 100%;
}
.small-up-2 > .cell {
  width: 50%;
}
.small-up-3 > .cell {
  width: 33.3333333333%;
}
.small-up-4 > .cell {
  width: 25%;
}
.small-up-5 > .cell {
  width: 20%;
}
.small-up-6 > .cell {
  width: 16.6666666667%;
}
.small-up-7 > .cell {
  width: 14.2857142857%;
}
.small-up-8 > .cell {
  width: 12.5%;
}
@media print, screen and (min-width: 48em) {
  .medium-up-1 > .cell {
    width: 100%;
  }
  .medium-up-2 > .cell {
    width: 50%;
  }
  .medium-up-3 > .cell {
    width: 33.3333333333%;
  }
  .medium-up-4 > .cell {
    width: 25%;
  }
  .medium-up-5 > .cell {
    width: 20%;
  }
  .medium-up-6 > .cell {
    width: 16.6666666667%;
  }
  .medium-up-7 > .cell {
    width: 14.2857142857%;
  }
  .medium-up-8 > .cell {
    width: 12.5%;
  }
}
@media print, screen and (min-width: 64em) {
  .large-up-1 > .cell {
    width: 100%;
  }
  .large-up-2 > .cell {
    width: 50%;
  }
  .large-up-3 > .cell {
    width: 33.3333333333%;
  }
  .large-up-4 > .cell {
    width: 25%;
  }
  .large-up-5 > .cell {
    width: 20%;
  }
  .large-up-6 > .cell {
    width: 16.6666666667%;
  }
  .large-up-7 > .cell {
    width: 14.2857142857%;
  }
  .large-up-8 > .cell {
    width: 12.5%;
  }
}
.grid-margin-x.small-up-1 > .cell {
  width: calc(100% - 4rem);
}
.grid-margin-x.small-up-2 > .cell {
  width: calc(50% - 4rem);
}
.grid-margin-x.small-up-3 > .cell {
  width: calc(33.33333% - 4rem);
}
.grid-margin-x.small-up-4 > .cell {
  width: calc(25% - 4rem);
}
.grid-margin-x.small-up-5 > .cell {
  width: calc(20% - 4rem);
}
.grid-margin-x.small-up-6 > .cell {
  width: calc(16.66667% - 4rem);
}
.grid-margin-x.small-up-7 > .cell {
  width: calc(14.28571% - 4rem);
}
.grid-margin-x.small-up-8 > .cell {
  width: calc(12.5% - 4rem);
}
@media print, screen and (min-width: 48em) {
  .grid-margin-x.small-up-1 > .cell {
    width: calc(100% - 2rem);
  }
  .grid-margin-x.small-up-2 > .cell {
    width: calc(50% - 2rem);
  }
  .grid-margin-x.small-up-3 > .cell {
    width: calc(33.33333% - 2rem);
  }
  .grid-margin-x.small-up-4 > .cell {
    width: calc(25% - 2rem);
  }
  .grid-margin-x.small-up-5 > .cell {
    width: calc(20% - 2rem);
  }
  .grid-margin-x.small-up-6 > .cell {
    width: calc(16.66667% - 2rem);
  }
  .grid-margin-x.small-up-7 > .cell {
    width: calc(14.28571% - 2rem);
  }
  .grid-margin-x.small-up-8 > .cell {
    width: calc(12.5% - 2rem);
  }
  .grid-margin-x.medium-up-1 > .cell {
    width: calc(100% - 2rem);
  }
  .grid-margin-x.medium-up-2 > .cell {
    width: calc(50% - 2rem);
  }
  .grid-margin-x.medium-up-3 > .cell {
    width: calc(33.33333% - 2rem);
  }
  .grid-margin-x.medium-up-4 > .cell {
    width: calc(25% - 2rem);
  }
  .grid-margin-x.medium-up-5 > .cell {
    width: calc(20% - 2rem);
  }
  .grid-margin-x.medium-up-6 > .cell {
    width: calc(16.66667% - 2rem);
  }
  .grid-margin-x.medium-up-7 > .cell {
    width: calc(14.28571% - 2rem);
  }
  .grid-margin-x.medium-up-8 > .cell {
    width: calc(12.5% - 2rem);
  }
}
@media print, screen and (min-width: 64em) {
  .grid-margin-x.large-up-1 > .cell {
    width: calc(100% - 2rem);
  }
  .grid-margin-x.large-up-2 > .cell {
    width: calc(50% - 2rem);
  }
  .grid-margin-x.large-up-3 > .cell {
    width: calc(33.33333% - 2rem);
  }
  .grid-margin-x.large-up-4 > .cell {
    width: calc(25% - 2rem);
  }
  .grid-margin-x.large-up-5 > .cell {
    width: calc(20% - 2rem);
  }
  .grid-margin-x.large-up-6 > .cell {
    width: calc(16.66667% - 2rem);
  }
  .grid-margin-x.large-up-7 > .cell {
    width: calc(14.28571% - 2rem);
  }
  .grid-margin-x.large-up-8 > .cell {
    width: calc(12.5% - 2rem);
  }
}
.small-margin-collapse,
.small-margin-collapse > .cell {
  margin-right: 0;
  margin-left: 0;
}
.small-margin-collapse > .small-1 {
  width: 8.3333333333%;
}
.small-margin-collapse > .small-2 {
  width: 16.6666666667%;
}
.small-margin-collapse > .small-3 {
  width: 25%;
}
.small-margin-collapse > .small-4 {
  width: 33.3333333333%;
}
.small-margin-collapse > .small-5 {
  width: 41.6666666667%;
}
.small-margin-collapse > .small-6 {
  width: 50%;
}
.small-margin-collapse > .small-7 {
  width: 58.3333333333%;
}
.small-margin-collapse > .small-8 {
  width: 66.6666666667%;
}
.small-margin-collapse > .small-9 {
  width: 75%;
}
.small-margin-collapse > .small-10 {
  width: 83.3333333333%;
}
.small-margin-collapse > .small-11 {
  width: 91.6666666667%;
}
.small-margin-collapse > .small-12 {
  width: 100%;
}
@media print, screen and (min-width: 48em) {
  .small-margin-collapse > .medium-1 {
    width: 8.3333333333%;
  }
  .small-margin-collapse > .medium-2 {
    width: 16.6666666667%;
  }
  .small-margin-collapse > .medium-3 {
    width: 25%;
  }
  .small-margin-collapse > .medium-4 {
    width: 33.3333333333%;
  }
  .small-margin-collapse > .medium-5 {
    width: 41.6666666667%;
  }
  .small-margin-collapse > .medium-6 {
    width: 50%;
  }
  .small-margin-collapse > .medium-7 {
    width: 58.3333333333%;
  }
  .small-margin-collapse > .medium-8 {
    width: 66.6666666667%;
  }
  .small-margin-collapse > .medium-9 {
    width: 75%;
  }
  .small-margin-collapse > .medium-10 {
    width: 83.3333333333%;
  }
  .small-margin-collapse > .medium-11 {
    width: 91.6666666667%;
  }
  .small-margin-collapse > .medium-12 {
    width: 100%;
  }
}
@media print, screen and (min-width: 64em) {
  .small-margin-collapse > .large-1 {
    width: 8.3333333333%;
  }
  .small-margin-collapse > .large-2 {
    width: 16.6666666667%;
  }
  .small-margin-collapse > .large-3 {
    width: 25%;
  }
  .small-margin-collapse > .large-4 {
    width: 33.3333333333%;
  }
  .small-margin-collapse > .large-5 {
    width: 41.6666666667%;
  }
  .small-margin-collapse > .large-6 {
    width: 50%;
  }
  .small-margin-collapse > .large-7 {
    width: 58.3333333333%;
  }
  .small-margin-collapse > .large-8 {
    width: 66.6666666667%;
  }
  .small-margin-collapse > .large-9 {
    width: 75%;
  }
  .small-margin-collapse > .large-10 {
    width: 83.3333333333%;
  }
  .small-margin-collapse > .large-11 {
    width: 91.6666666667%;
  }
  .small-margin-collapse > .large-12 {
    width: 100%;
  }
}
.small-padding-collapse {
  margin-right: 0;
  margin-left: 0;
}
.small-padding-collapse > .cell {
  padding-right: 0;
  padding-left: 0;
}
@media print, screen and (min-width: 48em) {
  .medium-margin-collapse,
  .medium-margin-collapse > .cell {
    margin-right: 0;
    margin-left: 0;
  }
}
@media print, screen and (min-width: 48em) {
  .medium-margin-collapse > .small-1 {
    width: 8.3333333333%;
  }
  .medium-margin-collapse > .small-2 {
    width: 16.6666666667%;
  }
  .medium-margin-collapse > .small-3 {
    width: 25%;
  }
  .medium-margin-collapse > .small-4 {
    width: 33.3333333333%;
  }
  .medium-margin-collapse > .small-5 {
    width: 41.6666666667%;
  }
  .medium-margin-collapse > .small-6 {
    width: 50%;
  }
  .medium-margin-collapse > .small-7 {
    width: 58.3333333333%;
  }
  .medium-margin-collapse > .small-8 {
    width: 66.6666666667%;
  }
  .medium-margin-collapse > .small-9 {
    width: 75%;
  }
  .medium-margin-collapse > .small-10 {
    width: 83.3333333333%;
  }
  .medium-margin-collapse > .small-11 {
    width: 91.6666666667%;
  }
  .medium-margin-collapse > .small-12 {
    width: 100%;
  }
}
@media print, screen and (min-width: 48em) {
  .medium-margin-collapse > .medium-1 {
    width: 8.3333333333%;
  }
  .medium-margin-collapse > .medium-2 {
    width: 16.6666666667%;
  }
  .medium-margin-collapse > .medium-3 {
    width: 25%;
  }
  .medium-margin-collapse > .medium-4 {
    width: 33.3333333333%;
  }
  .medium-margin-collapse > .medium-5 {
    width: 41.6666666667%;
  }
  .medium-margin-collapse > .medium-6 {
    width: 50%;
  }
  .medium-margin-collapse > .medium-7 {
    width: 58.3333333333%;
  }
  .medium-margin-collapse > .medium-8 {
    width: 66.6666666667%;
  }
  .medium-margin-collapse > .medium-9 {
    width: 75%;
  }
  .medium-margin-collapse > .medium-10 {
    width: 83.3333333333%;
  }
  .medium-margin-collapse > .medium-11 {
    width: 91.6666666667%;
  }
  .medium-margin-collapse > .medium-12 {
    width: 100%;
  }
}
@media print, screen and (min-width: 64em) {
  .medium-margin-collapse > .large-1 {
    width: 8.3333333333%;
  }
  .medium-margin-collapse > .large-2 {
    width: 16.6666666667%;
  }
  .medium-margin-collapse > .large-3 {
    width: 25%;
  }
  .medium-margin-collapse > .large-4 {
    width: 33.3333333333%;
  }
  .medium-margin-collapse > .large-5 {
    width: 41.6666666667%;
  }
  .medium-margin-collapse > .large-6 {
    width: 50%;
  }
  .medium-margin-collapse > .large-7 {
    width: 58.3333333333%;
  }
  .medium-margin-collapse > .large-8 {
    width: 66.6666666667%;
  }
  .medium-margin-collapse > .large-9 {
    width: 75%;
  }
  .medium-margin-collapse > .large-10 {
    width: 83.3333333333%;
  }
  .medium-margin-collapse > .large-11 {
    width: 91.6666666667%;
  }
  .medium-margin-collapse > .large-12 {
    width: 100%;
  }
}
@media print, screen and (min-width: 48em) {
  .medium-padding-collapse {
    margin-right: 0;
    margin-left: 0;
  }
  .medium-padding-collapse > .cell {
    padding-right: 0;
    padding-left: 0;
  }
}
@media print, screen and (min-width: 64em) {
  .large-margin-collapse,
  .large-margin-collapse > .cell {
    margin-right: 0;
    margin-left: 0;
  }
}
@media print, screen and (min-width: 64em) {
  .large-margin-collapse > .small-1 {
    width: 8.3333333333%;
  }
  .large-margin-collapse > .small-2 {
    width: 16.6666666667%;
  }
  .large-margin-collapse > .small-3 {
    width: 25%;
  }
  .large-margin-collapse > .small-4 {
    width: 33.3333333333%;
  }
  .large-margin-collapse > .small-5 {
    width: 41.6666666667%;
  }
  .large-margin-collapse > .small-6 {
    width: 50%;
  }
  .large-margin-collapse > .small-7 {
    width: 58.3333333333%;
  }
  .large-margin-collapse > .small-8 {
    width: 66.6666666667%;
  }
  .large-margin-collapse > .small-9 {
    width: 75%;
  }
  .large-margin-collapse > .small-10 {
    width: 83.3333333333%;
  }
  .large-margin-collapse > .small-11 {
    width: 91.6666666667%;
  }
  .large-margin-collapse > .small-12 {
    width: 100%;
  }
}
@media print, screen and (min-width: 64em) {
  .large-margin-collapse > .medium-1 {
    width: 8.3333333333%;
  }
  .large-margin-collapse > .medium-2 {
    width: 16.6666666667%;
  }
  .large-margin-collapse > .medium-3 {
    width: 25%;
  }
  .large-margin-collapse > .medium-4 {
    width: 33.3333333333%;
  }
  .large-margin-collapse > .medium-5 {
    width: 41.6666666667%;
  }
  .large-margin-collapse > .medium-6 {
    width: 50%;
  }
  .large-margin-collapse > .medium-7 {
    width: 58.3333333333%;
  }
  .large-margin-collapse > .medium-8 {
    width: 66.6666666667%;
  }
  .large-margin-collapse > .medium-9 {
    width: 75%;
  }
  .large-margin-collapse > .medium-10 {
    width: 83.3333333333%;
  }
  .large-margin-collapse > .medium-11 {
    width: 91.6666666667%;
  }
  .large-margin-collapse > .medium-12 {
    width: 100%;
  }
}
@media print, screen and (min-width: 64em) {
  .large-margin-collapse > .large-1 {
    width: 8.3333333333%;
  }
  .large-margin-collapse > .large-2 {
    width: 16.6666666667%;
  }
  .large-margin-collapse > .large-3 {
    width: 25%;
  }
  .large-margin-collapse > .large-4 {
    width: 33.3333333333%;
  }
  .large-margin-collapse > .large-5 {
    width: 41.6666666667%;
  }
  .large-margin-collapse > .large-6 {
    width: 50%;
  }
  .large-margin-collapse > .large-7 {
    width: 58.3333333333%;
  }
  .large-margin-collapse > .large-8 {
    width: 66.6666666667%;
  }
  .large-margin-collapse > .large-9 {
    width: 75%;
  }
  .large-margin-collapse > .large-10 {
    width: 83.3333333333%;
  }
  .large-margin-collapse > .large-11 {
    width: 91.6666666667%;
  }
  .large-margin-collapse > .large-12 {
    width: 100%;
  }
}
@media print, screen and (min-width: 64em) {
  .large-padding-collapse {
    margin-right: 0;
    margin-left: 0;
  }
  .large-padding-collapse > .cell {
    padding-right: 0;
    padding-left: 0;
  }
}
.small-offset-0 {
  margin-left: 0;
}
.grid-margin-x > .small-offset-0 {
  margin-left: 2rem;
}
.small-offset-1 {
  margin-left: 8.3333333333%;
}
.grid-margin-x > .small-offset-1 {
  margin-left: calc(8.33333% + 2rem);
}
.small-offset-2 {
  margin-left: 16.6666666667%;
}
.grid-margin-x > .small-offset-2 {
  margin-left: calc(16.66667% + 2rem);
}
.small-offset-3 {
  margin-left: 25%;
}
.grid-margin-x > .small-offset-3 {
  margin-left: calc(25% + 2rem);
}
.small-offset-4 {
  margin-left: 33.3333333333%;
}
.grid-margin-x > .small-offset-4 {
  margin-left: calc(33.33333% + 2rem);
}
.small-offset-5 {
  margin-left: 41.6666666667%;
}
.grid-margin-x > .small-offset-5 {
  margin-left: calc(41.66667% + 2rem);
}
.small-offset-6 {
  margin-left: 50%;
}
.grid-margin-x > .small-offset-6 {
  margin-left: calc(50% + 2rem);
}
.small-offset-7 {
  margin-left: 58.3333333333%;
}
.grid-margin-x > .small-offset-7 {
  margin-left: calc(58.33333% + 2rem);
}
.small-offset-8 {
  margin-left: 66.6666666667%;
}
.grid-margin-x > .small-offset-8 {
  margin-left: calc(66.66667% + 2rem);
}
.small-offset-9 {
  margin-left: 75%;
}
.grid-margin-x > .small-offset-9 {
  margin-left: calc(75% + 2rem);
}
.small-offset-10 {
  margin-left: 83.3333333333%;
}
.grid-margin-x > .small-offset-10 {
  margin-left: calc(83.33333% + 2rem);
}
.small-offset-11 {
  margin-left: 91.6666666667%;
}
.grid-margin-x > .small-offset-11 {
  margin-left: calc(91.66667% + 2rem);
}
@media print, screen and (min-width: 48em) {
  .medium-offset-0 {
    margin-left: 0;
  }
  .grid-margin-x > .medium-offset-0 {
    margin-left: 1rem;
  }
  .medium-offset-1 {
    margin-left: 8.3333333333%;
  }
  .grid-margin-x > .medium-offset-1 {
    margin-left: calc(8.33333% + 1rem);
  }
  .medium-offset-2 {
    margin-left: 16.6666666667%;
  }
  .grid-margin-x > .medium-offset-2 {
    margin-left: calc(16.66667% + 1rem);
  }
  .medium-offset-3 {
    margin-left: 25%;
  }
  .grid-margin-x > .medium-offset-3 {
    margin-left: calc(25% + 1rem);
  }
  .medium-offset-4 {
    margin-left: 33.3333333333%;
  }
  .grid-margin-x > .medium-offset-4 {
    margin-left: calc(33.33333% + 1rem);
  }
  .medium-offset-5 {
    margin-left: 41.6666666667%;
  }
  .grid-margin-x > .medium-offset-5 {
    margin-left: calc(41.66667% + 1rem);
  }
  .medium-offset-6 {
    margin-left: 50%;
  }
  .grid-margin-x > .medium-offset-6 {
    margin-left: calc(50% + 1rem);
  }
  .medium-offset-7 {
    margin-left: 58.3333333333%;
  }
  .grid-margin-x > .medium-offset-7 {
    margin-left: calc(58.33333% + 1rem);
  }
  .medium-offset-8 {
    margin-left: 66.6666666667%;
  }
  .grid-margin-x > .medium-offset-8 {
    margin-left: calc(66.66667% + 1rem);
  }
  .medium-offset-9 {
    margin-left: 75%;
  }
  .grid-margin-x > .medium-offset-9 {
    margin-left: calc(75% + 1rem);
  }
  .medium-offset-10 {
    margin-left: 83.3333333333%;
  }
  .grid-margin-x > .medium-offset-10 {
    margin-left: calc(83.33333% + 1rem);
  }
  .medium-offset-11 {
    margin-left: 91.6666666667%;
  }
  .grid-margin-x > .medium-offset-11 {
    margin-left: calc(91.66667% + 1rem);
  }
}
@media print, screen and (min-width: 64em) {
  .large-offset-0 {
    margin-left: 0;
  }
  .grid-margin-x > .large-offset-0 {
    margin-left: 1rem;
  }
  .large-offset-1 {
    margin-left: 8.3333333333%;
  }
  .grid-margin-x > .large-offset-1 {
    margin-left: calc(8.33333% + 1rem);
  }
  .large-offset-2 {
    margin-left: 16.6666666667%;
  }
  .grid-margin-x > .large-offset-2 {
    margin-left: calc(16.66667% + 1rem);
  }
  .large-offset-3 {
    margin-left: 25%;
  }
  .grid-margin-x > .large-offset-3 {
    margin-left: calc(25% + 1rem);
  }
  .large-offset-4 {
    margin-left: 33.3333333333%;
  }
  .grid-margin-x > .large-offset-4 {
    margin-left: calc(33.33333% + 1rem);
  }
  .large-offset-5 {
    margin-left: 41.6666666667%;
  }
  .grid-margin-x > .large-offset-5 {
    margin-left: calc(41.66667% + 1rem);
  }
  .large-offset-6 {
    margin-left: 50%;
  }
  .grid-margin-x > .large-offset-6 {
    margin-left: calc(50% + 1rem);
  }
  .large-offset-7 {
    margin-left: 58.3333333333%;
  }
  .grid-margin-x > .large-offset-7 {
    margin-left: calc(58.33333% + 1rem);
  }
  .large-offset-8 {
    margin-left: 66.6666666667%;
  }
  .grid-margin-x > .large-offset-8 {
    margin-left: calc(66.66667% + 1rem);
  }
  .large-offset-9 {
    margin-left: 75%;
  }
  .grid-margin-x > .large-offset-9 {
    margin-left: calc(75% + 1rem);
  }
  .large-offset-10 {
    margin-left: 83.3333333333%;
  }
  .grid-margin-x > .large-offset-10 {
    margin-left: calc(83.33333% + 1rem);
  }
  .large-offset-11 {
    margin-left: 91.6666666667%;
  }
  .grid-margin-x > .large-offset-11 {
    margin-left: calc(91.66667% + 1rem);
  }
}
.grid-y {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-flow: column nowrap;
  flex-flow: column nowrap;
}
.grid-y > .cell {
  height: auto;
  max-height: none;
}
.grid-y > .auto,
.grid-y > .shrink {
  height: auto;
}
.grid-y > .small-1,
.grid-y > .small-2,
.grid-y > .small-3,
.grid-y > .small-4,
.grid-y > .small-5,
.grid-y > .small-6,
.grid-y > .small-7,
.grid-y > .small-8,
.grid-y > .small-9,
.grid-y > .small-10,
.grid-y > .small-11,
.grid-y > .small-12,
.grid-y > .small-full,
.grid-y > .small-shrink {
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
}
@media print, screen and (min-width: 48em) {
  .grid-y > .medium-1,
  .grid-y > .medium-2,
  .grid-y > .medium-3,
  .grid-y > .medium-4,
  .grid-y > .medium-5,
  .grid-y > .medium-6,
  .grid-y > .medium-7,
  .grid-y > .medium-8,
  .grid-y > .medium-9,
  .grid-y > .medium-10,
  .grid-y > .medium-11,
  .grid-y > .medium-12,
  .grid-y > .medium-full,
  .grid-y > .medium-shrink {
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }
}
@media print, screen and (min-width: 64em) {
  .grid-y > .large-1,
  .grid-y > .large-2,
  .grid-y > .large-3,
  .grid-y > .large-4,
  .grid-y > .large-5,
  .grid-y > .large-6,
  .grid-y > .large-7,
  .grid-y > .large-8,
  .grid-y > .large-9,
  .grid-y > .large-10,
  .grid-y > .large-11,
  .grid-y > .large-12,
  .grid-y > .large-full,
  .grid-y > .large-shrink {
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }
}
.grid-y > .small-1,
.grid-y > .small-2,
.grid-y > .small-3,
.grid-y > .small-4,
.grid-y > .small-5,
.grid-y > .small-6,
.grid-y > .small-7,
.grid-y > .small-8,
.grid-y > .small-9,
.grid-y > .small-10,
.grid-y > .small-11,
.grid-y > .small-12 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
}
.grid-y > .small-1 {
  height: 8.3333333333%;
}
.grid-y > .small-2 {
  height: 16.6666666667%;
}
.grid-y > .small-3 {
  height: 25%;
}
.grid-y > .small-4 {
  height: 33.3333333333%;
}
.grid-y > .small-5 {
  height: 41.6666666667%;
}
.grid-y > .small-6 {
  height: 50%;
}
.grid-y > .small-7 {
  height: 58.3333333333%;
}
.grid-y > .small-8 {
  height: 66.6666666667%;
}
.grid-y > .small-9 {
  height: 75%;
}
.grid-y > .small-10 {
  height: 83.3333333333%;
}
.grid-y > .small-11 {
  height: 91.6666666667%;
}
.grid-y > .small-12 {
  height: 100%;
}
@media print, screen and (min-width: 48em) {
  .grid-y > .medium-auto {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 0px;
    flex: 1 1 0px;
    height: auto;
  }
  .grid-y > .medium-1,
  .grid-y > .medium-2,
  .grid-y > .medium-3,
  .grid-y > .medium-4,
  .grid-y > .medium-5,
  .grid-y > .medium-6,
  .grid-y > .medium-7,
  .grid-y > .medium-8,
  .grid-y > .medium-9,
  .grid-y > .medium-10,
  .grid-y > .medium-11,
  .grid-y > .medium-12,
  .grid-y > .medium-shrink {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
  }
  .grid-y > .medium-shrink {
    height: auto;
  }
  .grid-y > .medium-1 {
    height: 8.3333333333%;
  }
  .grid-y > .medium-2 {
    height: 16.6666666667%;
  }
  .grid-y > .medium-3 {
    height: 25%;
  }
  .grid-y > .medium-4 {
    height: 33.3333333333%;
  }
  .grid-y > .medium-5 {
    height: 41.6666666667%;
  }
  .grid-y > .medium-6 {
    height: 50%;
  }
  .grid-y > .medium-7 {
    height: 58.3333333333%;
  }
  .grid-y > .medium-8 {
    height: 66.6666666667%;
  }
  .grid-y > .medium-9 {
    height: 75%;
  }
  .grid-y > .medium-10 {
    height: 83.3333333333%;
  }
  .grid-y > .medium-11 {
    height: 91.6666666667%;
  }
  .grid-y > .medium-12 {
    height: 100%;
  }
}
@media print, screen and (min-width: 64em) {
  .grid-y > .large-auto {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 0px;
    flex: 1 1 0px;
    height: auto;
  }
  .grid-y > .large-1,
  .grid-y > .large-2,
  .grid-y > .large-3,
  .grid-y > .large-4,
  .grid-y > .large-5,
  .grid-y > .large-6,
  .grid-y > .large-7,
  .grid-y > .large-8,
  .grid-y > .large-9,
  .grid-y > .large-10,
  .grid-y > .large-11,
  .grid-y > .large-12,
  .grid-y > .large-shrink {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
  }
  .grid-y > .large-shrink {
    height: auto;
  }
  .grid-y > .large-1 {
    height: 8.3333333333%;
  }
  .grid-y > .large-2 {
    height: 16.6666666667%;
  }
  .grid-y > .large-3 {
    height: 25%;
  }
  .grid-y > .large-4 {
    height: 33.3333333333%;
  }
  .grid-y > .large-5 {
    height: 41.6666666667%;
  }
  .grid-y > .large-6 {
    height: 50%;
  }
  .grid-y > .large-7 {
    height: 58.3333333333%;
  }
  .grid-y > .large-8 {
    height: 66.6666666667%;
  }
  .grid-y > .large-9 {
    height: 75%;
  }
  .grid-y > .large-10 {
    height: 83.3333333333%;
  }
  .grid-y > .large-11 {
    height: 91.6666666667%;
  }
  .grid-y > .large-12 {
    height: 100%;
  }
}
.grid-padding-y .grid-padding-y {
  margin-top: -2rem;
  margin-bottom: -2rem;
}
@media print, screen and (min-width: 48em) {
  .grid-padding-y .grid-padding-y {
    margin-top: -1rem;
    margin-bottom: -1rem;
  }
}
.grid-padding-y > .cell {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
@media print, screen and (min-width: 48em) {
  .grid-padding-y > .cell {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }
}
.grid-frame {
  overflow: hidden;
  position: relative;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  width: 100vw;
}
.cell .grid-frame {
  width: 100%;
}
.cell-block {
  overflow-x: auto;
  max-width: 100%;
}
.cell-block,
.cell-block-y {
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: -ms-autohiding-scrollbar;
}
.cell-block-y {
  overflow-y: auto;
  max-height: 100%;
  min-height: 100%;
}
.cell-block-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  max-height: 100%;
}
.cell-block-container > .grid-x {
  max-height: 100%;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
}
@media print, screen and (min-width: 48em) {
  .medium-grid-frame {
    overflow: hidden;
    position: relative;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-box-align: stretch;
    -ms-flex-align: stretch;
    align-items: stretch;
    width: 100vw;
  }
  .cell .medium-grid-frame {
    width: 100%;
  }
  .medium-cell-block {
    overflow-x: auto;
    max-width: 100%;
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: -ms-autohiding-scrollbar;
  }
  .medium-cell-block-container {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    max-height: 100%;
  }
  .medium-cell-block-container > .grid-x {
    max-height: 100%;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
  }
  .medium-cell-block-y {
    overflow-y: auto;
    max-height: 100%;
    min-height: 100%;
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: -ms-autohiding-scrollbar;
  }
}
@media print, screen and (min-width: 64em) {
  .large-grid-frame {
    overflow: hidden;
    position: relative;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-box-align: stretch;
    -ms-flex-align: stretch;
    align-items: stretch;
    width: 100vw;
  }
  .cell .large-grid-frame {
    width: 100%;
  }
  .large-cell-block {
    overflow-x: auto;
    max-width: 100%;
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: -ms-autohiding-scrollbar;
  }
  .large-cell-block-container {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    max-height: 100%;
  }
  .large-cell-block-container > .grid-x {
    max-height: 100%;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
  }
  .large-cell-block-y {
    overflow-y: auto;
    max-height: 100%;
    min-height: 100%;
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: -ms-autohiding-scrollbar;
  }
}
.grid-y.grid-frame {
  width: auto;
  overflow: hidden;
  position: relative;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  height: 100vh;
}
@media print, screen and (min-width: 48em) {
  .grid-y.medium-grid-frame {
    width: auto;
    overflow: hidden;
    position: relative;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-box-align: stretch;
    -ms-flex-align: stretch;
    align-items: stretch;
    height: 100vh;
  }
}
@media print, screen and (min-width: 64em) {
  .grid-y.large-grid-frame {
    width: auto;
    overflow: hidden;
    position: relative;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-box-align: stretch;
    -ms-flex-align: stretch;
    align-items: stretch;
    height: 100vh;
  }
}
.cell .grid-y.grid-frame {
  height: 100%;
}
@media print, screen and (min-width: 48em) {
  .cell .grid-y.medium-grid-frame {
    height: 100%;
  }
}
@media print, screen and (min-width: 64em) {
  .cell .grid-y.large-grid-frame {
    height: 100%;
  }
}
.grid-margin-y {
  margin-top: -2rem;
  margin-bottom: -2rem;
}
@media print, screen and (min-width: 48em) {
  .grid-margin-y {
    margin-top: -1rem;
    margin-bottom: -1rem;
  }
}
.grid-margin-y > .cell {
  height: calc(100% - 4rem);
  margin-top: 2rem;
  margin-bottom: 2rem;
}
@media print, screen and (min-width: 48em) {
  .grid-margin-y > .cell {
    height: calc(100% - 2rem);
    margin-top: 1rem;
    margin-bottom: 1rem;
  }
}
.grid-margin-y > .auto,
.grid-margin-y > .shrink {
  height: auto;
}
.grid-margin-y > .small-1 {
  height: calc(8.33333% - 4rem);
}
.grid-margin-y > .small-2 {
  height: calc(16.66667% - 4rem);
}
.grid-margin-y > .small-3 {
  height: calc(25% - 4rem);
}
.grid-margin-y > .small-4 {
  height: calc(33.33333% - 4rem);
}
.grid-margin-y > .small-5 {
  height: calc(41.66667% - 4rem);
}
.grid-margin-y > .small-6 {
  height: calc(50% - 4rem);
}
.grid-margin-y > .small-7 {
  height: calc(58.33333% - 4rem);
}
.grid-margin-y > .small-8 {
  height: calc(66.66667% - 4rem);
}
.grid-margin-y > .small-9 {
  height: calc(75% - 4rem);
}
.grid-margin-y > .small-10 {
  height: calc(83.33333% - 4rem);
}
.grid-margin-y > .small-11 {
  height: calc(91.66667% - 4rem);
}
.grid-margin-y > .small-12 {
  height: calc(100% - 4rem);
}
@media print, screen and (min-width: 48em) {
  .grid-margin-y > .auto,
  .grid-margin-y > .shrink {
    height: auto;
  }
  .grid-margin-y > .small-1 {
    height: calc(8.33333% - 2rem);
  }
  .grid-margin-y > .small-2 {
    height: calc(16.66667% - 2rem);
  }
  .grid-margin-y > .small-3 {
    height: calc(25% - 2rem);
  }
  .grid-margin-y > .small-4 {
    height: calc(33.33333% - 2rem);
  }
  .grid-margin-y > .small-5 {
    height: calc(41.66667% - 2rem);
  }
  .grid-margin-y > .small-6 {
    height: calc(50% - 2rem);
  }
  .grid-margin-y > .small-7 {
    height: calc(58.33333% - 2rem);
  }
  .grid-margin-y > .small-8 {
    height: calc(66.66667% - 2rem);
  }
  .grid-margin-y > .small-9 {
    height: calc(75% - 2rem);
  }
  .grid-margin-y > .small-10 {
    height: calc(83.33333% - 2rem);
  }
  .grid-margin-y > .small-11 {
    height: calc(91.66667% - 2rem);
  }
  .grid-margin-y > .small-12 {
    height: calc(100% - 2rem);
  }
  .grid-margin-y > .s-medium-auto,
  .grid-margin-y > .s-medium-shrink {
    height: auto;
  }
  .grid-margin-y > .s-medium-1 {
    height: calc(8.33333% - 2rem);
  }
  .grid-margin-y > .s-medium-2 {
    height: calc(16.66667% - 2rem);
  }
  .grid-margin-y > .s-medium-3 {
    height: calc(25% - 2rem);
  }
  .grid-margin-y > .s-medium-4 {
    height: calc(33.33333% - 2rem);
  }
  .grid-margin-y > .s-medium-5 {
    height: calc(41.66667% - 2rem);
  }
  .grid-margin-y > .s-medium-6 {
    height: calc(50% - 2rem);
  }
  .grid-margin-y > .s-medium-7 {
    height: calc(58.33333% - 2rem);
  }
  .grid-margin-y > .s-medium-8 {
    height: calc(66.66667% - 2rem);
  }
  .grid-margin-y > .s-medium-9 {
    height: calc(75% - 2rem);
  }
  .grid-margin-y > .s-medium-10 {
    height: calc(83.33333% - 2rem);
  }
  .grid-margin-y > .s-medium-11 {
    height: calc(91.66667% - 2rem);
  }
  .grid-margin-y > .s-medium-12 {
    height: calc(100% - 2rem);
  }
  .grid-margin-y > .medium-auto,
  .grid-margin-y > .medium-shrink {
    height: auto;
  }
  .grid-margin-y > .medium-1 {
    height: calc(8.33333% - 2rem);
  }
  .grid-margin-y > .medium-2 {
    height: calc(16.66667% - 2rem);
  }
  .grid-margin-y > .medium-3 {
    height: calc(25% - 2rem);
  }
  .grid-margin-y > .medium-4 {
    height: calc(33.33333% - 2rem);
  }
  .grid-margin-y > .medium-5 {
    height: calc(41.66667% - 2rem);
  }
  .grid-margin-y > .medium-6 {
    height: calc(50% - 2rem);
  }
  .grid-margin-y > .medium-7 {
    height: calc(58.33333% - 2rem);
  }
  .grid-margin-y > .medium-8 {
    height: calc(66.66667% - 2rem);
  }
  .grid-margin-y > .medium-9 {
    height: calc(75% - 2rem);
  }
  .grid-margin-y > .medium-10 {
    height: calc(83.33333% - 2rem);
  }
  .grid-margin-y > .medium-11 {
    height: calc(91.66667% - 2rem);
  }
  .grid-margin-y > .medium-12 {
    height: calc(100% - 2rem);
  }
}
@media print, screen and (min-width: 64em) {
  .grid-margin-y > .large-auto,
  .grid-margin-y > .large-shrink {
    height: auto;
  }
  .grid-margin-y > .large-1 {
    height: calc(8.33333% - 2rem);
  }
  .grid-margin-y > .large-2 {
    height: calc(16.66667% - 2rem);
  }
  .grid-margin-y > .large-3 {
    height: calc(25% - 2rem);
  }
  .grid-margin-y > .large-4 {
    height: calc(33.33333% - 2rem);
  }
  .grid-margin-y > .large-5 {
    height: calc(41.66667% - 2rem);
  }
  .grid-margin-y > .large-6 {
    height: calc(50% - 2rem);
  }
  .grid-margin-y > .large-7 {
    height: calc(58.33333% - 2rem);
  }
  .grid-margin-y > .large-8 {
    height: calc(66.66667% - 2rem);
  }
  .grid-margin-y > .large-9 {
    height: calc(75% - 2rem);
  }
  .grid-margin-y > .large-10 {
    height: calc(83.33333% - 2rem);
  }
  .grid-margin-y > .large-11 {
    height: calc(91.66667% - 2rem);
  }
  .grid-margin-y > .large-12 {
    height: calc(100% - 2rem);
  }
}
.grid-frame.grid-margin-y {
  height: calc(100vh + 4rem);
}
@media print, screen and (min-width: 48em) {
  .grid-frame.grid-margin-y {
    height: calc(100vh + 2rem);
  }
}
@media print, screen and (min-width: 64em) {
  .grid-frame.grid-margin-y {
    height: calc(100vh + 2rem);
  }
}
@media print, screen and (min-width: 48em) {
  .grid-margin-y.medium-grid-frame {
    height: calc(100vh + 2rem);
  }
}
@media print, screen and (min-width: 64em) {
  .grid-margin-y.large-grid-frame {
    height: calc(100vh + 2rem);
  }
}
.hide {
  display: none !important;
}
.invisible {
  visibility: hidden;
}
@media print, screen and (max-width: 19.99875em) {
  .hide-for-small-only {
    display: none !important;
  }
}
@media screen and (max-width: 0em), screen and (min-width: 20em) {
  .show-for-small-only {
    display: none !important;
  }
}
@media print, screen and (min-width: 48em) {
  .hide-for-medium {
    display: none !important;
  }
}
@media screen and (max-width: 47.99875em) {
  .show-for-medium {
    display: none !important;
  }
}
@media print, screen and (min-width: 48em) and (max-width: 64.06125em) {
  .hide-for-medium-only {
    display: none !important;
  }
}
@media screen and (max-width: 47.99875em), screen and (min-width: 64.0625em) {
  .show-for-medium-only {
    display: none !important;
  }
}
@media print, screen and (min-width: 64em) {
  .hide-for-large {
    display: none !important;
  }
}
@media screen and (max-width: 63.99875em) {
  .show-for-large {
    display: none !important;
  }
}
@media print, screen and (min-width: 64em) and (max-width: 79.99875em) {
  .hide-for-large-only {
    display: none !important;
  }
}
@media screen and (max-width: 63.99875em), screen and (min-width: 80em) {
  .show-for-large-only {
    display: none !important;
  }
}
.show-for-sr,
.show-on-focus {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}
.show-on-focus:active,
.show-on-focus:focus {
  position: static !important;
  width: auto !important;
  height: auto !important;
  overflow: visible !important;
  clip: auto !important;
  white-space: normal !important;
}
.hide-for-portrait,
.show-for-landscape {
  display: block !important;
}
@media print, screen and (orientation: landscape) {
  .hide-for-portrait,
  .show-for-landscape {
    display: block !important;
  }
}
@media screen and (orientation: portrait) {
  .hide-for-portrait,
  .show-for-landscape {
    display: none !important;
  }
}
.hide-for-landscape,
.show-for-portrait {
  display: none !important;
}
@media print, screen and (orientation: landscape) {
  .hide-for-landscape,
  .show-for-portrait {
    display: none !important;
  }
}
@media screen and (orientation: portrait) {
  .hide-for-landscape,
  .show-for-portrait {
    display: block !important;
  }
}
.float-left {
  float: left !important;
}
.float-right {
  float: right !important;
}
.float-center {
  display: block;
  margin-right: auto;
  margin-left: auto;
}
.clearfix:after,
.clearfix:before {
  display: table;
  content: " ";
  -ms-flex-preferred-size: 0;
  flex-basis: 0;
  -webkit-box-ordinal-group: 2;
  -ms-flex-order: 1;
  order: 1;
}
.clearfix:after {
  clear: both;
}
body {
  font-family: du-font-secondary-regular, du-font-primary-l;
  margin: 0;
  padding: 0;
  font-size: 17px;
}
[dir="rtl"] body {
  font-family: du-font-secondary-regular-ar, du-font-primary-l;
}
body.fade-out {
  opacity: 0;
  -webkit-transition: none;
  transition: none;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 400;
}
:focus {
  outline: none;
}
b {
  font-weight: 700;
}
a {
  text-decoration: none;
  -webkit-transition: 0.3s ease-out;
  transition: 0.3s ease-out;
}
a:hover {
  color: inherit;
}
[dir="ltr"] .body ul {
  padding-left: 0;
}
[dir="rtl"] .body ul {
  padding-right: 0;
}
ul li {
  list-style: none;
}
section {
  padding: 60px 0;
}
@media only screen and (max-width: 991px) {
  section {
    padding: 40px 0;
  }
}
* {
  margin-top: 0;
}
img {
  text-indent: -9999px;
}
img:not([src]) {
  opacity: 0 !important;
}
.body {
  overflow: hidden;
  padding-top: 110px;
}
.colorInversion {
  -webkit-filter: grayscale(100%) !important;
  filter: grayscale(100%) !important;
}
.container {
  max-width: 1266px;
  margin: 0 auto;
}
@media only screen and (max-width: 767px) {
  .container {
    margin: 30px;
  }
}
@media only screen and (max-width: 991px) {
  .container {
    margin: 20px;
  }
}
.text-center {
  text-align: center;
}
.shoulder {
  font-size: 1.25rem;
  margin-bottom: 0 !important;
  font-family: du-font-secondary-regular, du-font-primary-l;
}
[dir="rtl"] .shoulder {
  font-family: du-font-secondary-regular-ar, du-font-primary-l;
}
.headline1 {
  font-family: du-font-primary-bold;
  font-size: 3rem;
  margin-bottom: 10px;
  line-height: 1.2;
}
@media only screen and (max-width: 767px) {
  .headline1 {
    font-size: 1.562rem;
  }
}
.headline2 {
  font-family: du-font-primary-bold;
  font-size: 1.937rem;
  line-height: 1.3;
  margin-bottom: 5px;
}
@media only screen and (max-width: 767px) {
  .headline2 {
    font-size: 1.375rem;
  }
}
.headline3 {
  font-family: du-font-primary-bold;
  font-size: 1.562rem;
  line-height: 1.3;
  margin-bottom: 9px;
}
@media only screen and (max-width: 767px) {
  .headline3 {
    font-size: 1.25rem;
    line-height: 1;
  }
}
.headline4 {
  font-family: du-font-primary-bold;
  font-size: 1.375rem;
  line-height: 1.3;
}
@media only screen and (max-width: 767px) {
  .headline4 {
    font-size: 1.25rem;
    line-height: 1;
  }
}
.headline5 {
  font-family: du-font-primary-bold;
  font-size: 1.25rem;
  line-height: 1.3;
}
@media only screen and (max-width: 767px) {
  .headline5 {
    font-size: 1.962rem;
    line-height: 1;
  }
}
.headline6 {
  font-family: du-font-primary-bold;
  font-size: 2.25rem;
  line-height: 1.3;
}
@media only screen and (max-width: 767px) {
  .headline6 {
    font-size: 1.93rem;
    line-height: 1;
  }
}
.subtitle1 {
  font-size: 1.25rem;
  font-family: du-font-secondary-bold, du-font-primary-b;
  margin-bottom: 0;
}
@media only screen and (max-width: 767px) {
  .subtitle1 {
    font-size: 1.062rem;
  }
}
[dir="rtl"] .subtitle1 {
  font-family: du-font-secondary-bold-ar, du-font-primary-b;
}
.subtitle2 {
  font-size: 1.25rem;
  font-family: du-font-secondary-bold, du-font-primary-b;
  margin-bottom: 0;
}
@media only screen and (max-width: 767px) {
  .subtitle2 {
    font-size: 1.062rem;
    line-height: 1.2;
  }
}
[dir="rtl"] .subtitle2 {
  font-family: du-font-secondary-bold-ar, du-font-primary-b;
}
.subtitle3 {
  font-size: 1.062rem;
  font-weight: 100;
}
.description1 {
  font-size: 1.25rem;
  font-family: du-font-secondary-regular, du-font-primary-l;
  margin-bottom: 0;
  font-weight: 100;
}
@media only screen and (max-width: 767px) {
  .description1 {
    font-size: 1.062rem;
  }
}
[dir="rtl"] .description1 {
  font-family: du-font-secondary-regular-ar, du-font-primary-l;
}
.body-text-small {
  font-size: 1.062rem;
  font-weight: 100;
}
@media only screen and (max-width: 767px) {
  .body-text-small {
    line-height: 1.5;
  }
}
[data-color="light"] {
  color: #fff;
}
[data-color="dark"] {
  color: #333;
}
.separation {
  border-top: 1px solid #ddd;
  margin: 20px 0;
  position: relative;
}
.card-link {
  color: #c724b1;
  font-family: du-font-secondary-bold, du-font-primary-b;
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  -ms-flex-item-align: end;
  align-self: flex-end;
  line-height: 1.2;
  font-size: 1.062rem;
  cursor: pointer;
}
[dir="rtl"] .card-link {
  font-family: du-font-secondary-bold-ar, du-font-primary-b;
}
.card-link .svg-bg-icon {
  display: inline-block;
  width: 16px;
  height: 12px;
  vertical-align: text-bottom;
  -webkit-transition: 0.3s ease-out;
  transition: 0.3s ease-out;
  -ms-flex-item-align: center;
  align-self: center;
}
[dir="ltr"] .card-link .svg-bg-icon {
  margin-left: 7px;
}
[dir="rtl"] .card-link .svg-bg-icon {
  margin-right: 7px;
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
}
.card-link:hover {
  color: #c724b1;
}
[dir="ltr"] .card-link:hover .svg-bg-icon {
  -webkit-transform: translateX(5px);
  transform: translateX(5px);
}
[dir="rtl"] .card-link:hover .svg-bg-icon {
  -webkit-transform: translateX(-5px) rotate(180deg);
  transform: translateX(-5px) rotate(180deg);
}
.btn {
  background: #6218be;
  background-image: linear-gradient(33deg, #753bbd 8%, #c724b1 53%);
  margin: 0;
  padding: 0 1.5em;
  border-radius: 7px;
  font-size: 1.062rem;
  color: #fff;
  letter-spacing: 0;
  text-align: center;
  white-space: nowrap;
  text-decoration: none;
  font-family: du-font-secondary-medium, du-font-primary-r;
  -webkit-transition: 0.3s ease-out;
  transition: 0.3s ease-out;
  position: relative;
  display: inline-block;
  line-height: 2.8;
  z-index: 1;
  min-width: 180px;
  text-overflow: ellipsis;
  overflow: hidden;
}
[dir="rtl"] .btn {
  font-family: du-font-secondary-medium-ar, du-font-primary-r;
  background-image: linear-gradient(-33deg, #753bbd 8%, #c724b1 53%);
}
@media only screen and (min-width: 767px) {
  .btn {
    font-size: 1.25rem;
    min-width: 220px;
    line-height: 2.6;
  }
}
.btn:before {
  border-radius: 6px;
  position: absolute;
  content: "";
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: -2;
  -webkit-transition: opacity 0.5s linear;
  transition: opacity 0.5s linear;
  opacity: 0;
  background-image: linear-gradient(12deg, #c724b1, #c724b1 40%, #753bbd);
}
[dir="rtl"] .btn:before {
  background-image: linear-gradient(-12deg, #c724b1, #c724b1 40%, #753bbd);
}
.btn:active,
.btn:hover,
.btn:visited {
  color: #fff;
  opacity: 1;
}
.btn:hover:before {
  opacity: 1;
}
.btn.secondary {
  background-color: #fff;
  border: 1px solid #ddd;
  color: #c724b1;
  background: none;
  font-size: 1.062rem;
  width: 80%;
  display: block;
  margin: 0 auto;
}
.btn.secondary:before {
  background-image: linear-gradient(33deg, #753bbd 8%, #c724b1 53%);
}
[dir="rtl"] .btn.secondary:before {
  background-image: linear-gradient(-33deg, #753bbd 8%, #c724b1 53%);
}
.btn.secondary:hover {
  border-color: #fff;
  color: #fff;
  background-color: transparent;
}
.btn.white {
  background-color: #fff;
  background-image: none;
  color: #c724b1;
}
.btn.border {
  background-color: #f8f8f8;
  min-width: 180px;
  background-image: linear-gradient(33deg, #753bbd 8%, #c724b1 53%);
  padding: 2px 1.5em;
  color: #c724b1;
  font-size: 1.062rem;
}
[dir="rtl"] .btn.border {
  background-image: linear-gradient(-33deg, #753bbd 8%, #c724b1 53%);
}
.btn.border:before {
  background-color: #f8f8f8;
  opacity: 1;
  background-image: none !important;
  margin: 2px;
}
.btn.border:hover {
  color: #fff;
}
.btn.border:hover:before {
  opacity: 0;
}
@media only screen and (max-width: 767px) {
  .btn.border {
    width: 100%;
  }
}
@media only screen and (max-width: 767px) {
  .grid-container.full-mobile {
    padding-right: 0;
    padding-left: 0;
    max-width: 100%;
    margin-left: auto;
    margin-right: auto;
  }
}
.slick-dots {
  text-align: center;
  margin: 0;
}
.slick-dots li {
  position: relative;
  display: inline-block;
  margin: 0 3px;
}
.slick-dots li,
.slick-dots li button {
  width: 10px;
  height: 10px;
  padding: 0;
  cursor: pointer;
}
.slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}
.slick-dots li button:before {
  content: "";
  font-size: 22px;
  line-height: 20px;
  position: absolute;
  top: 0;
  left: 0;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #c724b1;
  text-align: center;
  opacity: 0.5;
  color: #333;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
.slick-dots li.slick-active button:before {
  width: 10px;
  height: 10px;
  opacity: 1;
}
.online-exclusive {
  position: absolute;
  right: -10px;
  top: 10px;
  width: 175px;
  height: 175px;
  background-size: cover;
  background-repeat: no-repeat;
  z-index: 9;
}
[dir="ltr"] .online-exclusive {
  background-position: 100%;
}
[dir="rtl"] .online-exclusive {
  background-position: 0;
  right: auto;
  left: -10px;
}
@media only screen and (max-width: 991px) {
  .online-exclusive {
    width: 150px;
    height: 150px;
    top: 0;
    right: 0;
  }
  [dir="rtl"] .online-exclusive {
    right: auto;
    left: 0;
  }
}
.online-exclusive-badge {
  display: inline-block;
  padding: 4px 20px;
  margin-bottom: 15px;
  border-radius: 2px;
}
@media only screen and (max-width: 767px) {
  .online-exclusive-badge {
    padding: 0 20px;
  }
}
[dir="ltr"] .online-exclusive-badge {
  padding-left: 10px;
}
[dir="rtl"] .online-exclusive-badge {
  padding-right: 10px;
}
.online-exclusive-badge.magenta {
  background-color: #c724b1;
}
.online-exclusive-badge.aqua {
  background-color: #1779ba;
}
.online-exclusive-badge.violet {
  background-color: #753bbd;
}
.online-exclusive-badge .svg-bg-icon {
  width: 20px;
  height: 20px;
  display: inline-block;
  vertical-align: middle;
}
@media only screen and (max-width: 767px) {
  .online-exclusive-badge .svg-bg-icon {
    width: 16px;
    height: 16px;
  }
}
[dir="ltr"] .online-exclusive-badge .svg-bg-icon {
  margin-right: 7px;
}
[dir="rtl"] .online-exclusive-badge .svg-bg-icon {
  margin-left: 7px;
}
.online-exclusive-badge p {
  margin-bottom: 0;
  display: inline-block;
  font-family: du-font-secondary-bold, du-font-primary-b;
  font-size: 0.937rem;
}
.slick-slider {
  touch-action: auto;
  -ms-touch-action: auto;
  -webkit-overflow-scrolling: touch;
  -webkit-user-select: none;
}
.ducares-tag {
  border-radius: 14.5px;
  display: inline-block;
}
.ducares-tag .svg-bg-icon {
  width: 116px;
  height: 34px;
  display: block;
}
