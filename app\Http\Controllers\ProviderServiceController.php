<?php

namespace App\Http\Controllers;

use App\Base\Model\BaseModel;
use App\Components\Helper;
use App\Constants\CommonConstants;
use App\Constants\UserConstants;
use App\Models\ProviderService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Yajra\DataTables\Facades\DataTables;

class ProviderServiceController extends Controller
{
    public function index()
    {
        return view('provider-service.index');
    }

    public function show(ProviderService $providerService,Request $request)
    {
        return view('provider-service.show', compact('providerService'));
    }

    public function create()
    {
        $providerService=new ProviderService();
        return view('provider-service.create', compact('providerService'));
    }

    public function edit(ProviderService $providerService)
    {
        return view('provider-service.edit', compact('providerService'));
    }

    public function store(Request $request)
    {
        $providerService = new ProviderService();
        return $this->save($request, $providerService);
    }

    public function update(Request $request, ProviderService $providerService)
    {
        return $this->save($request, $providerService);
    }

    private function save(Request $request, ProviderService $providerService)
    {
        $isNewRecord = true;
        if ($providerService->id != null) {
            $isNewRecord = false;
        }

        $rules = [
            'provider_id' => ['required', 'integer'],
            'name' => ['required', 'string'],
            'sid' => ['required', 'string'],
            'type' => ['required', 'integer'],
            'is_active' => ['required', 'integer'],
            'is_chinese_result' => ['required', 'integer'],
            'price' => ['required','numeric'],
        ];

        if ($isNewRecord) {

        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            if ($isNewRecord) {
                return redirect()->route('provider-service.create')->withErrors($validator)->withInput();
            } else {
                return redirect()->route('provider-service.edit', $providerService->id)->withErrors($validator)->withInput();
            }
        }

        $providerService->provider_id = $request->input('provider_id');
        $providerService->name = $request->input('name');
        $providerService->sid = $request->input('sid');
        $providerService->type = (int)$request->input('type');
        $providerService->is_active = (int)$request->input('is_active');
        $providerService->is_chinese_result = (int)$request->input('is_chinese_result');
        $providerService->price = trim($request->input('price'));
        if ($isNewRecord) {
            $providerService->save();
        } else {
            $providerService->update();
        }

        \App\Models\Provider::generateCacheFile();
        resetServicesCache();
        return redirect()->route('provider-service.index')->with('success', 'Admin adjustment saved successfully.');
    }

    public function dataTable(Request $request)
    {
        try {
            if ($request->ajax()) {
                $query = ProviderService::query();
                BaseModel::buildFilterQuery($query, [
                    'q' => ['name', 'sid','price'],
                    'provider_id',
                    'is_active',
                    'is_chinese_result',
                    'type',
                ]);
                return Datatables::eloquent($query)
                    ->addColumn('checkboxes', function ($row) {
                        return '<input type="checkbox" name="pdr_checkbox[]" class="pdr_checkbox" value="' . $row->id . '" />';
                    })
                    ->addColumn('provider', function ($row) {
                        return $row->provider->displayName;
                    })
                    ->addColumn('type', function ($row) {
                        return $row->typeName;
                    })
                    ->addColumn('price', function ($row) {
                        return Helper::editPopupStructure($row,'price',null,'Price (in $)');
                    })
                    ->addColumn('sid', function ($row) {
                        return Helper::editPopupStructure($row,'sid',null,'Service ID');
                    })
                    ->addColumn('name', function ($row) {
                        return Helper::editPopupStructure($row,'name');
                    })
                    ->addColumn('created_at', function ($row) {
                        return Helper::displayTime($row->created_at);
                    })
                    ->addColumn('updated_at', function ($row) {
                        return Helper::displayTime($row->updated_at);
                    })
                    ->addColumn('is_active', function ($row) use ($query) {
                        $columnName = 'is_active';
                        return Helper::onOffButton($row, $columnName, $query);
                    })
                    ->addColumn('is_chinese_result', function ($row) use ($query) {
                        $columnName = 'is_chinese_result';
                        return Helper::onOffButton($row, $columnName, $query);
                    })
                    ->addColumn('actions', function ($row) {
                        $buttons = [];
                        //$buttons['Login']=['url' => route('user.loginAs', $row->id), 'icon' => 'las la-sign-in-alt'];
                        $buttons['view'] = ['url' => route('provider-service.show', $row->id)];
                        $buttons['edit'] = ['url' => route('provider-service.edit', $row->id)];
                        return Helper::getActionButtons($buttons);
                    })
                    ->rawColumns(['checkboxes', 'name', 'is_active', 'price','sid', 'is_chinese_result', 'actions'])
                    ->make(true);
            }
        } catch (\Exception $e) {
            //print_r(['message'=>$e->getMessage(),'trace'=>$e->getTraceAsString()]);
            die();
        }
    }
}
