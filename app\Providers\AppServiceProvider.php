<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Console\Commands\RabbitMQWorkerCommand;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        if ($this->app->runningInConsole()) {
            $this->commands([
                RabbitMQWorkerCommand::class,
                \App\Console\Commands\RabbitMQSetupCommand::class,
                \App\Console\Commands\TestRabbitMQDispatchCommand::class,
                \App\Console\Commands\RabbitMQInspectCommand::class,
                \App\Console\Commands\DebugRabbitMQCommand::class,
                \App\Console\Commands\TestRabbitMQConnectionCommand::class,
            ]);
        }
    }
}
