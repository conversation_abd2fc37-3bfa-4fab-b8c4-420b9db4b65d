<?php

namespace App\Jobs;

use App\Components\Helper;
use App\Components\RedisWrapper;
use App\Constants\CommonConstants;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\UserOrder;
use App\Models\Log;
use App\Constants\LogConstants;
use Illuminate\Support\Facades\Log as LaravelLog;

class ProcessOrderJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $orderId;

    /**
     * Create a new job instance.
     */
    public function __construct($orderId)
    {
        $this->orderId = $orderId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::insertLog([
                'user_id' => CommonConstants::ADMINISTRATIVE,
                'particulars' => 'Process order job '.$this->orderId,
                'type' => 'rabbitmq_order_job',
                'data' => [
                    'order_id' => $this->orderId,
                ]
            ]);

            $orderKey=$this->orderId;
            $orderDetails=RedisWrapper::fetchRecord($orderKey);
            if($orderDetails && is_array($orderDetails) && array_key_exists('status',$orderDetails)) {
                if($orderDetails['status']==CommonConstants::ORDER_STATUS_PENDING) {

                    $orderDetails['status']=CommonConstants::ORDER_STATUS_IN_PROGRESS;
                    RedisWrapper::saveRecord($orderKey,$orderDetails);

                    $model=new UserOrder();
                    if(array_key_exists('batch_ref',$orderDetails)) {
                        $model->batch_ref=$orderDetails['batch_ref'];
                    }
                    if(array_key_exists('order_ref',$orderDetails)) {
                        $model->order_ref=$orderDetails['order_ref'];
                    }
                    $model->user_id=$orderDetails['user_id'];
                    $model->imei=$orderDetails['imei'];
                    $model->service_id=$orderDetails['service_id'];
                    $model->provider_id=$orderDetails['provider_id'];
                    $model->provider_service_id=$orderDetails['provider_service_id'];
                    $model->price=$orderDetails['price'];
                    $model->created_at=date(CommonConstants::PHP_DATE_FORMAT);

                    $result=$model->placeOrder();

                    $model->updated_at=date(CommonConstants::PHP_DATE_FORMAT);
                    if(is_array($result)) {
                        if(array_key_exists('data',$result)) {
                            $model->raw_result=json_encode($result['data']);
                        }
                        if(array_key_exists('price',$result)) {
                            $model->cost_price=$result['price'];
                        }
                    }

                    $model->time_taken=(strtotime($model->updated_at)-strtotime($model->created_at));
                    if(array_key_exists('error',$result)) {
                        $model->result=$result['error'];
                        $model->status=CommonConstants::ORDER_STATUS_FAILED;
                    } else {
                        $model->result=$result['result'];
                        $model->status=CommonConstants::ORDER_STATUS_COMPLETED;
                    }

                    if($model->save()) {
                        if($model->status==CommonConstants::ORDER_STATUS_FAILED) {
                            creditUserBalance($model->user_id,$model->price);

                            $orderDetails['status']=CommonConstants::ORDER_STATUS_FAILED;
                            $orderDetails['result']=$model->result;
                            $orderDetails['is_deducted']=CommonConstants::NO;
                            //RedisWrapper::saveRecord($orderKey,$orderDetails);
                            RedisWrapper::deleteRecord($orderKey);
                        } else {
                            $model->deductBalance();

                            $orderDetails['status']=CommonConstants::ORDER_STATUS_COMPLETED;
                            $orderDetails['result']=$model->result;
                            //RedisWrapper::saveRecord($orderKey,$orderDetails);
                            RedisWrapper::deleteRecord($orderKey);
                        }

                    }
                }
            }
        } catch (\Exception $e) {
            Log::insertLog([
                'user_id' => CommonConstants::ADMINISTRATIVE,
                'particulars' => 'Process order job error '.$this->orderId,
                'type' => 'rabbitmq_job_error',
                'data' => [
                    'order_id' => $this->orderId,
                    'message' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]
            ]);
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::insertLog([
            'user_id' => CommonConstants::ADMINISTRATIVE,
            'particulars' => 'Process order job error '.$this->orderId,
            'type' => 'rabbitmq_job_error',
            'data' => [
                'order_id' => $this->orderId,
                'message' => $exception->getMessage(),
            ]
        ]);
    }
}
