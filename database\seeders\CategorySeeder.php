<?php

namespace Database\Seeders;

use App\Constants\CommonConstants;
use App\Models\Category;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $names=[
            'APPLE CARRIER CHECK SERVICES','Services Not In Use',
            'GENERIC CHECK SERVICES','APPLE GSX SERVICES','APPLE FMI & ICLOUD CHECK SERVICES','APPLE SIMLOCK CHECK SERVICES',
            'BLACKLIST CHECK SERVICES','APPLE INSTANT CHECK SERVICES','Apple IMEI-SN & SN-IMEI INSTANT CHECK SERVICES','APPLE MDM CHECK SERVICES',
            'Private Tool For Testing Services',
        ];

        if(count($names)>0) {
            $counter=1;
            $lastInserted=Category::query()->orderByDesc('display_order')->limit(1)->first();
            if($lastInserted) {
                $counter=$lastInserted->display_order;
            }

            foreach ($names as $name) {
                $isExist=Category::query()->where('name','=',$name)->first();
                if(!$isExist) {
                    $row = ['name' => $name];
                    $row['created_at'] = date(CommonConstants::PHP_DATE_FORMAT);
                    $row['display_order'] = $counter;
                    Category::create($row);
                    $counter++;
                }
            }
        }
    }
}
