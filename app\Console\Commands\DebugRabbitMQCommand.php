<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\ProcessOrderJob;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Log;
use PhpAmqpLib\Connection\AMQPStreamConnection;

class DebugRabbitMQCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'rabbitmq:debug';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Debug RabbitMQ connection and job dispatch';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Starting RabbitMQ Debug Session...');
        $this->info('=====================================');

        // Step 1: Check environment configuration
        $this->checkEnvironmentConfig();

        // Step 2: Test direct RabbitMQ connection
        $this->testDirectConnection();

        // Step 3: Test Laravel queue configuration
        $this->testLaravelQueueConfig();

        // Step 4: Test job dispatch with detailed logging
        $this->testJobDispatch();

        // Step 5: Check queue sizes
        $this->checkQueueSizes();

        $this->info('=====================================');
        $this->info('🔍 Debug session completed!');
    }

    private function checkEnvironmentConfig()
    {
        $this->info('📋 Step 1: Checking Environment Configuration');
        $this->info('--------------------------------------------');

        $config = config('queue.connections.rabbitmq');
        
        $this->info("Queue Connection: " . config('queue.default'));
        $this->info("RabbitMQ Host: " . $config['host']);
        $this->info("RabbitMQ Port: " . $config['port']);
        $this->info("RabbitMQ User: " . $config['user']);
        $this->info("RabbitMQ VHost: " . $config['vhost']);
        $this->info("RabbitMQ Queue: " . $config['queue']);
        $this->info("RabbitMQ Exchange: " . $config['exchange']);
        
        // Check if all required config values are present
        $required = ['host', 'port', 'user', 'password', 'vhost'];
        foreach ($required as $key) {
            if (empty($config[$key])) {
                $this->error("❌ Missing required config: {$key}");
            } else {
                $this->info("✅ {$key}: configured");
            }
        }
        $this->line('');
    }

    private function testDirectConnection()
    {
        $this->info('🔌 Step 2: Testing Direct RabbitMQ Connection');
        $this->info('----------------------------------------------');

        try {
            $config = config('queue.connections.rabbitmq');
            
            $this->info("Attempting connection to {$config['host']}:{$config['port']}...");
            
            $connection = new AMQPStreamConnection(
                $config['host'],
                $config['port'],
                $config['user'],
                $config['password'],
                $config['vhost'],
                false, // insist
                'AMQPLAIN', // login_method
                null, // login_response
                'en_US', // locale
                10.0, // connection_timeout
                10.0, // read_write_timeout
                null, // context
                false, // keepalive
                0 // heartbeat
            );

            $channel = $connection->channel();
            $this->info("✅ Direct connection successful!");
            
            // Test queue declaration
            list($queueName, $messageCount, $consumerCount) = $channel->queue_declare('queue', true);
            $this->info("✅ Queue 'queue' accessible - Messages: {$messageCount}, Consumers: {$consumerCount}");
            
            $channel->close();
            $connection->close();
            
        } catch (\Exception $e) {
            $this->error("❌ Direct connection failed: " . $e->getMessage());
            $this->error("Error details: " . $e->getTraceAsString());
        }
        $this->line('');
    }

    private function testLaravelQueueConfig()
    {
        $this->info('⚙️ Step 3: Testing Laravel Queue Configuration');
        $this->info('---------------------------------------------');

        try {
            $queueManager = app('queue');
            $this->info("✅ Queue manager loaded");
            
            $connection = $queueManager->connection('rabbitmq');
            $this->info("✅ RabbitMQ connection obtained");
            
            // Test if we can get queue size
            $size = $connection->size('queue');
            $this->info("✅ Queue size check successful: {$size} messages");
            
        } catch (\Exception $e) {
            $this->error("❌ Laravel queue config test failed: " . $e->getMessage());
            $this->error("Stack trace: " . $e->getTraceAsString());
        }
        $this->line('');
    }

    private function testJobDispatch()
    {
        $this->info('🚀 Step 4: Testing Job Dispatch');
        $this->info('-------------------------------');

        try {
            // Get initial queue size
            $initialSize = Queue::connection('rabbitmq')->size('queue');
            $this->info("Initial queue size: {$initialSize}");
            
            // Enable detailed logging
            Log::info('DEBUG: Starting job dispatch test');
            
            $testOrderId = rand(100000, 999999);
            $this->info("Dispatching test job with order ID: {$testOrderId}");
            
            // Dispatch job with detailed error handling
            try {
                ProcessOrderJob::dispatch($testOrderId)->onQueue('queue');
                $this->info("✅ Job dispatch call completed without exception");
                
                // Wait a moment for the job to be queued
                sleep(1);
                
                // Check if queue size increased
                $newSize = Queue::connection('rabbitmq')->size('queue');
                $this->info("Queue size after dispatch: {$newSize}");
                
                if ($newSize > $initialSize) {
                    $this->info("✅ Job successfully added to queue!");
                } else {
                    $this->error("❌ Job was not added to queue (size didn't increase)");
                }
                
            } catch (\Exception $e) {
                $this->error("❌ Job dispatch failed: " . $e->getMessage());
                $this->error("Stack trace: " . $e->getTraceAsString());
            }
            
        } catch (\Exception $e) {
            $this->error("❌ Job dispatch test setup failed: " . $e->getMessage());
        }
        $this->line('');
    }

    private function checkQueueSizes()
    {
        $this->info('📊 Step 5: Final Queue Size Check');
        $this->info('---------------------------------');

        $queues = ['queue', 'default', 'imei_checks', 'notifications'];
        
        foreach ($queues as $queueName) {
            try {
                $size = Queue::connection('rabbitmq')->size($queueName);
                $this->info("Queue '{$queueName}': {$size} messages");
            } catch (\Exception $e) {
                $this->error("Queue '{$queueName}': Error - " . $e->getMessage());
            }
        }
        $this->line('');
    }
}
