<?php

namespace App\Components;

use App\Base\Provider\BaseProvider;
use App\Constants\CommonConstants;
use App\Models\BlockedImeiSn;
use App\Models\Log;
use App\Models\User;
use Illuminate\Support\Facades\Http;

class ProviderFastBulk extends BaseProvider
{
    public static $providerID = CommonConstants::PROVIDER_FASTBULK;
    public static function placeOrder($orderModel) {
        $validate=self::validateRequest($orderModel);
        if($validate!==true) {
            return $validate;
        }

        $sid=self::$providerServiceDetails['sid'];
        $blockCheckServices=[3, 25];
        if (in_array($sid, $blockCheckServices)) {
            $isBlocked = BlockedImeiSn::isBlocked($orderModel->imei);
            if ($isBlocked) {
                return self::failedResult(self::DEFAULT_MESSAGE,['message'=>'IMEI or SN blocked']);
            }
        }

        $timeout=0;
        if($sid=="22") {
            $timeout=30;
        }
        if (is_numeric($orderModel->imei)) {
            $url = self::$providerDetails['api_url'] . "?ACCESS_KEY=".self::$providerDetails['api_key']."&IMEI=".$orderModel->imei."&SERVICE_ID=".$sid;
        } else {
            $url = self::$providerDetails['api_url'] . "?ACCESS_KEY=".self::$providerDetails['api_key']."&IMEI=".$orderModel->imei."&SERVICE_ID=".$sid."&SERIAL=".$orderModel->imei;
        }

        if($timeout) {
            $response = Http::timeout($timeout)->get($url);
        } else {
            $response = Http::get($url);
        }
        $result=$response->body();

        /* $curl = curl_init ($url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 40);
        curl_setopt($curl, CURLOPT_TIMEOUT, $timeout);
        $result = curl_exec($curl);
        curl_close($curl); */

        if($result && $result!="") {
            $result=self::parseResult($orderModel,$result);
            if(is_array($result) && array_key_exists('error',$result)) {
                if (in_array($sid, $blockCheckServices)) {
                    BlockedImeiSn::saveRecord($orderModel->imei);
                }
            }
            return $result;
        }

        return self::defaultResult();
    }

    public static function parseResult($orderModel,$result) {
        try {
            if ($result == "" || $result == false || stripos($result, "error code")!==false) {
                return self::failedResult(self::SERVICE_MAINTENANCE_MESSAGE,$result);
            }

            if (stripos($result, "only cellular devices supported") !== false) {
                return self::failedResult('Only cellular devices supported',$result);
            }

            if (stripos($result, "server error") !== false ||
                stripos($result, "Exception") !== false ||
                stripos($result, "wrong imei") !== false ||
                stripos($result, "Maintenance") !== false ||
                stripos($result, "wrong serial") !== false ||
                stripos($result, "only apple device supported") !== false ||
                stripos($result, "only apple devices supported") !== false ||
                stripos($result, "Maintenance") !== false ||
                strtoupper(trim($result)) == "ND" ||
                stripos($result, "missing") !== false ||
                stripos($result, "Connection Error") !== false ||
                stripos($result, "balance is low") !== FALSE || stripos($result, "error code")!==false || stripos($result, "error<br>find my iphone")!==false) {
                return self::failedResult(self::DEFAULT_MESSAGE,$result);
            }

            $finalResult=$result;
            if (stripos($finalResult, "MEID") !== false) {
                $ex = explode("<br>", $finalResult);
                if (count($ex) > 0) {
                    foreach ($ex as $k => $v) {
                        if (stripos($v, "MEID") !== false) {
                            unset($ex[$k]);
                            break;
                        }
                    }
                }
                if (count($ex) > 0) {
                    $finalResult = implode("<br>", $ex);
                }
            }

            $sid=self::$providerServiceDetails['sid'];
            switch ($sid) {
                case "7":
                    $new_csv_result = self::getCsvReport($finalResult, false, false, true);
                    if (is_array($new_csv_result) && array_key_exists('iCloud Status', $new_csv_result)) {
                        unset($new_csv_result['iCloud Status']);
                        $newResult = "";
                        foreach ($new_csv_result as $ky => $vl) {
                            $newResult .= trim($ky) . " : " . trim($vl) . "<br>";
                        }
                        $finalResult = $newResult;

                        if (stripos($finalResult, 'imei') !== false) {
                            $finalResult = preg_replace('/imei/i', 'IMEI/SN', $finalResult);
                        }
                        if (stripos($result, 'Serial Number') !== false) {
                            $finalResult = preg_replace('/Serial Number/i', 'IMEI/SN', $finalResult);
                        }
                    }
                    return self::successResult($finalResult,$result);
                    break;
                case "22":
                    if (stripos($finalResult, 'imei') !== false) {
                        $finalResult = preg_replace('/imei/i', 'IMEI/SN', $finalResult);
                    }
                    if (stripos($result, 'Serial Number') !== false) {
                        $finalResult = preg_replace('/Serial Number/i', 'IMEI/SN', $finalResult);
                    }

                    $new_data = array(
                        'Model' => '', 'IMEI/SN' => '', 'Find My iPhone' => '',
                    );

                    $new_csv_result = self::getCsvReport($finalResult, false, false, true);
                    if (is_array($new_csv_result) && count($new_csv_result) > 0) {
                        foreach ($new_csv_result as $key => $val) {
                            if(array_key_exists($key,$new_data) && $val!="") {
                                $new_data[$key]=$val;
                            }
                        }
                    }

                    $newHtmlResult = "";
                    foreach ($new_data as $key => $val) {
                        if ($val == "") {
                            continue;
                        }
                        $newHtmlResult .= $key . " : " . $val . "<br />";
                    }
                    $finalResult = $newHtmlResult;
                    if ($new_data['Find My iPhone'] == "") {
                        return self::failedResult(self::SERVICE_MAINTENANCE_MESSAGE,$result);
                    }

                    return self::successResult($finalResult,$result);
                    break;
                case "15":
                    $new_csv_result = self::getCsvReport($finalResult, false, false, true);
                    if (is_array($new_csv_result) && array_key_exists('Model', $new_csv_result)) {
                        $fetchModel=false;
                        if ($new_csv_result['Model'] == "") {
                            $fetchModel=true;
                        } else {
                            $isValid=self::isValidModelName($new_csv_result['Model']);
                            if(!$isValid) {
                                $fetchModel=true;
                            }
                        }

                        if($fetchModel) {
                            $model=self::fetchModelNameFromAPI($orderModel->imei);
                            if($model!="") {
                                $new_csv_result['Model']=$model;
                                $newResult = "";
                                foreach ($new_csv_result as $ky => $vl) {
                                    $newResult .= trim($ky) . " : " . trim($vl) . "<br>";
                                }
                                $finalResult = $newResult;
                            }
                        }
                    }
                    return self::successResult($finalResult,$result);
                    break;
                case "19":
                    $new_csv_result = self::getCsvReport($finalResult, false, false, true);
                    if (is_array($new_csv_result)) {
                        $new_data = array(
                            'Model' => '',
                            'IMEI/SN' => $orderModel->imei,
                            'Activation Status' => '', 'Warranty Status' => '',
                            'Estimated Purchase Date' => '',
                            'Telephone Technical Support' => '', 'Telephone Technical Support Expiration Date' => '',
                            'Telephone Technical Support Expires In' => '',
                            'Repairs And Service Coverage' => '', 'Repairs and Service Coverage' => '',
                            'Repairs And Service Expiration Date' => '', 'Repairs and Service Expiration Date' => '',
                            'Repairs And Service Expires In' => '', 'Repairs and Service Expires In' => '',
                            'AppleCare Eligible' => '', 'Valid Purchase Date' => '',
                            'Apple Care' => '', 'Refurbished Device' => '', 'Replaced Device' => '', 'Replaced by Apple' => '',
                            'Registered Device' => '', 'Loaner Device' => '',
                            'Next Policy ID' => '', 'Carrier' => '', 'Country' => '', 'SIM-Lock' => '', 'MESSAGE' => '',
                        );

                        foreach ($new_csv_result as $key => $value) {
                            if (array_key_exists($key, $new_data) && $value != "") {
                                if ($new_data[$key] == "") {
                                    $new_data[$key] = $value;
                                }
                            }
                        }
                        if ($new_data['Telephone Technical Support'] != "") {
                            if (trim(strtolower(strip_tags($new_data['Telephone Technical Support']))) == "active") {
                                $new_data['Telephone Technical Support Expiration Date'] = '';
                                $new_data['Telephone Technical Support Expires In'] = '';
                            }
                        }

                        if ($new_data['IMEI/SN'] != "") {
                            $newResult = "";
                            foreach ($new_data as $ky => $vl) {
                                if (trim($vl) != "") {
                                    $newResult .= trim($ky) . " : " . trim($vl) . "<br>";
                                }
                            }
                            $finalResult = $newResult;
                        }
                    }
                    return self::successResult($finalResult,$result);
                    break;
            }

            return self::successResult($finalResult, $result);
        } catch (\Exception $e) {

        }
        return self::defaultResult();
    }
}
