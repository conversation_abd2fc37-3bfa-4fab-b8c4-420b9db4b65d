<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('user_roles', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->string('name')->unique();
        });

        \App\Models\UserRole::upsert([['name'=>'Admin'],['name'=>'User']],['name']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_roles');
    }
};
