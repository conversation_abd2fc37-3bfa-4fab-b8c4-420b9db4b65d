<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20170731 at Sat Sep 26 15:10:29 2020
 By Aleksey,,,
Copyright (c) 2012 by Process Type Foundry, LLC. All rights reserved.
</metadata>
<defs>
<font id="Colfax-Medium" horiz-adv-x="565" >
  <font-face 
    font-family="Colfax"
    font-weight="500"
    font-stretch="normal"
    units-per-em="2048"
    panose-1="2 11 3 4 0 0 0 1 0 2"
    ascent="1536"
    descent="-512"
    x-height="1065"
    cap-height="1434"
    bbox="-127 -512 2533 1976"
    underline-thickness="147"
    underline-position="-351"
    unicode-range="U+0020-FB02"
  />
<missing-glyph horiz-adv-x="1404" 
d="M526 451l-90 90l178 176l-178 180l88 88l178 -178l183 178l88 -88l-180 -180l180 -176l-90 -90l-181 178zM219 33h967v1368h-967v-1368zM184 0v1434h1037v-1434h-1037z" />
    <glyph glyph-name=".notdef" horiz-adv-x="1404" 
d="M526 451l-90 90l178 176l-178 180l88 88l178 -178l183 178l88 -88l-180 -180l180 -176l-90 -90l-181 178zM219 33h967v1368h-967v-1368zM184 0v1434h1037v-1434h-1037z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="682" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="491" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="606" 
d="M225 414l-67 452v568h291v-568l-68 -452h-156zM143 143q0 71 44 115.5t116 44.5t116 -44.5t44 -115.5t-44 -115t-116 -44t-116 44t-44 115z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="997" 
d="M608 860v574h242v-574h-242zM147 860v574h242v-574h-242z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="1386" 
d="M788 0l43 393h-331l-43 -393h-213l43 393h-205v189h225l33 292h-215v189h235l41 371h213l-41 -371h332l41 371h213l-41 -371h197v-189h-217l-33 -292h207v-189h-228l-43 -393h-213zM553 874l-33 -292h332l33 292h-332z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="1208" 
d="M477 -225v237q-174 31 -271 140t-97 274v33h266v-33q0 -103 62.5 -156t176.5 -53q105 0 167.5 51t62.5 138q0 63 -32 101.5t-101 63.5l-273 99q-80 28 -134.5 57.5t-98.5 72t-65 101t-21 135.5q0 158 95 256t255 129v238h274v-240q156 -33 246 -133.5t90 -253.5v-43h-264
v37q0 88 -55.5 137t-159.5 49q-97 0 -153 -41.5t-56 -119.5q0 -58 33.5 -90.5t99.5 -57.5l262 -94q77 -29 132 -59.5t102.5 -75t71.5 -107t24 -141.5q0 -157 -100.5 -268.5t-263.5 -145.5v-237h-275z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="1810" 
d="M438 0l738 1434h206l-737 -1434h-207zM764 1133v-166q0 -144 -90.5 -233t-245.5 -89t-245.5 89t-90.5 233v166q0 144 90.5 232.5t245.5 88.5t245.5 -88.5t90.5 -232.5zM567 950v199q0 64 -36.5 101.5t-102.5 37.5t-102.5 -37.5t-36.5 -101.5v-199q0 -64 36.5 -101.5
t102.5 -37.5t102.5 37.5t36.5 101.5zM1729 467v-166q0 -144 -90.5 -232.5t-245.5 -88.5t-245.5 88.5t-90.5 232.5v166q0 144 90.5 232.5t245.5 88.5t245.5 -88.5t90.5 -232.5zM1532 285v198q0 64 -36.5 102t-102.5 38t-103 -38t-37 -102v-198q0 -64 37 -102t103 -38
t102.5 38t36.5 102z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="1409" 
d="M1378 139l-164 -168l-143 142q-155 -142 -412 -142q-251 0 -404 124t-153 333q0 268 254 401q-59 65 -92 130t-33 153q0 156 112 253t294 97q186 0 294.5 -96t108.5 -250v-84h-231v82q0 68 -44 113t-126 45q-77 0 -123.5 -41t-46.5 -113q0 -46 25.5 -84.5t74.5 -85.5
l453 -452q2 20 2 59v164h221v-174q0 -125 -41 -234zM672 166q167 0 256 92l-432 432q-148 -102 -148 -260q0 -123 91.5 -193.5t232.5 -70.5z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="536" 
d="M147 860v574h242v-574h-242z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="681" 
d="M383 1602h268q-120 -279 -176.5 -499.5t-56.5 -438.5t56.5 -438.5t176.5 -499.5h-268q-250 468 -250 938t250 938z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="681" 
d="M31 1602h268q250 -468 250 -938t-250 -938h-268q120 279 176.5 499.5t56.5 438.5t-56.5 438.5t-176.5 499.5z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="905" 
d="M172 846l-59 102l221 127l-221 129l59 103l223 -129v256h117v-256l221 129l60 -103l-222 -129l222 -127l-60 -102l-221 127v-254h-117v256z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="1232" 
d="M487 242v379h-344v233h344v379h258v-379h345v-233h-345v-379h-258z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="512" 
d="M31 -264l102 543h297l-184 -543h-215z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="837" 
d="M113 530v250h612v-250h-612z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="524" 
d="M102 143q0 71 44 115.5t116 44.5t116 -44.5t44 -115.5t-44 -115t-116 -44t-116 44t-44 115z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="970" 
d="M61 -215l578 1751h270l-577 -1751h-271z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="1310" 
d="M655 -29q-245 0 -392.5 139t-147.5 361v492q0 222 147.5 360.5t392.5 138.5t393 -138.5t148 -360.5v-492q0 -222 -148 -361t-393 -139zM913 467v500q0 129 -68 196.5t-190 67.5t-190 -67.5t-68 -196.5v-500q0 -129 68 -196.5t190 -67.5t190 67.5t68 196.5z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="806" 
d="M356 0v1147l-305 -113v250l355 150h233v-1434h-283z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="1232" 
d="M106 0v215l584 573q66 66 99.5 123.5t33.5 126.5q0 89 -58 141t-151 52q-103 0 -158 -57.5t-55 -161.5v-29h-268v41q0 211 128 324.5t351 113.5q221 0 357.5 -115.5t136.5 -306.5q0 -51 -10.5 -97.5t-33.5 -90t-45 -77t-60.5 -74t-63 -64.5t-68.5 -64l-372 -342h645v-231
h-992z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="1251" 
d="M180 1208v226h901v-228l-366 -366q185 -8 304.5 -116t119.5 -300q0 -129 -72 -233.5t-193 -162t-266 -57.5q-105 0 -196.5 29t-162.5 84t-113 141.5t-44 194.5h262q8 -101 77 -161t179 -60q113 0 181.5 63t68.5 156q0 96 -64.5 151.5t-183.5 55.5h-211v227l336 356h-557z
" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="1265" 
d="M748 0v307h-656v209l432 918h256l-413 -910h381v297h260v-297h182v-217h-182v-307h-260z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="1277" 
d="M629 -29q-199 0 -343 107t-171 303h268q19 -82 84 -131t162 -49q124 0 197 74t73 192q0 112 -71.5 186t-190.5 74q-165 0 -238 -125h-260l135 832h768v-226h-544l-60 -344q96 90 271 90q203 0 335 -131.5t132 -347.5q0 -104 -40.5 -197t-112 -160.5t-174 -107
t-220.5 -39.5z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="1257" 
d="M610 1434h301l-399 -549q68 65 211 65q92 0 174 -33.5t143.5 -94t97.5 -151.5t36 -200q0 -136 -71 -251t-195 -182t-273 -67q-246 0 -391.5 140.5t-145.5 361.5q0 131 39 232.5t113 207.5zM377 465q0 -120 68 -195t192 -75q115 0 187.5 78.5t72.5 193.5q0 120 -68.5 194
t-189.5 74q-118 0 -190 -75.5t-72 -194.5z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="1122" 
d="M158 0l592 1202h-648v232h938v-201l-594 -1233h-288z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="1298" 
d="M649 -29q-235 0 -382.5 119t-147.5 311q0 246 237 347q-84 45 -132 125t-48 192q0 185 133 291t340 106t340 -106t133 -291q0 -112 -48 -192t-132 -125q238 -101 238 -347q0 -192 -148 -311t-383 -119zM649 186q118 0 187 55.5t69 159.5q0 106 -69 164t-187 58t-187 -58
t-69 -164q0 -104 69 -159.5t187 -55.5zM444 1044q0 -90 56 -147t149 -57t149 57t56 147q0 96 -54.5 150.5t-150.5 54.5t-150.5 -54.5t-54.5 -150.5z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="1257" 
d="M659 0h-307l371 541q-26 -26 -79.5 -42t-108.5 -16q-92 0 -174 34t-143.5 95t-97.5 152t-36 199q0 220 148 359.5t386 139.5q243 0 392 -136.5t149 -360.5q0 -223 -119 -398zM881 969q0 119 -69.5 194.5t-190.5 75.5q-118 0 -189.5 -75.5t-71.5 -196.5t68.5 -195
t189.5 -74q119 0 191 78.5t72 192.5z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="524" 
d="M102 782q0 71 44 115.5t116 44.5t116 -44.5t44 -115.5t-44 -115t-116 -44t-116 44t-44 115zM102 143q0 71 44 115.5t116 44.5t116 -44.5t44 -115.5t-44 -115t-116 -44t-116 44t-44 115z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="524" 
d="M-4 -264l102 543h297l-184 -543h-215zM92 782q0 71 44 115.5t116 44.5t116 -44.5t44 -115.5t-44 -115t-116 -44t-116 44t-44 115z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="1181" 
d="M102 827l947 426v-254l-615 -262l615 -262v-254l-947 426v180z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="1265" 
d="M164 371v225h938v-225h-938zM164 879v225h938v-225h-938z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="1181" 
d="M133 1253l946 -426v-180l-946 -426v254l615 262l-615 262v254z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="1032" 
d="M362 430v94q0 116 30 191t100 127l94 69q66 47 94.5 82.5t28.5 91.5q0 70 -54 111t-145 41q-98 0 -150.5 -45t-52.5 -133v-47h-256v51q0 198 126 298.5t339 100.5q210 0 340.5 -103.5t130.5 -273.5q0 -101 -41 -173t-119 -130l-104 -75q-44 -32 -65.5 -55.5t-34.5 -63
t-13 -99.5v-59h-248zM332 143q0 71 44 115.5t116 44.5t115.5 -44.5t43.5 -115.5t-43.5 -115t-115.5 -44t-116 44t-44 115z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="1908" 
d="M1159 1012h205l-80 -455q-6 -30 -6 -57q0 -72 37 -112t100 -40q58 0 102 38t67.5 100t34.5 127t11 132q0 254 -167 408t-457 154q-165 0 -300.5 -57t-226 -158t-139.5 -240.5t-49 -304.5q0 -137 43.5 -253t125.5 -200.5t207 -132t281 -47.5q197 0 357 43l-21 -156
q-159 -47 -358 -47q-188 0 -344 61.5t-259.5 169t-160 252t-56.5 310.5q0 190 61.5 356t174.5 290.5t287 196.5t387 72q182 0 333 -54.5t251.5 -149.5t156 -224t55.5 -278q0 -87 -14.5 -167.5t-46.5 -152t-78.5 -124t-115 -83t-150.5 -30.5q-105 0 -182.5 44.5t-98.5 125.5
q-26 -58 -101.5 -101t-168.5 -43q-139 0 -228 91.5t-89 234.5q0 37 6 78l12 67q28 155 118 239.5t234 84.5q79 0 139.5 -32t92.5 -85zM764 674l-10 -60q-6 -42 -6 -55q0 -74 39 -119t108 -45t116 48t64 139l10 57q5 25 5 49q0 71 -40.5 116.5t-115.5 45.5q-70 0 -113 -47.5
t-57 -128.5z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="1413" 
d="M852 1434l514 -1434h-285l-96 301h-557l-96 -301h-285l514 1434h291zM707 1176l-207 -648h411z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="1357" 
d="M158 0v1434h643q187 0 298 -100t111 -263q0 -116 -49.5 -200t-130.5 -130q117 -25 191.5 -112t74.5 -226q0 -180 -134.5 -291.5t-350.5 -111.5h-653zM436 225h361q102 0 157.5 52.5t55.5 144.5q0 90 -57 142t-158 52h-359v-391zM436 840h301q89 0 138 52.5t49 137.5
q0 82 -50 130t-139 48h-299v-368z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="1445" 
d="M741 -29q-158 0 -279.5 48.5t-196.5 136t-112.5 203t-37.5 255.5v205q0 140 37.5 255.5t112.5 203t196.5 136t279.5 48.5q132 0 244 -43t187.5 -116.5t119.5 -171t49 -207.5h-266q-15 132 -99 217.5t-235 85.5q-173 0 -257.5 -106t-84.5 -287v-234q0 -181 84.5 -287
t257.5 -106q151 0 235 85.5t99 217.5h266q-5 -110 -49 -208t-119.5 -171.5t-187.5 -116.5t-244 -43z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="1439" 
d="M158 1434h581q141 0 253.5 -42t185.5 -117.5t111 -178.5t38 -226v-307q0 -252 -153.5 -407.5t-434.5 -155.5h-581v1434zM1042 561v311q0 158 -90 243t-262 85h-248v-967h248q172 0 262 85t90 243z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="1265" 
d="M158 0v1434h1003v-238h-717v-350h656v-240h-656v-368h719v-238h-1005z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="1210" 
d="M158 0v1434h981v-238h-695v-379h633v-237h-633v-580h-286z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="1464" 
d="M1335 946h-272q-5 119 -89 200t-231 81q-171 0 -257.5 -104t-86.5 -289v-226q0 -190 84 -298.5t262 -108.5q152 0 238 81.5t86 204.5v21h-321v233h602v-186q0 -260 -167 -422t-442 -162q-155 0 -276.5 50t-197 138.5t-114 203.5t-38.5 251v209q0 292 165 465.5t455 173.5
q272 0 433 -144t167 -372z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="1476" 
d="M158 0v1434h286v-588h588v588h287v-1434h-287v592h-588v-592h-286z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="622" 
d="M168 0v1434h287v-1434h-287z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="1245" 
d="M580 -29q-234 0 -370 121.5t-136 327.5v20h272v-18q0 -99 61.5 -156t172.5 -57t169 57.5t58 155.5v1012h287v-1012q0 -200 -143 -325.5t-371 -125.5z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="1333" 
d="M158 0v1434h286v-689l506 689h334l-539 -711l555 -723h-337l-519 702v-702h-286z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="1187" 
d="M158 0v1434h286v-1196h660v-238h-946z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="1654" 
d="M1497 1434v-1434h-274v881l-269 -533h-254l-268 533v-881h-274v1434h264l405 -820l406 820h264z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="1492" 
d="M158 0v1434h272l625 -955v955h280v-1434h-272l-625 954v-954h-280z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="1507" 
d="M1393 821v-209q0 -138 -41 -254.5t-119.5 -203t-201 -135t-277.5 -48.5q-154 0 -276.5 48.5t-201 135t-120 203t-41.5 254.5v209q0 138 41.5 254.5t120 203t201 135t276.5 48.5q155 0 277.5 -48.5t201 -135t119.5 -203t41 -254.5zM754 209q174 0 263 105.5t89 283.5v238
q0 178 -89 283.5t-263 105.5t-263.5 -106t-89.5 -283v-238q0 -177 89.5 -283t263.5 -106z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="1335" 
d="M158 0v1434h596q245 0 388.5 -132.5t143.5 -349.5t-146 -353t-386 -136h-310v-463h-286zM444 698h291q130 0 197 71t67 183q0 111 -66.5 178.5t-197.5 67.5h-291v-500z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="1507" 
d="M1393 821v-209q0 -156 -52.5 -284.5t-152.5 -214.5l106 -160l-200 -131l-117 178q-110 -29 -223 -29q-154 0 -276.5 48.5t-201 135t-120 203t-41.5 254.5v209q0 138 41.5 254.5t120 203t201 135t276.5 48.5q155 0 277.5 -48.5t201 -135t119.5 -203t41 -254.5zM754 209
q44 0 82 6l-113 174l199 123l114 -172q70 98 70 258v238q0 178 -89 283.5t-263 105.5t-263.5 -106t-89.5 -283v-238q0 -177 89.5 -283t263.5 -106z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="1429" 
d="M1008 0l-263 459h-301v-459h-286v1434h647q246 0 382 -135.5t136 -352.5q0 -152 -75 -268.5t-208 -173.5l299 -504h-331zM444 690h326q128 0 197 71t69 187q0 118 -69 185t-197 67h-326v-510z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="1339" 
d="M674 -29q-256 0 -408.5 118.5t-152.5 326.5v37h274v-31q0 -103 78 -166t219 -63q129 0 205 55t76 153q0 127 -136 162l-374 96q-167 42 -243.5 127.5t-76.5 243.5q0 201 142 316.5t385 115.5q236 0 377 -118.5t141 -309.5v-35h-265v29q0 96 -63.5 154.5t-189.5 58.5
q-117 0 -183.5 -47t-66.5 -139q0 -121 123 -152l393 -104q313 -82 313 -375q0 -133 -74 -237t-203 -160t-290 -56z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="1204" 
d="M459 0v1196h-406v238h1098v-238h-406v-1196h-286z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="1474" 
d="M737 -29q-275 0 -432.5 149.5t-157.5 395.5v918h285v-930q0 -138 82.5 -216.5t222.5 -78.5q139 0 221 78.5t82 216.5v930h287v-918q0 -246 -157.5 -395.5t-432.5 -149.5z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="1363" 
d="M1319 1434l-475 -1434h-324l-475 1434h293l344 -1121l344 1121h293z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="1978" 
d="M1128 1434l299 -1037l207 1037h283l-338 -1434h-276l-314 1104l-313 -1104h-277l-338 1434h283l207 -1037l299 1037h278z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="1368" 
d="M831 731l476 -731h-301l-322 502l-322 -502h-301l476 731l-455 703h295l307 -478l307 478h295z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="1302" 
d="M795 516v-516h-287v516l-492 918h312l323 -639l324 639h311z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="1277" 
d="M113 0v238l718 958h-688v238h1010v-238l-719 -958h737v-238h-1058z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="782" 
d="M154 -274v1876h577v-230h-299v-1417h299v-229h-577z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="970" 
d="M332 1536l577 -1751h-270l-578 1751h271z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="782" 
d="M629 1602v-1876h-578v229h299v1417h-299v230h578z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="1089" 
d="M664 1434l344 -586h-269l-194 358l-195 -358h-268l344 586h238z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="1060" 
d="M-2 -279v148h1065v-148h-1065z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="808" 
d="M371 1186l-213 315h344l149 -315h-280z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="1198" 
d="M496 -25q-182 0 -293 89t-111 243q0 168 115.5 253t314.5 85q87 0 157 -26.5t114 -63.5v145q0 75 -50 123.5t-135 48.5q-133 0 -184 -100h-258q45 154 164.5 236t292.5 82q200 0 324 -112.5t124 -297.5v-680h-268v98q-36 -53 -124.5 -88t-182.5 -35zM567 160
q93 0 159.5 45t66.5 112q0 69 -61 109.5t-157 40.5q-99 0 -156 -39.5t-57 -112.5q0 -72 57.5 -113.5t147.5 -41.5z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="1257" 
d="M719 -25q-111 0 -194.5 42t-121.5 110v-127h-266v1495h279v-549q46 64 125 104t180 40q139 0 242 -64.5t153.5 -169.5t50.5 -235v-177q0 -128 -51 -233t-154.5 -170.5t-242.5 -65.5zM651 201q116 0 177 72t61 184v151q0 116 -62.5 186t-177.5 70q-99 0 -166 -67.5
t-67 -184.5v-155q0 -113 65 -184.5t170 -71.5z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="1169" 
d="M594 -25q-236 0 -370 133.5t-134 348.5v151q0 215 134 348.5t370 133.5q212 0 345.5 -118.5t139.5 -303.5h-254q-10 86 -65.5 141t-159.5 55q-114 0 -172.5 -67t-58.5 -179v-172q0 -112 58.5 -178.5t172.5 -66.5q104 0 159.5 55t65.5 141h254q-6 -185 -139.5 -303.5
t-345.5 -118.5z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="1257" 
d="M539 -25q-139 0 -242.5 65t-155 170.5t-51.5 233.5v177q0 130 51 235t154 169.5t242 64.5q101 0 179.5 -38.5t125.5 -101.5v545h278v-1495h-266v127q-43 -67 -124.5 -109.5t-190.5 -42.5zM606 201q105 0 170.5 71.5t65.5 184.5v155q0 117 -67 184.5t-167 67.5
q-115 0 -177 -70t-62 -186v-151q0 -112 60.5 -184t176.5 -72z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="1146" 
d="M586 -25q-228 0 -362 127t-134 349v145q0 223 129 358.5t363 135.5q152 0 261.5 -64t161.5 -168t52 -235v-187h-697v-26q0 -102 56 -168t170 -66q85 0 141 38t78 97h244q-28 -147 -157.5 -241.5t-305.5 -94.5zM360 643v-10h441v26q0 105 -55.5 169.5t-163.5 64.5
q-114 0 -168 -67.5t-54 -182.5z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="743" 
d="M199 0v840h-136v225h136v98q0 173 81 252.5t248 79.5h185v-221h-121q-62 0 -88.5 -24t-26.5 -85v-100h215v-225h-215v-840h-278z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="1249" 
d="M602 -381q-192 0 -330 85t-170 233h271q53 -107 229 -107q106 0 169 54t63 143v168q-36 -59 -122 -102.5t-184 -43.5q-104 0 -189 36.5t-138.5 98t-82 140.5t-28.5 168v155q0 90 28.5 169.5t82 141t138.5 97t191 35.5q114 0 199.5 -46.5t116.5 -107.5v129h266v-1042
q0 -172 -144.5 -288t-365.5 -116zM594 272q102 0 171 62.5t69 171.5v123q0 106 -65.5 171.5t-166.5 65.5q-115 0 -175 -63t-60 -174v-123q0 -105 58.5 -169.5t168.5 -64.5z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="1220" 
d="M137 0v1495h279v-557q42 70 121 111t180 41q174 0 275.5 -109.5t101.5 -298.5v-682h-279v637q0 97 -48.5 154t-145.5 57q-96 0 -150.5 -56t-54.5 -161v-631h-279z" />
    <glyph glyph-name="i" unicode="i" 
d="M143 0v1065h279v-1065h-279zM121 1343q0 69 46 115.5t116 46.5t115.5 -46.5t45.5 -115.5t-45.5 -115t-115.5 -46t-116 46t-46 115z" />
    <glyph glyph-name="j" unicode="j" 
d="M-2 -356v215h63q43 0 62.5 15.5t19.5 53.5v1137h279v-1161q0 -126 -69 -193t-208 -67h-147zM121 1343q0 69 46 115.5t116 46.5t115.5 -46.5t45.5 -115.5t-45.5 -115t-115.5 -46t-116 46t-46 115z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="1114" 
d="M137 0v1495h279v-926l338 496h317l-366 -520l378 -545h-317l-350 520v-520h-279z" />
    <glyph glyph-name="l" unicode="l" 
d="M143 0v1495h279v-1495h-279z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="1878" 
d="M137 0v1065h266v-127q82 152 308 152q102 0 180.5 -39.5t126.5 -112.5q119 152 352 152q170 0 275.5 -109.5t105.5 -298.5v-682h-278v637q0 97 -47 154t-144 57q-94 0 -146.5 -55.5t-52.5 -161.5v-631h-278v637q0 97 -47 154t-144 57q-95 0 -146.5 -55.5t-51.5 -161.5
v-631h-279z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="1220" 
d="M137 0v1065h266v-127q82 152 314 152q174 0 275.5 -108.5t101.5 -299.5v-682h-279v637q0 97 -48.5 154t-145.5 57q-96 0 -150.5 -56t-54.5 -161v-631h-279z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="1183" 
d="M592 -25q-233 0 -367.5 136t-134.5 346v151q0 210 134.5 346t367.5 136t367.5 -136t134.5 -346v-151q0 -210 -134.5 -346t-367.5 -136zM823 438v189q0 109 -58.5 176t-172.5 67t-173 -67t-59 -176v-189q0 -109 59 -176t173 -67t172.5 67t58.5 176z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="1257" 
d="M719 1090q107 0 193 -37.5t141 -102.5t84.5 -149t29.5 -180v-177q0 -130 -50.5 -235t-153.5 -169.5t-242 -64.5q-103 0 -182.5 38t-122.5 102v-471h-279v1421h266v-127q43 66 125 109t191 43zM651 864q-105 0 -170 -71.5t-65 -184.5v-155q0 -117 67 -184.5t166 -67.5
q115 0 177.5 70t62.5 186v151q0 112 -61 184t-177 72z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="1257" 
d="M539 1090q111 0 193.5 -42t121.5 -110v127h266v-1421h-278v469q-37 -58 -120 -98t-185 -40q-139 0 -242 64.5t-154 169.5t-51 235v177q0 129 51.5 234t155 170t242.5 65zM606 864q-116 0 -176.5 -72t-60.5 -184v-151q0 -116 62 -186t177 -70q100 0 167 67.5t67 184.5v155
q0 113 -65.5 184.5t-170.5 71.5z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="806" 
d="M137 0v1065h264v-207q47 215 320 215h33v-276h-58q-139 0 -209.5 -73.5t-70.5 -231.5v-492h-279z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="1087" 
d="M551 -25q-209 0 -334 87.5t-125 252.5v19h246v-15q0 -69 58 -108t157 -39q90 0 146.5 34.5t56.5 94.5q0 76 -88 96l-316 72q-123 28 -183 95t-60 186q0 161 118 250.5t312 89.5q193 0 307.5 -91.5t114.5 -248.5v-17h-244v19q0 65 -42.5 102t-131.5 37q-85 0 -129.5 -30
t-44.5 -87q0 -39 20 -60t62 -30l323 -80q120 -30 179 -95.5t59 -176.5q0 -155 -131 -256t-330 -101z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="786" 
d="M508 0q-150 0 -225.5 79.5t-75.5 223.5v537h-152v225h152v291h280v-291h228v-225h-228v-516q0 -53 23 -76t78 -23h112v-225h-192z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="1212" 
d="M1075 1065v-1065h-266v127q-37 -68 -122 -110t-187 -42q-177 0 -275 111.5t-98 296.5v682h279v-643q0 -102 47.5 -157.5t140.5 -55.5q96 0 149.5 57.5t53.5 157.5v641h278z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="1060" 
d="M1038 1065l-362 -1065h-291l-362 1065h278l229 -780l230 780h278z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="1613" 
d="M1577 1065l-277 -1065h-260l-233 733l-234 -733h-260l-276 1065h270l156 -715l217 715h254l217 -715l156 715h270z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="1105" 
d="M694 541l373 -541h-293l-221 332l-221 -332h-293l373 541l-359 524h289l211 -315l211 315h289z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="1046" 
d="M559 -356h-274l106 360l-368 1061h278l229 -764l218 764h276z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="1034" 
d="M92 0v201l526 649h-501v215h823v-201l-526 -649h538v-215h-860z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="864" 
d="M51 776h64q170 0 170 166v291q0 184 92.5 275t290.5 91h155v-229h-100q-86 0 -123 -35.5t-37 -120.5v-296q0 -224 -209 -256q209 -32 209 -256v-297q0 -85 37 -120.5t123 -35.5h100v-229h-155q-198 0 -290.5 91t-92.5 275v291q0 166 -170 166h-64v229z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="675" 
d="M219 -307v1945h238v-1945h-238z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="864" 
d="M750 776h63v-229h-63q-170 0 -170 -166v-291q0 -184 -92.5 -275t-290.5 -91h-156v229h100q86 0 123 35.5t37 120.5v297q0 224 209 256q-209 32 -209 256v296q0 85 -37 120.5t-123 35.5h-100v229h156q198 0 290.5 -91t92.5 -275v-291q0 -166 170 -166z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="1314" 
d="M102 573q31 183 109 277t223 94q49 0 116 -22.5t121 -50t109.5 -50t83.5 -22.5q16 0 29 3.5t23 13t17 16.5t15 24.5t11.5 26t11 31t10.5 30.5l238 -82q-15 -55 -31.5 -99.5t-43 -89.5t-58 -75t-77.5 -49t-100 -19q-57 0 -144 36.5t-167.5 73t-122.5 36.5
q-31 0 -52.5 -10.5t-36 -35.5t-22.5 -48t-18 -66z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="491" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="606" 
d="M381 1020l68 -453v-567h-291v567l67 453h156zM463 1290q0 -71 -44 -115.5t-116 -44.5t-116 44.5t-44 115.5t44 115.5t116 44.5t116 -44.5t44 -115.5z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="1187" 
d="M387 776v-119q0 -108 56 -168.5t159 -60.5q92 0 146.5 47t62.5 119h258q-6 -135 -95.5 -238.5t-236.5 -138.5v-217h-278v217q-159 37 -250.5 153t-91.5 287v119q0 171 92 289t250 149v220h278v-220q146 -27 236 -132t96 -242h-258q-8 72 -62.5 119t-146.5 47
q-103 0 -159 -61t-56 -169z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="1376" 
d="M131 0v225h236v344h-207v226h207v241q0 208 124.5 317t333.5 109q213 0 334 -112t121 -301v-41h-258v43q0 81 -47.5 131.5t-138.5 50.5q-191 0 -191 -193v-245h385v-226h-385v-344h547v-225h-1061z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="1273" 
d="M1047 762v-100q0 -125 -68 -218l172 -270h-279l-84 137q-74 -22 -151 -22q-81 0 -152 22l-84 -137h-278l172 270q-68 93 -68 218v100q0 121 68 217l-172 270h278l84 -137q74 23 152 23q74 0 151 -23l84 137h279l-172 -270q68 -96 68 -217zM809 647v129q0 72 -44.5 118
t-127.5 46t-127.5 -46t-44.5 -118v-129q0 -72 44.5 -118t127.5 -46t127.5 46t44.5 118z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="1245" 
d="M143 670v207h185l-246 557h301l240 -633l239 633h301l-245 -557h184v-207h-277l-63 -146v-39h340v-206h-340v-279h-279v279h-340v206h340v39l-65 146h-275z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="675" 
d="M219 -307v794h238v-794h-238zM219 844v794h238v-794h-238z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="1265" 
d="M627 -279q-206 0 -333.5 98.5t-127.5 270.5v29h244v-19q0 -80 57 -128t160 -48q90 0 146 37t56 105q0 52 -27.5 79t-86.5 48l-334 116q-125 44 -191.5 119.5t-66.5 196.5q0 160 141 280q-69 72 -69 199q0 168 121.5 263t324.5 95q218 0 336.5 -98.5t118.5 -271.5v-25
h-240v27q0 77 -52 121.5t-157 44.5q-95 0 -146.5 -36t-51.5 -104q0 -51 26.5 -78.5t87.5 -50.5l326 -119q135 -49 199.5 -122t64.5 -191q0 -154 -131 -289q61 -74 61 -190q0 -155 -126 -257t-330 -102zM487 512l351 -127q61 71 61 147q0 93 -115 138l-338 125
q-69 -73 -69 -144q0 -52 25.5 -84.5t84.5 -54.5z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="1032" 
d="M152 1341q0 65 42.5 107.5t106.5 42.5q65 0 107.5 -42.5t42.5 -107.5q0 -64 -42.5 -106.5t-107.5 -42.5t-107 42t-42 107zM582 1341q0 65 42.5 107.5t106.5 42.5q65 0 107.5 -42.5t42.5 -107.5q0 -64 -42.5 -106.5t-107.5 -42.5t-107 42t-42 107z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="1691" 
d="M846 49q149 0 274.5 55t208.5 147t129.5 213t46.5 253t-46.5 252.5t-129.5 213t-208.5 147t-274.5 54.5q-148 0 -273.5 -54.5t-209.5 -147t-130.5 -213t-46.5 -252.5t46.5 -253t130.5 -213t209.5 -147t273.5 -55zM846 -25q-163 0 -303 58.5t-236 159t-150.5 236.5
t-54.5 288t54.5 288t150.5 236t236 158.5t303 58.5q164 0 304 -58.5t235.5 -159t149.5 -236t54 -287.5t-54 -288t-149.5 -236.5t-235.5 -159t-304 -58.5zM850 346q-153 0 -235.5 91.5t-82.5 226.5v106q0 135 82.5 226t235.5 91q129 0 213 -77.5t92 -192.5h-147
q-22 137 -158 137q-83 0 -122.5 -50.5t-39.5 -133.5v-106q0 -83 39.5 -134t122.5 -51q136 0 158 137h147q-8 -115 -92 -192.5t-213 -77.5z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="876" 
d="M115 469v164h647v-164h-647zM745 758h-178v61q-25 -30 -82.5 -54t-126.5 -24q-103 0 -172 63t-69 157q0 100 71 157.5t183 57.5q61 0 110 -17.5t76 -44.5v84q0 42 -28 71t-78 29q-81 0 -111 -61h-180q27 106 104.5 161.5t194.5 55.5q127 0 206.5 -76.5t79.5 -195.5v-424z
M426 872q55 0 93 24.5t38 66.5t-36.5 66t-88.5 24q-61 0 -96 -23t-35 -67q0 -43 34.5 -67t90.5 -24z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="1300" 
d="M354 123l-334 442l334 443h314l-332 -443l332 -442h-314zM926 123l-334 442l334 443h313l-332 -443l332 -442h-313z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="1191" 
d="M788 242v379h-686v233h947v-612h-261z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="1093" 
d="M547 573q-104 0 -191 36.5t-144 98.5t-88.5 141.5t-31.5 166.5q0 89 31.5 169t88.5 140.5t144 96.5t191 36q138 0 243 -61.5t158 -161.5t53 -219q0 -117 -53 -217t-158.5 -163t-242.5 -63zM547 635q170 0 276.5 109.5t106.5 271.5q0 163 -106.5 272t-276.5 109
t-276.5 -109t-106.5 -272q0 -162 106.5 -271.5t276.5 -109.5zM625 834l-64 122h-69v-122h-91v393h181q68 0 104.5 -36.5t36.5 -98.5q0 -84 -72 -121l78 -137h-104zM492 1028h71q70 0 70 64q0 61 -70 61h-71v-125z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="1011" 
d="M158 1227v219h696v-219h-696z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="827" 
d="M414 942q86 0 133 54.5t47 138.5q0 83 -47 137.5t-133 54.5t-133.5 -54.5t-47.5 -137.5t47.5 -138t133.5 -55zM414 815q-145 0 -233.5 92t-88.5 228t88 227.5t234 91.5q145 0 233 -91.5t88 -227.5t-88 -228t-233 -92z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="1253" 
d="M154 0v219h344v369h-344v233h344v379h258v-379h344v-233h-344v-369h344v-219h-946z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="825" 
d="M113 942v145l338 287q65 54 65 107q0 40 -28.5 64t-75.5 24q-109 0 -109 -99v-10h-180v17q0 121 79 188.5t212 67.5q131 0 215 -69t84 -179q0 -77 -40.5 -131.5t-115.5 -112.5l-180 -139h330v-160h-594z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="825" 
d="M160 1567v147h512v-147l-183 -160q101 -3 162.5 -62.5t61.5 -156.5q0 -113 -92 -186.5t-222 -73.5q-127 0 -210.5 68t-86.5 186h172q3 -46 37.5 -76.5t89.5 -30.5q58 0 91.5 28.5t33.5 76.5q0 92 -125 92h-127v137l162 158h-276z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="808" 
d="M158 1186l149 315h344l-217 -315h-276z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="1212" 
d="M1075 1065v-1065h-266v127q-45 -70 -125.5 -111t-181.5 -41q-60 0 -96 19v-350h-279v1421h279v-643q0 -213 188 -213q96 0 149.5 57.5t53.5 157.5v641h278z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="1255" 
d="M895 -356v1622h-215v-936q-259 0 -419.5 145t-160.5 404q0 263 154.5 409t425.5 146h405v-1790h-190z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="524" 
d="M102 586q0 71 44 115t116 44t116 -44t44 -115t-44 -115.5t-116 -44.5t-116 44.5t-44 115.5z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="692" 
d="M303 -381q-90 0 -145 12v113q48 -10 96 -10q82 0 82 45q0 41 -76 47l-74 6l39 168h133l-14 -74l33 -4q158 -20 158 -141q0 -73 -62.5 -117.5t-169.5 -44.5z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="636" 
d="M266 942v578l-184 -52v170l217 76h164v-772h-197z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="884" 
d="M137 469v164h615v-164h-615zM442 1454q154 0 241 -86t87 -225v-90q0 -140 -87 -226t-241 -86t-240.5 86t-86.5 226v90q0 140 86.5 225.5t240.5 85.5zM590 1049v98q0 158 -148 158q-147 0 -147 -158v-98q0 -158 147 -158q148 0 148 158z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="1300" 
d="M1280 565l-334 -442h-313l332 442l-332 443h313zM709 565l-334 -442h-314l332 442l-332 443h314z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="1705" 
d="M1311 0v150h-387v141l245 481h185l-234 -473h191v150h182v-150h100v-149h-100v-150h-182zM297 662v577l-184 -51v170l217 76h164v-772h-197zM338 0l737 1434h207l-737 -1434h-207z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="1728" 
d="M1016 0v145l338 287q65 54 65 107q0 40 -28.5 64t-75.5 24q-109 0 -109 -99v-10h-180v17q0 121 79 188.5t212 67.5q131 0 215 -69t84 -179q0 -77 -40.5 -131.5t-115.5 -112.5l-180 -139h330v-160h-594zM297 662v577l-184 -51v170l217 76h164v-772h-197zM338 0l737 1434
h207l-737 -1434h-207z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="1802" 
d="M170 1286v148h512v-148l-182 -160q101 -3 162 -62.5t61 -156.5q0 -113 -91.5 -186.5t-221.5 -73.5q-127 0 -210.5 68t-86.5 186h172q3 -46 37.5 -76t89.5 -30q58 0 91.5 28t33.5 76q0 92 -125 92h-127v137l161 158h-276zM1407 0v150h-387v141l246 481h184l-233 -473h190
v150h182v-150h101v-149h-101v-150h-182zM434 0l737 1434h207l-737 -1434h-207z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="1032" 
d="M670 1004v-95q0 -116 -30 -190.5t-99 -126.5l-95 -70q-44 -31 -66 -51t-39 -52t-17 -71q0 -70 53.5 -110.5t144.5 -40.5q98 0 150.5 45t52.5 133v47h256v-51q0 -198 -126 -299t-339 -101q-210 0 -340.5 103.5t-130.5 273.5q0 101 41 173t119 130l104 76q44 32 65.5 55
t34.5 62.5t13 99.5v60h248zM700 1290q0 -71 -43.5 -115.5t-115.5 -44.5t-116 44.5t-44 115.5t44 115.5t116 44.5t115.5 -44.5t43.5 -115.5z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="1413" 
d="M852 1434l514 -1434h-285l-96 301h-557l-96 -301h-285l514 1434h291zM707 1176l-207 -648h411zM555 1542l-201 289h342l142 -289h-283z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="1413" 
d="M852 1434l514 -1434h-285l-96 301h-557l-96 -301h-285l514 1434h291zM707 1176l-207 -648h411zM586 1542l141 289h342l-201 -289h-282z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="1413" 
d="M852 1434l514 -1434h-285l-96 301h-557l-96 -301h-285l514 1434h291zM707 1176l-207 -648h411zM844 1831l241 -289h-268l-110 137l-111 -137h-268l241 289h275z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="1413" 
d="M852 1434l514 -1434h-285l-96 301h-557l-96 -301h-285l514 1434h291zM707 1176l-207 -648h411zM457 1534l-185 41q27 139 78.5 202.5t167.5 63.5q74 0 214.5 -43t162.5 -43q9 0 16.5 1.5t13.5 3.5t12 7.5t10 8t8.5 10.5t6.5 11.5t6 13.5l5 12.5t5 14t5 13.5l182 -51
q-21 -70 -36.5 -109t-42.5 -77t-66 -54t-96 -16q-49 0 -190 43t-189 43q-37 0 -56.5 -24.5t-31.5 -71.5z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="1413" 
d="M852 1434l514 -1434h-285l-96 301h-557l-96 -301h-285l514 1434h291zM707 1176l-207 -648h411zM328 1692q0 64 42 106.5t107 42.5t107.5 -42.5t42.5 -106.5t-42.5 -107t-107.5 -43q-64 0 -106.5 42.5t-42.5 107.5zM786 1692q0 64 42.5 106.5t107.5 42.5q64 0 106.5 -42.5
t42.5 -106.5q0 -65 -42.5 -107.5t-106.5 -42.5t-107 43t-43 107z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="1413" 
d="M332 0h-285l498 1389q-56 53 -56 133q0 79 60.5 133.5t157.5 54.5q98 0 157.5 -54.5t59.5 -133.5q0 -77 -56 -133l498 -1389h-285l-96 301h-557zM707 1176l-207 -648h411zM707 1434q42 0 68 24.5t26 63.5q0 40 -25.5 63t-68.5 23t-69 -23t-26 -63q0 -39 26.5 -63.5
t68.5 -24.5z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="2187" 
d="M1079 0v303h-577l-195 -303h-307l961 1434h1122v-238h-717v-350h655v-240h-655v-368h719v-238h-1006zM649 532h430v668z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="1445" 
d="M735 -381q-89 0 -145 12v113q48 -10 96 -10q82 0 82 45q0 41 -76 47l-74 6l33 143q-135 13 -239 66.5t-168 139t-96.5 194.5t-32.5 239v205q0 140 37.5 255.5t112.5 203t196.5 136t279.5 48.5q132 0 244 -43t187.5 -116.5t119.5 -171t49 -207.5h-266q-15 132 -99 217.5
t-235 85.5q-173 0 -257.5 -106t-84.5 -287v-234q0 -181 84.5 -287t257.5 -106q151 0 235 85.5t99 217.5h266q-7 -142 -74.5 -259.5t-193.5 -193t-289 -84.5l-8 -47l33 -4q158 -20 158 -141q0 -73 -62.5 -117.5t-169.5 -44.5z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="1265" 
d="M158 0v1434h1003v-238h-717v-350h656v-240h-656v-368h719v-238h-1005zM553 1542l-201 289h342l142 -289h-283z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="1265" 
d="M158 0v1434h1003v-238h-717v-350h656v-240h-656v-368h719v-238h-1005zM506 1542l141 289h342l-201 -289h-282z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="1265" 
d="M158 0v1434h1003v-238h-717v-350h656v-240h-656v-368h719v-238h-1005zM807 1831l242 -289h-269l-110 137l-111 -137h-268l241 289h275z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="1265" 
d="M158 0v1434h1003v-238h-717v-350h656v-240h-656v-368h719v-238h-1005zM295 1692q0 64 42.5 106.5t106.5 42.5q65 0 107.5 -42.5t42.5 -106.5t-43 -107t-107 -43t-106.5 42.5t-42.5 107.5zM754 1692q0 64 42 106.5t107 42.5t107.5 -42.5t42.5 -106.5t-42.5 -107
t-107.5 -43q-64 0 -106.5 42.5t-42.5 107.5z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="622" 
d="M168 0v1434h287v-1434h-287zM172 1542l-201 289h342l142 -289h-283z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="622" 
d="M168 0v1434h287v-1434h-287zM184 1542l142 289h342l-201 -289h-283z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="622" 
d="M168 0v1434h287v-1434h-287zM449 1831l241 -289h-268l-111 137l-110 -137h-269l242 289h275z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="622" 
d="M168 0v1434h287v-1434h-287zM-68 1692q0 64 42.5 106.5t107.5 42.5q64 0 106.5 -42.5t42.5 -106.5q0 -65 -42.5 -107.5t-106.5 -42.5t-107 43t-43 107zM391 1692q0 64 42.5 106.5t107.5 42.5t107 -42.5t42 -106.5q0 -65 -42.5 -107.5t-106.5 -42.5q-65 0 -107.5 43
t-42.5 107z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="1513" 
d="M68 598v238h153v598h582q141 0 253.5 -42t185.5 -117.5t111 -178.5t38 -226v-307q0 -252 -153.5 -407.5t-434.5 -155.5h-582v598h-153zM1106 561v311q0 158 -90 243t-262 85h-248v-364h311v-238h-311v-365h248q172 0 262 85t90 243z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="1492" 
d="M158 0v1434h272l625 -955v955h280v-1434h-272l-625 954v-954h-280zM485 1534l-184 41q27 139 78.5 202.5t167.5 63.5q74 0 214.5 -43t162.5 -43q9 0 16.5 1.5t13.5 3.5t12 7.5t10 8t8.5 10.5t6.5 11.5t6 13.5l5 12.5t5 14t5 13.5l182 -51q-16 -56 -28.5 -89t-32 -69
t-42.5 -55t-58 -31t-81 -12q-49 0 -190 43t-189 43q-37 0 -56.5 -24.5t-31.5 -71.5z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="1507" 
d="M1393 821v-209q0 -138 -41 -254.5t-119.5 -203t-201 -135t-277.5 -48.5q-154 0 -276.5 48.5t-201 135t-120 203t-41.5 254.5v209q0 138 41.5 254.5t120 203t201 135t276.5 48.5q155 0 277.5 -48.5t201 -135t119.5 -203t41 -254.5zM754 209q174 0 263 105.5t89 283.5v238
q0 178 -89 283.5t-263 105.5t-263.5 -106t-89.5 -283v-238q0 -177 89.5 -283t263.5 -106zM623 1542l-201 289h342l141 -289h-282z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="1507" 
d="M1393 821v-209q0 -138 -41 -254.5t-119.5 -203t-201 -135t-277.5 -48.5q-154 0 -276.5 48.5t-201 135t-120 203t-41.5 254.5v209q0 138 41.5 254.5t120 203t201 135t276.5 48.5q155 0 277.5 -48.5t201 -135t119.5 -203t41 -254.5zM754 209q174 0 263 105.5t89 283.5v238
q0 178 -89 283.5t-263 105.5t-263.5 -106t-89.5 -283v-238q0 -177 89.5 -283t263.5 -106zM621 1542l141 289h342l-201 -289h-282z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="1507" 
d="M1393 821v-209q0 -138 -41 -254.5t-119.5 -203t-201 -135t-277.5 -48.5q-154 0 -276.5 48.5t-201 135t-120 203t-41.5 254.5v209q0 138 41.5 254.5t120 203t201 135t276.5 48.5q155 0 277.5 -48.5t201 -135t119.5 -203t41 -254.5zM754 209q174 0 263 105.5t89 283.5v238
q0 178 -89 283.5t-263 105.5t-263.5 -106t-89.5 -283v-238q0 -177 89.5 -283t263.5 -106zM891 1831l242 -289h-269l-110 137l-111 -137h-268l241 289h275z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="1507" 
d="M1393 821v-209q0 -138 -41 -254.5t-119.5 -203t-201 -135t-277.5 -48.5q-154 0 -276.5 48.5t-201 135t-120 203t-41.5 254.5v209q0 138 41.5 254.5t120 203t201 135t276.5 48.5q155 0 277.5 -48.5t201 -135t119.5 -203t41 -254.5zM754 209q174 0 263 105.5t89 283.5v238
q0 178 -89 283.5t-263 105.5t-263.5 -106t-89.5 -283v-238q0 -177 89.5 -283t263.5 -106zM504 1534l-185 41q27 139 78.5 202.5t167.5 63.5q74 0 214.5 -43t162.5 -43q9 0 16.5 1.5t13.5 3.5t12 7.5t10 8t8.5 10.5t6.5 11.5t6 13.5l5 12.5t5 14t5 13.5l182 -51
q-21 -70 -36.5 -109t-42.5 -77t-66 -54t-96 -16q-49 0 -190 43t-189 43q-37 0 -56.5 -24.5t-31.5 -71.5z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="1507" 
d="M1393 821v-209q0 -138 -41 -254.5t-119.5 -203t-201 -135t-277.5 -48.5q-154 0 -276.5 48.5t-201 135t-120 203t-41.5 254.5v209q0 138 41.5 254.5t120 203t201 135t276.5 48.5q155 0 277.5 -48.5t201 -135t119.5 -203t41 -254.5zM754 209q174 0 263 105.5t89 283.5v238
q0 178 -89 283.5t-263 105.5t-263.5 -106t-89.5 -283v-238q0 -177 89.5 -283t263.5 -106zM375 1692q0 64 42.5 106.5t106.5 42.5q65 0 107.5 -42.5t42.5 -106.5t-43 -107t-107 -43t-106.5 42.5t-42.5 107.5zM834 1692q0 64 42 106.5t107 42.5t107.5 -42.5t42.5 -106.5
t-42.5 -107t-107.5 -43q-64 0 -106.5 42.5t-42.5 107.5z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="1224" 
d="M143 1047l160 159l309 -309l310 309l159 -159l-309 -310l309 -309l-159 -160l-310 310l-309 -310l-160 160l310 309z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="1507" 
d="M1393 821v-209q0 -138 -41 -254.5t-119.5 -203t-201 -135t-277.5 -48.5q-197 0 -344 80l-101 -153h-194l166 252q-166 172 -166 462v209q0 138 41.5 254.5t120 203t201 135t276.5 48.5q198 0 342 -80l102 154h195l-166 -252q166 -172 166 -463zM401 598q0 -122 39 -207
l516 780q-87 54 -202 54q-174 0 -263.5 -106t-89.5 -283v-238zM754 209q174 0 263 105.5t89 283.5v238q0 130 -41 204l-514 -778q71 -53 203 -53z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="1474" 
d="M737 -29q-275 0 -432.5 149.5t-157.5 395.5v918h285v-930q0 -138 82.5 -216.5t222.5 -78.5q139 0 221 78.5t82 216.5v930h287v-918q0 -246 -157.5 -395.5t-432.5 -149.5zM627 1542l-201 289h342l141 -289h-282z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="1474" 
d="M737 -29q-275 0 -432.5 149.5t-157.5 395.5v918h285v-930q0 -138 82.5 -216.5t222.5 -78.5q139 0 221 78.5t82 216.5v930h287v-918q0 -246 -157.5 -395.5t-432.5 -149.5zM580 1542l141 289h342l-201 -289h-282z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="1474" 
d="M737 -29q-275 0 -432.5 149.5t-157.5 395.5v918h285v-930q0 -138 82.5 -216.5t222.5 -78.5q139 0 221 78.5t82 216.5v930h287v-918q0 -246 -157.5 -395.5t-432.5 -149.5zM874 1831l242 -289h-268l-111 137l-110 -137h-269l242 289h274z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="1474" 
d="M737 -29q-275 0 -432.5 149.5t-157.5 395.5v918h285v-930q0 -138 82.5 -216.5t222.5 -78.5q139 0 221 78.5t82 216.5v930h287v-918q0 -246 -157.5 -395.5t-432.5 -149.5zM358 1692q0 64 42.5 106.5t107.5 42.5q64 0 106.5 -42.5t42.5 -106.5q0 -65 -42.5 -107.5
t-106.5 -42.5t-107 43t-43 107zM817 1692q0 64 42.5 106.5t107.5 42.5t107 -42.5t42 -106.5q0 -65 -42.5 -107.5t-106.5 -42.5q-65 0 -107.5 43t-42.5 107z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="1302" 
d="M795 516v-516h-287v516l-492 918h312l323 -639l324 639h311zM514 1542l141 289h342l-200 -289h-283z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="1335" 
d="M158 0v1434h286v-215h310q245 0 388.5 -132.5t143.5 -349.5t-146 -353t-386 -136h-310v-248h-286zM444 483h291q130 0 197 71t67 183q0 111 -66.5 178.5t-197.5 67.5h-291v-500z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="1261" 
d="M137 0v1061q0 146 63 247t170.5 148t250.5 47q219 0 356 -111.5t137 -291.5q0 -115 -50.5 -204t-141.5 -136q134 -39 204 -122t70 -226q0 -195 -136.5 -303.5t-363.5 -108.5h-69v221h57q109 0 171.5 54t62.5 155q0 92 -62 144.5t-174 52.5h-57v217q97 6 154 69.5
t57 155.5q0 96 -55 153.5t-158 57.5q-97 0 -152 -51.5t-55 -155.5v-1073h-279z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="1198" 
d="M496 -25q-182 0 -293 89t-111 243q0 168 115.5 253t314.5 85q87 0 157 -26.5t114 -63.5v145q0 75 -50 123.5t-135 48.5q-133 0 -184 -100h-258q45 154 164.5 236t292.5 82q200 0 324 -112.5t124 -297.5v-680h-268v98q-36 -53 -124.5 -88t-182.5 -35zM567 160
q93 0 159.5 45t66.5 112q0 69 -61 109.5t-157 40.5q-99 0 -156 -39.5t-57 -112.5q0 -72 57.5 -113.5t147.5 -41.5zM498 1186l-213 315h344l149 -315h-280z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="1198" 
d="M496 -25q-182 0 -293 89t-111 243q0 168 115.5 253t314.5 85q87 0 157 -26.5t114 -63.5v145q0 75 -50 123.5t-135 48.5q-133 0 -184 -100h-258q45 154 164.5 236t292.5 82q200 0 324 -112.5t124 -297.5v-680h-268v98q-36 -53 -124.5 -88t-182.5 -35zM567 160
q93 0 159.5 45t66.5 112q0 69 -61 109.5t-157 40.5q-99 0 -156 -39.5t-57 -112.5q0 -72 57.5 -113.5t147.5 -41.5zM489 1186l150 315h344l-217 -315h-277z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="1198" 
d="M496 -25q-182 0 -293 89t-111 243q0 168 115.5 253t314.5 85q87 0 157 -26.5t114 -63.5v145q0 75 -50 123.5t-135 48.5q-133 0 -184 -100h-258q45 154 164.5 236t292.5 82q200 0 324 -112.5t124 -297.5v-680h-268v98q-36 -53 -124.5 -88t-182.5 -35zM567 160
q93 0 159.5 45t66.5 112q0 69 -61 109.5t-157 40.5q-99 0 -156 -39.5t-57 -112.5q0 -72 57.5 -113.5t147.5 -41.5zM780 1501l230 -315h-254l-111 157l-110 -157h-254l229 315h270z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="1198" 
d="M496 -25q-182 0 -293 89t-111 243q0 168 115.5 253t314.5 85q87 0 157 -26.5t114 -63.5v145q0 75 -50 123.5t-135 48.5q-133 0 -184 -100h-258q45 154 164.5 236t292.5 82q200 0 324 -112.5t124 -297.5v-680h-268v98q-36 -53 -124.5 -88t-182.5 -35zM567 160
q93 0 159.5 45t66.5 112q0 69 -61 109.5t-157 40.5q-99 0 -156 -39.5t-57 -112.5q0 -72 57.5 -113.5t147.5 -41.5zM397 1182l-174 43q19 127 71.5 195.5t162.5 68.5q58 0 178.5 -44t146.5 -44q25 0 42.5 15t23.5 28t17 45q2 5 3 8l172 -53q-14 -49 -24.5 -78t-29.5 -67
t-41 -58t-56.5 -34.5t-77.5 -14.5q-49 0 -168 44t-162 44q-25 0 -42 -14.5t-24 -31t-18 -52.5z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="1198" 
d="M496 -25q-182 0 -293 89t-111 243q0 168 115.5 253t314.5 85q87 0 157 -26.5t114 -63.5v145q0 75 -50 123.5t-135 48.5q-133 0 -184 -100h-258q45 154 164.5 236t292.5 82q200 0 324 -112.5t124 -297.5v-680h-268v98q-36 -53 -124.5 -88t-182.5 -35zM567 160
q93 0 159.5 45t66.5 112q0 69 -61 109.5t-157 40.5q-99 0 -156 -39.5t-57 -112.5q0 -72 57.5 -113.5t147.5 -41.5zM285 1341q0 65 42.5 107.5t106.5 42.5t107 -42.5t43 -107.5q0 -64 -42.5 -106.5t-107.5 -42.5q-64 0 -106.5 42t-42.5 107zM715 1341q0 65 42.5 107.5
t106.5 42.5t107 -42.5t43 -107.5q0 -64 -42.5 -106.5t-107.5 -42.5q-64 0 -106.5 42t-42.5 107z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="1198" 
d="M496 -25q-182 0 -293 89t-111 243q0 168 115.5 253t314.5 85q87 0 157 -26.5t114 -63.5v145q0 75 -50 123.5t-135 48.5q-133 0 -184 -100h-258q45 154 164.5 236t292.5 82q200 0 324 -112.5t124 -297.5v-680h-268v98q-36 -53 -124.5 -88t-182.5 -35zM567 160
q93 0 159.5 45t66.5 112q0 69 -61 109.5t-157 40.5q-99 0 -156 -39.5t-57 -112.5q0 -72 57.5 -113.5t147.5 -41.5zM637 1288q42 0 68 24.5t26 63.5q0 40 -25.5 63t-68.5 23t-68.5 -23t-25.5 -63q0 -39 26 -63.5t68 -24.5zM637 1186q-100 0 -158.5 55t-58.5 135q0 79 60 134
t157 55q98 0 157.5 -55t59.5 -134q0 -80 -58.5 -135t-158.5 -55z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="1873" 
d="M1296 -25q-125 0 -222.5 41.5t-158.5 116.5q-62 -67 -163.5 -112.5t-231.5 -45.5q-192 0 -310 96t-118 251q0 109 58 184t152.5 109t219.5 34q88 0 158.5 -26t112.5 -66v129q0 79 -48 131.5t-135 52.5q-72 0 -115 -24t-71 -74h-258q37 157 154.5 237.5t291.5 80.5
q212 0 328 -132q55 61 147.5 96.5t204.5 35.5q152 0 261.5 -64t161.5 -168t52 -235v-187h-696v-26q0 -102 55.5 -168t169.5 -66q85 0 141.5 38t78.5 97h243q-28 -147 -157.5 -241.5t-305.5 -94.5zM567 162q94 0 160 44t66 111q0 70 -61.5 112t-156.5 42q-101 0 -157 -39.5
t-56 -112.5q0 -72 57.5 -114.5t147.5 -42.5zM1071 633h440v26q0 105 -55.5 169.5t-163.5 64.5q-110 0 -165.5 -62.5t-55.5 -166.5v-31z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="1169" 
d="M608 -381q-90 0 -145 12v113q48 -10 96 -10q82 0 82 45q0 41 -76 47l-73 6l32 148q-205 18 -319.5 148.5t-114.5 328.5v151q0 215 134 348.5t370 133.5q212 0 345.5 -118.5t139.5 -303.5h-254q-10 86 -65.5 141t-159.5 55q-114 0 -172.5 -67t-58.5 -179v-172
q0 -112 58.5 -178.5t172.5 -66.5q104 0 159.5 55t65.5 141h254q-6 -170 -120.5 -284t-299.5 -133l-10 -54l33 -4q158 -20 158 -141q0 -73 -62.5 -117.5t-169.5 -44.5z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="1146" 
d="M586 -25q-228 0 -362 127t-134 349v145q0 223 129 358.5t363 135.5q152 0 261.5 -64t161.5 -168t52 -235v-187h-697v-26q0 -102 56 -168t170 -66q85 0 141 38t78 97h244q-28 -147 -157.5 -241.5t-305.5 -94.5zM360 643v-10h441v26q0 105 -55.5 169.5t-163.5 64.5
q-114 0 -168 -67.5t-54 -182.5zM449 1186l-213 315h344l149 -315h-280z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="1146" 
d="M586 -25q-228 0 -362 127t-134 349v145q0 223 129 358.5t363 135.5q152 0 261.5 -64t161.5 -168t52 -235v-187h-697v-26q0 -102 56 -168t170 -66q85 0 141 38t78 97h244q-28 -147 -157.5 -241.5t-305.5 -94.5zM360 643v-10h441v26q0 105 -55.5 169.5t-163.5 64.5
q-114 0 -168 -67.5t-54 -182.5zM442 1186l150 315h344l-217 -315h-277z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="1146" 
d="M586 -25q-228 0 -362 127t-134 349v145q0 223 129 358.5t363 135.5q152 0 261.5 -64t161.5 -168t52 -235v-187h-697v-26q0 -102 56 -168t170 -66q85 0 141 38t78 97h244q-28 -147 -157.5 -241.5t-305.5 -94.5zM360 643v-10h441v26q0 105 -55.5 169.5t-163.5 64.5
q-114 0 -168 -67.5t-54 -182.5zM721 1501l229 -315h-254l-110 157l-111 -157h-254l230 315h270z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="1146" 
d="M586 -25q-228 0 -362 127t-134 349v145q0 223 129 358.5t363 135.5q152 0 261.5 -64t161.5 -168t52 -235v-187h-697v-26q0 -102 56 -168t170 -66q85 0 141 38t78 97h244q-28 -147 -157.5 -241.5t-305.5 -94.5zM360 643v-10h441v26q0 105 -55.5 169.5t-163.5 64.5
q-114 0 -168 -67.5t-54 -182.5zM215 1341q0 65 42.5 107.5t107.5 42.5q64 0 106.5 -42.5t42.5 -107.5t-42 -107t-107 -42t-107.5 42.5t-42.5 106.5zM645 1341q0 65 42.5 107.5t107.5 42.5q64 0 106.5 -42.5t42.5 -107.5t-42 -107t-107 -42t-107.5 42.5t-42.5 106.5z" />
    <glyph glyph-name="igrave" unicode="&#xec;" 
d="M143 0v1065h279v-1065h-279zM139 1186l-213 315h344l150 -315h-281z" />
    <glyph glyph-name="iacute" unicode="&#xed;" 
d="M143 0v1065h279v-1065h-279zM156 1186l149 315h344l-217 -315h-276z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" 
d="M143 0v1065h279v-1065h-279zM418 1501l229 -315h-254l-110 157l-111 -157h-254l229 315h271z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" 
d="M143 0v1065h279v-1065h-279zM-82 1341q0 65 43 107.5t107 42.5t106.5 -42.5t42.5 -107.5t-42.5 -107t-106.5 -42q-65 0 -107.5 42.5t-42.5 106.5zM348 1341q0 65 43 107.5t107 42.5t106.5 -42.5t42.5 -107.5t-42.5 -107t-106.5 -42q-65 0 -107.5 42.5t-42.5 106.5z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="1185" 
d="M319 1094l-61 161l191 68q-92 69 -263 172h332q87 -48 148 -94l231 84l59 -160l-143 -53q131 -135 207 -302.5t76 -353.5v-159q0 -213 -139.5 -349.5t-372.5 -136.5q-219 0 -356.5 133t-137.5 338v129q0 207 116.5 338t315.5 131q161 0 252 -88q-78 144 -176 242z
M590 188q111 0 172 65t61 171v154q0 116 -61 184.5t-168 68.5q-114 0 -173 -67.5t-59 -190.5v-155q0 -102 62.5 -166t165.5 -64z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="1220" 
d="M137 0v1065h266v-127q82 152 314 152q174 0 275.5 -108.5t101.5 -299.5v-682h-279v637q0 97 -48.5 154t-145.5 57q-96 0 -150.5 -56t-54.5 -161v-631h-279zM389 1182l-174 43q19 127 71.5 195.5t162.5 68.5q58 0 178.5 -44t146.5 -44q25 0 42.5 15t23.5 28t17 45q2 5 3 8
l172 -53q-14 -49 -24.5 -78t-29.5 -67t-41 -58t-56.5 -34.5t-77.5 -14.5q-49 0 -168 44t-162 44q-25 0 -42 -14.5t-24 -31t-18 -52.5z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="1183" 
d="M592 -25q-233 0 -367.5 136t-134.5 346v151q0 210 134.5 346t367.5 136t367.5 -136t134.5 -346v-151q0 -210 -134.5 -346t-367.5 -136zM823 438v189q0 109 -58.5 176t-172.5 67t-173 -67t-59 -176v-189q0 -109 59 -176t173 -67t172.5 67t58.5 176zM449 1186l-213 315h344
l149 -315h-280z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="1183" 
d="M592 -25q-233 0 -367.5 136t-134.5 346v151q0 210 134.5 346t367.5 136t367.5 -136t134.5 -346v-151q0 -210 -134.5 -346t-367.5 -136zM823 438v189q0 109 -58.5 176t-172.5 67t-173 -67t-59 -176v-189q0 -109 59 -176t173 -67t172.5 67t58.5 176zM455 1186l149 315h344
l-217 -315h-276z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="1183" 
d="M592 -25q-233 0 -367.5 136t-134.5 346v151q0 210 134.5 346t367.5 136t367.5 -136t134.5 -346v-151q0 -210 -134.5 -346t-367.5 -136zM823 438v189q0 109 -58.5 176t-172.5 67t-173 -67t-59 -176v-189q0 -109 59 -176t173 -67t172.5 67t58.5 176zM727 1501l229 -315
h-254l-110 157l-111 -157h-254l230 315h270z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="1183" 
d="M592 -25q-233 0 -367.5 136t-134.5 346v151q0 210 134.5 346t367.5 136t367.5 -136t134.5 -346v-151q0 -210 -134.5 -346t-367.5 -136zM823 438v189q0 109 -58.5 176t-172.5 67t-173 -67t-59 -176v-189q0 -109 59 -176t173 -67t172.5 67t58.5 176zM369 1182l-174 43
q19 128 71 196t162 68q58 0 179 -44t147 -44q25 0 42.5 15t23.5 28t17 45q2 5 3 8l172 -53q-14 -49 -24.5 -78.5t-30 -66.5t-41.5 -57.5t-56 -35t-78 -14.5q-49 0 -167.5 44t-161.5 44q-25 0 -42 -14.5t-24 -31t-18 -52.5z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="1183" 
d="M592 -25q-233 0 -367.5 136t-134.5 346v151q0 210 134.5 346t367.5 136t367.5 -136t134.5 -346v-151q0 -210 -134.5 -346t-367.5 -136zM823 438v189q0 109 -58.5 176t-172.5 67t-173 -67t-59 -176v-189q0 -109 59 -176t173 -67t172.5 67t58.5 176zM227 1341
q0 65 42.5 107.5t107.5 42.5q64 0 106.5 -42.5t42.5 -107.5t-42 -107t-107 -42t-107.5 42.5t-42.5 106.5zM657 1341q0 65 42.5 107.5t107.5 42.5q64 0 106.5 -42.5t42.5 -107.5t-42 -107t-107 -42t-107.5 42.5t-42.5 106.5z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="1224" 
d="M143 618v238h938v-238h-938zM457 328q0 69 42.5 112t112.5 43t113 -43t43 -112t-43 -112.5t-113 -43.5t-112.5 43.5t-42.5 112.5zM457 1147q0 69 42.5 112.5t112.5 43.5t113 -43.5t43 -112.5t-43 -112.5t-113 -43.5t-112.5 43.5t-42.5 112.5z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="1183" 
d="M592 -25q-147 0 -260 58l-90 -135h-152l139 206q-139 133 -139 353v151q0 210 134.5 346t367.5 136q145 0 258 -58l92 135h152l-140 -209q68 -65 104 -155.5t36 -194.5v-151q0 -210 -134.5 -346t-367.5 -136zM360 438q0 -58 19 -110l342 510q-56 32 -129 32
q-114 0 -173 -67t-59 -176v-189zM823 438v189q0 59 -18 108l-342 -510q49 -30 129 -30q114 0 172.5 67t58.5 176z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="1212" 
d="M1075 1065v-1065h-266v127q-37 -68 -122 -110t-187 -42q-177 0 -275 111.5t-98 296.5v682h279v-643q0 -102 47.5 -157.5t140.5 -55.5q96 0 149.5 57.5t53.5 157.5v641h278zM500 1186l-213 315h344l149 -315h-280z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="1212" 
d="M1075 1065v-1065h-266v127q-37 -68 -122 -110t-187 -42q-177 0 -275 111.5t-98 296.5v682h279v-643q0 -102 47.5 -157.5t140.5 -55.5q96 0 149.5 57.5t53.5 157.5v641h278zM444 1186l150 315h344l-217 -315h-277z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="1212" 
d="M1075 1065v-1065h-266v127q-37 -68 -122 -110t-187 -42q-177 0 -275 111.5t-98 296.5v682h279v-643q0 -102 47.5 -157.5t140.5 -55.5q96 0 149.5 57.5t53.5 157.5v641h278zM754 1501l229 -315h-254l-111 157l-110 -157h-254l229 315h271z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="1212" 
d="M1075 1065v-1065h-266v127q-37 -68 -122 -110t-187 -42q-177 0 -275 111.5t-98 296.5v682h279v-643q0 -102 47.5 -157.5t140.5 -55.5q96 0 149.5 57.5t53.5 157.5v641h278zM258 1341q0 65 43 107.5t107 42.5t106.5 -42.5t42.5 -107.5t-42.5 -107t-106.5 -42
q-65 0 -107.5 42.5t-42.5 106.5zM688 1341q0 65 43 107.5t107 42.5t106.5 -42.5t42.5 -107.5t-42.5 -107t-106.5 -42q-65 0 -107.5 42.5t-42.5 106.5z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="1046" 
d="M559 -356h-274l106 360l-368 1061h278l229 -764l218 764h276zM391 1186l150 315h344l-217 -315h-277z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="1257" 
d="M719 1090q107 0 193 -37.5t141 -102.5t84.5 -149t29.5 -180v-177q0 -130 -50.5 -235t-153.5 -169.5t-242 -64.5q-103 0 -182.5 38t-122.5 102v-471h-279v1851h279v-541q44 60 122.5 98t180.5 38zM651 864q-105 0 -170 -71.5t-65 -184.5v-155q0 -117 67 -184.5t166 -67.5
q115 0 177.5 70t62.5 186v151q0 112 -61 184t-177 72z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="1046" 
d="M559 -356h-274l106 360l-368 1061h278l229 -764l218 764h276zM176 1341q0 65 43 107.5t107 42.5t106.5 -42.5t42.5 -107.5t-42.5 -107t-106.5 -42q-65 0 -107.5 42.5t-42.5 106.5zM606 1341q0 65 43 107.5t107 42.5t106.5 -42.5t42.5 -107.5t-42.5 -107t-106.5 -42
q-65 0 -107.5 42.5t-42.5 106.5z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="1413" 
d="M852 1434l514 -1434h-285l-96 301h-557l-96 -301h-285l514 1434h291zM707 1176l-207 -648h411zM322 1581v223h770v-223h-770z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="1198" 
d="M496 -25q-182 0 -293 89t-111 243q0 168 115.5 253t314.5 85q87 0 157 -26.5t114 -63.5v145q0 75 -50 123.5t-135 48.5q-133 0 -184 -100h-258q45 154 164.5 236t292.5 82q200 0 324 -112.5t124 -297.5v-680h-268v98q-36 -53 -124.5 -88t-182.5 -35zM567 160
q93 0 159.5 45t66.5 112q0 69 -61 109.5t-157 40.5q-99 0 -156 -39.5t-57 -112.5q0 -72 57.5 -113.5t147.5 -41.5zM281 1227v219h696v-219h-696z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="1413" 
d="M852 1434l514 -1434h-285l-96 301h-557l-96 -301h-285l514 1434h291zM707 1176l-207 -648h411zM707 1542q-160 0 -264.5 77t-104.5 212h217q0 -56 41 -90.5t111 -34.5t110.5 34.5t40.5 90.5h217q0 -135 -104 -212t-264 -77z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="1198" 
d="M496 -25q-182 0 -293 89t-111 243q0 168 115.5 253t314.5 85q87 0 157 -26.5t114 -63.5v145q0 75 -50 123.5t-135 48.5q-133 0 -184 -100h-258q45 154 164.5 236t292.5 82q200 0 324 -112.5t124 -297.5v-680h-268v98q-36 -53 -124.5 -88t-182.5 -35zM567 160
q93 0 159.5 45t66.5 112q0 69 -61 109.5t-157 40.5q-99 0 -156 -39.5t-57 -112.5q0 -72 57.5 -113.5t147.5 -41.5zM647 1186q-163 0 -262.5 83.5t-99.5 231.5h204q0 -68 42 -108.5t116 -40.5t116 40.5t42 108.5h205q0 -148 -100 -231.5t-263 -83.5z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="1417" 
d="M1196 -381q-96 0 -153.5 50t-57.5 138q0 109 96 193l-96 301h-557l-96 -301h-285l514 1434h291l514 -1434q-39 -25 -63 -43.5t-46 -51.5t-22 -67q0 -57 70 -57q30 0 51 6v-147q-82 -21 -160 -21zM707 1176l-207 -648h411z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="1198" 
d="M496 -25q-182 0 -293 89t-111 243q0 168 115.5 253t314.5 85q87 0 157 -26.5t114 -63.5v145q0 75 -50 123.5t-135 48.5q-133 0 -184 -100h-258q45 154 164.5 236t292.5 82q200 0 324 -112.5t124 -297.5v-680q-28 -20 -43.5 -33t-37.5 -35t-33 -45.5t-11 -48.5
q0 -57 70 -57q30 0 51 6v-147q-82 -21 -160 -21q-97 0 -154 47.5t-57 136.5q0 100 107 197v98q-36 -53 -124.5 -88t-182.5 -35zM567 160q93 0 159.5 45t66.5 112q0 69 -61 109.5t-157 40.5q-99 0 -156 -39.5t-57 -112.5q0 -72 57.5 -113.5t147.5 -41.5z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="1445" 
d="M741 -29q-158 0 -279.5 48.5t-196.5 136t-112.5 203t-37.5 255.5v205q0 140 37.5 255.5t112.5 203t196.5 136t279.5 48.5q132 0 244 -43t187.5 -116.5t119.5 -171t49 -207.5h-266q-15 132 -99 217.5t-235 85.5q-173 0 -257.5 -106t-84.5 -287v-234q0 -181 84.5 -287
t257.5 -106q151 0 235 85.5t99 217.5h266q-5 -110 -49 -208t-119.5 -171.5t-187.5 -116.5t-244 -43zM584 1542l141 289h342l-201 -289h-282z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="1169" 
d="M594 -25q-236 0 -370 133.5t-134 348.5v151q0 215 134 348.5t370 133.5q212 0 345.5 -118.5t139.5 -303.5h-254q-10 86 -65.5 141t-159.5 55q-114 0 -172.5 -67t-58.5 -179v-172q0 -112 58.5 -178.5t172.5 -66.5q104 0 159.5 55t65.5 141h254q-6 -185 -139.5 -303.5
t-345.5 -118.5zM459 1186l149 315h344l-217 -315h-276z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="1445" 
d="M741 -29q-158 0 -279.5 48.5t-196.5 136t-112.5 203t-37.5 255.5v205q0 140 37.5 255.5t112.5 203t196.5 136t279.5 48.5q132 0 244 -43t187.5 -116.5t119.5 -171t49 -207.5h-266q-15 132 -99 217.5t-235 85.5q-173 0 -257.5 -106t-84.5 -287v-234q0 -181 84.5 -287
t257.5 -106q151 0 235 85.5t99 217.5h266q-5 -110 -49 -208t-119.5 -171.5t-187.5 -116.5t-244 -43zM891 1831l242 -289h-269l-110 137l-111 -137h-268l241 289h275z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="1169" 
d="M594 -25q-236 0 -370 133.5t-134 348.5v151q0 215 134 348.5t370 133.5q212 0 345.5 -118.5t139.5 -303.5h-254q-10 86 -65.5 141t-159.5 55q-114 0 -172.5 -67t-58.5 -179v-172q0 -112 58.5 -178.5t172.5 -66.5q104 0 159.5 55t65.5 141h254q-6 -185 -139.5 -303.5
t-345.5 -118.5zM731 1501l230 -315h-254l-111 157l-111 -157h-254l230 315h270z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="1445" 
d="M741 -29q-158 0 -279.5 48.5t-196.5 136t-112.5 203t-37.5 255.5v205q0 140 37.5 255.5t112.5 203t196.5 136t279.5 48.5q132 0 244 -43t187.5 -116.5t119.5 -171t49 -207.5h-266q-15 132 -99 217.5t-235 85.5q-173 0 -257.5 -106t-84.5 -287v-234q0 -181 84.5 -287
t257.5 -106q151 0 235 85.5t99 217.5h266q-5 -110 -49 -208t-119.5 -171.5t-187.5 -116.5t-244 -43zM582 1704q0 69 45.5 115.5t115.5 46.5t116 -46.5t46 -115.5t-46 -115.5t-116 -46.5t-115.5 46.5t-45.5 115.5z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="1169" 
d="M594 -25q-236 0 -370 133.5t-134 348.5v151q0 215 134 348.5t370 133.5q212 0 345.5 -118.5t139.5 -303.5h-254q-10 86 -65.5 141t-159.5 55q-114 0 -172.5 -67t-58.5 -179v-172q0 -112 58.5 -178.5t172.5 -66.5q104 0 159.5 55t65.5 141h254q-6 -185 -139.5 -303.5
t-345.5 -118.5zM434 1348q0 69 46 115t116 46t116 -46t46 -115t-46 -115.5t-116 -46.5t-116 46.5t-46 115.5z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="1445" 
d="M741 -29q-158 0 -279.5 48.5t-196.5 136t-112.5 203t-37.5 255.5v205q0 140 37.5 255.5t112.5 203t196.5 136t279.5 48.5q132 0 244 -43t187.5 -116.5t119.5 -171t49 -207.5h-266q-15 132 -99 217.5t-235 85.5q-173 0 -257.5 -106t-84.5 -287v-234q0 -181 84.5 -287
t257.5 -106q151 0 235 85.5t99 217.5h266q-5 -110 -49 -208t-119.5 -171.5t-187.5 -116.5t-244 -43zM1130 1831l-241 -289h-275l-241 289h268l111 -137l110 137h268z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="1169" 
d="M594 -25q-236 0 -370 133.5t-134 348.5v151q0 215 134 348.5t370 133.5q212 0 345.5 -118.5t139.5 -303.5h-254q-10 86 -65.5 141t-159.5 55q-114 0 -172.5 -67t-58.5 -179v-172q0 -112 58.5 -178.5t172.5 -66.5q104 0 159.5 55t65.5 141h254q-6 -185 -139.5 -303.5
t-345.5 -118.5zM973 1501l-230 -315h-270l-229 315h254l110 -158l111 158h254z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="1439" 
d="M158 1434h581q141 0 253.5 -42t185.5 -117.5t111 -178.5t38 -226v-307q0 -252 -153.5 -407.5t-434.5 -155.5h-581v1434zM1042 561v311q0 158 -90 243t-262 85h-248v-967h248q172 0 262 85t90 243zM1092 1831l-242 -289h-275l-241 289h268l111 -137l110 137h269z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="1626" 
d="M1272 1038l82 457h303l-160 -457h-225zM539 -25q-139 0 -242.5 65t-155 170.5t-51.5 233.5v177q0 130 51 235t154 169.5t242 64.5q101 0 179.5 -38.5t125.5 -101.5v545h278v-1495h-266v127q-43 -67 -124.5 -109.5t-190.5 -42.5zM606 201q105 0 170.5 71.5t65.5 184.5v155
q0 117 -67 184.5t-167 67.5q-115 0 -177 -70t-62 -186v-151q0 -112 60.5 -184t176.5 -72z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="1513" 
d="M68 598v238h153v598h582q141 0 253.5 -42t185.5 -117.5t111 -178.5t38 -226v-307q0 -252 -153.5 -407.5t-434.5 -155.5h-582v598h-153zM1106 561v311q0 158 -90 243t-262 85h-248v-364h311v-238h-311v-365h248q172 0 262 85t90 243z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="1271" 
d="M539 -25q-139 0 -242.5 65t-155 170.5t-51.5 233.5v177q0 130 51 235t154 169.5t242 64.5q101 0 179.5 -38.5t125.5 -101.5v209h-205v191h205v145h278v-145h121v-191h-121v-1159h-266v127q-43 -67 -124.5 -109.5t-190.5 -42.5zM606 201q105 0 170.5 71.5t65.5 184.5v155
q0 117 -67 184.5t-167 67.5q-115 0 -177 -70t-62 -186v-151q0 -112 60.5 -184t176.5 -72z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="1265" 
d="M158 0v1434h1003v-238h-717v-350h656v-240h-656v-368h719v-238h-1005zM279 1581v223h770v-223h-770z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="1146" 
d="M586 -25q-228 0 -362 127t-134 349v145q0 223 129 358.5t363 135.5q152 0 261.5 -64t161.5 -168t52 -235v-187h-697v-26q0 -102 56 -168t170 -66q85 0 141 38t78 97h244q-28 -147 -157.5 -241.5t-305.5 -94.5zM360 643v-10h441v26q0 105 -55.5 169.5t-163.5 64.5
q-114 0 -168 -67.5t-54 -182.5zM238 1227v219h696v-219h-696z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="1265" 
d="M158 0v1434h1003v-238h-717v-350h656v-240h-656v-368h719v-238h-1005zM688 1542q-160 0 -264.5 77t-104.5 212h218q0 -56 40.5 -90.5t110.5 -34.5t111 34.5t41 90.5h217q0 -135 -104.5 -212t-264.5 -77z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="1146" 
d="M586 -25q-228 0 -362 127t-134 349v145q0 223 129 358.5t363 135.5q152 0 261.5 -64t161.5 -168t52 -235v-187h-697v-26q0 -102 56 -168t170 -66q85 0 141 38t78 97h244q-28 -147 -157.5 -241.5t-305.5 -94.5zM360 643v-10h441v26q0 105 -55.5 169.5t-163.5 64.5
q-114 0 -168 -67.5t-54 -182.5zM598 1186q-163 0 -262.5 83.5t-99.5 231.5h204q0 -68 42 -108.5t116 -40.5t116 40.5t42 108.5h205q0 -148 -100 -231.5t-263 -83.5z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="1265" 
d="M158 0v1434h1003v-238h-717v-350h656v-240h-656v-368h719v-238h-1005zM512 1704q0 69 46 115.5t116 46.5t116 -46.5t46 -115.5t-46 -115.5t-116 -46.5t-116 46.5t-46 115.5z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="1146" 
d="M586 -25q-228 0 -362 127t-134 349v145q0 223 129 358.5t363 135.5q152 0 261.5 -64t161.5 -168t52 -235v-187h-697v-26q0 -102 56 -168t170 -66q85 0 141 38t78 97h244q-28 -147 -157.5 -241.5t-305.5 -94.5zM360 643v-10h441v26q0 105 -55.5 169.5t-163.5 64.5
q-114 0 -168 -67.5t-54 -182.5zM414 1348q0 69 45.5 115t115.5 46t116 -46t46 -115t-46 -115.5t-116 -46.5t-115.5 46.5t-45.5 115.5z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="1265" 
d="M158 0v1434h1003v-238h-717v-350h656v-240h-656v-368h719v-238q-125 -78 -125 -162q0 -57 70 -57q30 0 51 6v-147q-82 -21 -160 -21q-100 0 -155.5 47.5t-55.5 132.5q0 123 107 201h-737z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="1146" 
d="M586 -25q-228 0 -362 127t-134 349v145q0 223 129 358.5t363 135.5q152 0 261.5 -64t161.5 -168t52 -235v-187h-697v-26q0 -102 56 -168t170 -66q85 0 141 38t78 97h244q-19 -102 -125 -215q-89 -92 -125.5 -150t-36.5 -108q0 -57 69 -57q31 0 52 6v-147
q-82 -21 -160 -21q-100 0 -155.5 45t-55.5 129q0 55 35.5 103.5t85.5 80.5q-12 -2 -47 -2zM360 643v-10h441v26q0 105 -55.5 169.5t-163.5 64.5q-114 0 -168 -67.5t-54 -182.5z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="1265" 
d="M158 0v1434h1003v-238h-717v-350h656v-240h-656v-368h719v-238h-1005zM1069 1831l-242 -289h-274l-242 289h269l110 -137l111 137h268z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="1146" 
d="M586 -25q-228 0 -362 127t-134 349v145q0 223 129 358.5t363 135.5q152 0 261.5 -64t161.5 -168t52 -235v-187h-697v-26q0 -102 56 -168t170 -66q85 0 141 38t78 97h244q-28 -147 -157.5 -241.5t-305.5 -94.5zM360 643v-10h441v26q0 105 -55.5 169.5t-163.5 64.5
q-114 0 -168 -67.5t-54 -182.5zM956 1501l-229 -315h-270l-230 315h254l111 -158l110 158h254z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="1464" 
d="M1335 946h-272q-5 119 -89 200t-231 81q-171 0 -257.5 -104t-86.5 -289v-226q0 -190 84 -298.5t262 -108.5q152 0 238 81.5t86 204.5v21h-321v233h602v-186q0 -260 -167 -422t-442 -162q-155 0 -276.5 50t-197 138.5t-114 203.5t-38.5 251v209q0 292 165 465.5t455 173.5
q272 0 433 -144t167 -372zM883 1831l241 -289h-268l-111 137l-110 -137h-268l241 289h275z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="1249" 
d="M602 -381q-192 0 -330 85t-170 233h271q53 -107 229 -107q106 0 169 54t63 143v168q-36 -59 -122 -102.5t-184 -43.5q-104 0 -189 36.5t-138.5 98t-82 140.5t-28.5 168v155q0 90 28.5 169.5t82 141t138.5 97t191 35.5q114 0 199.5 -46.5t116.5 -107.5v129h266v-1042
q0 -172 -144.5 -288t-365.5 -116zM594 272q102 0 171 62.5t69 171.5v123q0 106 -65.5 171.5t-166.5 65.5q-115 0 -175 -63t-60 -174v-123q0 -105 58.5 -169.5t168.5 -64.5zM778 1501l230 -315h-254l-111 157l-111 -157h-253l229 315h270z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="1464" 
d="M1335 946h-272q-5 119 -89 200t-231 81q-171 0 -257.5 -104t-86.5 -289v-226q0 -190 84 -298.5t262 -108.5q152 0 238 81.5t86 204.5v21h-321v233h602v-186q0 -260 -167 -422t-442 -162q-155 0 -276.5 50t-197 138.5t-114 203.5t-38.5 251v209q0 292 165 465.5t455 173.5
q272 0 433 -144t167 -372zM748 1542q-160 0 -264.5 77t-104.5 212h217q0 -56 41 -90.5t111 -34.5t110.5 34.5t40.5 90.5h217q0 -135 -104 -212t-264 -77z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="1249" 
d="M602 -381q-192 0 -330 85t-170 233h271q53 -107 229 -107q106 0 169 54t63 143v168q-36 -59 -122 -102.5t-184 -43.5q-104 0 -189 36.5t-138.5 98t-82 140.5t-28.5 168v155q0 90 28.5 169.5t82 141t138.5 97t191 35.5q114 0 199.5 -46.5t116.5 -107.5v129h266v-1042
q0 -172 -144.5 -288t-365.5 -116zM594 272q102 0 171 62.5t69 171.5v123q0 106 -65.5 171.5t-166.5 65.5q-115 0 -175 -63t-60 -174v-123q0 -105 58.5 -169.5t168.5 -64.5zM623 1186q-163 0 -263 83.5t-100 231.5h205q0 -68 42 -108.5t116 -40.5t115.5 40.5t41.5 108.5h205
q0 -148 -99.5 -231.5t-262.5 -83.5z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="1464" 
d="M1335 946h-272q-5 119 -89 200t-231 81q-171 0 -257.5 -104t-86.5 -289v-226q0 -190 84 -298.5t262 -108.5q152 0 238 81.5t86 204.5v21h-321v233h602v-186q0 -260 -167 -422t-442 -162q-155 0 -276.5 50t-197 138.5t-114 203.5t-38.5 251v209q0 292 165 465.5t455 173.5
q272 0 433 -144t167 -372zM573 1704q0 69 46 115.5t116 46.5t116 -46.5t46 -115.5t-46 -115.5t-116 -46.5t-116 46.5t-46 115.5z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="1249" 
d="M602 -381q-192 0 -330 85t-170 233h271q53 -107 229 -107q106 0 169 54t63 143v168q-36 -59 -122 -102.5t-184 -43.5q-104 0 -189 36.5t-138.5 98t-82 140.5t-28.5 168v155q0 90 28.5 169.5t82 141t138.5 97t191 35.5q114 0 199.5 -46.5t116.5 -107.5v129h266v-1042
q0 -172 -144.5 -288t-365.5 -116zM594 272q102 0 171 62.5t69 171.5v123q0 106 -65.5 171.5t-166.5 65.5q-115 0 -175 -63t-60 -174v-123q0 -105 58.5 -169.5t168.5 -64.5zM455 1348q0 69 45.5 115t115.5 46t116 -46t46 -115t-46 -115.5t-116 -46.5t-115.5 46.5t-45.5 115.5
z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="1464" 
d="M1335 946h-272q-5 119 -89 200t-231 81q-171 0 -257.5 -104t-86.5 -289v-226q0 -190 84 -298.5t262 -108.5q152 0 238 81.5t86 204.5v21h-321v233h602v-186q0 -260 -167 -422t-442 -162q-155 0 -276.5 50t-197 138.5t-114 203.5t-38.5 251v209q0 292 165 465.5t455 173.5
q272 0 433 -144t167 -372zM530 -512l64 362h315l-143 -362h-236z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="1249" 
d="M602 -381q-192 0 -330 85t-170 233h271q53 -107 229 -107q106 0 169 54t63 143v168q-36 -59 -122 -102.5t-184 -43.5q-104 0 -189 36.5t-138.5 98t-82 140.5t-28.5 168v155q0 90 28.5 169.5t82 141t138.5 97t191 35.5q114 0 199.5 -46.5t116.5 -107.5v129h266v-1042
q0 -172 -144.5 -288t-365.5 -116zM594 272q102 0 171 62.5t69 171.5v123q0 106 -65.5 171.5t-166.5 65.5q-115 0 -175 -63t-60 -174v-123q0 -105 58.5 -169.5t168.5 -64.5zM459 1186l151 315h275l-82 -315h-344z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="1476" 
d="M158 0v1434h286v-588h588v588h287v-1434h-287v592h-588v-592h-286zM874 1831l242 -289h-268l-111 137l-110 -137h-269l242 289h274z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="1220" 
d="M137 0v1495h279v-557q42 70 121 111t180 41q174 0 275.5 -109.5t101.5 -298.5v-682h-279v637q0 97 -48.5 154t-145.5 57q-96 0 -150.5 -56t-54.5 -161v-631h-279zM420 1892l242 -288h-269l-110 137l-111 -137h-268l241 288h275z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="1611" 
d="M70 1036v174h155v224h287v-224h588v224h286v-224h156v-174h-156v-1036h-286v592h-588v-592h-287v1036h-155zM512 846h588v190h-588v-190z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="1234" 
d="M31 1159v191h121v145h278v-145h205v-191h-205v-221q42 70 121 111t180 41q174 0 275.5 -109.5t101.5 -298.5v-682h-279v637q0 97 -48.5 154t-145.5 57q-96 0 -150.5 -56t-54.5 -161v-631h-278v1159h-121z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="622" 
d="M168 0v1434h287v-1434h-287zM57 1534l-184 41q27 139 78.5 202.5t167.5 63.5q74 0 214.5 -43t162.5 -43q9 0 16.5 1.5t13.5 3.5t12 7.5t10 8t8.5 10.5t6.5 11.5t6 13.5l5 12.5t5 14t5 13.5l182 -51q-16 -56 -28.5 -89t-32 -69t-42.5 -55t-58 -31t-81 -12q-49 0 -190 43
t-189 43q-37 0 -56.5 -24.5t-31.5 -71.5z" />
    <glyph glyph-name="itilde" unicode="&#x129;" 
d="M143 0v1065h279v-1065h-279zM47 1182l-174 43q19 128 71 196t162 68q58 0 179 -44t147 -44q25 0 42.5 15t23.5 28t17 45q2 5 3 8l172 -53q-14 -49 -24.5 -78t-29.5 -67t-41 -58t-56.5 -34.5t-77.5 -14.5q-49 0 -168 44t-162 44q-25 0 -42 -14.5t-24 -31t-18 -52.5z" />
    <glyph glyph-name="uni012A" unicode="&#x12a;" horiz-adv-x="622" 
d="M168 0v1434h287v-1434h-287zM-74 1581v223h770v-223h-770z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" 
d="M143 0v1065h279v-1065h-279zM-66 1227v219h697v-219h-697z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="622" 
d="M168 0v1434h287v-1434h-287zM311 1542q-160 0 -264 77t-104 212h217q0 -56 40.5 -90.5t110.5 -34.5t111 34.5t41 90.5h217q0 -135 -104.5 -212t-264.5 -77z" />
    <glyph glyph-name="uni012D" unicode="&#x12d;" 
d="M143 0v1065h279v-1065h-279zM283 1186q-163 0 -263 83.5t-100 231.5h205q0 -68 42 -108.5t116 -40.5t115.5 40.5t41.5 108.5h205q0 -148 -99.5 -231.5t-262.5 -83.5z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="622" 
d="M322 -381q-103 0 -162.5 54t-59.5 149q0 57 17.5 96.5t50.5 81.5v1434h287v-1434q-28 -24 -46 -41.5t-38.5 -51.5t-20.5 -65q0 -61 66 -61q13 0 33 4v-152q-54 -14 -127 -14z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" 
d="M291 -381q-103 0 -162 49.5t-59 138.5q0 104 73 193v1065h279v-1065q-32 -29 -47.5 -44t-35.5 -49t-20 -65q0 -61 64 -61q15 0 35 4v-152q-63 -14 -127 -14zM121 1343q0 69 46 115.5t116 46.5t115.5 -46.5t45.5 -115.5t-45.5 -115t-115.5 -46t-116 46t-46 115z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="622" 
d="M168 0v1434h287v-1434h-287zM150 1704q0 69 45.5 115.5t115.5 46.5t116 -46.5t46 -115.5t-46 -115.5t-116 -46.5t-115.5 46.5t-45.5 115.5z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" 
d="M143 0v1065h279v-1065h-279z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="1867" 
d="M168 0v1434h287v-1434h-287zM1202 -29q-234 0 -370 121.5t-136 327.5v20h273v-18q0 -99 61 -156t172 -57t169.5 57.5t58.5 155.5v1012h286v-1012q0 -200 -143 -325.5t-371 -125.5z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="1130" 
d="M143 0v1065h279v-1065h-279zM121 1343q0 69 46 115.5t116 46.5t115.5 -46.5t45.5 -115.5t-45.5 -115t-115.5 -46t-116 46t-46 115zM563 -356v215h64q43 0 62.5 15.5t19.5 53.5v1137h278v-1161q0 -127 -68.5 -193.5t-207.5 -66.5h-148zM686 1343q0 69 46 115.5t116 46.5
t116 -46.5t46 -115.5t-46 -115t-116 -46t-116 46t-46 115z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="1245" 
d="M580 -29q-234 0 -370 121.5t-136 327.5v20h272v-18q0 -99 61.5 -156t172.5 -57t169 57.5t58 155.5v1012h287v-1012q0 -200 -143 -325.5t-371 -125.5zM1083 1831l242 -289h-268l-111 137l-110 -137h-269l242 289h274z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" 
d="M-2 -356v215h63q43 0 62.5 15.5t19.5 53.5v1137h279v-1161q0 -126 -69 -193t-208 -67h-147zM418 1501l229 -315h-254l-110 157l-111 -157h-254l229 315h271z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="1333" 
d="M158 0v1434h286v-689l506 689h334l-539 -711l555 -723h-337l-519 702v-702h-286zM473 -512l64 362h315l-143 -362h-236z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="1114" 
d="M137 0v1495h279v-926l338 496h317l-366 -520l378 -545h-317l-350 520v-520h-279zM373 -512l63 362h316l-144 -362h-235z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="1114" 
d="M137 0v1065h279v-496l338 496h317l-366 -520l378 -545h-317l-350 520v-520h-279z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="1187" 
d="M158 0v1434h286v-1196h660v-238h-946zM174 1542l141 289h342l-200 -289h-283z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" 
d="M143 0v1495h279v-1495h-279zM152 1604l141 288h342l-201 -288h-282z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="1187" 
d="M158 0v1434h286v-1196h660v-238h-946zM424 -512l63 362h316l-144 -362h-235z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" 
d="M143 0v1495h279v-1495h-279zM55 -512l64 362h315l-143 -362h-236z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="1187" 
d="M668 977l82 457h303l-160 -457h-225zM158 0v1434h286v-1196h660v-238h-946z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="931" 
d="M578 1038l81 457h304l-160 -457h-225zM147 0v1495h279v-1495h-279z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="1187" 
d="M158 0v1434h286v-1196h660v-238h-946zM629 709q0 71 43.5 115t115.5 44t116 -44t44 -115t-44 -115.5t-116 -44.5t-115.5 44.5t-43.5 115.5z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="929" 
d="M147 0v1495h279v-1495h-279zM559 600q0 71 44 115.5t116 44.5t116 -44.5t44 -115.5t-44 -115.5t-116 -44.5t-116 44.5t-44 115.5z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="1216" 
d="M41 406v253l145 86v689h287v-514l277 165v-254l-277 -165v-428h660v-238h-947v492z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="600" 
d="M49 403v273l101 82v737h278v-510l123 100v-272l-123 -100v-713h-278v485z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="1492" 
d="M158 0v1434h272l625 -955v955h280v-1434h-272l-625 954v-954h-280zM588 1542l141 289h342l-201 -289h-282z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="1220" 
d="M137 0v1065h266v-127q82 152 314 152q174 0 275.5 -108.5t101.5 -299.5v-682h-279v637q0 97 -48.5 154t-145.5 57q-96 0 -150.5 -56t-54.5 -161v-631h-279zM477 1186l150 315h344l-217 -315h-277z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="1492" 
d="M158 0v1434h272l625 -955v955h280v-1434h-272l-625 954v-954h-280zM541 -512l63 362h316l-144 -362h-235z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" horiz-adv-x="1220" 
d="M137 0v1065h266v-127q82 152 314 152q174 0 275.5 -108.5t101.5 -299.5v-682h-279v637q0 97 -48.5 154t-145.5 57q-96 0 -150.5 -56t-54.5 -161v-631h-279zM406 -512l63 362h315l-143 -362h-235z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="1492" 
d="M158 0v1434h272l625 -955v955h280v-1434h-272l-625 954v-954h-280zM1126 1831l-241 -289h-275l-241 289h268l111 -137l110 137h268z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="1220" 
d="M137 0v1065h266v-127q82 152 314 152q174 0 275.5 -108.5t101.5 -299.5v-682h-279v637q0 97 -48.5 154t-145.5 57q-96 0 -150.5 -56t-54.5 -161v-631h-279zM979 1501l-229 -315h-271l-229 315h254l110 -158l111 158h254z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" horiz-adv-x="1499" 
d="M0 1038l82 457h303l-160 -457h-225zM416 0v1065h266v-127q82 152 313 152q174 0 275.5 -108.5t101.5 -299.5v-682h-278v637q0 97 -49 154t-146 57q-96 0 -150.5 -56t-54.5 -161v-631h-278z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="1492" 
d="M158 0v1434h272l625 -955v955h280v-1440q0 -179 -89 -264.5t-298 -85.5h-117v221h87q73 0 105 29t32 94v24l-617 942v-954h-280z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="1220" 
d="M137 0v1065h266v-127q82 152 314 152q174 0 275.5 -108.5t101.5 -299.5v-707q0 -165 -89.5 -248t-289.5 -83h-107v215h76q64 0 97.5 32.5t33.5 94.5v651q0 97 -48.5 154t-145.5 57q-96 0 -150.5 -56t-54.5 -161v-631h-279z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="1507" 
d="M1393 821v-209q0 -138 -41 -254.5t-119.5 -203t-201 -135t-277.5 -48.5q-154 0 -276.5 48.5t-201 135t-120 203t-41.5 254.5v209q0 138 41.5 254.5t120 203t201 135t276.5 48.5q155 0 277.5 -48.5t201 -135t119.5 -203t41 -254.5zM754 209q174 0 263 105.5t89 283.5v238
q0 178 -89 283.5t-263 105.5t-263.5 -106t-89.5 -283v-238q0 -177 89.5 -283t263.5 -106zM369 1581v223h770v-223h-770z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="1183" 
d="M244 1227v219h696v-219h-696zM592 -25q-233 0 -367.5 136t-134.5 346v151q0 210 134.5 346t367.5 136t367.5 -136t134.5 -346v-151q0 -210 -134.5 -346t-367.5 -136zM823 438v189q0 109 -58.5 176t-172.5 67t-173 -67t-59 -176v-189q0 -109 59 -176t173 -67t172.5 67
t58.5 176z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="1507" 
d="M1393 821v-209q0 -138 -41 -254.5t-119.5 -203t-201 -135t-277.5 -48.5q-154 0 -276.5 48.5t-201 135t-120 203t-41.5 254.5v209q0 138 41.5 254.5t120 203t201 135t276.5 48.5q155 0 277.5 -48.5t201 -135t119.5 -203t41 -254.5zM754 209q174 0 263 105.5t89 283.5v238
q0 178 -89 283.5t-263 105.5t-263.5 -106t-89.5 -283v-238q0 -177 89.5 -283t263.5 -106zM756 1542q-160 0 -264.5 77t-104.5 212h217q0 -56 41 -90.5t111 -34.5t110.5 34.5t40.5 90.5h217q0 -135 -104 -212t-264 -77z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="1183" 
d="M592 -25q-233 0 -367.5 136t-134.5 346v151q0 210 134.5 346t367.5 136t367.5 -136t134.5 -346v-151q0 -210 -134.5 -346t-367.5 -136zM823 438v189q0 109 -58.5 176t-172.5 67t-173 -67t-59 -176v-189q0 -109 59 -176t173 -67t172.5 67t58.5 176zM592 1186
q-163 0 -263 83.5t-100 231.5h205q0 -68 42 -108.5t116 -40.5t116 40.5t42 108.5h204q0 -148 -99.5 -231.5t-262.5 -83.5z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="1507" 
d="M442 1542l88 289h295l-131 -289h-252zM870 1542l88 289h295l-131 -289h-252zM1393 821v-209q0 -138 -41 -254.5t-119.5 -203t-201 -135t-277.5 -48.5q-154 0 -276.5 48.5t-201 135t-120 203t-41.5 254.5v209q0 138 41.5 254.5t120 203t201 135t276.5 48.5
q155 0 277.5 -48.5t201 -135t119.5 -203t41 -254.5zM754 209q174 0 263 105.5t89 283.5v238q0 178 -89 283.5t-263 105.5t-263.5 -106t-89.5 -283v-238q0 -177 89.5 -283t263.5 -106z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="1183" 
d="M279 1186l102 315h301l-150 -315h-253zM690 1186l103 315h301l-150 -315h-254zM592 -25q-233 0 -367.5 136t-134.5 346v151q0 210 134.5 346t367.5 136t367.5 -136t134.5 -346v-151q0 -210 -134.5 -346t-367.5 -136zM823 438v189q0 109 -58.5 176t-172.5 67t-173 -67
t-59 -176v-189q0 -109 59 -176t173 -67t172.5 67t58.5 176z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="2179" 
d="M780 0q-324 0 -494.5 174t-170.5 475v135q0 301 170.5 475.5t494.5 174.5h1295v-238h-717v-350h655v-240h-655v-368h719v-238h-1297zM399 637q0 -187 95.5 -293t287.5 -106h289v958h-289q-192 0 -287.5 -106t-95.5 -293v-160z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="1896" 
d="M592 -25q-159 0 -274 64.5t-171.5 172t-56.5 245.5v151q0 210 134.5 346t367.5 136q113 0 199 -35t163 -105q130 140 361 140q152 0 261.5 -64t161.5 -168t52 -235v-187h-696v-26q0 -102 55.5 -168t169.5 -66q85 0 141 38t78 97h244q-28 -147 -157.5 -241.5t-305.5 -94.5
q-115 0 -212.5 36.5t-154.5 101.5q-54 -59 -151.5 -98.5t-208.5 -39.5zM823 438v189q0 109 -58.5 176t-172.5 67t-173 -67t-59 -176v-189q0 -109 59 -176t173 -67t172.5 67t58.5 176zM1094 643v-10h440v26q0 105 -55.5 169.5t-163.5 64.5q-114 0 -167.5 -67.5t-53.5 -182.5z
" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="1429" 
d="M1008 0l-263 459h-301v-459h-286v1434h647q246 0 382 -135.5t136 -352.5q0 -152 -75 -268.5t-208 -173.5l299 -504h-331zM444 690h326q128 0 197 71t69 187q0 118 -69 185t-197 67h-326v-510zM510 1542l141 289h342l-200 -289h-283z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="806" 
d="M137 0v1065h264v-207q47 215 320 215h33v-276h-58q-139 0 -209.5 -73.5t-70.5 -231.5v-492h-279zM289 1186l149 315h344l-217 -315h-276z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="1429" 
d="M1008 0l-263 459h-301v-459h-286v1434h647q246 0 382 -135.5t136 -352.5q0 -152 -75 -268.5t-208 -173.5l299 -504h-331zM444 690h326q128 0 197 71t69 187q0 118 -69 185t-197 67h-326v-510zM500 -512l63 362h316l-144 -362h-235z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="806" 
d="M137 0v1065h264v-207q47 215 320 215h33v-276h-58q-139 0 -209.5 -73.5t-70.5 -231.5v-492h-279zM47 -512l64 362h315l-143 -362h-236z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="1429" 
d="M1008 0l-263 459h-301v-459h-286v1434h647q246 0 382 -135.5t136 -352.5q0 -152 -75 -268.5t-208 -173.5l299 -504h-331zM444 690h326q128 0 197 71t69 187q0 118 -69 185t-197 67h-326v-510zM1063 1831l-242 -289h-274l-242 289h268l111 -137l111 137h268z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="806" 
d="M137 0v1065h264v-207q47 215 320 215h33v-276h-58q-139 0 -209.5 -73.5t-70.5 -231.5v-492h-279zM834 1501l-230 -315h-270l-230 315h254l111 -158l111 158h254z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="1339" 
d="M674 -29q-256 0 -408.5 118.5t-152.5 326.5v37h274v-31q0 -103 78 -166t219 -63q129 0 205 55t76 153q0 127 -136 162l-374 96q-167 42 -243.5 127.5t-76.5 243.5q0 201 142 316.5t385 115.5q236 0 377 -118.5t141 -309.5v-35h-265v29q0 96 -63.5 154.5t-189.5 58.5
q-117 0 -183.5 -47t-66.5 -139q0 -121 123 -152l393 -104q313 -82 313 -375q0 -133 -74 -237t-203 -160t-290 -56zM516 1542l141 289h342l-200 -289h-283z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="1087" 
d="M551 -25q-209 0 -334 87.5t-125 252.5v19h246v-15q0 -69 58 -108t157 -39q90 0 146.5 34.5t56.5 94.5q0 76 -88 96l-316 72q-123 28 -183 95t-60 186q0 161 118 250.5t312 89.5q193 0 307.5 -91.5t114.5 -248.5v-17h-244v19q0 65 -42.5 102t-131.5 37q-85 0 -129.5 -30
t-44.5 -87q0 -39 20 -60t62 -30l323 -80q120 -30 179 -95.5t59 -176.5q0 -155 -131 -256t-330 -101zM401 1186l150 315h344l-217 -315h-277z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="1339" 
d="M674 -29q-256 0 -408.5 118.5t-152.5 326.5v37h274v-31q0 -103 78 -166t219 -63q129 0 205 55t76 153q0 127 -136 162l-374 96q-167 42 -243.5 127.5t-76.5 243.5q0 201 142 316.5t385 115.5q236 0 377 -118.5t141 -309.5v-35h-265v29q0 96 -63.5 154.5t-189.5 58.5
q-117 0 -183.5 -47t-66.5 -139q0 -121 123 -152l393 -104q313 -82 313 -375q0 -133 -74 -237t-203 -160t-290 -56zM819 1831l242 -289h-268l-111 137l-111 -137h-268l242 289h274z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="1087" 
d="M551 -25q-209 0 -334 87.5t-125 252.5v19h246v-15q0 -69 58 -108t157 -39q90 0 146.5 34.5t56.5 94.5q0 76 -88 96l-316 72q-123 28 -183 95t-60 186q0 161 118 250.5t312 89.5q193 0 307.5 -91.5t114.5 -248.5v-17h-244v19q0 65 -42.5 102t-131.5 37q-85 0 -129.5 -30
t-44.5 -87q0 -39 20 -60t62 -30l323 -80q120 -30 179 -95.5t59 -176.5q0 -155 -131 -256t-330 -101zM692 1501l230 -315h-254l-111 157l-111 -157h-253l229 315h270z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="1339" 
d="M680 -381q-90 0 -145 12v113q48 -10 96 -10q82 0 82 45q0 41 -76 47l-74 6l33 143q-224 17 -353.5 133.5t-129.5 307.5v37h274v-31q0 -103 78 -166t219 -63q129 0 205 55t76 153q0 127 -136 162l-374 96q-167 42 -243.5 127.5t-76.5 243.5q0 201 142 316.5t385 115.5
q236 0 377 -118.5t141 -309.5v-35h-265v29q0 96 -63.5 154.5t-189.5 58.5q-117 0 -183.5 -47t-66.5 -139q0 -121 123 -152l393 -104q313 -82 313 -375q0 -189 -143.5 -312.5t-368.5 -138.5l-8 -47l33 -4q157 -20 157 -141q0 -73 -62 -117.5t-169 -44.5z" />
    <glyph glyph-name="Scedilla" unicode="&#xf6c1;" horiz-adv-x="1339" 
d="M680 -381q-90 0 -145 12v113q48 -10 96 -10q82 0 82 45q0 41 -76 47l-74 6l33 143q-224 17 -353.5 133.5t-129.5 307.5v37h274v-31q0 -103 78 -166t219 -63q129 0 205 55t76 153q0 127 -136 162l-374 96q-167 42 -243.5 127.5t-76.5 243.5q0 201 142 316.5t385 115.5
q236 0 377 -118.5t141 -309.5v-35h-265v29q0 96 -63.5 154.5t-189.5 58.5q-117 0 -183.5 -47t-66.5 -139q0 -121 123 -152l393 -104q313 -82 313 -375q0 -189 -143.5 -312.5t-368.5 -138.5l-8 -47l33 -4q157 -20 157 -141q0 -73 -62 -117.5t-169 -44.5z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="1087" 
d="M561 -381q-89 0 -145 12v113q48 -10 96 -10q82 0 82 45q0 41 -76 47l-74 6l33 148q-178 14 -281.5 99t-103.5 236v19h246v-15q0 -69 58 -108t157 -39q90 0 146.5 34.5t56.5 94.5q0 76 -88 96l-316 72q-123 28 -183 95t-60 186q0 161 118 250.5t312 89.5
q193 0 307.5 -91.5t114.5 -248.5v-17h-244v19q0 65 -42.5 102t-131.5 37q-85 0 -129.5 -30t-44.5 -87q0 -39 20 -60t62 -30l323 -80q120 -30 179 -95.5t59 -176.5q0 -141 -112.5 -239.5t-287.5 -115.5l-10 -51l33 -4q158 -20 158 -141q0 -73 -62.5 -117.5t-169.5 -44.5z" />
    <glyph glyph-name="scedilla" unicode="&#xf6c2;" horiz-adv-x="1087" 
d="M561 -381q-89 0 -145 12v113q48 -10 96 -10q82 0 82 45q0 41 -76 47l-74 6l33 148q-178 14 -281.5 99t-103.5 236v19h246v-15q0 -69 58 -108t157 -39q90 0 146.5 34.5t56.5 94.5q0 76 -88 96l-316 72q-123 28 -183 95t-60 186q0 161 118 250.5t312 89.5
q193 0 307.5 -91.5t114.5 -248.5v-17h-244v19q0 65 -42.5 102t-131.5 37q-85 0 -129.5 -30t-44.5 -87q0 -39 20 -60t62 -30l323 -80q120 -30 179 -95.5t59 -176.5q0 -141 -112.5 -239.5t-287.5 -115.5l-10 -51l33 -4q158 -20 158 -141q0 -73 -62.5 -117.5t-169.5 -44.5z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="1339" 
d="M674 -29q-256 0 -408.5 118.5t-152.5 326.5v37h274v-31q0 -103 78 -166t219 -63q129 0 205 55t76 153q0 127 -136 162l-374 96q-167 42 -243.5 127.5t-76.5 243.5q0 201 142 316.5t385 115.5q236 0 377 -118.5t141 -309.5v-35h-265v29q0 96 -63.5 154.5t-189.5 58.5
q-117 0 -183.5 -47t-66.5 -139q0 -121 123 -152l393 -104q313 -82 313 -375q0 -133 -74 -237t-203 -160t-290 -56zM1055 1831l-242 -289h-274l-242 289h268l111 -137l110 137h269z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="1087" 
d="M551 -25q-209 0 -334 87.5t-125 252.5v19h246v-15q0 -69 58 -108t157 -39q90 0 146.5 34.5t56.5 94.5q0 76 -88 96l-316 72q-123 28 -183 95t-60 186q0 161 118 250.5t312 89.5q193 0 307.5 -91.5t114.5 -248.5v-17h-244v19q0 65 -42.5 102t-131.5 37q-85 0 -129.5 -30
t-44.5 -87q0 -39 20 -60t62 -30l323 -80q120 -30 179 -95.5t59 -176.5q0 -155 -131 -256t-330 -101zM907 1501l-229 -315h-270l-230 315h254l111 -158l110 158h254z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x162;" horiz-adv-x="1204" 
d="M459 0v1196h-406v238h1098v-238h-406v-1196h-286zM377 -512l63 362h316l-144 -362h-235z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x21a;" horiz-adv-x="1204" 
d="M459 0v1196h-406v238h1098v-238h-406v-1196h-286zM377 -512l63 362h316l-144 -362h-235z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x163;" horiz-adv-x="786" 
d="M508 0q-150 0 -225.5 79.5t-75.5 223.5v537h-152v225h152v291h280v-291h228v-225h-228v-516q0 -53 23 -76t78 -23h112v-225h-192zM250 -512l63 362h316l-144 -362h-235z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x21b;" horiz-adv-x="786" 
d="M508 0q-150 0 -225.5 79.5t-75.5 223.5v537h-152v225h152v291h280v-291h228v-225h-228v-516q0 -53 23 -76t78 -23h112v-225h-192zM250 -512l63 362h316l-144 -362h-235z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="1204" 
d="M459 0v1196h-406v238h1098v-238h-406v-1196h-286zM981 1831l-242 -289h-274l-242 289h269l110 -137l111 137h268z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="888" 
d="M586 1182l37 313h297l-115 -313h-219zM508 0q-150 0 -225.5 79.5t-75.5 223.5v537h-152v225h152v291h280v-291h228v-225h-228v-516q0 -53 23 -76t78 -23h112v-225h-192z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="1204" 
d="M197 555v229h262v412h-406v238h1098v-238h-406v-412h263v-229h-263v-555h-286v555h-262z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="786" 
d="M57 438v211h150v191h-152v225h152v291h280v-291h228v-225h-228v-191h195v-211h-195v-114q0 -53 23 -76t78 -23h112v-225h-192q-150 0 -225.5 79.5t-75.5 223.5v135h-150z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="1474" 
d="M737 -29q-275 0 -432.5 149.5t-157.5 395.5v918h285v-930q0 -138 82.5 -216.5t222.5 -78.5q139 0 221 78.5t82 216.5v930h287v-918q0 -246 -157.5 -395.5t-432.5 -149.5zM481 1534l-184 41q27 139 78.5 202.5t167.5 63.5q74 0 214.5 -43t162.5 -43q9 0 16.5 1.5t13.5 3.5
t12 7.5t10 8t8.5 10.5t6.5 11.5t6 13.5l5 12.5t5 14t5 13.5l182 -51q-16 -56 -28.5 -89t-32 -69t-42.5 -55t-58 -31t-81 -12q-49 0 -190 43t-189 43q-37 0 -56.5 -24.5t-31.5 -71.5z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="1212" 
d="M1075 1065v-1065h-266v127q-37 -68 -122 -110t-187 -42q-177 0 -275 111.5t-98 296.5v682h279v-643q0 -102 47.5 -157.5t140.5 -55.5q96 0 149.5 57.5t53.5 157.5v641h278zM391 1182l-174 43q19 127 71.5 195.5t162.5 68.5q58 0 178.5 -44t146.5 -44q25 0 42.5 15
t23.5 28t17 45q2 5 3 8l172 -53q-14 -49 -24.5 -78t-29.5 -67t-41 -58t-56.5 -34.5t-77.5 -14.5q-49 0 -168 44t-162 44q-25 0 -42 -14.5t-24 -31t-18 -52.5z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="1474" 
d="M737 -29q-275 0 -432.5 149.5t-157.5 395.5v918h285v-930q0 -138 82.5 -216.5t222.5 -78.5q139 0 221 78.5t82 216.5v930h287v-918q0 -246 -157.5 -395.5t-432.5 -149.5zM352 1581v223h770v-223h-770z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="1212" 
d="M1075 1065v-1065h-266v127q-37 -68 -122 -110t-187 -42q-177 0 -275 111.5t-98 296.5v682h279v-643q0 -102 47.5 -157.5t140.5 -55.5q96 0 149.5 57.5t53.5 157.5v641h278zM274 1227v219h697v-219h-697z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="1474" 
d="M737 -29q-275 0 -432.5 149.5t-157.5 395.5v918h285v-930q0 -138 82.5 -216.5t222.5 -78.5q139 0 221 78.5t82 216.5v930h287v-918q0 -246 -157.5 -395.5t-432.5 -149.5zM737 1542q-160 0 -264 77t-104 212h217q0 -56 40.5 -90.5t110.5 -34.5t111 34.5t41 90.5h217
q0 -135 -104.5 -212t-264.5 -77z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="1212" 
d="M1075 1065v-1065h-266v127q-37 -68 -122 -110t-187 -42q-177 0 -275 111.5t-98 296.5v682h279v-643q0 -102 47.5 -157.5t140.5 -55.5q96 0 149.5 57.5t53.5 157.5v641h278zM639 1186q-163 0 -263 83.5t-100 231.5h205q0 -68 42 -108.5t116 -40.5t116 40.5t42 108.5h204
q0 -148 -99.5 -231.5t-262.5 -83.5z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="1474" 
d="M737 -29q-275 0 -432.5 149.5t-157.5 395.5v918h285v-930q0 -138 82.5 -216.5t222.5 -78.5q139 0 221 78.5t82 216.5v930h287v-918q0 -246 -157.5 -395.5t-432.5 -149.5zM737 1645q42 0 68 24.5t26 63.5q0 40 -25.5 63t-68.5 23t-68.5 -23t-25.5 -63q0 -39 26 -63.5
t68 -24.5zM737 1542q-100 0 -158.5 55.5t-58.5 135.5q0 79 60 133.5t157 54.5q98 0 157.5 -54.5t59.5 -133.5q0 -80 -58.5 -135.5t-158.5 -55.5z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="1212" 
d="M1075 1065v-1065h-266v127q-37 -68 -122 -110t-187 -42q-177 0 -275 111.5t-98 296.5v682h279v-643q0 -102 47.5 -157.5t140.5 -55.5q96 0 149.5 57.5t53.5 157.5v641h278zM614 1288q42 0 68.5 24.5t26.5 63.5q0 40 -26 63t-69 23t-68.5 -23t-25.5 -63q0 -39 26 -63.5
t68 -24.5zM614 1186q-100 0 -158.5 55t-58.5 135q0 79 60 134t157 55q98 0 157.5 -55t59.5 -134q0 -80 -58.5 -135t-158.5 -55z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="1474" 
d="M737 -29q-275 0 -432.5 149.5t-157.5 395.5v918h285v-930q0 -138 82.5 -216.5t222.5 -78.5q139 0 221 78.5t82 216.5v930h287v-918q0 -246 -157.5 -395.5t-432.5 -149.5zM403 1542l89 289h294l-131 -289h-252zM831 1542l89 289h294l-131 -289h-252z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="1212" 
d="M1075 1065v-1065h-266v127q-37 -68 -122 -110t-187 -42q-177 0 -275 111.5t-98 296.5v682h279v-643q0 -102 47.5 -157.5t140.5 -55.5q96 0 149.5 57.5t53.5 157.5v641h278zM279 1186l102 315h301l-150 -315h-253zM690 1186l103 315h301l-150 -315h-254z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="1474" 
d="M827 -381q-100 0 -155.5 47.5t-55.5 132.5q0 104 107 172q-268 4 -422 153.5t-154 391.5v918h285v-930q0 -138 82.5 -216.5t222.5 -78.5q139 0 221 78.5t82 216.5v930h287v-918q0 -317 -272 -477q-85 -50 -137 -98.5t-52 -102.5q0 -57 70 -57q30 0 51 6v-147
q-82 -21 -160 -21z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="1212" 
d="M1075 1065v-1065q-24 -17 -41 -31t-39 -36t-33.5 -46t-11.5 -49q0 -57 70 -57q30 0 51 6v-147q-82 -21 -160 -21q-94 0 -152.5 49t-58.5 137q0 67 29.5 111.5t79.5 83.5v127q-37 -68 -122 -110t-187 -42q-177 0 -275 111.5t-98 296.5v682h279v-643q0 -102 47.5 -157.5
t140.5 -55.5q96 0 149.5 57.5t53.5 157.5v641h278z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="1978" 
d="M1128 1434l299 -1037l207 1037h283l-338 -1434h-276l-314 1104l-313 -1104h-277l-338 1434h283l207 -1037l299 1037h278zM1126 1831l242 -289h-268l-111 137l-110 -137h-269l242 289h274z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="1613" 
d="M1577 1065l-277 -1065h-260l-233 733l-234 -733h-260l-276 1065h270l156 -715l217 715h254l217 -715l156 715h270zM942 1501l229 -315h-253l-111 157l-111 -157h-254l230 315h270z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="1302" 
d="M795 516v-516h-287v516l-492 918h312l323 -639l324 639h311zM788 1831l242 -289h-268l-111 137l-110 -137h-269l242 289h274z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="1046" 
d="M559 -356h-274l106 360l-368 1061h278l229 -764l218 764h276zM678 1501l229 -315h-254l-110 157l-111 -157h-254l230 315h270z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="1302" 
d="M795 516v-516h-287v516l-492 918h312l323 -639l324 639h311zM272 1692q0 64 42.5 106.5t107.5 42.5q64 0 106.5 -42.5t42.5 -106.5q0 -65 -42.5 -107.5t-106.5 -42.5t-107 43t-43 107zM731 1692q0 64 42.5 106.5t107.5 42.5t107 -42.5t42 -106.5q0 -65 -42.5 -107.5
t-106.5 -42.5q-65 0 -107.5 43t-42.5 107z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="1277" 
d="M113 0v238l718 958h-688v238h1010v-238l-719 -958h737v-238h-1058zM498 1542l141 289h342l-201 -289h-282z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="1034" 
d="M92 0v201l526 649h-501v215h823v-201l-526 -649h538v-215h-860zM379 1186l149 315h344l-217 -315h-276z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="1277" 
d="M113 0v238l718 958h-688v238h1010v-238l-719 -958h737v-238h-1058zM498 1704q0 69 45.5 115.5t115.5 46.5t116 -46.5t46 -115.5t-46 -115.5t-116 -46.5t-115.5 46.5t-45.5 115.5z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="1034" 
d="M92 0v201l526 649h-501v215h823v-201l-526 -649h538v-215h-860zM379 1348q0 69 46 115t116 46t115.5 -46t45.5 -115t-45.5 -115.5t-115.5 -46.5t-116 46.5t-46 115.5z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="1277" 
d="M113 0v238l718 958h-688v238h1010v-238l-719 -958h737v-238h-1058zM1059 1831l-242 -289h-274l-242 289h268l111 -137l111 137h268z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="1034" 
d="M92 0v201l526 649h-501v215h823v-201l-526 -649h538v-215h-860zM918 1501l-230 -315h-270l-230 315h254l111 -158l111 158h254z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="950" 
d="M51 -356v227h103q56 0 80.5 23.5t29.5 76.5l60 697h-154v227h174l21 231q14 156 89.5 232t219.5 76h205v-228h-131q-54 0 -77 -24t-28 -80l-18 -207h190v-227h-211l-63 -727q-13 -157 -90.5 -227t-231.5 -70h-168z" />
    <glyph glyph-name="Aringacute" unicode="&#x1fa;" horiz-adv-x="1413" 
d="M922 1516q0 -70 -56 -123l500 -1393h-285l-96 301h-557l-96 -301h-285l498 1391q-53 50 -53 125q0 79 57 130.5t158 51.5t158 -51.5t57 -130.5zM707 1176l-207 -648h411zM590 1751l96 225h340l-154 -225h-282zM707 1434q44 0 70 23t26 59q0 39 -25.5 61t-70.5 22
t-71 -22.5t-26 -60.5q0 -36 26 -59t71 -23z" />
    <glyph glyph-name="aringacute" unicode="&#x1fb;" horiz-adv-x="1198" 
d="M651 1260q46 0 73.5 25t27.5 61q0 39 -27.5 63.5t-73.5 24.5t-73 -24.5t-27 -63.5q0 -37 27 -61.5t73 -24.5zM651 1165q-101 0 -158 51t-57 130t57 130.5t158 51.5t158 -51.5t57 -130.5t-57 -130t-158 -51zM537 1591l96 226h340l-154 -226h-282zM496 -25q-182 0 -293 89
t-111 243q0 168 115.5 253t314.5 85q87 0 157 -26.5t114 -63.5v145q0 75 -50 123.5t-135 48.5q-133 0 -184 -100h-258q45 154 164.5 236t292.5 82q200 0 324 -112.5t124 -297.5v-680h-268v98q-36 -53 -124.5 -88t-182.5 -35zM567 160q93 0 159.5 45t66.5 112q0 69 -61 109.5
t-157 40.5q-99 0 -156 -39.5t-57 -112.5q0 -72 57.5 -113.5t147.5 -41.5z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="2187" 
d="M1051 1542l141 289h342l-201 -289h-282zM1079 0v303h-577l-195 -303h-307l961 1434h1122v-238h-717v-350h655v-240h-655v-368h719v-238h-1006zM649 532h430v668z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="1873" 
d="M811 1186l150 315h344l-218 -315h-276zM1296 -25q-125 0 -222.5 41.5t-158.5 116.5q-62 -67 -163.5 -112.5t-231.5 -45.5q-192 0 -310 96t-118 251q0 109 58 184t152.5 109t219.5 34q88 0 158.5 -26t112.5 -66v129q0 79 -48 131.5t-135 52.5q-72 0 -115 -24t-71 -74h-258
q37 157 154.5 237.5t291.5 80.5q212 0 328 -132q55 61 147.5 96.5t204.5 35.5q152 0 261.5 -64t161.5 -168t52 -235v-187h-696v-26q0 -102 55.5 -168t169.5 -66q85 0 141.5 38t78.5 97h243q-28 -147 -157.5 -241.5t-305.5 -94.5zM567 162q94 0 160 44t66 111q0 70 -61.5 112
t-156.5 42q-101 0 -157 -39.5t-56 -112.5q0 -72 57.5 -114.5t147.5 -42.5zM1071 633h440v26q0 105 -55.5 169.5t-163.5 64.5q-110 0 -165.5 -62.5t-55.5 -166.5v-31z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="1507" 
d="M612 1542l142 289h342l-201 -289h-283zM1393 821v-209q0 -138 -41 -254.5t-119.5 -203t-201 -135t-277.5 -48.5q-197 0 -344 80l-101 -153h-194l166 252q-166 172 -166 462v209q0 138 41.5 254.5t120 203t201 135t276.5 48.5q198 0 342 -80l102 154h195l-166 -252
q166 -172 166 -463zM401 598q0 -122 39 -207l516 780q-87 54 -202 54q-174 0 -263.5 -106t-89.5 -283v-238zM754 209q174 0 263 105.5t89 283.5v238q0 130 -41 204l-514 -778q71 -53 203 -53z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" horiz-adv-x="1183" 
d="M442 1186l150 315h344l-217 -315h-277zM592 -25q-147 0 -260 58l-90 -135h-152l139 206q-139 133 -139 353v151q0 210 134.5 346t367.5 136q145 0 258 -58l92 135h152l-140 -209q68 -65 104 -155.5t36 -194.5v-151q0 -210 -134.5 -346t-367.5 -136zM360 438
q0 -58 19 -110l342 510q-56 32 -129 32q-114 0 -173 -67t-59 -176v-189zM823 438v189q0 59 -18 108l-342 -510q49 -30 129 -30q114 0 172.5 67t58.5 176z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="1339" 
d="M674 -29q-256 0 -408.5 118.5t-152.5 326.5v37h274v-31q0 -103 78 -166t219 -63q129 0 205 55t76 153q0 127 -136 162l-374 96q-167 42 -243.5 127.5t-76.5 243.5q0 201 142 316.5t385 115.5q236 0 377 -118.5t141 -309.5v-35h-265v29q0 96 -63.5 154.5t-189.5 58.5
q-117 0 -183.5 -47t-66.5 -139q0 -121 123 -152l393 -104q313 -82 313 -375q0 -133 -74 -237t-203 -160t-290 -56zM461 -512l63 362h316l-144 -362h-235z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="1087" 
d="M551 -25q-209 0 -334 87.5t-125 252.5v19h246v-15q0 -69 58 -108t157 -39q90 0 146.5 34.5t56.5 94.5q0 76 -88 96l-316 72q-123 28 -183 95t-60 186q0 161 118 250.5t312 89.5q193 0 307.5 -91.5t114.5 -248.5v-17h-244v19q0 65 -42.5 102t-131.5 37q-85 0 -129.5 -30
t-44.5 -87q0 -39 20 -60t62 -30l323 -80q120 -30 179 -95.5t59 -176.5q0 -155 -131 -256t-330 -101zM342 -512l64 362h315l-143 -362h-236z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="1044" 
d="M657 1501l230 -315h-254l-111 157l-110 -157h-254l229 315h270z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="1044" 
d="M887 1501l-230 -315h-270l-229 315h254l110 -158l111 158h254z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="1040" 
d="M520 1186q-163 0 -262.5 83.5t-99.5 231.5h204q0 -68 42 -108.5t116 -40.5t116 40.5t42 108.5h205q0 -148 -100 -231.5t-263 -83.5z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="638" 
d="M158 1348q0 69 45.5 115t115.5 46t116 -46t46 -115t-46 -115.5t-116 -46.5t-115.5 46.5t-45.5 115.5z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="749" 
d="M375 1288q42 0 68 24.5t26 63.5q0 40 -25.5 63t-68.5 23t-68.5 -23t-25.5 -63q0 -39 26 -63.5t68 -24.5zM375 1186q-100 0 -158.5 55t-58.5 135q0 79 60 134t157 55q98 0 157.5 -55t59.5 -134q0 -80 -58.5 -135t-158.5 -55z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="690" 
d="M369 -381q-100 0 -155.5 47.5t-55.5 132.5q0 123 106 201h268q-124 -78 -124 -162q0 -57 69 -57q30 0 51 6v-147q-82 -21 -159 -21z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="1132" 
d="M332 1182l-174 43q19 128 71 196t162 68q58 0 179 -44t147 -44q25 0 42.5 15t23.5 28t17 45q2 5 3 8l172 -53q-14 -49 -24.5 -78.5t-30 -66.5t-41.5 -57.5t-56 -35t-78 -14.5q-49 0 -167.5 44t-161.5 44q-25 0 -42 -14.5t-24 -31t-18 -52.5z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="1130" 
d="M158 1186l102 315h301l-149 -315h-254zM569 1186l103 315h301l-150 -315h-254z" />
    <glyph glyph-name="uni0312" unicode="&#x312;" horiz-adv-x="743" 
d="M158 1186l151 315h275l-82 -315h-344z" />
    <glyph glyph-name="uni0315" unicode="&#x315;" horiz-adv-x="700" 
d="M158 1038l82 457h303l-160 -457h-225z" />
    <glyph glyph-name="uni0326" unicode="&#x326;" horiz-adv-x="696" 
d="M158 -512l63 362h316l-144 -362h-235z" />
    <glyph glyph-name="Omega" unicode="&#x3a9;" horiz-adv-x="1519" 
d="M1386 227v-227h-466v227q84 93 139 226.5t55 273.5v72q0 202 -86.5 314t-267.5 112t-267.5 -112t-86.5 -314v-72q0 -140 55 -273.5t139 -226.5v-227h-467v227h180q-190 230 -190 510v78q0 188 70.5 332.5t216.5 229.5t350 85t350 -85t216.5 -229.5t70.5 -332.5v-78
q0 -279 -191 -510h180z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="1490" 
d="M291 0v844h-205v221h1319v-221h-246v-506q0 -57 26.5 -84t88.5 -27h119v-227h-193q-160 0 -239.5 82.5t-79.5 234.5v527h-312v-844h-278z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="1978" 
d="M1128 1434l299 -1037l207 1037h283l-338 -1434h-276l-314 1104l-313 -1104h-277l-338 1434h283l207 -1037l299 1037h278zM840 1542l-201 289h342l141 -289h-282z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="1613" 
d="M1577 1065l-277 -1065h-260l-233 733l-234 -733h-260l-276 1065h270l156 -715l217 715h254l217 -715l156 715h270zM653 1186l-213 315h344l150 -315h-281z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="1978" 
d="M1128 1434l299 -1037l207 1037h283l-338 -1434h-276l-314 1104l-313 -1104h-277l-338 1434h283l207 -1037l299 1037h278zM864 1542l142 289h342l-201 -289h-283z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="1613" 
d="M1577 1065l-277 -1065h-260l-233 733l-234 -733h-260l-276 1065h270l156 -715l217 715h254l217 -715l156 715h270zM690 1186l150 315h344l-217 -315h-277z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="1978" 
d="M1128 1434l299 -1037l207 1037h283l-338 -1434h-276l-314 1104l-313 -1104h-277l-338 1434h283l207 -1037l299 1037h278zM610 1692q0 64 42.5 106.5t107.5 42.5q64 0 106.5 -42.5t42.5 -106.5q0 -65 -42.5 -107.5t-106.5 -42.5t-107 43t-43 107zM1069 1692
q0 64 42.5 106.5t107.5 42.5t107 -42.5t42 -106.5q0 -65 -42.5 -107.5t-106.5 -42.5q-65 0 -107.5 43t-42.5 107z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="1613" 
d="M1577 1065l-277 -1065h-260l-233 733l-234 -733h-260l-276 1065h270l156 -715l217 715h254l217 -715l156 715h270zM442 1341q0 65 43 107.5t107 42.5t106.5 -42.5t42.5 -107.5t-42.5 -107t-106.5 -42q-65 0 -107.5 42.5t-42.5 106.5zM872 1341q0 65 43 107.5t107 42.5
t106.5 -42.5t42.5 -107.5t-42.5 -107t-106.5 -42q-65 0 -107.5 42.5t-42.5 106.5z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="1302" 
d="M795 516v-516h-287v516l-492 918h312l323 -639l324 639h311zM516 1542l-201 289h342l142 -289h-283z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="1046" 
d="M559 -356h-274l106 360l-368 1061h278l229 -764l218 764h276zM399 1186l-213 315h344l150 -315h-281z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="1257" 
d="M113 530v250h1032v-250h-1032z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="1869" 
d="M113 530v250h1644v-250h-1644z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="485" 
d="M432 1434l-104 -543h-308l185 543h227z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="485" 
d="M53 891l105 543h307l-184 -543h-228z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="524" 
d="M31 -264l104 543h307l-184 -543h-227z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="956" 
d="M903 1434l-104 -543h-307l184 543h227zM432 1434l-104 -543h-308l185 543h227z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="956" 
d="M53 891l105 543h307l-184 -543h-228zM524 891l105 543h307l-184 -543h-228z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="995" 
d="M31 -264l104 543h307l-184 -543h-227zM502 -264l104 543h307l-184 -543h-227z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="991" 
d="M383 -205v1106h-268v191h268v342h225v-342h269v-191h-269v-1106h-225z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="1011" 
d="M393 328v573h-268v191h268v342h225v-342h269v-191h-269v-573h269v-191h-269v-342h-225v342h-268v191h268z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="847" 
d="M424 455q-127 0 -204 78t-77 196q0 119 77 197t204 78q128 0 204.5 -77.5t76.5 -197.5q0 -118 -76.5 -196t-204.5 -78z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="1572" 
d="M102 143q0 71 44 115.5t116 44.5t116 -44.5t44 -115.5t-44 -115t-116 -44t-116 44t-44 115zM627 143q0 71 43.5 115.5t115.5 44.5t116 -44.5t44 -115.5t-44 -115t-116 -44t-115.5 44t-43.5 115zM1151 143q0 71 44 115.5t116 44.5t115.5 -44.5t43.5 -115.5t-43.5 -115
t-115.5 -44t-116 44t-44 115z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="2615" 
d="M438 0l738 1434h206l-737 -1434h-207zM764 1133v-166q0 -144 -90.5 -233t-245.5 -89t-245.5 89t-90.5 233v166q0 144 90.5 232.5t245.5 88.5t245.5 -88.5t90.5 -232.5zM567 950v199q0 64 -36.5 101.5t-102.5 37.5t-102.5 -37.5t-36.5 -101.5v-199q0 -64 36.5 -101.5
t102.5 -37.5t102.5 37.5t36.5 101.5zM1729 467v-166q0 -144 -90.5 -232.5t-245.5 -88.5t-245.5 88.5t-90.5 232.5v166q0 144 90.5 232.5t245.5 88.5t245.5 -88.5t90.5 -232.5zM1532 285v198q0 64 -36.5 102t-102.5 38t-103 -38t-37 -102v-198q0 -64 37 -102t103 -38
t102.5 38t36.5 102zM2533 467v-166q0 -144 -90 -232.5t-245 -88.5t-245.5 88.5t-90.5 232.5v166q0 144 90.5 232.5t245.5 88.5t245 -88.5t90 -232.5zM2337 285v198q0 64 -36.5 102t-102.5 38t-103 -38t-37 -102v-198q0 -64 37 -102t103 -38t102.5 38t36.5 102z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="729" 
d="M354 123l-334 442l334 443h314l-332 -443l332 -442h-314z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="729" 
d="M709 565l-334 -442h-314l332 442l-332 443h314z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="821" 
d="M-61 0l737 1434h207l-738 -1434h-206z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="1374" 
d="M92 795v190h150q2 100 38 186t100.5 151t163.5 102.5t220 37.5q113 0 208 -37t159 -100.5t101 -148t40 -179.5h-252q-5 107 -71.5 174.5t-180.5 67.5q-122 0 -186 -67.5t-64 -180.5v-6h307v-190h-307v-156h307v-190h-307v-7q0 -113 64 -180t186 -67q114 0 180.5 67
t71.5 174h252q-3 -95 -40 -179.5t-101 -148t-159 -100.5t-208 -37q-121 0 -220 37.5t-163.5 102.5t-100.5 151.5t-38 186.5h-150v190h150v156h-150z" />
    <glyph glyph-name="published" unicode="&#x2117;" horiz-adv-x="1691" 
d="M846 49q149 0 274.5 55t208.5 147t129.5 213t46.5 253t-46.5 252.5t-129.5 213t-208.5 147t-274.5 54.5q-148 0 -273.5 -54.5t-209.5 -147t-130.5 -213t-46.5 -252.5t46.5 -253t130.5 -213t209.5 -147t273.5 -55zM846 -25q-163 0 -303 58.5t-236 159t-150.5 236.5
t-54.5 288t54.5 288t150.5 236t236 158.5t303 58.5q164 0 304 -58.5t235.5 -159t149.5 -236t54 -287.5t-54 -288t-149.5 -236.5t-235.5 -159t-304 -58.5zM604 352v725h307q122 0 196.5 -69t74.5 -179q0 -113 -75 -183.5t-196 -70.5h-151v-223h-156zM760 707h133
q64 0 98.5 34.5t34.5 87.5t-34.5 85t-98.5 32h-133v-239z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="1339" 
d="M242 827v496h-181v111h488v-111h-180v-496h-127zM1233 1434v-607h-123v357l-106 -217h-123l-107 217v-357h-123v607h117l174 -357l174 357h117z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" horiz-adv-x="1220" 
d="M598 -29q-231 0 -368.5 133.5t-137.5 337.5v66q0 208 120.5 332.5t319.5 124.5q98 0 179.5 -33t124.5 -86q-104 356 -570 356h-41v232h56q410 0 623.5 -224t213.5 -628v-90q0 -147 -64 -265.5t-183.5 -187t-272.5 -68.5zM602 195q109 0 175.5 66.5t66.5 170.5v82
q0 110 -66.5 174t-171.5 64q-115 0 -177 -65.5t-62 -174.5v-78q0 -107 63 -173t172 -66z" />
    <glyph glyph-name="Delta" unicode="&#x2206;" horiz-adv-x="1409" 
d="M102 0v221l476 1213h253l476 -1213v-221h-1205zM387 238h633l-315 899z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="1693" 
d="M352 -356v1562h-266v228h1522v-228h-267v-1562h-274v1562h-440v-1562h-275z" />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="1239" 
d="M127 -356v227l434 668l-434 667v228h1006v-228h-697l424 -667l-424 -668h697v-227h-1006z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="1206" 
d="M133 621v233h940v-233h-940z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="1509" 
d="M627 -205l-252 692h-262v240h436l229 -670l357 1479h262l-451 -1741h-319z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="1863" 
d="M1354 338q-137 0 -249.5 60t-172.5 149q-60 -89 -172.5 -149t-249.5 -60q-179 0 -288 107.5t-109 291.5t109 292t288 108q137 0 249.5 -60t172.5 -149q60 89 172.5 149t249.5 60q179 0 288 -108t109 -292t-109 -291.5t-288 -107.5zM549 920q-86 0 -138.5 -49t-52.5 -134
t52.5 -133.5t138.5 -48.5q162 0 266 182q-104 183 -266 183zM1315 920q-162 0 -266 -183q104 -182 266 -182q86 0 138 48.5t52 133.5t-52 134t-138 49z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="890" 
d="M86 -356v229h86q65 0 93 25.5t28 89.5v1114q0 170 84 251t250 81h157v-230h-94q-65 0 -93 -26.5t-28 -87.5v-1115q0 -331 -331 -331h-152z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="1314" 
d="M901 336q-44 0 -92 12.5t-73.5 23t-86.5 38.5q-12 5 -51.5 23.5t-53.5 24t-39.5 12.5t-47.5 7q-37 0 -74.5 -20t-56 -37t-52.5 -53l-151 137q66 95 143.5 143.5t198.5 48.5q44 0 90.5 -12.5t73 -24.5t86.5 -41q8 -4 29 -15t28.5 -14t24 -11t25 -10.5t22.5 -6.5t26.5 -5
t26.5 -1q52 0 91 20t79 62l135 -150q-124 -151 -301 -151zM901 782q-44 0 -92 12.5t-73.5 23t-86.5 38.5q-15 7 -52 24.5t-52.5 23.5t-40.5 13t-47 7q-37 0 -75 -20.5t-55.5 -37t-52.5 -53.5l-151 137q44 63 86.5 102.5t107.5 65t148 25.5q44 0 90.5 -12.5t73 -24.5
t86.5 -41q9 -5 33.5 -17.5t32.5 -16t27 -11.5t30 -11t27 -5.5t32 -2.5q52 0 91 20t79 62l135 -149q-125 -152 -301 -152z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="1286" 
d="M174 879v225h520l103 217h245l-102 -217h172v-225h-278l-134 -283h412v-225h-518l-102 -217h-246l102 217h-174v225h281l133 283h-414z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="1222" 
d="M123 0v236h946v-236h-946zM123 903l946 330v-246l-604 -196l604 -197v-246l-946 330v225z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="1222" 
d="M1100 236v-236h-946v236h946zM154 1233l946 -330v-225l-946 -330v246l604 197l-604 196v246z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" horiz-adv-x="1314" 
d="M1243 717l-459 -717h-254l-458 717l458 717h254zM657 242l289 475l-289 475l-288 -475z" />
    <glyph glyph-name="apple" unicode="&#xf8ff;" horiz-adv-x="1679" 
d="M543 -25q-152 0 -255.5 103.5t-103.5 255.5v741q0 152 103.5 255.5t255.5 103.5h717q152 0 255 -102.5t103 -254.5t-103 -254t-255 -102h-359v-387q0 -152 -103 -255.5t-255 -103.5z" />
    <glyph glyph-name="fi" unicode="&#xfb01;" horiz-adv-x="1308" 
d="M199 0v840h-136v225h136v98q0 173 81 252.5t248 79.5h185v-221h-121q-62 0 -88.5 -24t-26.5 -85v-100h215v-225h-215v-840h-278zM887 0v1065h278v-1065h-278zM864 1343q0 69 46 115.5t116 46.5t116 -46.5t46 -115.5t-46 -115t-116 -46t-116 46t-46 115z" />
    <glyph glyph-name="fl" unicode="&#xfb02;" horiz-adv-x="1308" 
d="M199 0v840h-136v225h136v98q0 173 81 252.5t248 79.5h185v-221h-121q-62 0 -88.5 -24t-26.5 -85v-100h215v-225h-215v-840h-278zM887 0v1495h278v-1495h-278z" />
    <glyph glyph-name="grave.case" horiz-adv-x="798" 
d="M358 1542l-200 289h342l141 -289h-283z" />
    <glyph glyph-name="tilde.case" horiz-adv-x="1208" 
d="M342 1534l-184 41q27 140 78 203t167 63q74 0 214.5 -43t162.5 -43q9 0 16.5 1.5t13.5 3.5t12 7.5t10 8t8.5 10.5t6.5 11.5t6 13.5l5 12.5t5 14t5 13.5l183 -51q-16 -56 -28.5 -89t-32 -69t-42.5 -55t-58 -31t-81 -12q-49 0 -190 43t-189 43q-37 0 -56.5 -24.5
t-31.5 -71.5z" />
    <glyph glyph-name="dieresis.case" horiz-adv-x="1073" 
d="M158 1692q0 64 42 106.5t107 42.5t107.5 -42.5t42.5 -106.5t-42.5 -107t-107.5 -43q-64 0 -106.5 42.5t-42.5 107.5zM616 1692q0 64 42.5 106.5t107.5 42.5q64 0 106.5 -42.5t42.5 -106.5q0 -65 -42.5 -107.5t-106.5 -42.5t-107 43t-43 107z" />
    <glyph glyph-name="acute.case" horiz-adv-x="798" 
d="M158 1542l141 289h342l-201 -289h-282z" />
    <glyph glyph-name="circumflex.case" horiz-adv-x="1073" 
d="M674 1831l241 -289h-268l-110 137l-111 -137h-268l241 289h275z" />
    <glyph glyph-name="macron.case" horiz-adv-x="1085" 
d="M158 1581v223h770v-223h-770z" />
    <glyph glyph-name="breve.case" horiz-adv-x="1052" 
d="M526 1542q-160 0 -264 77t-104 212h217q0 -56 40.5 -90.5t110.5 -34.5t111 34.5t41 90.5h217q0 -135 -104.5 -212t-264.5 -77z" />
    <glyph glyph-name="dotaccent.case" horiz-adv-x="638" 
d="M158 1704q0 69 45.5 115.5t115.5 46.5t116 -46.5t46 -115.5t-46 -115.5t-116 -46.5t-115.5 46.5t-45.5 115.5z" />
    <glyph glyph-name="caron.case" horiz-adv-x="1073" 
d="M915 1831l-241 -289h-275l-241 289h268l111 -137l110 137h268z" />
    <glyph glyph-name="hungarumlaut.case" horiz-adv-x="1142" 
d="M166 1542l88 289h295l-131 -289h-252zM594 1542l88 289h295l-131 -289h-252z" />
    <glyph glyph-name="ring.case" horiz-adv-x="749" 
d="M375 1645q42 0 68 24.5t26 63.5q0 40 -25.5 63t-68.5 23t-68.5 -23t-25.5 -63q0 -39 26 -63.5t68 -24.5zM375 1542q-100 0 -158.5 55.5t-58.5 135.5q0 79 60 133.5t157 54.5q98 0 157.5 -54.5t59.5 -133.5q0 -80 -58.5 -135.5t-158.5 -55.5z" />
    <glyph glyph-name="J.salt" horiz-adv-x="1253" 
d="M588 -29q-234 0 -370 121.5t-136 327.5v20h272v-18q0 -99 61.5 -156t172.5 -57t169 57.5t58 155.5v774h-608v238h895v-1012q0 -200 -143 -325.5t-371 -125.5z" />
    <glyph glyph-name="IJ.salt" horiz-adv-x="1875" 
d="M168 0v1434h287v-1434h-287zM1210 -29q-234 0 -369.5 121.5t-135.5 327.5v20h272v-18q0 -99 61 -156t172 -57t169.5 57.5t58.5 155.5v774h-609v238h895v-1012q0 -200 -143 -325.5t-371 -125.5z" />
    <glyph glyph-name="Jcircumflex.salt" horiz-adv-x="1253" 
d="M823 1831l242 -289h-268l-111 137l-111 -137h-268l242 289h274zM588 -29q-234 0 -370 121.5t-136 327.5v20h272v-18q0 -99 61.5 -156t172.5 -57t169 57.5t58 155.5v774h-608v238h895v-1012q0 -200 -143 -325.5t-371 -125.5z" />
    <glyph glyph-name="ornm01.ornm" horiz-adv-x="2199" 
d="M1030 0l717 717l-717 717h336l717 -717l-717 -717h-336zM178 600v234h1147v-234h-1147z" />
    <glyph glyph-name="ornm02.ornm" horiz-adv-x="2199" 
d="M834 0l-717 717l717 717h335l-716 -717l716 -717h-335zM874 600v234h1147v-234h-1147z" />
    <glyph glyph-name="ornm03.ornm" horiz-adv-x="1816" 
d="M1323 182v1014h-1014l238 238h1030v-1014zM358 51l-180 180l752 754l180 -182z" />
    <glyph glyph-name="ornm04.ornm" horiz-adv-x="1816" 
d="M494 182l-254 238v1014h1030l237 -238h-1013v-1014zM1458 51l-751 752l180 182l751 -754z" />
    <glyph glyph-name="ornm05.ornm" horiz-adv-x="1816" 
d="M547 0l-238 238h1014v1013l254 -237v-1014h-1030zM930 449l-752 753l180 180l752 -751z" />
    <glyph glyph-name="ornm06.ornm" horiz-adv-x="1816" 
d="M240 0v1014l254 237v-1013h1013l-237 -238h-1030zM887 449l-180 182l751 751l180 -180z" />
    <glyph glyph-name="ornm07.ornm" horiz-adv-x="1792" 
d="M178 410v335l717 717l717 -717v-335l-717 716zM766 -334v1032h258v-1032h-258z" />
    <glyph glyph-name="ornm08.ornm" horiz-adv-x="1792" 
d="M895 -358l-717 716v336l717 -717l717 717v-336zM766 406v1028h258v-1028h-258z" />
    <glyph glyph-name="ornm09.ornm" horiz-adv-x="1974" 
d="M965 33l577 577l-577 578h335l576 -578l-576 -577h-335zM158 494v233h946v-233h-946z" />
    <glyph glyph-name="ornm10.ornm" horiz-adv-x="1974" 
d="M674 33l-578 577l578 578h336l-578 -578l578 -577h-336zM870 494v233h945v-233h-945z" />
    <glyph glyph-name="ornm11.ornm" horiz-adv-x="1679" 
d="M1219 23v815h-818l238 227h821v-805zM324 -225l-166 164l667 667l166 -164z" />
    <glyph glyph-name="ornm12.ornm" horiz-adv-x="1679" 
d="M463 23l-244 237v805h821l238 -227h-815v-815zM1358 -225l-668 667l164 164l668 -667z" />
    <glyph glyph-name="ornm13.ornm" horiz-adv-x="1679" 
d="M639 0l-238 225h818v817l241 -237v-805h-821zM825 459l-667 667l166 164l667 -667z" />
    <glyph glyph-name="ornm14.ornm" horiz-adv-x="1679" 
d="M219 0v805l244 237v-817h815l-238 -225h-821zM854 459l-164 164l668 667l164 -164z" />
    <glyph glyph-name="ornm15.ornm" horiz-adv-x="1509" 
d="M178 328v336l578 577l575 -577v-336l-575 577zM631 -324v791h248v-791h-248z" />
    <glyph glyph-name="ornm16.ornm" horiz-adv-x="1509" 
d="M756 -344l-578 575v336l578 -575l575 575v-336zM631 428v793h248v-793h-248z" />
    <glyph glyph-name="ornm17.ornm" horiz-adv-x="1527" 
d="M236 0q0 325 12 477q-57 29 -72 84q3 4 27 21t26 36q6 42 2 62t-17 60t-17 65q-3 32 0.5 121.5t-4.5 130.5q-2 28 -23.5 80.5t-24.5 72.5q-1 30 5 80t4 85t-21 59q60 2 181.5 -2.5t177.5 -1.5q5 9 6 22.5t-0.5 36t-1.5 31.5h43q17 -15 25.5 -43.5t14 -68t7.5 -48.5
q18 -10 81.5 -25t84.5 -35q23 20 58 26t67 0.5t61 -17.5q-10 -17 -7 -21t27 -4q0 -25 13 -47q7 1 11.5 12t12 17t23.5 0q14 -13 49.5 -36t50.5 -40q31 0 65.5 23.5t49.5 27.5q6 -21 28.5 -27.5t47.5 -3t51.5 0t38.5 -20.5q5 0 23 2t33 2t22 -4q-30 -35 -102 -62t-93 -41
q-36 -22 -79 -78t-52 -65q-19 -18 -53.5 -43.5t-59.5 -48t-39 -47.5q0 -20 6.5 -64.5t4.5 -72.5q-11 -11 -40.5 -29.5t-45 -37t-17.5 -48.5q4 -4 15.5 -15t17 -19.5t8.5 -18.5q-16 -21 -19.5 -52.5t-0.5 -59t-4 -62.5q12 -9 68 -46t86.5 -64.5t51.5 -61.5q26 26 57 9t38 -44
q-11 -50 -4 -119h-985z" />
    <glyph glyph-name="ornm18.ornm" horiz-adv-x="1832" 
d="M260 -4q-16 0 -39 29q-6 11 0 104q0 9 2 30t2 38q0 45 4 77q-56 -41 -118 -47q-13 -1 -16.5 10t5.5 17q84 24 136 70l32 30q36 52 58 107q4 14 11.5 35t12.5 37t8 30l-4 119q-6 75 -6 84q0 49 17 90t38.5 66.5t57 48.5t55 32t49.5 21q7 3 10 4q139 47 283 47q47 0 141 -8
q31 -4 62 -6t46 -2h15q18 0 43 4q12 3 23.5 14.5t19.5 24t17 33t13 31.5t10 31l7 20q19 65 84 139q2 4 5.5 37t9.5 41q9 2 31 0.5t26 -4.5q2 0 57 -11q25 0 29 -6q4 -3 10 -10.5t11 -11.5q14 -3 24 -13q2 -4 27 -30q4 -4 10 -16t6 -17q0 -11 51 -43q11 -8 74 -47
q35 -17 29 -37q-5 -13 -41.5 -25t-55.5 -12t-49 6q-27 7 -62.5 8t-49.5 -6q-6 -1 -17.5 -26t-13.5 -35q-6 -66 -14 -103q-11 -44 -30 -106t-22 -74q-2 -10 -5 -34t-7 -45.5t-10 -39.5q-7 -13 -17 -29t-15 -25t-7 -15q-11 -13 -27 -112.5t-20 -149.5q-9 -93 -6 -224
q0 -17 19.5 -41.5t35.5 -21.5q11 0 19.5 -3t11.5 -6l2 -3q3 -4 2.5 -18t-6.5 -17l-8 -4l-41 2q2 -1 5 -3.5t6.5 -8t0.5 -9.5l-8 -8h-86q-11 4 -25 31q-1 2 -4.5 18.5t-7.5 37t-6 26.5q-3 9 -11.5 14t-9.5 10q0 4 3 26.5t3 49.5q0 4 -6 10.5t-10 14.5q-2 6 -2 47v90t-2 69
q0 7 -4.5 52t-5.5 61q-4 17 1 32t5 17q-2 8 -10.5 10t-27 0t-24.5 -2q-35 0 -91 18t-103 52q-24 18 -54 52t-53 54.5t-45 20.5t-46.5 -10t-37.5 -25q-16 -14 -33 -71q-2 -8 -2 -43q-3 -30 -20 -99q-3 -13 -29 -88q-4 -12 -17 -31.5t-24 -33.5l-10 -13q-8 -11 -21 -24.5
t-21 -22.5t-9 -14q-6 -9 -11.5 -24t-7.5 -26l-2 -10q-2 -110 0 -126q0 -12 3.5 -22t3.5 -11q6 -3 13.5 -10t14.5 -11q2 0 7.5 -1t8.5 -2t6.5 -3.5t4.5 -5.5q2 -9 0 -18.5t-8 -10.5h-54q-11 4 -28 15l-21 18q-2 2 -5 33t-5.5 64t-3.5 36l-1 8q-1 8 -1.5 19.5t-1.5 25.5
t-0.5 29.5t2.5 26.5q0 18 -6 18q-4 0 -11 -10t-18.5 -25t-15.5 -20q-4 -9 -24 -72.5t-21 -79.5v-84l16 -16q16 -7 16 -21q0 -15 -14 -24h-39z" />
    <hkern u1="&#x20;" u2="&#x1ef3;" k="16" />
    <hkern u1="&#x20;" u2="&#x1ef2;" k="27" />
    <hkern u1="&#x20;" u2="&#x1e84;" k="27" />
    <hkern u1="&#x20;" u2="&#x1e82;" k="27" />
    <hkern u1="&#x20;" u2="&#x1e80;" k="27" />
    <hkern u1="&#x20;" u2="&#x178;" k="27" />
    <hkern u1="&#x20;" u2="&#x177;" k="16" />
    <hkern u1="&#x20;" u2="&#x176;" k="27" />
    <hkern u1="&#x20;" u2="&#x174;" k="27" />
    <hkern u1="&#x20;" u2="&#x164;" k="53" />
    <hkern u1="&#x20;" u2="&#x162;" k="53" />
    <hkern u1="&#x20;" u2="&#xff;" k="16" />
    <hkern u1="&#x20;" u2="&#xfd;" k="16" />
    <hkern u1="&#x20;" u2="&#xdd;" k="27" />
    <hkern u1="&#x20;" u2="y" k="16" />
    <hkern u1="&#x20;" u2="Y" k="27" />
    <hkern u1="&#x20;" u2="W" k="27" />
    <hkern u1="&#x20;" u2="T" k="53" />
    <hkern u1="&#x20;" u2="v" k="4" />
    <hkern u1="&#x20;" u2="V" k="37" />
    <hkern u1="&#x21;" g2="Jcircumflex.salt" k="10" />
    <hkern u1="&#x21;" g2="J.salt" k="10" />
    <hkern u1="&#x21;" u2="&#x2039;" k="12" />
    <hkern u1="&#x21;" u2="&#x164;" k="-10" />
    <hkern u1="&#x21;" u2="&#x162;" k="-10" />
    <hkern u1="&#x21;" u2="&#x134;" k="10" />
    <hkern u1="&#x21;" u2="&#xab;" k="12" />
    <hkern u1="&#x21;" u2="T" k="-10" />
    <hkern u1="&#x21;" u2="J" k="10" />
    <hkern u1="&#x21;" u2="\" k="12" />
    <hkern u1="&#x21;" u2="&#x37;" k="6" />
    <hkern u1="&#x21;" u2="&#x36;" k="12" />
    <hkern u1="&#x21;" u2="&#x35;" k="10" />
    <hkern u1="&#x21;" u2="&#x2f;" k="25" />
    <hkern u1="&#x23;" u2="&#x203a;" k="16" />
    <hkern u1="&#x23;" u2="&#x2026;" k="37" />
    <hkern u1="&#x23;" u2="&#x201e;" k="55" />
    <hkern u1="&#x23;" u2="&#x201c;" k="-12" />
    <hkern u1="&#x23;" u2="&#x201a;" k="55" />
    <hkern u1="&#x23;" u2="&#x2018;" k="-12" />
    <hkern u1="&#x23;" u2="&#xbb;" k="16" />
    <hkern u1="&#x23;" u2="&#x7d;" k="37" />
    <hkern u1="&#x23;" u2="]" k="37" />
    <hkern u1="&#x23;" u2="&#x2e;" k="37" />
    <hkern u1="&#x23;" u2="&#x2c;" k="55" />
    <hkern u1="&#x23;" u2="&#x29;" k="37" />
    <hkern u1="&#x23;" u2="&#x2122;" k="20" />
    <hkern u1="&#x23;" u2="\" k="45" />
    <hkern u1="&#x23;" u2="&#x40;" k="10" />
    <hkern u1="&#x23;" u2="&#x39;" k="-8" />
    <hkern u1="&#x23;" u2="&#x34;" k="-12" />
    <hkern u1="&#x23;" u2="&#x31;" k="-12" />
    <hkern u1="&#x23;" u2="&#x2f;" k="102" />
    <hkern u1="&#x24;" u2="&#x201e;" k="20" />
    <hkern u1="&#x24;" u2="&#x201d;" k="37" />
    <hkern u1="&#x24;" u2="&#x201c;" k="25" />
    <hkern u1="&#x24;" u2="&#x201a;" k="20" />
    <hkern u1="&#x24;" u2="&#x2019;" k="37" />
    <hkern u1="&#x24;" u2="&#x2018;" k="25" />
    <hkern u1="&#x24;" u2="&#x149;" k="37" />
    <hkern u1="&#x24;" u2="&#x7d;" k="18" />
    <hkern u1="&#x24;" u2="]" k="18" />
    <hkern u1="&#x24;" u2="&#x2c;" k="20" />
    <hkern u1="&#x24;" u2="&#x29;" k="18" />
    <hkern u1="&#x24;" u2="\" k="80" />
    <hkern u1="&#x24;" u2="&#x37;" k="6" />
    <hkern u1="&#x24;" u2="&#x2f;" k="57" />
    <hkern u1="&#x25;" u2="&#x2122;" k="137" />
    <hkern u1="&#x25;" u2="&#xbf;" k="-45" />
    <hkern u1="&#x25;" u2="&#xae;" k="61" />
    <hkern u1="&#x25;" u2="\" k="143" />
    <hkern u1="&#x25;" u2="&#x3f;" k="33" />
    <hkern u1="&#x25;" u2="&#x39;" k="8" />
    <hkern u1="&#x25;" u2="&#x38;" k="-12" />
    <hkern u1="&#x25;" u2="&#x37;" k="-12" />
    <hkern u1="&#x25;" u2="&#x36;" k="-25" />
    <hkern u1="&#x25;" u2="&#x35;" k="-25" />
    <hkern u1="&#x25;" u2="&#x34;" k="-25" />
    <hkern u1="&#x25;" u2="&#x33;" k="-25" />
    <hkern u1="&#x25;" u2="&#x32;" k="-25" />
    <hkern u1="&#x25;" u2="&#x31;" k="12" />
    <hkern u1="&#x25;" u2="&#x30;" k="-18" />
    <hkern u1="&#x25;" u2="&#x2f;" k="16" />
    <hkern u1="&#x25;" u2="&#x2a;" k="68" />
    <hkern u1="&#x26;" u2="&#x2039;" k="12" />
    <hkern u1="&#x26;" u2="&#x2030;" k="31" />
    <hkern u1="&#x26;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x26;" u2="&#x201e;" k="-20" />
    <hkern u1="&#x26;" u2="&#x201d;" k="113" />
    <hkern u1="&#x26;" u2="&#x201c;" k="94" />
    <hkern u1="&#x26;" u2="&#x201a;" k="-20" />
    <hkern u1="&#x26;" u2="&#x2019;" k="113" />
    <hkern u1="&#x26;" u2="&#x2018;" k="94" />
    <hkern u1="&#x26;" u2="&#x2014;" k="27" />
    <hkern u1="&#x26;" u2="&#x2013;" k="27" />
    <hkern u1="&#x26;" u2="&#x1ef3;" k="37" />
    <hkern u1="&#x26;" u2="&#x1ef2;" k="164" />
    <hkern u1="&#x26;" u2="&#x1e85;" k="16" />
    <hkern u1="&#x26;" u2="&#x1e84;" k="80" />
    <hkern u1="&#x26;" u2="&#x1e83;" k="16" />
    <hkern u1="&#x26;" u2="&#x1e82;" k="80" />
    <hkern u1="&#x26;" u2="&#x1e81;" k="16" />
    <hkern u1="&#x26;" u2="&#x1e80;" k="80" />
    <hkern u1="&#x26;" u2="&#x178;" k="164" />
    <hkern u1="&#x26;" u2="&#x177;" k="37" />
    <hkern u1="&#x26;" u2="&#x176;" k="164" />
    <hkern u1="&#x26;" u2="&#x175;" k="16" />
    <hkern u1="&#x26;" u2="&#x174;" k="80" />
    <hkern u1="&#x26;" u2="&#x164;" k="117" />
    <hkern u1="&#x26;" u2="&#x162;" k="117" />
    <hkern u1="&#x26;" u2="&#x149;" k="113" />
    <hkern u1="&#x26;" u2="&#xff;" k="37" />
    <hkern u1="&#x26;" u2="&#xfd;" k="37" />
    <hkern u1="&#x26;" u2="&#xdd;" k="164" />
    <hkern u1="&#x26;" u2="&#xab;" k="12" />
    <hkern u1="&#x26;" u2="&#x7d;" k="31" />
    <hkern u1="&#x26;" u2="y" k="37" />
    <hkern u1="&#x26;" u2="w" k="16" />
    <hkern u1="&#x26;" u2="]" k="31" />
    <hkern u1="&#x26;" u2="Y" k="164" />
    <hkern u1="&#x26;" u2="W" k="80" />
    <hkern u1="&#x26;" u2="T" k="117" />
    <hkern u1="&#x26;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x26;" u2="&#x2d;" k="27" />
    <hkern u1="&#x26;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x26;" u2="&#x29;" k="31" />
    <hkern u1="&#x26;" u2="&#x25;" k="31" />
    <hkern u1="&#x26;" u2="&#xa5;" k="10" />
    <hkern u1="&#x26;" u2="&#xa2;" k="20" />
    <hkern u1="&#x26;" u2="v" k="37" />
    <hkern u1="&#x26;" u2="\" k="160" />
    <hkern u1="&#x26;" u2="X" k="-10" />
    <hkern u1="&#x26;" u2="V" k="123" />
    <hkern u1="&#x26;" u2="&#x40;" k="10" />
    <hkern u1="&#x26;" u2="&#x3f;" k="82" />
    <hkern u1="&#x26;" u2="&#x39;" k="31" />
    <hkern u1="&#x26;" u2="&#x37;" k="20" />
    <hkern u1="&#x26;" u2="&#x35;" k="10" />
    <hkern u1="&#x26;" u2="&#x31;" k="31" />
    <hkern u1="&#x26;" u2="&#x2a;" k="78" />
    <hkern u1="&#x28;" u2="&#x20ac;" k="82" />
    <hkern u1="&#x28;" u2="&#xbf;" k="37" />
    <hkern u1="&#x28;" u2="&#xae;" k="20" />
    <hkern u1="&#x28;" u2="&#xa5;" k="20" />
    <hkern u1="&#x28;" u2="&#xa2;" k="45" />
    <hkern u1="&#x28;" u2="x" k="6" />
    <hkern u1="&#x28;" u2="v" k="27" />
    <hkern u1="&#x28;" u2="j" k="-47" />
    <hkern u1="&#x28;" u2="i" k="10" />
    <hkern u1="&#x28;" u2="\" k="20" />
    <hkern u1="&#x28;" u2="V" k="-6" />
    <hkern u1="&#x28;" u2="&#x40;" k="45" />
    <hkern u1="&#x28;" u2="&#x3f;" k="23" />
    <hkern u1="&#x28;" u2="&#x39;" k="47" />
    <hkern u1="&#x28;" u2="&#x38;" k="47" />
    <hkern u1="&#x28;" u2="&#x37;" k="10" />
    <hkern u1="&#x28;" u2="&#x36;" k="53" />
    <hkern u1="&#x28;" u2="&#x35;" k="33" />
    <hkern u1="&#x28;" u2="&#x34;" k="55" />
    <hkern u1="&#x28;" u2="&#x33;" k="18" />
    <hkern u1="&#x28;" u2="&#x32;" k="10" />
    <hkern u1="&#x28;" u2="&#x31;" k="18" />
    <hkern u1="&#x28;" u2="&#x30;" k="37" />
    <hkern u1="&#x28;" u2="&#x2a;" k="51" />
    <hkern u1="&#x28;" u2="&#x26;" k="41" />
    <hkern u1="&#x28;" u2="&#x24;" k="20" />
    <hkern u1="&#x28;" u2="&#x23;" k="47" />
    <hkern u1="&#x29;" u2="&#x2122;" k="4" />
    <hkern u1="&#x29;" u2="\" k="72" />
    <hkern u1="&#x29;" u2="&#x2f;" k="41" />
    <hkern u1="&#x29;" u2="&#x2a;" k="20" />
    <hkern u1="&#x2a;" g2="Jcircumflex.salt" k="90" />
    <hkern u1="&#x2a;" g2="J.salt" k="90" />
    <hkern u1="&#x2a;" u2="&#x203a;" k="20" />
    <hkern u1="&#x2a;" u2="&#x2039;" k="45" />
    <hkern u1="&#x2a;" u2="&#x2026;" k="225" />
    <hkern u1="&#x2a;" u2="&#x201e;" k="215" />
    <hkern u1="&#x2a;" u2="&#x201d;" k="12" />
    <hkern u1="&#x2a;" u2="&#x201c;" k="6" />
    <hkern u1="&#x2a;" u2="&#x201a;" k="215" />
    <hkern u1="&#x2a;" u2="&#x2019;" k="12" />
    <hkern u1="&#x2a;" u2="&#x2018;" k="6" />
    <hkern u1="&#x2a;" u2="&#x2014;" k="20" />
    <hkern u1="&#x2a;" u2="&#x2013;" k="20" />
    <hkern u1="&#x2a;" u2="&#x1ef3;" k="-20" />
    <hkern u1="&#x2a;" u2="&#x1ef2;" k="31" />
    <hkern u1="&#x2a;" u2="&#x1e85;" k="-31" />
    <hkern u1="&#x2a;" u2="&#x1e84;" k="16" />
    <hkern u1="&#x2a;" u2="&#x1e83;" k="-31" />
    <hkern u1="&#x2a;" u2="&#x1e82;" k="16" />
    <hkern u1="&#x2a;" u2="&#x1e81;" k="-31" />
    <hkern u1="&#x2a;" u2="&#x1e80;" k="16" />
    <hkern u1="&#x2a;" u2="&#x219;" k="35" />
    <hkern u1="&#x2a;" u2="&#x1ff;" k="41" />
    <hkern u1="&#x2a;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x2a;" u2="&#x1fd;" k="72" />
    <hkern u1="&#x2a;" u2="&#x1fb;" k="72" />
    <hkern u1="&#x2a;" u2="&#x1fa;" k="215" />
    <hkern u1="&#x2a;" u2="&#x17e;" k="4" />
    <hkern u1="&#x2a;" u2="&#x17d;" k="37" />
    <hkern u1="&#x2a;" u2="&#x17c;" k="4" />
    <hkern u1="&#x2a;" u2="&#x17b;" k="37" />
    <hkern u1="&#x2a;" u2="&#x17a;" k="4" />
    <hkern u1="&#x2a;" u2="&#x179;" k="37" />
    <hkern u1="&#x2a;" u2="&#x178;" k="31" />
    <hkern u1="&#x2a;" u2="&#x177;" k="-20" />
    <hkern u1="&#x2a;" u2="&#x176;" k="31" />
    <hkern u1="&#x2a;" u2="&#x175;" k="-31" />
    <hkern u1="&#x2a;" u2="&#x174;" k="16" />
    <hkern u1="&#x2a;" u2="&#x161;" k="35" />
    <hkern u1="&#x2a;" u2="&#x160;" k="31" />
    <hkern u1="&#x2a;" u2="&#x15f;" k="35" />
    <hkern u1="&#x2a;" u2="&#x15e;" k="31" />
    <hkern u1="&#x2a;" u2="&#x15d;" k="35" />
    <hkern u1="&#x2a;" u2="&#x15c;" k="31" />
    <hkern u1="&#x2a;" u2="&#x15b;" k="35" />
    <hkern u1="&#x2a;" u2="&#x15a;" k="31" />
    <hkern u1="&#x2a;" u2="&#x152;" k="10" />
    <hkern u1="&#x2a;" u2="&#x151;" k="41" />
    <hkern u1="&#x2a;" u2="&#x150;" k="10" />
    <hkern u1="&#x2a;" u2="&#x14f;" k="41" />
    <hkern u1="&#x2a;" u2="&#x14e;" k="10" />
    <hkern u1="&#x2a;" u2="&#x14d;" k="41" />
    <hkern u1="&#x2a;" u2="&#x14c;" k="10" />
    <hkern u1="&#x2a;" u2="&#x149;" k="12" />
    <hkern u1="&#x2a;" u2="&#x134;" k="389" />
    <hkern u1="&#x2a;" u2="&#x123;" k="41" />
    <hkern u1="&#x2a;" u2="&#x122;" k="10" />
    <hkern u1="&#x2a;" u2="&#x121;" k="41" />
    <hkern u1="&#x2a;" u2="&#x120;" k="10" />
    <hkern u1="&#x2a;" u2="&#x11f;" k="41" />
    <hkern u1="&#x2a;" u2="&#x11e;" k="10" />
    <hkern u1="&#x2a;" u2="&#x11d;" k="41" />
    <hkern u1="&#x2a;" u2="&#x11c;" k="10" />
    <hkern u1="&#x2a;" u2="&#x11b;" k="41" />
    <hkern u1="&#x2a;" u2="&#x119;" k="41" />
    <hkern u1="&#x2a;" u2="&#x117;" k="41" />
    <hkern u1="&#x2a;" u2="&#x115;" k="41" />
    <hkern u1="&#x2a;" u2="&#x113;" k="41" />
    <hkern u1="&#x2a;" u2="&#x111;" k="41" />
    <hkern u1="&#x2a;" u2="&#x10f;" k="41" />
    <hkern u1="&#x2a;" u2="&#x10d;" k="41" />
    <hkern u1="&#x2a;" u2="&#x10c;" k="10" />
    <hkern u1="&#x2a;" u2="&#x10b;" k="41" />
    <hkern u1="&#x2a;" u2="&#x10a;" k="10" />
    <hkern u1="&#x2a;" u2="&#x109;" k="41" />
    <hkern u1="&#x2a;" u2="&#x108;" k="10" />
    <hkern u1="&#x2a;" u2="&#x107;" k="41" />
    <hkern u1="&#x2a;" u2="&#x106;" k="10" />
    <hkern u1="&#x2a;" u2="&#x105;" k="72" />
    <hkern u1="&#x2a;" u2="&#x104;" k="215" />
    <hkern u1="&#x2a;" u2="&#x103;" k="72" />
    <hkern u1="&#x2a;" u2="&#x102;" k="215" />
    <hkern u1="&#x2a;" u2="&#x101;" k="72" />
    <hkern u1="&#x2a;" u2="&#x100;" k="215" />
    <hkern u1="&#x2a;" u2="&#xff;" k="-20" />
    <hkern u1="&#x2a;" u2="&#xfd;" k="-20" />
    <hkern u1="&#x2a;" u2="&#xf8;" k="41" />
    <hkern u1="&#x2a;" u2="&#xf6;" k="41" />
    <hkern u1="&#x2a;" u2="&#xf5;" k="41" />
    <hkern u1="&#x2a;" u2="&#xf4;" k="41" />
    <hkern u1="&#x2a;" u2="&#xf3;" k="41" />
    <hkern u1="&#x2a;" u2="&#xf2;" k="41" />
    <hkern u1="&#x2a;" u2="&#xeb;" k="41" />
    <hkern u1="&#x2a;" u2="&#xea;" k="41" />
    <hkern u1="&#x2a;" u2="&#xe9;" k="41" />
    <hkern u1="&#x2a;" u2="&#xe8;" k="41" />
    <hkern u1="&#x2a;" u2="&#xe7;" k="41" />
    <hkern u1="&#x2a;" u2="&#xe6;" k="72" />
    <hkern u1="&#x2a;" u2="&#xe5;" k="72" />
    <hkern u1="&#x2a;" u2="&#xe4;" k="72" />
    <hkern u1="&#x2a;" u2="&#xe3;" k="72" />
    <hkern u1="&#x2a;" u2="&#xe2;" k="72" />
    <hkern u1="&#x2a;" u2="&#xe1;" k="72" />
    <hkern u1="&#x2a;" u2="&#xe0;" k="72" />
    <hkern u1="&#x2a;" u2="&#xdd;" k="31" />
    <hkern u1="&#x2a;" u2="&#xd8;" k="10" />
    <hkern u1="&#x2a;" u2="&#xd6;" k="10" />
    <hkern u1="&#x2a;" u2="&#xd5;" k="10" />
    <hkern u1="&#x2a;" u2="&#xd4;" k="10" />
    <hkern u1="&#x2a;" u2="&#xd3;" k="10" />
    <hkern u1="&#x2a;" u2="&#xd2;" k="10" />
    <hkern u1="&#x2a;" u2="&#xc7;" k="10" />
    <hkern u1="&#x2a;" u2="&#xc5;" k="215" />
    <hkern u1="&#x2a;" u2="&#xc4;" k="215" />
    <hkern u1="&#x2a;" u2="&#xc3;" k="215" />
    <hkern u1="&#x2a;" u2="&#xc2;" k="215" />
    <hkern u1="&#x2a;" u2="&#xc1;" k="215" />
    <hkern u1="&#x2a;" u2="&#xc0;" k="215" />
    <hkern u1="&#x2a;" u2="&#xbb;" k="20" />
    <hkern u1="&#x2a;" u2="&#xab;" k="45" />
    <hkern u1="&#x2a;" u2="&#x7d;" k="61" />
    <hkern u1="&#x2a;" u2="&#x7b;" k="20" />
    <hkern u1="&#x2a;" u2="z" k="4" />
    <hkern u1="&#x2a;" u2="y" k="-20" />
    <hkern u1="&#x2a;" u2="w" k="-31" />
    <hkern u1="&#x2a;" u2="s" k="35" />
    <hkern u1="&#x2a;" u2="q" k="41" />
    <hkern u1="&#x2a;" u2="o" k="41" />
    <hkern u1="&#x2a;" u2="g" k="41" />
    <hkern u1="&#x2a;" u2="e" k="41" />
    <hkern u1="&#x2a;" u2="d" k="41" />
    <hkern u1="&#x2a;" u2="c" k="41" />
    <hkern u1="&#x2a;" u2="a" k="72" />
    <hkern u1="&#x2a;" u2="]" k="61" />
    <hkern u1="&#x2a;" u2="[" k="20" />
    <hkern u1="&#x2a;" u2="Z" k="37" />
    <hkern u1="&#x2a;" u2="Y" k="31" />
    <hkern u1="&#x2a;" u2="W" k="16" />
    <hkern u1="&#x2a;" u2="S" k="31" />
    <hkern u1="&#x2a;" u2="Q" k="10" />
    <hkern u1="&#x2a;" u2="O" k="10" />
    <hkern u1="&#x2a;" u2="J" k="389" />
    <hkern u1="&#x2a;" u2="G" k="10" />
    <hkern u1="&#x2a;" u2="C" k="10" />
    <hkern u1="&#x2a;" u2="A" k="215" />
    <hkern u1="&#x2a;" u2="&#x3b;" k="37" />
    <hkern u1="&#x2a;" u2="&#x3a;" k="37" />
    <hkern u1="&#x2a;" u2="&#x2e;" k="225" />
    <hkern u1="&#x2a;" u2="&#x2d;" k="20" />
    <hkern u1="&#x2a;" u2="&#x2c;" k="215" />
    <hkern u1="&#x2a;" u2="&#x29;" k="61" />
    <hkern u1="&#x2a;" u2="&#x28;" k="20" />
    <hkern u1="&#x2a;" u2="&#xbf;" k="190" />
    <hkern u1="&#x2a;" u2="&#xa5;" k="20" />
    <hkern u1="&#x2a;" u2="&#xa3;" k="51" />
    <hkern u1="&#x2a;" u2="&#xa2;" k="10" />
    <hkern u1="&#x2a;" u2="&#xa1;" k="37" />
    <hkern u1="&#x2a;" u2="v" k="-31" />
    <hkern u1="&#x2a;" u2="j" k="10" />
    <hkern u1="&#x2a;" u2="\" k="20" />
    <hkern u1="&#x2a;" u2="X" k="80" />
    <hkern u1="&#x2a;" u2="V" k="16" />
    <hkern u1="&#x2a;" u2="&#x40;" k="41" />
    <hkern u1="&#x2a;" u2="&#x38;" k="41" />
    <hkern u1="&#x2a;" u2="&#x37;" k="10" />
    <hkern u1="&#x2a;" u2="&#x36;" k="51" />
    <hkern u1="&#x2a;" u2="&#x35;" k="41" />
    <hkern u1="&#x2a;" u2="&#x34;" k="100" />
    <hkern u1="&#x2a;" u2="&#x33;" k="20" />
    <hkern u1="&#x2a;" u2="&#x32;" k="20" />
    <hkern u1="&#x2a;" u2="&#x2f;" k="242" />
    <hkern u1="&#x2a;" u2="&#x26;" k="53" />
    <hkern u1="&#x2a;" u2="&#x23;" k="27" />
    <hkern u1="&#x2c;" u2="&#xa5;" k="51" />
    <hkern u1="&#x2c;" u2="&#xa2;" k="35" />
    <hkern u1="&#x2c;" u2="x" k="43" />
    <hkern u1="&#x2c;" u2="v" k="111" />
    <hkern u1="&#x2c;" u2="j" k="18" />
    <hkern u1="&#x2c;" u2="\" k="184" />
    <hkern u1="&#x2c;" u2="X" k="33" />
    <hkern u1="&#x2c;" u2="V" k="197" />
    <hkern u1="&#x2c;" u2="&#x40;" k="10" />
    <hkern u1="&#x2c;" u2="&#x3f;" k="113" />
    <hkern u1="&#x2c;" u2="&#x39;" k="145" />
    <hkern u1="&#x2c;" u2="&#x38;" k="16" />
    <hkern u1="&#x2c;" u2="&#x37;" k="55" />
    <hkern u1="&#x2c;" u2="&#x36;" k="8" />
    <hkern u1="&#x2c;" u2="&#x35;" k="8" />
    <hkern u1="&#x2c;" u2="&#x34;" k="16" />
    <hkern u1="&#x2c;" u2="&#x31;" k="72" />
    <hkern u1="&#x2c;" u2="&#x30;" k="10" />
    <hkern u1="&#x2c;" u2="&#x2a;" k="215" />
    <hkern u1="&#x2c;" u2="&#x23;" k="8" />
    <hkern u1="&#x2c;" u2="&#x21;" k="12" />
    <hkern u1="&#x2d;" u2="&#x2122;" k="37" />
    <hkern u1="&#x2d;" u2="&#xa5;" k="20" />
    <hkern u1="&#x2d;" u2="x" k="72" />
    <hkern u1="&#x2d;" u2="v" k="23" />
    <hkern u1="&#x2d;" u2="\" k="92" />
    <hkern u1="&#x2d;" u2="X" k="129" />
    <hkern u1="&#x2d;" u2="V" k="102" />
    <hkern u1="&#x2d;" u2="&#x3f;" k="20" />
    <hkern u1="&#x2d;" u2="&#x39;" k="18" />
    <hkern u1="&#x2d;" u2="&#x37;" k="68" />
    <hkern u1="&#x2d;" u2="&#x35;" k="10" />
    <hkern u1="&#x2d;" u2="&#x33;" k="14" />
    <hkern u1="&#x2d;" u2="&#x32;" k="20" />
    <hkern u1="&#x2d;" u2="&#x31;" k="33" />
    <hkern u1="&#x2d;" u2="&#x2f;" k="102" />
    <hkern u1="&#x2d;" u2="&#x2a;" k="10" />
    <hkern u1="&#x2e;" u2="&#x2122;" k="225" />
    <hkern u1="&#x2e;" u2="&#x20ac;" k="53" />
    <hkern u1="&#x2e;" u2="&#xae;" k="143" />
    <hkern u1="&#x2e;" u2="&#xa5;" k="55" />
    <hkern u1="&#x2e;" u2="&#xa2;" k="53" />
    <hkern u1="&#x2e;" u2="x" k="10" />
    <hkern u1="&#x2e;" u2="v" k="143" />
    <hkern u1="&#x2e;" u2="\" k="215" />
    <hkern u1="&#x2e;" u2="X" k="18" />
    <hkern u1="&#x2e;" u2="V" k="276" />
    <hkern u1="&#x2e;" u2="&#x3f;" k="82" />
    <hkern u1="&#x2e;" u2="&#x39;" k="143" />
    <hkern u1="&#x2e;" u2="&#x37;" k="41" />
    <hkern u1="&#x2e;" u2="&#x36;" k="10" />
    <hkern u1="&#x2e;" u2="&#x35;" k="20" />
    <hkern u1="&#x2e;" u2="&#x34;" k="35" />
    <hkern u1="&#x2e;" u2="&#x31;" k="143" />
    <hkern u1="&#x2e;" u2="&#x30;" k="10" />
    <hkern u1="&#x2e;" u2="&#x2a;" k="205" />
    <hkern u1="&#x2e;" u2="&#x26;" k="10" />
    <hkern u1="&#x2f;" g2="Jcircumflex.salt" k="74" />
    <hkern u1="&#x2f;" g2="J.salt" k="74" />
    <hkern u1="&#x2f;" u2="&#xfb02;" k="45" />
    <hkern u1="&#x2f;" u2="&#xfb01;" k="45" />
    <hkern u1="&#x2f;" u2="&#x2117;" k="82" />
    <hkern u1="&#x2f;" u2="&#x203a;" k="100" />
    <hkern u1="&#x2f;" u2="&#x2039;" k="123" />
    <hkern u1="&#x2f;" u2="&#x2030;" k="25" />
    <hkern u1="&#x2f;" u2="&#x2026;" k="279" />
    <hkern u1="&#x2f;" u2="&#x201e;" k="225" />
    <hkern u1="&#x2f;" u2="&#x201a;" k="225" />
    <hkern u1="&#x2f;" u2="&#x2014;" k="158" />
    <hkern u1="&#x2f;" u2="&#x2013;" k="158" />
    <hkern u1="&#x2f;" u2="&#x1ef3;" k="29" />
    <hkern u1="&#x2f;" u2="&#x1ef2;" k="-12" />
    <hkern u1="&#x2f;" u2="&#x1e85;" k="31" />
    <hkern u1="&#x2f;" u2="&#x1e84;" k="-12" />
    <hkern u1="&#x2f;" u2="&#x1e83;" k="31" />
    <hkern u1="&#x2f;" u2="&#x1e82;" k="-12" />
    <hkern u1="&#x2f;" u2="&#x1e81;" k="31" />
    <hkern u1="&#x2f;" u2="&#x1e80;" k="-12" />
    <hkern u1="&#x2f;" u2="&#x219;" k="123" />
    <hkern u1="&#x2f;" u2="&#x1ff;" k="135" />
    <hkern u1="&#x2f;" u2="&#x1fe;" k="70" />
    <hkern u1="&#x2f;" u2="&#x1fd;" k="176" />
    <hkern u1="&#x2f;" u2="&#x1fb;" k="176" />
    <hkern u1="&#x2f;" u2="&#x1fa;" k="170" />
    <hkern u1="&#x2f;" u2="&#x17e;" k="59" />
    <hkern u1="&#x2f;" u2="&#x17c;" k="59" />
    <hkern u1="&#x2f;" u2="&#x17a;" k="59" />
    <hkern u1="&#x2f;" u2="&#x178;" k="-12" />
    <hkern u1="&#x2f;" u2="&#x177;" k="29" />
    <hkern u1="&#x2f;" u2="&#x176;" k="-12" />
    <hkern u1="&#x2f;" u2="&#x175;" k="31" />
    <hkern u1="&#x2f;" u2="&#x174;" k="-12" />
    <hkern u1="&#x2f;" u2="&#x173;" k="94" />
    <hkern u1="&#x2f;" u2="&#x171;" k="94" />
    <hkern u1="&#x2f;" u2="&#x16f;" k="94" />
    <hkern u1="&#x2f;" u2="&#x16d;" k="94" />
    <hkern u1="&#x2f;" u2="&#x16b;" k="94" />
    <hkern u1="&#x2f;" u2="&#x169;" k="94" />
    <hkern u1="&#x2f;" u2="&#x164;" k="-12" />
    <hkern u1="&#x2f;" u2="&#x163;" k="27" />
    <hkern u1="&#x2f;" u2="&#x162;" k="-12" />
    <hkern u1="&#x2f;" u2="&#x161;" k="123" />
    <hkern u1="&#x2f;" u2="&#x160;" k="63" />
    <hkern u1="&#x2f;" u2="&#x15f;" k="123" />
    <hkern u1="&#x2f;" u2="&#x15e;" k="63" />
    <hkern u1="&#x2f;" u2="&#x15d;" k="123" />
    <hkern u1="&#x2f;" u2="&#x15c;" k="63" />
    <hkern u1="&#x2f;" u2="&#x15b;" k="123" />
    <hkern u1="&#x2f;" u2="&#x15a;" k="63" />
    <hkern u1="&#x2f;" u2="&#x159;" k="92" />
    <hkern u1="&#x2f;" u2="&#x157;" k="92" />
    <hkern u1="&#x2f;" u2="&#x155;" k="92" />
    <hkern u1="&#x2f;" u2="&#x152;" k="70" />
    <hkern u1="&#x2f;" u2="&#x151;" k="135" />
    <hkern u1="&#x2f;" u2="&#x150;" k="70" />
    <hkern u1="&#x2f;" u2="&#x14f;" k="135" />
    <hkern u1="&#x2f;" u2="&#x14e;" k="70" />
    <hkern u1="&#x2f;" u2="&#x14d;" k="135" />
    <hkern u1="&#x2f;" u2="&#x14c;" k="70" />
    <hkern u1="&#x2f;" u2="&#x14b;" k="92" />
    <hkern u1="&#x2f;" u2="&#x148;" k="92" />
    <hkern u1="&#x2f;" u2="&#x146;" k="92" />
    <hkern u1="&#x2f;" u2="&#x144;" k="92" />
    <hkern u1="&#x2f;" u2="&#x134;" k="156" />
    <hkern u1="&#x2f;" u2="&#x123;" k="135" />
    <hkern u1="&#x2f;" u2="&#x122;" k="70" />
    <hkern u1="&#x2f;" u2="&#x121;" k="135" />
    <hkern u1="&#x2f;" u2="&#x120;" k="70" />
    <hkern u1="&#x2f;" u2="&#x11f;" k="135" />
    <hkern u1="&#x2f;" u2="&#x11e;" k="70" />
    <hkern u1="&#x2f;" u2="&#x11d;" k="135" />
    <hkern u1="&#x2f;" u2="&#x11c;" k="70" />
    <hkern u1="&#x2f;" u2="&#x11b;" k="135" />
    <hkern u1="&#x2f;" u2="&#x119;" k="135" />
    <hkern u1="&#x2f;" u2="&#x117;" k="135" />
    <hkern u1="&#x2f;" u2="&#x115;" k="135" />
    <hkern u1="&#x2f;" u2="&#x113;" k="135" />
    <hkern u1="&#x2f;" u2="&#x111;" k="135" />
    <hkern u1="&#x2f;" u2="&#x10f;" k="135" />
    <hkern u1="&#x2f;" u2="&#x10d;" k="135" />
    <hkern u1="&#x2f;" u2="&#x10c;" k="70" />
    <hkern u1="&#x2f;" u2="&#x10b;" k="135" />
    <hkern u1="&#x2f;" u2="&#x10a;" k="70" />
    <hkern u1="&#x2f;" u2="&#x109;" k="135" />
    <hkern u1="&#x2f;" u2="&#x108;" k="70" />
    <hkern u1="&#x2f;" u2="&#x107;" k="135" />
    <hkern u1="&#x2f;" u2="&#x106;" k="70" />
    <hkern u1="&#x2f;" u2="&#x105;" k="176" />
    <hkern u1="&#x2f;" u2="&#x104;" k="170" />
    <hkern u1="&#x2f;" u2="&#x103;" k="176" />
    <hkern u1="&#x2f;" u2="&#x102;" k="170" />
    <hkern u1="&#x2f;" u2="&#x101;" k="176" />
    <hkern u1="&#x2f;" u2="&#x100;" k="170" />
    <hkern u1="&#x2f;" u2="&#xff;" k="29" />
    <hkern u1="&#x2f;" u2="&#xfd;" k="29" />
    <hkern u1="&#x2f;" u2="&#xfc;" k="94" />
    <hkern u1="&#x2f;" u2="&#xfb;" k="94" />
    <hkern u1="&#x2f;" u2="&#xfa;" k="94" />
    <hkern u1="&#x2f;" u2="&#xf9;" k="94" />
    <hkern u1="&#x2f;" u2="&#xf8;" k="135" />
    <hkern u1="&#x2f;" u2="&#xf6;" k="135" />
    <hkern u1="&#x2f;" u2="&#xf5;" k="135" />
    <hkern u1="&#x2f;" u2="&#xf4;" k="135" />
    <hkern u1="&#x2f;" u2="&#xf3;" k="135" />
    <hkern u1="&#x2f;" u2="&#xf2;" k="135" />
    <hkern u1="&#x2f;" u2="&#xeb;" k="135" />
    <hkern u1="&#x2f;" u2="&#xea;" k="135" />
    <hkern u1="&#x2f;" u2="&#xe9;" k="135" />
    <hkern u1="&#x2f;" u2="&#xe8;" k="135" />
    <hkern u1="&#x2f;" u2="&#xe7;" k="135" />
    <hkern u1="&#x2f;" u2="&#xe6;" k="176" />
    <hkern u1="&#x2f;" u2="&#xe5;" k="176" />
    <hkern u1="&#x2f;" u2="&#xe4;" k="176" />
    <hkern u1="&#x2f;" u2="&#xe3;" k="176" />
    <hkern u1="&#x2f;" u2="&#xe2;" k="176" />
    <hkern u1="&#x2f;" u2="&#xe1;" k="176" />
    <hkern u1="&#x2f;" u2="&#xe0;" k="176" />
    <hkern u1="&#x2f;" u2="&#xdd;" k="-12" />
    <hkern u1="&#x2f;" u2="&#xd8;" k="70" />
    <hkern u1="&#x2f;" u2="&#xd6;" k="70" />
    <hkern u1="&#x2f;" u2="&#xd5;" k="70" />
    <hkern u1="&#x2f;" u2="&#xd4;" k="70" />
    <hkern u1="&#x2f;" u2="&#xd3;" k="70" />
    <hkern u1="&#x2f;" u2="&#xd2;" k="70" />
    <hkern u1="&#x2f;" u2="&#xc7;" k="70" />
    <hkern u1="&#x2f;" u2="&#xc5;" k="170" />
    <hkern u1="&#x2f;" u2="&#xc4;" k="170" />
    <hkern u1="&#x2f;" u2="&#xc3;" k="170" />
    <hkern u1="&#x2f;" u2="&#xc2;" k="170" />
    <hkern u1="&#x2f;" u2="&#xc1;" k="170" />
    <hkern u1="&#x2f;" u2="&#xc0;" k="170" />
    <hkern u1="&#x2f;" u2="&#xbb;" k="100" />
    <hkern u1="&#x2f;" u2="&#xba;" k="20" />
    <hkern u1="&#x2f;" u2="&#xab;" k="123" />
    <hkern u1="&#x2f;" u2="&#xaa;" k="20" />
    <hkern u1="&#x2f;" u2="&#xa9;" k="82" />
    <hkern u1="&#x2f;" u2="&#x7b;" k="53" />
    <hkern u1="&#x2f;" u2="z" k="59" />
    <hkern u1="&#x2f;" u2="y" k="29" />
    <hkern u1="&#x2f;" u2="w" k="31" />
    <hkern u1="&#x2f;" u2="u" k="94" />
    <hkern u1="&#x2f;" u2="t" k="27" />
    <hkern u1="&#x2f;" u2="s" k="123" />
    <hkern u1="&#x2f;" u2="r" k="92" />
    <hkern u1="&#x2f;" u2="q" k="135" />
    <hkern u1="&#x2f;" u2="p" k="92" />
    <hkern u1="&#x2f;" u2="o" k="135" />
    <hkern u1="&#x2f;" u2="n" k="92" />
    <hkern u1="&#x2f;" u2="m" k="92" />
    <hkern u1="&#x2f;" u2="g" k="135" />
    <hkern u1="&#x2f;" u2="f" k="45" />
    <hkern u1="&#x2f;" u2="e" k="135" />
    <hkern u1="&#x2f;" u2="d" k="135" />
    <hkern u1="&#x2f;" u2="c" k="135" />
    <hkern u1="&#x2f;" u2="a" k="176" />
    <hkern u1="&#x2f;" u2="[" k="53" />
    <hkern u1="&#x2f;" u2="Y" k="-12" />
    <hkern u1="&#x2f;" u2="W" k="-12" />
    <hkern u1="&#x2f;" u2="T" k="-12" />
    <hkern u1="&#x2f;" u2="S" k="63" />
    <hkern u1="&#x2f;" u2="Q" k="70" />
    <hkern u1="&#x2f;" u2="O" k="70" />
    <hkern u1="&#x2f;" u2="J" k="156" />
    <hkern u1="&#x2f;" u2="G" k="70" />
    <hkern u1="&#x2f;" u2="C" k="70" />
    <hkern u1="&#x2f;" u2="A" k="170" />
    <hkern u1="&#x2f;" u2="&#x3b;" k="170" />
    <hkern u1="&#x2f;" u2="&#x3a;" k="170" />
    <hkern u1="&#x2f;" u2="&#x2e;" k="279" />
    <hkern u1="&#x2f;" u2="&#x2d;" k="158" />
    <hkern u1="&#x2f;" u2="&#x2c;" k="225" />
    <hkern u1="&#x2f;" u2="&#x28;" k="53" />
    <hkern u1="&#x2f;" u2="&#x25;" k="25" />
    <hkern u1="&#x2f;" u2="&#x2122;" k="-29" />
    <hkern u1="&#x2f;" u2="&#x20ac;" k="84" />
    <hkern u1="&#x2f;" u2="&#xbf;" k="143" />
    <hkern u1="&#x2f;" u2="&#xae;" k="53" />
    <hkern u1="&#x2f;" u2="&#xa5;" k="14" />
    <hkern u1="&#x2f;" u2="&#xa3;" k="125" />
    <hkern u1="&#x2f;" u2="&#xa2;" k="76" />
    <hkern u1="&#x2f;" u2="&#xa1;" k="20" />
    <hkern u1="&#x2f;" u2="x" k="51" />
    <hkern u1="&#x2f;" u2="v" k="23" />
    <hkern u1="&#x2f;" u2="V" k="-6" />
    <hkern u1="&#x2f;" u2="&#x40;" k="143" />
    <hkern u1="&#x2f;" u2="&#x3f;" k="35" />
    <hkern u1="&#x2f;" u2="&#x39;" k="45" />
    <hkern u1="&#x2f;" u2="&#x38;" k="70" />
    <hkern u1="&#x2f;" u2="&#x36;" k="127" />
    <hkern u1="&#x2f;" u2="&#x35;" k="72" />
    <hkern u1="&#x2f;" u2="&#x34;" k="111" />
    <hkern u1="&#x2f;" u2="&#x33;" k="55" />
    <hkern u1="&#x2f;" u2="&#x32;" k="57" />
    <hkern u1="&#x2f;" u2="&#x31;" k="10" />
    <hkern u1="&#x2f;" u2="&#x30;" k="59" />
    <hkern u1="&#x2f;" u2="&#x2a;" k="10" />
    <hkern u1="&#x2f;" u2="&#x26;" k="133" />
    <hkern u1="&#x2f;" u2="&#x24;" k="57" />
    <hkern u1="&#x2f;" u2="&#x23;" k="92" />
    <hkern u1="&#x30;" u2="&#x2026;" k="10" />
    <hkern u1="&#x30;" u2="&#x201e;" k="20" />
    <hkern u1="&#x30;" u2="&#x201a;" k="20" />
    <hkern u1="&#x30;" u2="&#x7d;" k="37" />
    <hkern u1="&#x30;" u2="]" k="37" />
    <hkern u1="&#x30;" u2="&#x2e;" k="10" />
    <hkern u1="&#x30;" u2="&#x2c;" k="20" />
    <hkern u1="&#x30;" u2="&#x29;" k="37" />
    <hkern u1="&#x30;" u2="&#x2122;" k="27" />
    <hkern u1="&#x30;" u2="\" k="27" />
    <hkern u1="&#x30;" u2="&#x37;" k="6" />
    <hkern u1="&#x30;" u2="&#x2f;" k="47" />
    <hkern u1="&#x32;" u2="&#x203a;" k="10" />
    <hkern u1="&#x32;" u2="&#x2039;" k="10" />
    <hkern u1="&#x32;" u2="&#x201e;" k="10" />
    <hkern u1="&#x32;" u2="&#x201d;" k="20" />
    <hkern u1="&#x32;" u2="&#x201c;" k="20" />
    <hkern u1="&#x32;" u2="&#x201a;" k="10" />
    <hkern u1="&#x32;" u2="&#x2019;" k="20" />
    <hkern u1="&#x32;" u2="&#x2018;" k="20" />
    <hkern u1="&#x32;" u2="&#x2014;" k="25" />
    <hkern u1="&#x32;" u2="&#x2013;" k="25" />
    <hkern u1="&#x32;" u2="&#x149;" k="20" />
    <hkern u1="&#x32;" u2="&#xbb;" k="10" />
    <hkern u1="&#x32;" u2="&#xab;" k="10" />
    <hkern u1="&#x32;" u2="&#x7d;" k="18" />
    <hkern u1="&#x32;" u2="]" k="18" />
    <hkern u1="&#x32;" u2="&#x2d;" k="25" />
    <hkern u1="&#x32;" u2="&#x2c;" k="10" />
    <hkern u1="&#x32;" u2="&#x29;" k="18" />
    <hkern u1="&#x32;" u2="&#x2122;" k="20" />
    <hkern u1="&#x32;" u2="&#xa3;" k="4" />
    <hkern u1="&#x32;" u2="&#xa2;" k="10" />
    <hkern u1="&#x32;" u2="\" k="51" />
    <hkern u1="&#x32;" u2="&#x39;" k="10" />
    <hkern u1="&#x32;" u2="&#x37;" k="6" />
    <hkern u1="&#x32;" u2="&#x36;" k="31" />
    <hkern u1="&#x32;" u2="&#x35;" k="20" />
    <hkern u1="&#x32;" u2="&#x34;" k="25" />
    <hkern u1="&#x32;" u2="&#x33;" k="12" />
    <hkern u1="&#x32;" u2="&#x30;" k="6" />
    <hkern u1="&#x32;" u2="&#x2f;" k="47" />
    <hkern u1="&#x32;" u2="&#x2a;" k="10" />
    <hkern u1="&#x33;" u2="&#x203a;" k="33" />
    <hkern u1="&#x33;" u2="&#x2030;" k="25" />
    <hkern u1="&#x33;" u2="&#x201e;" k="31" />
    <hkern u1="&#x33;" u2="&#x201d;" k="51" />
    <hkern u1="&#x33;" u2="&#x201c;" k="57" />
    <hkern u1="&#x33;" u2="&#x201a;" k="31" />
    <hkern u1="&#x33;" u2="&#x2019;" k="51" />
    <hkern u1="&#x33;" u2="&#x2018;" k="57" />
    <hkern u1="&#x33;" u2="&#x2014;" k="14" />
    <hkern u1="&#x33;" u2="&#x2013;" k="14" />
    <hkern u1="&#x33;" u2="&#x149;" k="51" />
    <hkern u1="&#x33;" u2="&#xbb;" k="33" />
    <hkern u1="&#x33;" u2="&#x7d;" k="37" />
    <hkern u1="&#x33;" u2="]" k="37" />
    <hkern u1="&#x33;" u2="&#x3b;" k="20" />
    <hkern u1="&#x33;" u2="&#x3a;" k="20" />
    <hkern u1="&#x33;" u2="&#x2d;" k="14" />
    <hkern u1="&#x33;" u2="&#x2c;" k="31" />
    <hkern u1="&#x33;" u2="&#x29;" k="37" />
    <hkern u1="&#x33;" u2="&#x25;" k="25" />
    <hkern u1="&#x33;" u2="&#x2122;" k="27" />
    <hkern u1="&#x33;" u2="&#xae;" k="31" />
    <hkern u1="&#x33;" u2="\" k="31" />
    <hkern u1="&#x33;" u2="&#x3f;" k="20" />
    <hkern u1="&#x33;" u2="&#x39;" k="31" />
    <hkern u1="&#x33;" u2="&#x37;" k="20" />
    <hkern u1="&#x33;" u2="&#x36;" k="10" />
    <hkern u1="&#x33;" u2="&#x32;" k="10" />
    <hkern u1="&#x33;" u2="&#x31;" k="10" />
    <hkern u1="&#x33;" u2="&#x2f;" k="80" />
    <hkern u1="&#x33;" u2="&#x2a;" k="27" />
    <hkern u1="&#x34;" u2="&#x203a;" k="16" />
    <hkern u1="&#x34;" u2="&#x2039;" k="-33" />
    <hkern u1="&#x34;" u2="&#x2030;" k="45" />
    <hkern u1="&#x34;" u2="&#x2026;" k="6" />
    <hkern u1="&#x34;" u2="&#x201e;" k="4" />
    <hkern u1="&#x34;" u2="&#x201d;" k="145" />
    <hkern u1="&#x34;" u2="&#x201c;" k="131" />
    <hkern u1="&#x34;" u2="&#x201a;" k="4" />
    <hkern u1="&#x34;" u2="&#x2019;" k="145" />
    <hkern u1="&#x34;" u2="&#x2018;" k="131" />
    <hkern u1="&#x34;" u2="&#x2014;" k="8" />
    <hkern u1="&#x34;" u2="&#x2013;" k="8" />
    <hkern u1="&#x34;" u2="&#x149;" k="145" />
    <hkern u1="&#x34;" u2="&#xbb;" k="16" />
    <hkern u1="&#x34;" u2="&#xba;" k="16" />
    <hkern u1="&#x34;" u2="&#xab;" k="-33" />
    <hkern u1="&#x34;" u2="&#xaa;" k="16" />
    <hkern u1="&#x34;" u2="&#x7d;" k="37" />
    <hkern u1="&#x34;" u2="]" k="37" />
    <hkern u1="&#x34;" u2="&#x2e;" k="6" />
    <hkern u1="&#x34;" u2="&#x2d;" k="8" />
    <hkern u1="&#x34;" u2="&#x2c;" k="4" />
    <hkern u1="&#x34;" u2="&#x29;" k="37" />
    <hkern u1="&#x34;" u2="&#x25;" k="45" />
    <hkern u1="&#x34;" u2="&#x2122;" k="170" />
    <hkern u1="&#x34;" u2="&#xae;" k="111" />
    <hkern u1="&#x34;" u2="\" k="102" />
    <hkern u1="&#x34;" u2="&#x3f;" k="72" />
    <hkern u1="&#x34;" u2="&#x39;" k="61" />
    <hkern u1="&#x34;" u2="&#x37;" k="39" />
    <hkern u1="&#x34;" u2="&#x35;" k="12" />
    <hkern u1="&#x34;" u2="&#x31;" k="68" />
    <hkern u1="&#x34;" u2="&#x2f;" k="33" />
    <hkern u1="&#x34;" u2="&#x2a;" k="98" />
    <hkern u1="&#x35;" u2="&#x203a;" k="31" />
    <hkern u1="&#x35;" u2="&#x2039;" k="-16" />
    <hkern u1="&#x35;" u2="&#x2030;" k="25" />
    <hkern u1="&#x35;" u2="&#x2026;" k="14" />
    <hkern u1="&#x35;" u2="&#x201e;" k="31" />
    <hkern u1="&#x35;" u2="&#x201d;" k="70" />
    <hkern u1="&#x35;" u2="&#x201c;" k="51" />
    <hkern u1="&#x35;" u2="&#x201a;" k="31" />
    <hkern u1="&#x35;" u2="&#x2019;" k="70" />
    <hkern u1="&#x35;" u2="&#x2018;" k="51" />
    <hkern u1="&#x35;" u2="&#x149;" k="70" />
    <hkern u1="&#x35;" u2="&#xbb;" k="31" />
    <hkern u1="&#x35;" u2="&#xab;" k="-16" />
    <hkern u1="&#x35;" u2="&#x7d;" k="43" />
    <hkern u1="&#x35;" u2="]" k="43" />
    <hkern u1="&#x35;" u2="&#x3b;" k="14" />
    <hkern u1="&#x35;" u2="&#x3a;" k="14" />
    <hkern u1="&#x35;" u2="&#x2e;" k="14" />
    <hkern u1="&#x35;" u2="&#x2c;" k="31" />
    <hkern u1="&#x35;" u2="&#x29;" k="43" />
    <hkern u1="&#x35;" u2="&#x25;" k="25" />
    <hkern u1="&#x35;" u2="&#x2122;" k="53" />
    <hkern u1="&#x35;" u2="&#xae;" k="61" />
    <hkern u1="&#x35;" u2="\" k="47" />
    <hkern u1="&#x35;" u2="&#x3f;" k="31" />
    <hkern u1="&#x35;" u2="&#x39;" k="41" />
    <hkern u1="&#x35;" u2="&#x37;" k="78" />
    <hkern u1="&#x35;" u2="&#x36;" k="10" />
    <hkern u1="&#x35;" u2="&#x35;" k="10" />
    <hkern u1="&#x35;" u2="&#x33;" k="10" />
    <hkern u1="&#x35;" u2="&#x32;" k="20" />
    <hkern u1="&#x35;" u2="&#x31;" k="53" />
    <hkern u1="&#x35;" u2="&#x2f;" k="74" />
    <hkern u1="&#x35;" u2="&#x2a;" k="41" />
    <hkern u1="&#x35;" u2="&#x21;" k="10" />
    <hkern u1="&#x36;" u2="&#x203a;" k="31" />
    <hkern u1="&#x36;" u2="&#x2039;" k="-29" />
    <hkern u1="&#x36;" u2="&#x2030;" k="8" />
    <hkern u1="&#x36;" u2="&#x2026;" k="10" />
    <hkern u1="&#x36;" u2="&#x201e;" k="35" />
    <hkern u1="&#x36;" u2="&#x201d;" k="94" />
    <hkern u1="&#x36;" u2="&#x201c;" k="82" />
    <hkern u1="&#x36;" u2="&#x201a;" k="35" />
    <hkern u1="&#x36;" u2="&#x2019;" k="94" />
    <hkern u1="&#x36;" u2="&#x2018;" k="82" />
    <hkern u1="&#x36;" u2="&#x149;" k="94" />
    <hkern u1="&#x36;" u2="&#xbb;" k="31" />
    <hkern u1="&#x36;" u2="&#xab;" k="-29" />
    <hkern u1="&#x36;" u2="&#x7d;" k="53" />
    <hkern u1="&#x36;" u2="]" k="53" />
    <hkern u1="&#x36;" u2="&#x2e;" k="10" />
    <hkern u1="&#x36;" u2="&#x2c;" k="35" />
    <hkern u1="&#x36;" u2="&#x29;" k="53" />
    <hkern u1="&#x36;" u2="&#x25;" k="8" />
    <hkern u1="&#x36;" u2="&#x2122;" k="98" />
    <hkern u1="&#x36;" u2="&#xae;" k="61" />
    <hkern u1="&#x36;" u2="\" k="80" />
    <hkern u1="&#x36;" u2="&#x3f;" k="51" />
    <hkern u1="&#x36;" u2="&#x39;" k="41" />
    <hkern u1="&#x36;" u2="&#x37;" k="35" />
    <hkern u1="&#x36;" u2="&#x32;" k="10" />
    <hkern u1="&#x36;" u2="&#x31;" k="53" />
    <hkern u1="&#x36;" u2="&#x2f;" k="63" />
    <hkern u1="&#x36;" u2="&#x2a;" k="45" />
    <hkern u1="&#x36;" u2="&#x21;" k="6" />
    <hkern u1="&#x37;" u2="&#x2117;" k="51" />
    <hkern u1="&#x37;" u2="&#x203a;" k="41" />
    <hkern u1="&#x37;" u2="&#x2039;" k="115" />
    <hkern u1="&#x37;" u2="&#x2026;" k="287" />
    <hkern u1="&#x37;" u2="&#x201e;" k="266" />
    <hkern u1="&#x37;" u2="&#x201d;" k="4" />
    <hkern u1="&#x37;" u2="&#x201c;" k="6" />
    <hkern u1="&#x37;" u2="&#x201a;" k="266" />
    <hkern u1="&#x37;" u2="&#x2019;" k="4" />
    <hkern u1="&#x37;" u2="&#x2018;" k="6" />
    <hkern u1="&#x37;" u2="&#x2014;" k="115" />
    <hkern u1="&#x37;" u2="&#x2013;" k="115" />
    <hkern u1="&#x37;" u2="&#x149;" k="4" />
    <hkern u1="&#x37;" u2="&#xbb;" k="41" />
    <hkern u1="&#x37;" u2="&#xba;" k="10" />
    <hkern u1="&#x37;" u2="&#xab;" k="115" />
    <hkern u1="&#x37;" u2="&#xaa;" k="10" />
    <hkern u1="&#x37;" u2="&#xa9;" k="51" />
    <hkern u1="&#x37;" u2="&#x7d;" k="10" />
    <hkern u1="&#x37;" u2="]" k="10" />
    <hkern u1="&#x37;" u2="&#x3b;" k="143" />
    <hkern u1="&#x37;" u2="&#x3a;" k="143" />
    <hkern u1="&#x37;" u2="&#x2e;" k="287" />
    <hkern u1="&#x37;" u2="&#x2d;" k="115" />
    <hkern u1="&#x37;" u2="&#x2c;" k="266" />
    <hkern u1="&#x37;" u2="&#x29;" k="10" />
    <hkern u1="&#x37;" u2="&#x2122;" k="-37" />
    <hkern u1="&#x37;" u2="&#x20ac;" k="25" />
    <hkern u1="&#x37;" u2="&#xbf;" k="131" />
    <hkern u1="&#x37;" u2="&#xae;" k="16" />
    <hkern u1="&#x37;" u2="&#xa3;" k="72" />
    <hkern u1="&#x37;" u2="&#xa2;" k="61" />
    <hkern u1="&#x37;" u2="&#x40;" k="86" />
    <hkern u1="&#x37;" u2="&#x39;" k="20" />
    <hkern u1="&#x37;" u2="&#x38;" k="68" />
    <hkern u1="&#x37;" u2="&#x37;" k="16" />
    <hkern u1="&#x37;" u2="&#x36;" k="123" />
    <hkern u1="&#x37;" u2="&#x35;" k="61" />
    <hkern u1="&#x37;" u2="&#x34;" k="117" />
    <hkern u1="&#x37;" u2="&#x33;" k="39" />
    <hkern u1="&#x37;" u2="&#x32;" k="45" />
    <hkern u1="&#x37;" u2="&#x30;" k="45" />
    <hkern u1="&#x37;" u2="&#x2f;" k="207" />
    <hkern u1="&#x37;" u2="&#x26;" k="72" />
    <hkern u1="&#x37;" u2="&#x23;" k="25" />
    <hkern u1="&#x38;" u2="&#x203a;" k="12" />
    <hkern u1="&#x38;" u2="&#x201e;" k="20" />
    <hkern u1="&#x38;" u2="&#x201d;" k="61" />
    <hkern u1="&#x38;" u2="&#x201c;" k="45" />
    <hkern u1="&#x38;" u2="&#x201a;" k="20" />
    <hkern u1="&#x38;" u2="&#x2019;" k="61" />
    <hkern u1="&#x38;" u2="&#x2018;" k="45" />
    <hkern u1="&#x38;" u2="&#x149;" k="61" />
    <hkern u1="&#x38;" u2="&#xbb;" k="12" />
    <hkern u1="&#x38;" u2="&#x7d;" k="47" />
    <hkern u1="&#x38;" u2="]" k="47" />
    <hkern u1="&#x38;" u2="&#x2c;" k="20" />
    <hkern u1="&#x38;" u2="&#x29;" k="47" />
    <hkern u1="&#x38;" u2="&#x2122;" k="51" />
    <hkern u1="&#x38;" u2="&#xae;" k="25" />
    <hkern u1="&#x38;" u2="\" k="70" />
    <hkern u1="&#x38;" u2="&#x3f;" k="31" />
    <hkern u1="&#x38;" u2="&#x39;" k="20" />
    <hkern u1="&#x38;" u2="&#x37;" k="25" />
    <hkern u1="&#x38;" u2="&#x32;" k="6" />
    <hkern u1="&#x38;" u2="&#x31;" k="41" />
    <hkern u1="&#x38;" u2="&#x2f;" k="59" />
    <hkern u1="&#x38;" u2="&#x2a;" k="41" />
    <hkern u1="&#x39;" u2="&#x203a;" k="10" />
    <hkern u1="&#x39;" u2="&#x2039;" k="31" />
    <hkern u1="&#x39;" u2="&#x2026;" k="152" />
    <hkern u1="&#x39;" u2="&#x201e;" k="152" />
    <hkern u1="&#x39;" u2="&#x201c;" k="-20" />
    <hkern u1="&#x39;" u2="&#x201a;" k="152" />
    <hkern u1="&#x39;" u2="&#x2018;" k="-20" />
    <hkern u1="&#x39;" u2="&#x2014;" k="23" />
    <hkern u1="&#x39;" u2="&#x2013;" k="23" />
    <hkern u1="&#x39;" u2="&#xbb;" k="10" />
    <hkern u1="&#x39;" u2="&#xab;" k="31" />
    <hkern u1="&#x39;" u2="&#x7d;" k="47" />
    <hkern u1="&#x39;" u2="]" k="47" />
    <hkern u1="&#x39;" u2="&#x3b;" k="20" />
    <hkern u1="&#x39;" u2="&#x3a;" k="20" />
    <hkern u1="&#x39;" u2="&#x2e;" k="152" />
    <hkern u1="&#x39;" u2="&#x2d;" k="23" />
    <hkern u1="&#x39;" u2="&#x2c;" k="152" />
    <hkern u1="&#x39;" u2="&#x29;" k="47" />
    <hkern u1="&#x39;" u2="&#x2122;" k="10" />
    <hkern u1="&#x39;" u2="&#xbf;" k="45" />
    <hkern u1="&#x39;" u2="&#xa3;" k="20" />
    <hkern u1="&#x39;" u2="&#xa1;" k="10" />
    <hkern u1="&#x39;" u2="\" k="37" />
    <hkern u1="&#x39;" u2="&#x38;" k="16" />
    <hkern u1="&#x39;" u2="&#x37;" k="33" />
    <hkern u1="&#x39;" u2="&#x36;" k="51" />
    <hkern u1="&#x39;" u2="&#x35;" k="31" />
    <hkern u1="&#x39;" u2="&#x34;" k="45" />
    <hkern u1="&#x39;" u2="&#x33;" k="25" />
    <hkern u1="&#x39;" u2="&#x32;" k="16" />
    <hkern u1="&#x39;" u2="&#x2f;" k="143" />
    <hkern u1="&#x39;" u2="&#x26;" k="20" />
    <hkern u1="&#x3a;" u2="&#xa5;" k="45" />
    <hkern u1="&#x3a;" u2="&#xa2;" k="10" />
    <hkern u1="&#x3a;" u2="x" k="6" />
    <hkern u1="&#x3a;" u2="v" k="31" />
    <hkern u1="&#x3a;" u2="\" k="102" />
    <hkern u1="&#x3a;" u2="X" k="20" />
    <hkern u1="&#x3a;" u2="V" k="143" />
    <hkern u1="&#x3a;" u2="&#x3f;" k="18" />
    <hkern u1="&#x3a;" u2="&#x39;" k="10" />
    <hkern u1="&#x3a;" u2="&#x37;" k="41" />
    <hkern u1="&#x3a;" u2="&#x36;" k="14" />
    <hkern u1="&#x3a;" u2="&#x35;" k="14" />
    <hkern u1="&#x3a;" u2="&#x31;" k="31" />
    <hkern u1="&#x3a;" u2="&#x2f;" k="43" />
    <hkern u1="&#x3a;" u2="&#x2a;" k="27" />
    <hkern u1="&#x3b;" u2="&#xa5;" k="45" />
    <hkern u1="&#x3b;" u2="&#xa2;" k="10" />
    <hkern u1="&#x3b;" u2="x" k="6" />
    <hkern u1="&#x3b;" u2="v" k="31" />
    <hkern u1="&#x3b;" u2="\" k="102" />
    <hkern u1="&#x3b;" u2="X" k="20" />
    <hkern u1="&#x3b;" u2="V" k="143" />
    <hkern u1="&#x3b;" u2="&#x3f;" k="18" />
    <hkern u1="&#x3b;" u2="&#x39;" k="10" />
    <hkern u1="&#x3b;" u2="&#x37;" k="41" />
    <hkern u1="&#x3b;" u2="&#x36;" k="14" />
    <hkern u1="&#x3b;" u2="&#x35;" k="14" />
    <hkern u1="&#x3b;" u2="&#x31;" k="31" />
    <hkern u1="&#x3b;" u2="&#x2f;" k="43" />
    <hkern u1="&#x3b;" u2="&#x2a;" k="27" />
    <hkern u1="&#x3f;" g2="Jcircumflex.salt" k="47" />
    <hkern u1="&#x3f;" g2="J.salt" k="47" />
    <hkern u1="&#x3f;" u2="&#xfb02;" k="-37" />
    <hkern u1="&#x3f;" u2="&#xfb01;" k="-37" />
    <hkern u1="&#x3f;" u2="&#x2117;" k="4" />
    <hkern u1="&#x3f;" u2="&#x2039;" k="37" />
    <hkern u1="&#x3f;" u2="&#x2026;" k="143" />
    <hkern u1="&#x3f;" u2="&#x201e;" k="164" />
    <hkern u1="&#x3f;" u2="&#x201a;" k="164" />
    <hkern u1="&#x3f;" u2="&#x2014;" k="49" />
    <hkern u1="&#x3f;" u2="&#x2013;" k="49" />
    <hkern u1="&#x3f;" u2="&#x1ef3;" k="-29" />
    <hkern u1="&#x3f;" u2="&#x1e85;" k="-31" />
    <hkern u1="&#x3f;" u2="&#x1e83;" k="-31" />
    <hkern u1="&#x3f;" u2="&#x1e81;" k="-31" />
    <hkern u1="&#x3f;" u2="&#x219;" k="20" />
    <hkern u1="&#x3f;" u2="&#x1ff;" k="20" />
    <hkern u1="&#x3f;" u2="&#x1fd;" k="80" />
    <hkern u1="&#x3f;" u2="&#x1fb;" k="80" />
    <hkern u1="&#x3f;" u2="&#x1fa;" k="119" />
    <hkern u1="&#x3f;" u2="&#x17d;" k="18" />
    <hkern u1="&#x3f;" u2="&#x17b;" k="18" />
    <hkern u1="&#x3f;" u2="&#x179;" k="18" />
    <hkern u1="&#x3f;" u2="&#x177;" k="-29" />
    <hkern u1="&#x3f;" u2="&#x175;" k="-31" />
    <hkern u1="&#x3f;" u2="&#x164;" k="-23" />
    <hkern u1="&#x3f;" u2="&#x162;" k="-23" />
    <hkern u1="&#x3f;" u2="&#x161;" k="20" />
    <hkern u1="&#x3f;" u2="&#x15f;" k="20" />
    <hkern u1="&#x3f;" u2="&#x15d;" k="20" />
    <hkern u1="&#x3f;" u2="&#x15b;" k="20" />
    <hkern u1="&#x3f;" u2="&#x151;" k="20" />
    <hkern u1="&#x3f;" u2="&#x14f;" k="20" />
    <hkern u1="&#x3f;" u2="&#x14d;" k="20" />
    <hkern u1="&#x3f;" u2="&#x134;" k="135" />
    <hkern u1="&#x3f;" u2="&#x123;" k="20" />
    <hkern u1="&#x3f;" u2="&#x121;" k="20" />
    <hkern u1="&#x3f;" u2="&#x11f;" k="20" />
    <hkern u1="&#x3f;" u2="&#x11d;" k="20" />
    <hkern u1="&#x3f;" u2="&#x11b;" k="20" />
    <hkern u1="&#x3f;" u2="&#x119;" k="20" />
    <hkern u1="&#x3f;" u2="&#x117;" k="20" />
    <hkern u1="&#x3f;" u2="&#x115;" k="20" />
    <hkern u1="&#x3f;" u2="&#x113;" k="20" />
    <hkern u1="&#x3f;" u2="&#x111;" k="20" />
    <hkern u1="&#x3f;" u2="&#x10f;" k="20" />
    <hkern u1="&#x3f;" u2="&#x10d;" k="20" />
    <hkern u1="&#x3f;" u2="&#x10b;" k="20" />
    <hkern u1="&#x3f;" u2="&#x109;" k="20" />
    <hkern u1="&#x3f;" u2="&#x107;" k="20" />
    <hkern u1="&#x3f;" u2="&#x105;" k="80" />
    <hkern u1="&#x3f;" u2="&#x104;" k="119" />
    <hkern u1="&#x3f;" u2="&#x103;" k="80" />
    <hkern u1="&#x3f;" u2="&#x102;" k="119" />
    <hkern u1="&#x3f;" u2="&#x101;" k="80" />
    <hkern u1="&#x3f;" u2="&#x100;" k="119" />
    <hkern u1="&#x3f;" u2="&#xff;" k="-29" />
    <hkern u1="&#x3f;" u2="&#xfd;" k="-29" />
    <hkern u1="&#x3f;" u2="&#xf8;" k="20" />
    <hkern u1="&#x3f;" u2="&#xf6;" k="20" />
    <hkern u1="&#x3f;" u2="&#xf5;" k="20" />
    <hkern u1="&#x3f;" u2="&#xf4;" k="20" />
    <hkern u1="&#x3f;" u2="&#xf3;" k="20" />
    <hkern u1="&#x3f;" u2="&#xf2;" k="20" />
    <hkern u1="&#x3f;" u2="&#xeb;" k="20" />
    <hkern u1="&#x3f;" u2="&#xea;" k="20" />
    <hkern u1="&#x3f;" u2="&#xe9;" k="20" />
    <hkern u1="&#x3f;" u2="&#xe8;" k="20" />
    <hkern u1="&#x3f;" u2="&#xe7;" k="20" />
    <hkern u1="&#x3f;" u2="&#xe6;" k="80" />
    <hkern u1="&#x3f;" u2="&#xe5;" k="80" />
    <hkern u1="&#x3f;" u2="&#xe4;" k="80" />
    <hkern u1="&#x3f;" u2="&#xe3;" k="80" />
    <hkern u1="&#x3f;" u2="&#xe2;" k="80" />
    <hkern u1="&#x3f;" u2="&#xe1;" k="80" />
    <hkern u1="&#x3f;" u2="&#xe0;" k="80" />
    <hkern u1="&#x3f;" u2="&#xc5;" k="119" />
    <hkern u1="&#x3f;" u2="&#xc4;" k="119" />
    <hkern u1="&#x3f;" u2="&#xc3;" k="119" />
    <hkern u1="&#x3f;" u2="&#xc2;" k="119" />
    <hkern u1="&#x3f;" u2="&#xc1;" k="119" />
    <hkern u1="&#x3f;" u2="&#xc0;" k="119" />
    <hkern u1="&#x3f;" u2="&#xab;" k="37" />
    <hkern u1="&#x3f;" u2="&#xa9;" k="4" />
    <hkern u1="&#x3f;" u2="&#x7d;" k="23" />
    <hkern u1="&#x3f;" u2="y" k="-29" />
    <hkern u1="&#x3f;" u2="w" k="-31" />
    <hkern u1="&#x3f;" u2="s" k="20" />
    <hkern u1="&#x3f;" u2="q" k="20" />
    <hkern u1="&#x3f;" u2="o" k="20" />
    <hkern u1="&#x3f;" u2="g" k="20" />
    <hkern u1="&#x3f;" u2="f" k="-37" />
    <hkern u1="&#x3f;" u2="e" k="20" />
    <hkern u1="&#x3f;" u2="d" k="20" />
    <hkern u1="&#x3f;" u2="c" k="20" />
    <hkern u1="&#x3f;" u2="a" k="80" />
    <hkern u1="&#x3f;" u2="]" k="23" />
    <hkern u1="&#x3f;" u2="Z" k="18" />
    <hkern u1="&#x3f;" u2="T" k="-23" />
    <hkern u1="&#x3f;" u2="J" k="135" />
    <hkern u1="&#x3f;" u2="A" k="119" />
    <hkern u1="&#x3f;" u2="&#x3b;" k="20" />
    <hkern u1="&#x3f;" u2="&#x3a;" k="20" />
    <hkern u1="&#x3f;" u2="&#x2e;" k="143" />
    <hkern u1="&#x3f;" u2="&#x2d;" k="49" />
    <hkern u1="&#x3f;" u2="&#x2c;" k="164" />
    <hkern u1="&#x3f;" u2="&#x29;" k="23" />
    <hkern u1="&#x3f;" u2="&#xae;" k="-12" />
    <hkern u1="&#x3f;" u2="&#xa3;" k="25" />
    <hkern u1="&#x3f;" u2="v" k="-33" />
    <hkern u1="&#x3f;" u2="\" k="18" />
    <hkern u1="&#x3f;" u2="X" k="37" />
    <hkern u1="&#x3f;" u2="V" k="12" />
    <hkern u1="&#x3f;" u2="&#x40;" k="20" />
    <hkern u1="&#x3f;" u2="&#x38;" k="20" />
    <hkern u1="&#x3f;" u2="&#x36;" k="31" />
    <hkern u1="&#x3f;" u2="&#x35;" k="20" />
    <hkern u1="&#x3f;" u2="&#x34;" k="51" />
    <hkern u1="&#x3f;" u2="&#x33;" k="20" />
    <hkern u1="&#x3f;" u2="&#x31;" k="-41" />
    <hkern u1="&#x3f;" u2="&#x2f;" k="170" />
    <hkern u1="&#x3f;" u2="&#x2a;" k="-31" />
    <hkern u1="&#x3f;" u2="&#x26;" k="37" />
    <hkern u1="&#x40;" g2="Jcircumflex.salt" k="10" />
    <hkern u1="&#x40;" g2="J.salt" k="10" />
    <hkern u1="&#x40;" u2="&#x2026;" k="45" />
    <hkern u1="&#x40;" u2="&#x201e;" k="61" />
    <hkern u1="&#x40;" u2="&#x201a;" k="61" />
    <hkern u1="&#x40;" u2="&#x1ef2;" k="106" />
    <hkern u1="&#x40;" u2="&#x1e84;" k="37" />
    <hkern u1="&#x40;" u2="&#x1e82;" k="37" />
    <hkern u1="&#x40;" u2="&#x1e80;" k="37" />
    <hkern u1="&#x40;" u2="&#x1fd;" k="10" />
    <hkern u1="&#x40;" u2="&#x1fb;" k="10" />
    <hkern u1="&#x40;" u2="&#x1fa;" k="80" />
    <hkern u1="&#x40;" u2="&#x178;" k="106" />
    <hkern u1="&#x40;" u2="&#x176;" k="106" />
    <hkern u1="&#x40;" u2="&#x174;" k="37" />
    <hkern u1="&#x40;" u2="&#x164;" k="20" />
    <hkern u1="&#x40;" u2="&#x162;" k="20" />
    <hkern u1="&#x40;" u2="&#x134;" k="29" />
    <hkern u1="&#x40;" u2="&#x105;" k="10" />
    <hkern u1="&#x40;" u2="&#x104;" k="80" />
    <hkern u1="&#x40;" u2="&#x103;" k="10" />
    <hkern u1="&#x40;" u2="&#x102;" k="80" />
    <hkern u1="&#x40;" u2="&#x101;" k="10" />
    <hkern u1="&#x40;" u2="&#x100;" k="80" />
    <hkern u1="&#x40;" u2="&#xe6;" k="10" />
    <hkern u1="&#x40;" u2="&#xe5;" k="10" />
    <hkern u1="&#x40;" u2="&#xe4;" k="10" />
    <hkern u1="&#x40;" u2="&#xe3;" k="10" />
    <hkern u1="&#x40;" u2="&#xe2;" k="10" />
    <hkern u1="&#x40;" u2="&#xe1;" k="10" />
    <hkern u1="&#x40;" u2="&#xe0;" k="10" />
    <hkern u1="&#x40;" u2="&#xdd;" k="106" />
    <hkern u1="&#x40;" u2="&#xc5;" k="80" />
    <hkern u1="&#x40;" u2="&#xc4;" k="80" />
    <hkern u1="&#x40;" u2="&#xc3;" k="80" />
    <hkern u1="&#x40;" u2="&#xc2;" k="80" />
    <hkern u1="&#x40;" u2="&#xc1;" k="80" />
    <hkern u1="&#x40;" u2="&#xc0;" k="80" />
    <hkern u1="&#x40;" u2="&#x7d;" k="53" />
    <hkern u1="&#x40;" u2="a" k="10" />
    <hkern u1="&#x40;" u2="]" k="53" />
    <hkern u1="&#x40;" u2="Y" k="106" />
    <hkern u1="&#x40;" u2="W" k="37" />
    <hkern u1="&#x40;" u2="T" k="20" />
    <hkern u1="&#x40;" u2="J" k="29" />
    <hkern u1="&#x40;" u2="A" k="80" />
    <hkern u1="&#x40;" u2="&#x3b;" k="20" />
    <hkern u1="&#x40;" u2="&#x3a;" k="20" />
    <hkern u1="&#x40;" u2="&#x2e;" k="45" />
    <hkern u1="&#x40;" u2="&#x2c;" k="61" />
    <hkern u1="&#x40;" u2="&#x29;" k="53" />
    <hkern u1="&#x40;" u2="&#x2122;" k="16" />
    <hkern u1="&#x40;" u2="x" k="10" />
    <hkern u1="&#x40;" u2="\" k="84" />
    <hkern u1="&#x40;" u2="X" k="76" />
    <hkern u1="&#x40;" u2="V" k="53" />
    <hkern u1="&#x40;" u2="&#x37;" k="27" />
    <hkern u1="&#x40;" u2="&#x36;" k="10" />
    <hkern u1="&#x40;" u2="&#x34;" k="6" />
    <hkern u1="&#x40;" u2="&#x2f;" k="133" />
    <hkern u1="&#x40;" u2="&#x26;" k="14" />
    <hkern u1="A" u2="&#x2122;" k="225" />
    <hkern u1="A" u2="&#xae;" k="195" />
    <hkern u1="A" u2="x" k="-20" />
    <hkern u1="A" u2="v" k="102" />
    <hkern u1="A" u2="\" k="154" />
    <hkern u1="A" u2="X" k="-31" />
    <hkern u1="A" u2="V" k="195" />
    <hkern u1="A" u2="&#x40;" k="10" />
    <hkern u1="A" u2="&#x3f;" k="131" />
    <hkern u1="A" u2="&#x2a;" k="215" />
    <hkern u1="A" u2="&#x26;" k="20" />
    <hkern u1="B" u2="&#xfb02;" k="20" />
    <hkern u1="B" u2="&#xfb01;" k="20" />
    <hkern u1="B" u2="&#x203a;" k="33" />
    <hkern u1="B" u2="&#x2039;" k="-10" />
    <hkern u1="B" u2="&#x2026;" k="20" />
    <hkern u1="B" u2="&#x201e;" k="20" />
    <hkern u1="B" u2="&#x201d;" k="61" />
    <hkern u1="B" u2="&#x201c;" k="51" />
    <hkern u1="B" u2="&#x201a;" k="20" />
    <hkern u1="B" u2="&#x2019;" k="61" />
    <hkern u1="B" u2="&#x2018;" k="51" />
    <hkern u1="B" u2="&#x2014;" k="6" />
    <hkern u1="B" u2="&#x2013;" k="6" />
    <hkern u1="B" u2="&#x1ef3;" k="29" />
    <hkern u1="B" u2="&#x1ef2;" k="82" />
    <hkern u1="B" u2="&#x1e85;" k="25" />
    <hkern u1="B" u2="&#x1e84;" k="43" />
    <hkern u1="B" u2="&#x1e83;" k="25" />
    <hkern u1="B" u2="&#x1e82;" k="43" />
    <hkern u1="B" u2="&#x1e81;" k="25" />
    <hkern u1="B" u2="&#x1e80;" k="43" />
    <hkern u1="B" u2="&#x1fa;" k="16" />
    <hkern u1="B" u2="&#x178;" k="82" />
    <hkern u1="B" u2="&#x177;" k="29" />
    <hkern u1="B" u2="&#x176;" k="82" />
    <hkern u1="B" u2="&#x175;" k="25" />
    <hkern u1="B" u2="&#x174;" k="43" />
    <hkern u1="B" u2="&#x172;" k="4" />
    <hkern u1="B" u2="&#x170;" k="4" />
    <hkern u1="B" u2="&#x16e;" k="4" />
    <hkern u1="B" u2="&#x16c;" k="4" />
    <hkern u1="B" u2="&#x16a;" k="4" />
    <hkern u1="B" u2="&#x168;" k="4" />
    <hkern u1="B" u2="&#x164;" k="59" />
    <hkern u1="B" u2="&#x163;" k="20" />
    <hkern u1="B" u2="&#x162;" k="59" />
    <hkern u1="B" u2="&#x149;" k="61" />
    <hkern u1="B" u2="&#x104;" k="16" />
    <hkern u1="B" u2="&#x102;" k="16" />
    <hkern u1="B" u2="&#x100;" k="16" />
    <hkern u1="B" u2="&#xff;" k="29" />
    <hkern u1="B" u2="&#xfd;" k="29" />
    <hkern u1="B" u2="&#xdd;" k="82" />
    <hkern u1="B" u2="&#xdc;" k="4" />
    <hkern u1="B" u2="&#xdb;" k="4" />
    <hkern u1="B" u2="&#xda;" k="4" />
    <hkern u1="B" u2="&#xd9;" k="4" />
    <hkern u1="B" u2="&#xc5;" k="16" />
    <hkern u1="B" u2="&#xc4;" k="16" />
    <hkern u1="B" u2="&#xc3;" k="16" />
    <hkern u1="B" u2="&#xc2;" k="16" />
    <hkern u1="B" u2="&#xc1;" k="16" />
    <hkern u1="B" u2="&#xc0;" k="16" />
    <hkern u1="B" u2="&#xbb;" k="33" />
    <hkern u1="B" u2="&#xab;" k="-10" />
    <hkern u1="B" u2="&#x7d;" k="23" />
    <hkern u1="B" u2="y" k="29" />
    <hkern u1="B" u2="w" k="25" />
    <hkern u1="B" u2="t" k="20" />
    <hkern u1="B" u2="f" k="20" />
    <hkern u1="B" u2="]" k="23" />
    <hkern u1="B" u2="Y" k="82" />
    <hkern u1="B" u2="W" k="43" />
    <hkern u1="B" u2="U" k="4" />
    <hkern u1="B" u2="T" k="59" />
    <hkern u1="B" u2="A" k="16" />
    <hkern u1="B" u2="&#x3b;" k="14" />
    <hkern u1="B" u2="&#x3a;" k="14" />
    <hkern u1="B" u2="&#x2e;" k="20" />
    <hkern u1="B" u2="&#x2d;" k="6" />
    <hkern u1="B" u2="&#x2c;" k="20" />
    <hkern u1="B" u2="&#x29;" k="23" />
    <hkern u1="B" u2="&#x2122;" k="61" />
    <hkern u1="B" u2="&#xbf;" k="-20" />
    <hkern u1="B" u2="&#xae;" k="41" />
    <hkern u1="B" u2="x" k="29" />
    <hkern u1="B" u2="v" k="23" />
    <hkern u1="B" u2="\" k="98" />
    <hkern u1="B" u2="X" k="33" />
    <hkern u1="B" u2="V" k="72" />
    <hkern u1="B" u2="&#x3f;" k="20" />
    <hkern u1="B" u2="&#x2f;" k="53" />
    <hkern u1="B" u2="&#x2a;" k="55" />
    <hkern u1="C" u2="x" k="12" />
    <hkern u1="C" u2="v" k="-6" />
    <hkern u1="C" u2="\" k="61" />
    <hkern u1="C" u2="X" k="39" />
    <hkern u1="C" u2="V" k="41" />
    <hkern u1="C" u2="&#x2f;" k="70" />
    <hkern u1="D" u2="&#x2122;" k="31" />
    <hkern u1="D" u2="x" k="31" />
    <hkern u1="D" u2="\" k="76" />
    <hkern u1="D" u2="X" k="68" />
    <hkern u1="D" u2="V" k="53" />
    <hkern u1="D" u2="&#x2f;" k="57" />
    <hkern u1="D" u2="&#x2a;" k="10" />
    <hkern u1="E" u2="&#x2122;" k="-12" />
    <hkern u1="E" u2="x" k="-10" />
    <hkern u1="E" u2="\" k="4" />
    <hkern u1="E" u2="X" k="-6" />
    <hkern u1="F" g2="Jcircumflex.salt" k="43" />
    <hkern u1="F" g2="J.salt" k="43" />
    <hkern u1="F" u2="&#xfb02;" k="8" />
    <hkern u1="F" u2="&#xfb01;" k="8" />
    <hkern u1="F" u2="&#x2026;" k="174" />
    <hkern u1="F" u2="&#x201e;" k="123" />
    <hkern u1="F" u2="&#x201a;" k="123" />
    <hkern u1="F" u2="&#x1ef3;" k="4" />
    <hkern u1="F" u2="&#x1ef2;" k="-10" />
    <hkern u1="F" u2="&#x1e84;" k="-20" />
    <hkern u1="F" u2="&#x1e82;" k="-20" />
    <hkern u1="F" u2="&#x1e80;" k="-20" />
    <hkern u1="F" u2="&#x219;" k="10" />
    <hkern u1="F" u2="&#x1ff;" k="12" />
    <hkern u1="F" u2="&#x1fe;" k="18" />
    <hkern u1="F" u2="&#x1fd;" k="61" />
    <hkern u1="F" u2="&#x1fb;" k="61" />
    <hkern u1="F" u2="&#x1fa;" k="121" />
    <hkern u1="F" u2="&#x17e;" k="10" />
    <hkern u1="F" u2="&#x17d;" k="10" />
    <hkern u1="F" u2="&#x17c;" k="10" />
    <hkern u1="F" u2="&#x17b;" k="10" />
    <hkern u1="F" u2="&#x17a;" k="10" />
    <hkern u1="F" u2="&#x179;" k="10" />
    <hkern u1="F" u2="&#x178;" k="-10" />
    <hkern u1="F" u2="&#x177;" k="4" />
    <hkern u1="F" u2="&#x176;" k="-10" />
    <hkern u1="F" u2="&#x174;" k="-20" />
    <hkern u1="F" u2="&#x173;" k="10" />
    <hkern u1="F" u2="&#x171;" k="10" />
    <hkern u1="F" u2="&#x16f;" k="10" />
    <hkern u1="F" u2="&#x16d;" k="10" />
    <hkern u1="F" u2="&#x16b;" k="10" />
    <hkern u1="F" u2="&#x169;" k="10" />
    <hkern u1="F" u2="&#x161;" k="10" />
    <hkern u1="F" u2="&#x160;" k="12" />
    <hkern u1="F" u2="&#x15f;" k="10" />
    <hkern u1="F" u2="&#x15e;" k="12" />
    <hkern u1="F" u2="&#x15d;" k="10" />
    <hkern u1="F" u2="&#x15c;" k="12" />
    <hkern u1="F" u2="&#x15b;" k="10" />
    <hkern u1="F" u2="&#x15a;" k="12" />
    <hkern u1="F" u2="&#x159;" k="10" />
    <hkern u1="F" u2="&#x157;" k="10" />
    <hkern u1="F" u2="&#x155;" k="10" />
    <hkern u1="F" u2="&#x152;" k="18" />
    <hkern u1="F" u2="&#x151;" k="12" />
    <hkern u1="F" u2="&#x150;" k="18" />
    <hkern u1="F" u2="&#x14f;" k="12" />
    <hkern u1="F" u2="&#x14e;" k="18" />
    <hkern u1="F" u2="&#x14d;" k="12" />
    <hkern u1="F" u2="&#x14c;" k="18" />
    <hkern u1="F" u2="&#x14b;" k="10" />
    <hkern u1="F" u2="&#x148;" k="10" />
    <hkern u1="F" u2="&#x146;" k="10" />
    <hkern u1="F" u2="&#x144;" k="10" />
    <hkern u1="F" u2="&#x134;" k="92" />
    <hkern u1="F" u2="&#x123;" k="12" />
    <hkern u1="F" u2="&#x122;" k="18" />
    <hkern u1="F" u2="&#x121;" k="12" />
    <hkern u1="F" u2="&#x120;" k="18" />
    <hkern u1="F" u2="&#x11f;" k="12" />
    <hkern u1="F" u2="&#x11e;" k="18" />
    <hkern u1="F" u2="&#x11d;" k="12" />
    <hkern u1="F" u2="&#x11c;" k="18" />
    <hkern u1="F" u2="&#x11b;" k="12" />
    <hkern u1="F" u2="&#x119;" k="12" />
    <hkern u1="F" u2="&#x117;" k="12" />
    <hkern u1="F" u2="&#x115;" k="12" />
    <hkern u1="F" u2="&#x113;" k="12" />
    <hkern u1="F" u2="&#x111;" k="12" />
    <hkern u1="F" u2="&#x10f;" k="12" />
    <hkern u1="F" u2="&#x10d;" k="12" />
    <hkern u1="F" u2="&#x10c;" k="18" />
    <hkern u1="F" u2="&#x10b;" k="12" />
    <hkern u1="F" u2="&#x10a;" k="18" />
    <hkern u1="F" u2="&#x109;" k="12" />
    <hkern u1="F" u2="&#x108;" k="18" />
    <hkern u1="F" u2="&#x107;" k="12" />
    <hkern u1="F" u2="&#x106;" k="18" />
    <hkern u1="F" u2="&#x105;" k="61" />
    <hkern u1="F" u2="&#x104;" k="121" />
    <hkern u1="F" u2="&#x103;" k="61" />
    <hkern u1="F" u2="&#x102;" k="121" />
    <hkern u1="F" u2="&#x101;" k="61" />
    <hkern u1="F" u2="&#x100;" k="121" />
    <hkern u1="F" u2="&#xff;" k="4" />
    <hkern u1="F" u2="&#xfd;" k="4" />
    <hkern u1="F" u2="&#xfc;" k="10" />
    <hkern u1="F" u2="&#xfb;" k="10" />
    <hkern u1="F" u2="&#xfa;" k="10" />
    <hkern u1="F" u2="&#xf9;" k="10" />
    <hkern u1="F" u2="&#xf8;" k="12" />
    <hkern u1="F" u2="&#xf6;" k="12" />
    <hkern u1="F" u2="&#xf5;" k="12" />
    <hkern u1="F" u2="&#xf4;" k="12" />
    <hkern u1="F" u2="&#xf3;" k="12" />
    <hkern u1="F" u2="&#xf2;" k="12" />
    <hkern u1="F" u2="&#xeb;" k="12" />
    <hkern u1="F" u2="&#xea;" k="12" />
    <hkern u1="F" u2="&#xe9;" k="12" />
    <hkern u1="F" u2="&#xe8;" k="12" />
    <hkern u1="F" u2="&#xe7;" k="12" />
    <hkern u1="F" u2="&#xe6;" k="61" />
    <hkern u1="F" u2="&#xe5;" k="61" />
    <hkern u1="F" u2="&#xe4;" k="61" />
    <hkern u1="F" u2="&#xe3;" k="61" />
    <hkern u1="F" u2="&#xe2;" k="61" />
    <hkern u1="F" u2="&#xe1;" k="61" />
    <hkern u1="F" u2="&#xe0;" k="61" />
    <hkern u1="F" u2="&#xdd;" k="-10" />
    <hkern u1="F" u2="&#xd8;" k="18" />
    <hkern u1="F" u2="&#xd6;" k="18" />
    <hkern u1="F" u2="&#xd5;" k="18" />
    <hkern u1="F" u2="&#xd4;" k="18" />
    <hkern u1="F" u2="&#xd3;" k="18" />
    <hkern u1="F" u2="&#xd2;" k="18" />
    <hkern u1="F" u2="&#xc7;" k="18" />
    <hkern u1="F" u2="&#xc5;" k="121" />
    <hkern u1="F" u2="&#xc4;" k="121" />
    <hkern u1="F" u2="&#xc3;" k="121" />
    <hkern u1="F" u2="&#xc2;" k="121" />
    <hkern u1="F" u2="&#xc1;" k="121" />
    <hkern u1="F" u2="&#xc0;" k="121" />
    <hkern u1="F" u2="&#x7d;" k="-10" />
    <hkern u1="F" u2="z" k="10" />
    <hkern u1="F" u2="y" k="4" />
    <hkern u1="F" u2="u" k="10" />
    <hkern u1="F" u2="s" k="10" />
    <hkern u1="F" u2="r" k="10" />
    <hkern u1="F" u2="q" k="12" />
    <hkern u1="F" u2="p" k="10" />
    <hkern u1="F" u2="o" k="12" />
    <hkern u1="F" u2="n" k="10" />
    <hkern u1="F" u2="m" k="10" />
    <hkern u1="F" u2="g" k="12" />
    <hkern u1="F" u2="f" k="8" />
    <hkern u1="F" u2="e" k="12" />
    <hkern u1="F" u2="d" k="12" />
    <hkern u1="F" u2="c" k="12" />
    <hkern u1="F" u2="a" k="61" />
    <hkern u1="F" u2="]" k="-10" />
    <hkern u1="F" u2="Z" k="10" />
    <hkern u1="F" u2="Y" k="-10" />
    <hkern u1="F" u2="W" k="-20" />
    <hkern u1="F" u2="S" k="12" />
    <hkern u1="F" u2="Q" k="18" />
    <hkern u1="F" u2="O" k="18" />
    <hkern u1="F" u2="J" k="92" />
    <hkern u1="F" u2="G" k="18" />
    <hkern u1="F" u2="C" k="18" />
    <hkern u1="F" u2="A" k="121" />
    <hkern u1="F" u2="&#x3b;" k="27" />
    <hkern u1="F" u2="&#x3a;" k="27" />
    <hkern u1="F" u2="&#x2e;" k="174" />
    <hkern u1="F" u2="&#x2c;" k="123" />
    <hkern u1="F" u2="&#x29;" k="-10" />
    <hkern u1="F" u2="&#x2122;" k="-20" />
    <hkern u1="F" u2="&#xbf;" k="20" />
    <hkern u1="F" u2="&#xae;" k="-16" />
    <hkern u1="F" u2="x" k="20" />
    <hkern u1="F" u2="\" k="-20" />
    <hkern u1="F" u2="V" k="-20" />
    <hkern u1="F" u2="&#x40;" k="10" />
    <hkern u1="F" u2="&#x2f;" k="92" />
    <hkern u1="G" u2="&#x2122;" k="10" />
    <hkern u1="G" u2="x" k="10" />
    <hkern u1="G" u2="\" k="80" />
    <hkern u1="G" u2="X" k="31" />
    <hkern u1="G" u2="V" k="37" />
    <hkern u1="G" u2="&#x2f;" k="53" />
    <hkern u1="J" u2="&#x2f;" k="37" />
    <hkern u1="K" u2="&#x2122;" k="-12" />
    <hkern u1="K" u2="&#xbf;" k="16" />
    <hkern u1="K" u2="&#xae;" k="68" />
    <hkern u1="K" u2="x" k="-29" />
    <hkern u1="K" u2="v" k="82" />
    <hkern u1="K" u2="X" k="-23" />
    <hkern u1="K" u2="&#x40;" k="31" />
    <hkern u1="K" u2="&#x3f;" k="31" />
    <hkern u1="K" u2="&#x2f;" k="-20" />
    <hkern u1="K" u2="&#x2a;" k="37" />
    <hkern u1="K" u2="&#x26;" k="20" />
    <hkern u1="L" u2="&#x2122;" k="418" />
    <hkern u1="L" u2="&#xae;" k="358" />
    <hkern u1="L" u2="v" k="123" />
    <hkern u1="L" u2="\" k="193" />
    <hkern u1="L" u2="X" k="-10" />
    <hkern u1="L" u2="V" k="190" />
    <hkern u1="L" u2="&#x40;" k="16" />
    <hkern u1="L" u2="&#x3f;" k="133" />
    <hkern u1="L" u2="&#x2f;" k="-16" />
    <hkern u1="L" u2="&#x2a;" k="420" />
    <hkern u1="L" u2="&#x26;" k="10" />
    <hkern u1="O" u2="&#x2122;" k="31" />
    <hkern u1="O" u2="x" k="31" />
    <hkern u1="O" u2="\" k="76" />
    <hkern u1="O" u2="X" k="68" />
    <hkern u1="O" u2="V" k="53" />
    <hkern u1="O" u2="&#x2f;" k="57" />
    <hkern u1="O" u2="&#x2a;" k="10" />
    <hkern u1="P" g2="Jcircumflex.salt" k="49" />
    <hkern u1="P" g2="J.salt" k="49" />
    <hkern u1="P" u2="&#xfb02;" k="-20" />
    <hkern u1="P" u2="&#xfb01;" k="-20" />
    <hkern u1="P" u2="&#x2117;" k="-10" />
    <hkern u1="P" u2="&#x2026;" k="256" />
    <hkern u1="P" u2="&#x201e;" k="205" />
    <hkern u1="P" u2="&#x201d;" k="-41" />
    <hkern u1="P" u2="&#x201c;" k="-53" />
    <hkern u1="P" u2="&#x201a;" k="205" />
    <hkern u1="P" u2="&#x2019;" k="-41" />
    <hkern u1="P" u2="&#x2018;" k="-53" />
    <hkern u1="P" u2="&#x1ef3;" k="-31" />
    <hkern u1="P" u2="&#x1ef2;" k="20" />
    <hkern u1="P" u2="&#x1e85;" k="-31" />
    <hkern u1="P" u2="&#x1e83;" k="-31" />
    <hkern u1="P" u2="&#x1e81;" k="-31" />
    <hkern u1="P" u2="&#x1ff;" k="6" />
    <hkern u1="P" u2="&#x1fd;" k="41" />
    <hkern u1="P" u2="&#x1fb;" k="41" />
    <hkern u1="P" u2="&#x1fa;" k="133" />
    <hkern u1="P" u2="&#x17d;" k="10" />
    <hkern u1="P" u2="&#x17b;" k="10" />
    <hkern u1="P" u2="&#x179;" k="10" />
    <hkern u1="P" u2="&#x178;" k="20" />
    <hkern u1="P" u2="&#x177;" k="-31" />
    <hkern u1="P" u2="&#x176;" k="20" />
    <hkern u1="P" u2="&#x175;" k="-31" />
    <hkern u1="P" u2="&#x164;" k="4" />
    <hkern u1="P" u2="&#x163;" k="-10" />
    <hkern u1="P" u2="&#x162;" k="4" />
    <hkern u1="P" u2="&#x151;" k="6" />
    <hkern u1="P" u2="&#x14f;" k="6" />
    <hkern u1="P" u2="&#x14d;" k="6" />
    <hkern u1="P" u2="&#x149;" k="-41" />
    <hkern u1="P" u2="&#x134;" k="117" />
    <hkern u1="P" u2="&#x123;" k="6" />
    <hkern u1="P" u2="&#x121;" k="6" />
    <hkern u1="P" u2="&#x11f;" k="6" />
    <hkern u1="P" u2="&#x11d;" k="6" />
    <hkern u1="P" u2="&#x11b;" k="6" />
    <hkern u1="P" u2="&#x119;" k="6" />
    <hkern u1="P" u2="&#x117;" k="6" />
    <hkern u1="P" u2="&#x115;" k="6" />
    <hkern u1="P" u2="&#x113;" k="6" />
    <hkern u1="P" u2="&#x111;" k="6" />
    <hkern u1="P" u2="&#x10f;" k="6" />
    <hkern u1="P" u2="&#x10d;" k="6" />
    <hkern u1="P" u2="&#x10b;" k="6" />
    <hkern u1="P" u2="&#x109;" k="6" />
    <hkern u1="P" u2="&#x107;" k="6" />
    <hkern u1="P" u2="&#x105;" k="41" />
    <hkern u1="P" u2="&#x104;" k="133" />
    <hkern u1="P" u2="&#x103;" k="41" />
    <hkern u1="P" u2="&#x102;" k="133" />
    <hkern u1="P" u2="&#x101;" k="41" />
    <hkern u1="P" u2="&#x100;" k="133" />
    <hkern u1="P" u2="&#xff;" k="-31" />
    <hkern u1="P" u2="&#xfd;" k="-31" />
    <hkern u1="P" u2="&#xf8;" k="6" />
    <hkern u1="P" u2="&#xf6;" k="6" />
    <hkern u1="P" u2="&#xf5;" k="6" />
    <hkern u1="P" u2="&#xf4;" k="6" />
    <hkern u1="P" u2="&#xf3;" k="6" />
    <hkern u1="P" u2="&#xf2;" k="6" />
    <hkern u1="P" u2="&#xeb;" k="6" />
    <hkern u1="P" u2="&#xea;" k="6" />
    <hkern u1="P" u2="&#xe9;" k="6" />
    <hkern u1="P" u2="&#xe8;" k="6" />
    <hkern u1="P" u2="&#xe7;" k="6" />
    <hkern u1="P" u2="&#xe6;" k="41" />
    <hkern u1="P" u2="&#xe5;" k="41" />
    <hkern u1="P" u2="&#xe4;" k="41" />
    <hkern u1="P" u2="&#xe3;" k="41" />
    <hkern u1="P" u2="&#xe2;" k="41" />
    <hkern u1="P" u2="&#xe1;" k="41" />
    <hkern u1="P" u2="&#xe0;" k="41" />
    <hkern u1="P" u2="&#xdd;" k="20" />
    <hkern u1="P" u2="&#xc5;" k="133" />
    <hkern u1="P" u2="&#xc4;" k="133" />
    <hkern u1="P" u2="&#xc3;" k="133" />
    <hkern u1="P" u2="&#xc2;" k="133" />
    <hkern u1="P" u2="&#xc1;" k="133" />
    <hkern u1="P" u2="&#xc0;" k="133" />
    <hkern u1="P" u2="&#xba;" k="-20" />
    <hkern u1="P" u2="&#xaa;" k="-20" />
    <hkern u1="P" u2="&#xa9;" k="-10" />
    <hkern u1="P" u2="&#x7d;" k="37" />
    <hkern u1="P" u2="y" k="-31" />
    <hkern u1="P" u2="w" k="-31" />
    <hkern u1="P" u2="t" k="-10" />
    <hkern u1="P" u2="q" k="6" />
    <hkern u1="P" u2="o" k="6" />
    <hkern u1="P" u2="g" k="6" />
    <hkern u1="P" u2="f" k="-20" />
    <hkern u1="P" u2="e" k="6" />
    <hkern u1="P" u2="d" k="6" />
    <hkern u1="P" u2="c" k="6" />
    <hkern u1="P" u2="a" k="41" />
    <hkern u1="P" u2="]" k="37" />
    <hkern u1="P" u2="Z" k="10" />
    <hkern u1="P" u2="Y" k="20" />
    <hkern u1="P" u2="T" k="4" />
    <hkern u1="P" u2="J" k="117" />
    <hkern u1="P" u2="A" k="133" />
    <hkern u1="P" u2="&#x3b;" k="14" />
    <hkern u1="P" u2="&#x3a;" k="14" />
    <hkern u1="P" u2="&#x2e;" k="256" />
    <hkern u1="P" u2="&#x2c;" k="205" />
    <hkern u1="P" u2="&#x29;" k="37" />
    <hkern u1="P" u2="&#xbf;" k="25" />
    <hkern u1="P" u2="&#xae;" k="-20" />
    <hkern u1="P" u2="v" k="-31" />
    <hkern u1="P" u2="\" k="25" />
    <hkern u1="P" u2="X" k="51" />
    <hkern u1="P" u2="V" k="14" />
    <hkern u1="P" u2="&#x40;" k="10" />
    <hkern u1="P" u2="&#x3f;" k="-31" />
    <hkern u1="P" u2="&#x2f;" k="141" />
    <hkern u1="P" u2="&#x2a;" k="-10" />
    <hkern u1="P" u2="&#x21;" k="-10" />
    <hkern u1="Q" u2="&#x2122;" k="31" />
    <hkern u1="Q" u2="x" k="31" />
    <hkern u1="Q" u2="\" k="76" />
    <hkern u1="Q" u2="X" k="68" />
    <hkern u1="Q" u2="V" k="53" />
    <hkern u1="Q" u2="&#x2f;" k="57" />
    <hkern u1="Q" u2="&#x2a;" k="10" />
    <hkern u1="R" u2="&#x2122;" k="31" />
    <hkern u1="R" u2="&#xbf;" k="10" />
    <hkern u1="R" u2="&#xae;" k="10" />
    <hkern u1="R" u2="x" k="-25" />
    <hkern u1="R" u2="\" k="51" />
    <hkern u1="R" u2="X" k="-20" />
    <hkern u1="R" u2="V" k="47" />
    <hkern u1="R" u2="&#x40;" k="10" />
    <hkern u1="R" u2="&#x2a;" k="20" />
    <hkern u1="R" u2="&#x26;" k="10" />
    <hkern u1="S" u2="&#x2122;" k="51" />
    <hkern u1="S" u2="&#xae;" k="10" />
    <hkern u1="S" u2="x" k="27" />
    <hkern u1="S" u2="v" k="10" />
    <hkern u1="S" u2="\" k="84" />
    <hkern u1="S" u2="X" k="37" />
    <hkern u1="S" u2="V" k="74" />
    <hkern u1="S" u2="&#x2f;" k="63" />
    <hkern u1="S" u2="&#x2a;" k="20" />
    <hkern u1="T" u2="&#x2122;" k="-16" />
    <hkern u1="T" u2="&#xbf;" k="109" />
    <hkern u1="T" u2="x" k="102" />
    <hkern u1="T" u2="v" k="72" />
    <hkern u1="T" u2="i" k="10" />
    <hkern u1="T" u2="X" k="-8" />
    <hkern u1="T" u2="V" k="-23" />
    <hkern u1="T" u2="&#x40;" k="57" />
    <hkern u1="T" u2="&#x3f;" k="-12" />
    <hkern u1="T" u2="&#x2f;" k="178" />
    <hkern u1="T" u2="&#x26;" k="16" />
    <hkern u1="T" u2="&#x21;" k="-10" />
    <hkern u1="U" u2="x" k="10" />
    <hkern u1="U" u2="X" k="10" />
    <hkern u1="U" u2="&#x2f;" k="49" />
    <hkern u1="V" g2="Jcircumflex.salt" k="57" />
    <hkern u1="V" g2="J.salt" k="57" />
    <hkern u1="V" u2="&#xfb02;" k="37" />
    <hkern u1="V" u2="&#xfb01;" k="37" />
    <hkern u1="V" u2="&#x2117;" k="51" />
    <hkern u1="V" u2="&#x203a;" k="80" />
    <hkern u1="V" u2="&#x2039;" k="102" />
    <hkern u1="V" u2="&#x2026;" k="276" />
    <hkern u1="V" u2="&#x201e;" k="197" />
    <hkern u1="V" u2="&#x201d;" k="14" />
    <hkern u1="V" u2="&#x201c;" k="16" />
    <hkern u1="V" u2="&#x201a;" k="197" />
    <hkern u1="V" u2="&#x2019;" k="14" />
    <hkern u1="V" u2="&#x2018;" k="16" />
    <hkern u1="V" u2="&#x2014;" k="102" />
    <hkern u1="V" u2="&#x2013;" k="102" />
    <hkern u1="V" u2="&#x1ef3;" k="27" />
    <hkern u1="V" u2="&#x1e85;" k="37" />
    <hkern u1="V" u2="&#x1e83;" k="37" />
    <hkern u1="V" u2="&#x1e81;" k="37" />
    <hkern u1="V" u2="&#x219;" k="109" />
    <hkern u1="V" u2="&#x1ff;" k="123" />
    <hkern u1="V" u2="&#x1fe;" k="53" />
    <hkern u1="V" u2="&#x1fd;" k="164" />
    <hkern u1="V" u2="&#x1fb;" k="164" />
    <hkern u1="V" u2="&#x1fa;" k="195" />
    <hkern u1="V" u2="&#x17e;" k="57" />
    <hkern u1="V" u2="&#x17c;" k="57" />
    <hkern u1="V" u2="&#x17a;" k="57" />
    <hkern u1="V" u2="&#x177;" k="27" />
    <hkern u1="V" u2="&#x175;" k="37" />
    <hkern u1="V" u2="&#x173;" k="63" />
    <hkern u1="V" u2="&#x171;" k="63" />
    <hkern u1="V" u2="&#x16f;" k="63" />
    <hkern u1="V" u2="&#x16d;" k="63" />
    <hkern u1="V" u2="&#x16b;" k="63" />
    <hkern u1="V" u2="&#x169;" k="63" />
    <hkern u1="V" u2="&#x164;" k="-23" />
    <hkern u1="V" u2="&#x163;" k="31" />
    <hkern u1="V" u2="&#x162;" k="-23" />
    <hkern u1="V" u2="&#x161;" k="109" />
    <hkern u1="V" u2="&#x160;" k="41" />
    <hkern u1="V" u2="&#x15f;" k="109" />
    <hkern u1="V" u2="&#x15e;" k="41" />
    <hkern u1="V" u2="&#x15d;" k="109" />
    <hkern u1="V" u2="&#x15c;" k="41" />
    <hkern u1="V" u2="&#x15b;" k="109" />
    <hkern u1="V" u2="&#x15a;" k="41" />
    <hkern u1="V" u2="&#x159;" k="72" />
    <hkern u1="V" u2="&#x157;" k="72" />
    <hkern u1="V" u2="&#x155;" k="72" />
    <hkern u1="V" u2="&#x152;" k="53" />
    <hkern u1="V" u2="&#x151;" k="123" />
    <hkern u1="V" u2="&#x150;" k="53" />
    <hkern u1="V" u2="&#x14f;" k="123" />
    <hkern u1="V" u2="&#x14e;" k="53" />
    <hkern u1="V" u2="&#x14d;" k="123" />
    <hkern u1="V" u2="&#x14c;" k="53" />
    <hkern u1="V" u2="&#x14b;" k="72" />
    <hkern u1="V" u2="&#x149;" k="14" />
    <hkern u1="V" u2="&#x148;" k="72" />
    <hkern u1="V" u2="&#x146;" k="72" />
    <hkern u1="V" u2="&#x144;" k="72" />
    <hkern u1="V" u2="&#x134;" k="154" />
    <hkern u1="V" u2="&#x123;" k="123" />
    <hkern u1="V" u2="&#x122;" k="53" />
    <hkern u1="V" u2="&#x121;" k="123" />
    <hkern u1="V" u2="&#x120;" k="53" />
    <hkern u1="V" u2="&#x11f;" k="123" />
    <hkern u1="V" u2="&#x11e;" k="53" />
    <hkern u1="V" u2="&#x11d;" k="123" />
    <hkern u1="V" u2="&#x11c;" k="53" />
    <hkern u1="V" u2="&#x11b;" k="123" />
    <hkern u1="V" u2="&#x119;" k="123" />
    <hkern u1="V" u2="&#x117;" k="123" />
    <hkern u1="V" u2="&#x115;" k="123" />
    <hkern u1="V" u2="&#x113;" k="123" />
    <hkern u1="V" u2="&#x111;" k="123" />
    <hkern u1="V" u2="&#x10f;" k="123" />
    <hkern u1="V" u2="&#x10d;" k="123" />
    <hkern u1="V" u2="&#x10c;" k="53" />
    <hkern u1="V" u2="&#x10b;" k="123" />
    <hkern u1="V" u2="&#x10a;" k="53" />
    <hkern u1="V" u2="&#x109;" k="123" />
    <hkern u1="V" u2="&#x108;" k="53" />
    <hkern u1="V" u2="&#x107;" k="123" />
    <hkern u1="V" u2="&#x106;" k="53" />
    <hkern u1="V" u2="&#x105;" k="164" />
    <hkern u1="V" u2="&#x104;" k="195" />
    <hkern u1="V" u2="&#x103;" k="164" />
    <hkern u1="V" u2="&#x102;" k="195" />
    <hkern u1="V" u2="&#x101;" k="164" />
    <hkern u1="V" u2="&#x100;" k="195" />
    <hkern u1="V" u2="&#xff;" k="27" />
    <hkern u1="V" u2="&#xfd;" k="27" />
    <hkern u1="V" u2="&#xfc;" k="63" />
    <hkern u1="V" u2="&#xfb;" k="63" />
    <hkern u1="V" u2="&#xfa;" k="63" />
    <hkern u1="V" u2="&#xf9;" k="63" />
    <hkern u1="V" u2="&#xf8;" k="123" />
    <hkern u1="V" u2="&#xf6;" k="123" />
    <hkern u1="V" u2="&#xf5;" k="123" />
    <hkern u1="V" u2="&#xf4;" k="123" />
    <hkern u1="V" u2="&#xf3;" k="123" />
    <hkern u1="V" u2="&#xf2;" k="123" />
    <hkern u1="V" u2="&#xeb;" k="123" />
    <hkern u1="V" u2="&#xea;" k="123" />
    <hkern u1="V" u2="&#xe9;" k="123" />
    <hkern u1="V" u2="&#xe8;" k="123" />
    <hkern u1="V" u2="&#xe7;" k="123" />
    <hkern u1="V" u2="&#xe6;" k="164" />
    <hkern u1="V" u2="&#xe5;" k="164" />
    <hkern u1="V" u2="&#xe4;" k="164" />
    <hkern u1="V" u2="&#xe3;" k="164" />
    <hkern u1="V" u2="&#xe2;" k="164" />
    <hkern u1="V" u2="&#xe1;" k="164" />
    <hkern u1="V" u2="&#xe0;" k="164" />
    <hkern u1="V" u2="&#xd8;" k="53" />
    <hkern u1="V" u2="&#xd6;" k="53" />
    <hkern u1="V" u2="&#xd5;" k="53" />
    <hkern u1="V" u2="&#xd4;" k="53" />
    <hkern u1="V" u2="&#xd3;" k="53" />
    <hkern u1="V" u2="&#xd2;" k="53" />
    <hkern u1="V" u2="&#xc7;" k="53" />
    <hkern u1="V" u2="&#xc5;" k="195" />
    <hkern u1="V" u2="&#xc4;" k="195" />
    <hkern u1="V" u2="&#xc3;" k="195" />
    <hkern u1="V" u2="&#xc2;" k="195" />
    <hkern u1="V" u2="&#xc1;" k="195" />
    <hkern u1="V" u2="&#xc0;" k="195" />
    <hkern u1="V" u2="&#xbb;" k="80" />
    <hkern u1="V" u2="&#xab;" k="102" />
    <hkern u1="V" u2="&#xa9;" k="51" />
    <hkern u1="V" u2="&#x7d;" k="-6" />
    <hkern u1="V" u2="z" k="57" />
    <hkern u1="V" u2="y" k="27" />
    <hkern u1="V" u2="w" k="37" />
    <hkern u1="V" u2="u" k="63" />
    <hkern u1="V" u2="t" k="31" />
    <hkern u1="V" u2="s" k="109" />
    <hkern u1="V" u2="r" k="72" />
    <hkern u1="V" u2="q" k="123" />
    <hkern u1="V" u2="p" k="72" />
    <hkern u1="V" u2="o" k="123" />
    <hkern u1="V" u2="n" k="72" />
    <hkern u1="V" u2="m" k="72" />
    <hkern u1="V" u2="g" k="123" />
    <hkern u1="V" u2="f" k="37" />
    <hkern u1="V" u2="e" k="123" />
    <hkern u1="V" u2="d" k="123" />
    <hkern u1="V" u2="c" k="123" />
    <hkern u1="V" u2="a" k="164" />
    <hkern u1="V" u2="]" k="-6" />
    <hkern u1="V" u2="T" k="-23" />
    <hkern u1="V" u2="S" k="41" />
    <hkern u1="V" u2="Q" k="53" />
    <hkern u1="V" u2="O" k="53" />
    <hkern u1="V" u2="J" k="154" />
    <hkern u1="V" u2="G" k="53" />
    <hkern u1="V" u2="C" k="53" />
    <hkern u1="V" u2="A" k="195" />
    <hkern u1="V" u2="&#x3b;" k="143" />
    <hkern u1="V" u2="&#x3a;" k="143" />
    <hkern u1="V" u2="&#x2e;" k="276" />
    <hkern u1="V" u2="&#x2d;" k="102" />
    <hkern u1="V" u2="&#x2c;" k="197" />
    <hkern u1="V" u2="&#x29;" k="-6" />
    <hkern u1="V" u2="&#x2122;" k="-33" />
    <hkern u1="V" u2="&#xbf;" k="98" />
    <hkern u1="V" u2="&#xae;" k="20" />
    <hkern u1="V" u2="&#xa1;" k="37" />
    <hkern u1="V" u2="x" k="49" />
    <hkern u1="V" u2="v" k="20" />
    <hkern u1="V" u2="i" k="10" />
    <hkern u1="V" u2="h" k="6" />
    <hkern u1="V" u2="&#x40;" k="63" />
    <hkern u1="V" u2="&#x3f;" k="12" />
    <hkern u1="V" u2="&#x2f;" k="184" />
    <hkern u1="V" u2="&#x2a;" k="16" />
    <hkern u1="V" u2="&#x26;" k="53" />
    <hkern u1="W" u2="&#x2122;" k="-33" />
    <hkern u1="W" u2="&#xbf;" k="59" />
    <hkern u1="W" u2="&#xa1;" k="20" />
    <hkern u1="W" u2="x" k="31" />
    <hkern u1="W" u2="v" k="10" />
    <hkern u1="W" u2="i" k="6" />
    <hkern u1="W" u2="\" k="-10" />
    <hkern u1="W" u2="&#x40;" k="37" />
    <hkern u1="W" u2="&#x2f;" k="125" />
    <hkern u1="W" u2="&#x2a;" k="16" />
    <hkern u1="W" u2="&#x26;" k="23" />
    <hkern u1="X" g2="Jcircumflex.salt" k="27" />
    <hkern u1="X" g2="J.salt" k="27" />
    <hkern u1="X" u2="&#xfb02;" k="20" />
    <hkern u1="X" u2="&#xfb01;" k="20" />
    <hkern u1="X" u2="&#x2117;" k="55" />
    <hkern u1="X" u2="&#x203a;" k="20" />
    <hkern u1="X" u2="&#x2039;" k="82" />
    <hkern u1="X" u2="&#x2026;" k="18" />
    <hkern u1="X" u2="&#x201e;" k="-16" />
    <hkern u1="X" u2="&#x201d;" k="72" />
    <hkern u1="X" u2="&#x201c;" k="96" />
    <hkern u1="X" u2="&#x201a;" k="-16" />
    <hkern u1="X" u2="&#x2019;" k="72" />
    <hkern u1="X" u2="&#x2018;" k="96" />
    <hkern u1="X" u2="&#x2014;" k="129" />
    <hkern u1="X" u2="&#x2013;" k="129" />
    <hkern u1="X" u2="&#x1ef3;" k="88" />
    <hkern u1="X" u2="&#x1e85;" k="61" />
    <hkern u1="X" u2="&#x1e83;" k="61" />
    <hkern u1="X" u2="&#x1e81;" k="61" />
    <hkern u1="X" u2="&#x219;" k="31" />
    <hkern u1="X" u2="&#x1ff;" k="61" />
    <hkern u1="X" u2="&#x1fe;" k="68" />
    <hkern u1="X" u2="&#x1fd;" k="41" />
    <hkern u1="X" u2="&#x1fb;" k="41" />
    <hkern u1="X" u2="&#x1fa;" k="-33" />
    <hkern u1="X" u2="&#x177;" k="88" />
    <hkern u1="X" u2="&#x175;" k="61" />
    <hkern u1="X" u2="&#x173;" k="41" />
    <hkern u1="X" u2="&#x172;" k="10" />
    <hkern u1="X" u2="&#x171;" k="41" />
    <hkern u1="X" u2="&#x170;" k="10" />
    <hkern u1="X" u2="&#x16f;" k="41" />
    <hkern u1="X" u2="&#x16e;" k="10" />
    <hkern u1="X" u2="&#x16d;" k="41" />
    <hkern u1="X" u2="&#x16c;" k="10" />
    <hkern u1="X" u2="&#x16b;" k="41" />
    <hkern u1="X" u2="&#x16a;" k="10" />
    <hkern u1="X" u2="&#x169;" k="41" />
    <hkern u1="X" u2="&#x168;" k="10" />
    <hkern u1="X" u2="&#x164;" k="-8" />
    <hkern u1="X" u2="&#x163;" k="51" />
    <hkern u1="X" u2="&#x162;" k="-8" />
    <hkern u1="X" u2="&#x161;" k="31" />
    <hkern u1="X" u2="&#x160;" k="25" />
    <hkern u1="X" u2="&#x15f;" k="31" />
    <hkern u1="X" u2="&#x15e;" k="25" />
    <hkern u1="X" u2="&#x15d;" k="31" />
    <hkern u1="X" u2="&#x15c;" k="25" />
    <hkern u1="X" u2="&#x15b;" k="31" />
    <hkern u1="X" u2="&#x15a;" k="25" />
    <hkern u1="X" u2="&#x152;" k="68" />
    <hkern u1="X" u2="&#x151;" k="61" />
    <hkern u1="X" u2="&#x150;" k="68" />
    <hkern u1="X" u2="&#x14f;" k="61" />
    <hkern u1="X" u2="&#x14e;" k="68" />
    <hkern u1="X" u2="&#x14d;" k="61" />
    <hkern u1="X" u2="&#x14c;" k="68" />
    <hkern u1="X" u2="&#x149;" k="72" />
    <hkern u1="X" u2="&#x134;" k="45" />
    <hkern u1="X" u2="&#x123;" k="61" />
    <hkern u1="X" u2="&#x122;" k="68" />
    <hkern u1="X" u2="&#x121;" k="61" />
    <hkern u1="X" u2="&#x120;" k="68" />
    <hkern u1="X" u2="&#x11f;" k="61" />
    <hkern u1="X" u2="&#x11e;" k="68" />
    <hkern u1="X" u2="&#x11d;" k="61" />
    <hkern u1="X" u2="&#x11c;" k="68" />
    <hkern u1="X" u2="&#x11b;" k="61" />
    <hkern u1="X" u2="&#x119;" k="61" />
    <hkern u1="X" u2="&#x117;" k="61" />
    <hkern u1="X" u2="&#x115;" k="61" />
    <hkern u1="X" u2="&#x113;" k="61" />
    <hkern u1="X" u2="&#x111;" k="61" />
    <hkern u1="X" u2="&#x10f;" k="61" />
    <hkern u1="X" u2="&#x10d;" k="61" />
    <hkern u1="X" u2="&#x10c;" k="68" />
    <hkern u1="X" u2="&#x10b;" k="61" />
    <hkern u1="X" u2="&#x10a;" k="68" />
    <hkern u1="X" u2="&#x109;" k="61" />
    <hkern u1="X" u2="&#x108;" k="68" />
    <hkern u1="X" u2="&#x107;" k="61" />
    <hkern u1="X" u2="&#x106;" k="68" />
    <hkern u1="X" u2="&#x105;" k="41" />
    <hkern u1="X" u2="&#x104;" k="-33" />
    <hkern u1="X" u2="&#x103;" k="41" />
    <hkern u1="X" u2="&#x102;" k="-33" />
    <hkern u1="X" u2="&#x101;" k="41" />
    <hkern u1="X" u2="&#x100;" k="-33" />
    <hkern u1="X" u2="&#xff;" k="88" />
    <hkern u1="X" u2="&#xfd;" k="88" />
    <hkern u1="X" u2="&#xfc;" k="41" />
    <hkern u1="X" u2="&#xfb;" k="41" />
    <hkern u1="X" u2="&#xfa;" k="41" />
    <hkern u1="X" u2="&#xf9;" k="41" />
    <hkern u1="X" u2="&#xf8;" k="61" />
    <hkern u1="X" u2="&#xf6;" k="61" />
    <hkern u1="X" u2="&#xf5;" k="61" />
    <hkern u1="X" u2="&#xf4;" k="61" />
    <hkern u1="X" u2="&#xf3;" k="61" />
    <hkern u1="X" u2="&#xf2;" k="61" />
    <hkern u1="X" u2="&#xeb;" k="61" />
    <hkern u1="X" u2="&#xea;" k="61" />
    <hkern u1="X" u2="&#xe9;" k="61" />
    <hkern u1="X" u2="&#xe8;" k="61" />
    <hkern u1="X" u2="&#xe7;" k="61" />
    <hkern u1="X" u2="&#xe6;" k="41" />
    <hkern u1="X" u2="&#xe5;" k="41" />
    <hkern u1="X" u2="&#xe4;" k="41" />
    <hkern u1="X" u2="&#xe3;" k="41" />
    <hkern u1="X" u2="&#xe2;" k="41" />
    <hkern u1="X" u2="&#xe1;" k="41" />
    <hkern u1="X" u2="&#xe0;" k="41" />
    <hkern u1="X" u2="&#xdc;" k="10" />
    <hkern u1="X" u2="&#xdb;" k="10" />
    <hkern u1="X" u2="&#xda;" k="10" />
    <hkern u1="X" u2="&#xd9;" k="10" />
    <hkern u1="X" u2="&#xd8;" k="68" />
    <hkern u1="X" u2="&#xd6;" k="68" />
    <hkern u1="X" u2="&#xd5;" k="68" />
    <hkern u1="X" u2="&#xd4;" k="68" />
    <hkern u1="X" u2="&#xd3;" k="68" />
    <hkern u1="X" u2="&#xd2;" k="68" />
    <hkern u1="X" u2="&#xc7;" k="68" />
    <hkern u1="X" u2="&#xc5;" k="-33" />
    <hkern u1="X" u2="&#xc4;" k="-33" />
    <hkern u1="X" u2="&#xc3;" k="-33" />
    <hkern u1="X" u2="&#xc2;" k="-33" />
    <hkern u1="X" u2="&#xc1;" k="-33" />
    <hkern u1="X" u2="&#xc0;" k="-33" />
    <hkern u1="X" u2="&#xbb;" k="20" />
    <hkern u1="X" u2="&#xba;" k="35" />
    <hkern u1="X" u2="&#xab;" k="82" />
    <hkern u1="X" u2="&#xaa;" k="35" />
    <hkern u1="X" u2="&#xa9;" k="55" />
    <hkern u1="X" u2="y" k="88" />
    <hkern u1="X" u2="w" k="61" />
    <hkern u1="X" u2="u" k="41" />
    <hkern u1="X" u2="t" k="51" />
    <hkern u1="X" u2="s" k="31" />
    <hkern u1="X" u2="q" k="61" />
    <hkern u1="X" u2="o" k="61" />
    <hkern u1="X" u2="g" k="61" />
    <hkern u1="X" u2="f" k="20" />
    <hkern u1="X" u2="e" k="61" />
    <hkern u1="X" u2="d" k="61" />
    <hkern u1="X" u2="c" k="61" />
    <hkern u1="X" u2="a" k="41" />
    <hkern u1="X" u2="U" k="10" />
    <hkern u1="X" u2="T" k="-8" />
    <hkern u1="X" u2="S" k="25" />
    <hkern u1="X" u2="Q" k="68" />
    <hkern u1="X" u2="O" k="68" />
    <hkern u1="X" u2="J" k="45" />
    <hkern u1="X" u2="G" k="68" />
    <hkern u1="X" u2="C" k="68" />
    <hkern u1="X" u2="A" k="-33" />
    <hkern u1="X" u2="&#x3b;" k="20" />
    <hkern u1="X" u2="&#x3a;" k="20" />
    <hkern u1="X" u2="&#x2e;" k="18" />
    <hkern u1="X" u2="&#x2d;" k="129" />
    <hkern u1="X" u2="&#x2c;" k="-16" />
    <hkern u1="X" u2="&#x2122;" k="-16" />
    <hkern u1="X" u2="&#xbf;" k="18" />
    <hkern u1="X" u2="&#xae;" k="72" />
    <hkern u1="X" u2="x" k="-20" />
    <hkern u1="X" u2="v" k="92" />
    <hkern u1="X" u2="&#x40;" k="47" />
    <hkern u1="X" u2="&#x3f;" k="31" />
    <hkern u1="X" u2="&#x2a;" k="80" />
    <hkern u1="X" u2="&#x26;" k="25" />
    <hkern u1="Y" u2="&#x2122;" k="-43" />
    <hkern u1="Y" u2="&#xbf;" k="154" />
    <hkern u1="Y" u2="&#xae;" k="37" />
    <hkern u1="Y" u2="&#xa1;" k="37" />
    <hkern u1="Y" u2="x" k="94" />
    <hkern u1="Y" u2="v" k="66" />
    <hkern u1="Y" u2="i" k="4" />
    <hkern u1="Y" u2="&#x40;" k="135" />
    <hkern u1="Y" u2="&#x3f;" k="10" />
    <hkern u1="Y" u2="&#x2f;" k="225" />
    <hkern u1="Y" u2="&#x2a;" k="31" />
    <hkern u1="Y" u2="&#x26;" k="100" />
    <hkern u1="Z" u2="&#x2122;" k="4" />
    <hkern u1="Z" u2="&#xbf;" k="8" />
    <hkern u1="Z" u2="&#xae;" k="25" />
    <hkern u1="Z" u2="x" k="-6" />
    <hkern u1="Z" u2="v" k="10" />
    <hkern u1="Z" u2="X" k="-10" />
    <hkern u1="Z" u2="&#x3f;" k="18" />
    <hkern u1="Z" u2="&#x2a;" k="20" />
    <hkern u1="[" u2="&#x20ac;" k="82" />
    <hkern u1="[" u2="&#xbf;" k="37" />
    <hkern u1="[" u2="&#xae;" k="20" />
    <hkern u1="[" u2="&#xa5;" k="20" />
    <hkern u1="[" u2="&#xa2;" k="45" />
    <hkern u1="[" u2="x" k="6" />
    <hkern u1="[" u2="v" k="27" />
    <hkern u1="[" u2="j" k="-47" />
    <hkern u1="[" u2="i" k="10" />
    <hkern u1="[" u2="\" k="20" />
    <hkern u1="[" u2="V" k="-6" />
    <hkern u1="[" u2="&#x40;" k="45" />
    <hkern u1="[" u2="&#x3f;" k="23" />
    <hkern u1="[" u2="&#x39;" k="47" />
    <hkern u1="[" u2="&#x38;" k="47" />
    <hkern u1="[" u2="&#x37;" k="10" />
    <hkern u1="[" u2="&#x36;" k="53" />
    <hkern u1="[" u2="&#x35;" k="33" />
    <hkern u1="[" u2="&#x34;" k="55" />
    <hkern u1="[" u2="&#x33;" k="18" />
    <hkern u1="[" u2="&#x32;" k="10" />
    <hkern u1="[" u2="&#x31;" k="18" />
    <hkern u1="[" u2="&#x30;" k="37" />
    <hkern u1="[" u2="&#x2a;" k="51" />
    <hkern u1="[" u2="&#x26;" k="41" />
    <hkern u1="[" u2="&#x24;" k="20" />
    <hkern u1="[" u2="&#x23;" k="47" />
    <hkern u1="\" g2="Jcircumflex.salt" k="45" />
    <hkern u1="\" g2="J.salt" k="45" />
    <hkern u1="\" u2="&#xfb02;" k="20" />
    <hkern u1="\" u2="&#xfb01;" k="20" />
    <hkern u1="\" u2="&#x2117;" k="92" />
    <hkern u1="\" u2="&#x2039;" k="57" />
    <hkern u1="\" u2="&#x2030;" k="193" />
    <hkern u1="\" u2="&#x2026;" k="29" />
    <hkern u1="\" u2="&#x201e;" k="-41" />
    <hkern u1="\" u2="&#x201d;" k="250" />
    <hkern u1="\" u2="&#x201c;" k="238" />
    <hkern u1="\" u2="&#x201a;" k="-41" />
    <hkern u1="\" u2="&#x2019;" k="250" />
    <hkern u1="\" u2="&#x2018;" k="238" />
    <hkern u1="\" u2="&#x2014;" k="135" />
    <hkern u1="\" u2="&#x2013;" k="135" />
    <hkern u1="\" u2="&#x1ef3;" k="55" />
    <hkern u1="\" u2="&#x1ef2;" k="225" />
    <hkern u1="\" u2="&#x1e85;" k="51" />
    <hkern u1="\" u2="&#x1e84;" k="115" />
    <hkern u1="\" u2="&#x1e83;" k="51" />
    <hkern u1="\" u2="&#x1e82;" k="115" />
    <hkern u1="\" u2="&#x1e81;" k="51" />
    <hkern u1="\" u2="&#x1e80;" k="115" />
    <hkern u1="\" u2="&#x219;" k="25" />
    <hkern u1="\" u2="&#x1ff;" k="39" />
    <hkern u1="\" u2="&#x1fe;" k="76" />
    <hkern u1="\" u2="&#x1fd;" k="27" />
    <hkern u1="\" u2="&#x1fb;" k="27" />
    <hkern u1="\" u2="&#x178;" k="225" />
    <hkern u1="\" u2="&#x177;" k="55" />
    <hkern u1="\" u2="&#x176;" k="225" />
    <hkern u1="\" u2="&#x175;" k="51" />
    <hkern u1="\" u2="&#x174;" k="115" />
    <hkern u1="\" u2="&#x173;" k="35" />
    <hkern u1="\" u2="&#x172;" k="59" />
    <hkern u1="\" u2="&#x171;" k="35" />
    <hkern u1="\" u2="&#x170;" k="59" />
    <hkern u1="\" u2="&#x16f;" k="35" />
    <hkern u1="\" u2="&#x16e;" k="59" />
    <hkern u1="\" u2="&#x16d;" k="35" />
    <hkern u1="\" u2="&#x16c;" k="59" />
    <hkern u1="\" u2="&#x16b;" k="35" />
    <hkern u1="\" u2="&#x16a;" k="59" />
    <hkern u1="\" u2="&#x169;" k="35" />
    <hkern u1="\" u2="&#x168;" k="59" />
    <hkern u1="\" u2="&#x164;" k="147" />
    <hkern u1="\" u2="&#x163;" k="86" />
    <hkern u1="\" u2="&#x162;" k="147" />
    <hkern u1="\" u2="&#x161;" k="25" />
    <hkern u1="\" u2="&#x160;" k="43" />
    <hkern u1="\" u2="&#x15f;" k="25" />
    <hkern u1="\" u2="&#x15e;" k="43" />
    <hkern u1="\" u2="&#x15d;" k="25" />
    <hkern u1="\" u2="&#x15c;" k="43" />
    <hkern u1="\" u2="&#x15b;" k="25" />
    <hkern u1="\" u2="&#x15a;" k="43" />
    <hkern u1="\" u2="&#x152;" k="76" />
    <hkern u1="\" u2="&#x151;" k="39" />
    <hkern u1="\" u2="&#x150;" k="76" />
    <hkern u1="\" u2="&#x14f;" k="39" />
    <hkern u1="\" u2="&#x14e;" k="76" />
    <hkern u1="\" u2="&#x14d;" k="39" />
    <hkern u1="\" u2="&#x14c;" k="76" />
    <hkern u1="\" u2="&#x149;" k="250" />
    <hkern u1="\" u2="&#x134;" k="41" />
    <hkern u1="\" u2="&#x123;" k="39" />
    <hkern u1="\" u2="&#x122;" k="76" />
    <hkern u1="\" u2="&#x121;" k="39" />
    <hkern u1="\" u2="&#x120;" k="76" />
    <hkern u1="\" u2="&#x11f;" k="39" />
    <hkern u1="\" u2="&#x11e;" k="76" />
    <hkern u1="\" u2="&#x11d;" k="39" />
    <hkern u1="\" u2="&#x11c;" k="76" />
    <hkern u1="\" u2="&#x11b;" k="39" />
    <hkern u1="\" u2="&#x119;" k="39" />
    <hkern u1="\" u2="&#x117;" k="39" />
    <hkern u1="\" u2="&#x115;" k="39" />
    <hkern u1="\" u2="&#x113;" k="39" />
    <hkern u1="\" u2="&#x111;" k="39" />
    <hkern u1="\" u2="&#x10f;" k="39" />
    <hkern u1="\" u2="&#x10d;" k="39" />
    <hkern u1="\" u2="&#x10c;" k="76" />
    <hkern u1="\" u2="&#x10b;" k="39" />
    <hkern u1="\" u2="&#x10a;" k="76" />
    <hkern u1="\" u2="&#x109;" k="39" />
    <hkern u1="\" u2="&#x108;" k="76" />
    <hkern u1="\" u2="&#x107;" k="39" />
    <hkern u1="\" u2="&#x106;" k="76" />
    <hkern u1="\" u2="&#x105;" k="27" />
    <hkern u1="\" u2="&#x103;" k="27" />
    <hkern u1="\" u2="&#x101;" k="27" />
    <hkern u1="\" u2="&#xff;" k="55" />
    <hkern u1="\" u2="&#xfd;" k="55" />
    <hkern u1="\" u2="&#xfc;" k="35" />
    <hkern u1="\" u2="&#xfb;" k="35" />
    <hkern u1="\" u2="&#xfa;" k="35" />
    <hkern u1="\" u2="&#xf9;" k="35" />
    <hkern u1="\" u2="&#xf8;" k="39" />
    <hkern u1="\" u2="&#xf6;" k="39" />
    <hkern u1="\" u2="&#xf5;" k="39" />
    <hkern u1="\" u2="&#xf4;" k="39" />
    <hkern u1="\" u2="&#xf3;" k="39" />
    <hkern u1="\" u2="&#xf2;" k="39" />
    <hkern u1="\" u2="&#xeb;" k="39" />
    <hkern u1="\" u2="&#xea;" k="39" />
    <hkern u1="\" u2="&#xe9;" k="39" />
    <hkern u1="\" u2="&#xe8;" k="39" />
    <hkern u1="\" u2="&#xe7;" k="39" />
    <hkern u1="\" u2="&#xe6;" k="27" />
    <hkern u1="\" u2="&#xe5;" k="27" />
    <hkern u1="\" u2="&#xe4;" k="27" />
    <hkern u1="\" u2="&#xe3;" k="27" />
    <hkern u1="\" u2="&#xe2;" k="27" />
    <hkern u1="\" u2="&#xe1;" k="27" />
    <hkern u1="\" u2="&#xe0;" k="27" />
    <hkern u1="\" u2="&#xdd;" k="225" />
    <hkern u1="\" u2="&#xdc;" k="59" />
    <hkern u1="\" u2="&#xdb;" k="59" />
    <hkern u1="\" u2="&#xda;" k="59" />
    <hkern u1="\" u2="&#xd9;" k="59" />
    <hkern u1="\" u2="&#xd8;" k="76" />
    <hkern u1="\" u2="&#xd6;" k="76" />
    <hkern u1="\" u2="&#xd5;" k="76" />
    <hkern u1="\" u2="&#xd4;" k="76" />
    <hkern u1="\" u2="&#xd3;" k="76" />
    <hkern u1="\" u2="&#xd2;" k="76" />
    <hkern u1="\" u2="&#xc7;" k="76" />
    <hkern u1="\" u2="&#xba;" k="94" />
    <hkern u1="\" u2="&#xab;" k="57" />
    <hkern u1="\" u2="&#xaa;" k="94" />
    <hkern u1="\" u2="&#xa9;" k="92" />
    <hkern u1="\" u2="&#x7d;" k="-16" />
    <hkern u1="\" u2="&#x7b;" k="20" />
    <hkern u1="\" u2="y" k="55" />
    <hkern u1="\" u2="w" k="51" />
    <hkern u1="\" u2="u" k="35" />
    <hkern u1="\" u2="t" k="86" />
    <hkern u1="\" u2="s" k="25" />
    <hkern u1="\" u2="q" k="39" />
    <hkern u1="\" u2="o" k="39" />
    <hkern u1="\" u2="g" k="39" />
    <hkern u1="\" u2="f" k="20" />
    <hkern u1="\" u2="e" k="39" />
    <hkern u1="\" u2="d" k="39" />
    <hkern u1="\" u2="c" k="39" />
    <hkern u1="\" u2="a" k="27" />
    <hkern u1="\" u2="]" k="-16" />
    <hkern u1="\" u2="[" k="20" />
    <hkern u1="\" u2="Y" k="225" />
    <hkern u1="\" u2="W" k="115" />
    <hkern u1="\" u2="U" k="59" />
    <hkern u1="\" u2="T" k="147" />
    <hkern u1="\" u2="S" k="43" />
    <hkern u1="\" u2="Q" k="76" />
    <hkern u1="\" u2="O" k="76" />
    <hkern u1="\" u2="J" k="41" />
    <hkern u1="\" u2="G" k="76" />
    <hkern u1="\" u2="C" k="76" />
    <hkern u1="\" u2="&#x3b;" k="51" />
    <hkern u1="\" u2="&#x3a;" k="51" />
    <hkern u1="\" u2="&#x2e;" k="29" />
    <hkern u1="\" u2="&#x2d;" k="135" />
    <hkern u1="\" u2="&#x2c;" k="-41" />
    <hkern u1="\" u2="&#x29;" k="-16" />
    <hkern u1="\" u2="&#x28;" k="20" />
    <hkern u1="\" u2="&#x25;" k="193" />
    <hkern u1="\" u2="&#x2122;" k="250" />
    <hkern u1="\" u2="&#x20ac;" k="84" />
    <hkern u1="\" u2="&#xbf;" k="29" />
    <hkern u1="\" u2="&#xae;" k="236" />
    <hkern u1="\" u2="&#xa5;" k="72" />
    <hkern u1="\" u2="&#xa3;" k="10" />
    <hkern u1="\" u2="&#xa2;" k="104" />
    <hkern u1="\" u2="v" k="88" />
    <hkern u1="\" u2="j" k="-33" />
    <hkern u1="\" u2="V" k="209" />
    <hkern u1="\" u2="&#x40;" k="41" />
    <hkern u1="\" u2="&#x3f;" k="166" />
    <hkern u1="\" u2="&#x39;" k="127" />
    <hkern u1="\" u2="&#x38;" k="35" />
    <hkern u1="\" u2="&#x37;" k="18" />
    <hkern u1="\" u2="&#x36;" k="31" />
    <hkern u1="\" u2="&#x35;" k="41" />
    <hkern u1="\" u2="&#x34;" k="33" />
    <hkern u1="\" u2="&#x33;" k="27" />
    <hkern u1="\" u2="&#x32;" k="-6" />
    <hkern u1="\" u2="&#x31;" k="80" />
    <hkern u1="\" u2="&#x30;" k="45" />
    <hkern u1="\" u2="&#x2a;" k="246" />
    <hkern u1="\" u2="&#x26;" k="68" />
    <hkern u1="\" u2="&#x24;" k="37" />
    <hkern u1="\" u2="&#x23;" k="35" />
    <hkern u1="\" u2="&#x21;" k="12" />
    <hkern u1="]" u2="&#x2122;" k="4" />
    <hkern u1="]" u2="\" k="72" />
    <hkern u1="]" u2="&#x2f;" k="41" />
    <hkern u1="]" u2="&#x2a;" k="20" />
    <hkern u1="a" u2="&#x2122;" k="115" />
    <hkern u1="a" u2="&#xae;" k="18" />
    <hkern u1="a" u2="x" k="-4" />
    <hkern u1="a" u2="v" k="23" />
    <hkern u1="a" u2="\" k="117" />
    <hkern u1="a" u2="&#x3f;" k="74" />
    <hkern u1="a" u2="&#x2a;" k="20" />
    <hkern u1="b" u2="&#x2122;" k="61" />
    <hkern u1="b" u2="&#xae;" k="16" />
    <hkern u1="b" u2="x" k="41" />
    <hkern u1="b" u2="v" k="20" />
    <hkern u1="b" u2="\" k="119" />
    <hkern u1="b" u2="&#x3f;" k="27" />
    <hkern u1="b" u2="&#x2f;" k="53" />
    <hkern u1="b" u2="&#x2a;" k="41" />
    <hkern u1="c" u2="&#x2122;" k="61" />
    <hkern u1="c" u2="&#xae;" k="10" />
    <hkern u1="c" u2="x" k="12" />
    <hkern u1="c" u2="v" k="6" />
    <hkern u1="c" u2="\" k="111" />
    <hkern u1="c" u2="&#x3f;" k="31" />
    <hkern u1="c" u2="&#x2f;" k="20" />
    <hkern u1="c" u2="&#x2a;" k="20" />
    <hkern u1="e" u2="&#x2122;" k="74" />
    <hkern u1="e" u2="&#xae;" k="16" />
    <hkern u1="e" u2="x" k="25" />
    <hkern u1="e" u2="v" k="20" />
    <hkern u1="e" u2="\" k="111" />
    <hkern u1="e" u2="&#x3f;" k="31" />
    <hkern u1="e" u2="&#x2f;" k="27" />
    <hkern u1="e" u2="&#x2a;" k="20" />
    <hkern u1="f" u2="&#xfb02;" k="4" />
    <hkern u1="f" u2="&#xfb01;" k="4" />
    <hkern u1="f" u2="&#x2117;" k="-10" />
    <hkern u1="f" u2="&#x2039;" k="35" />
    <hkern u1="f" u2="&#x2026;" k="113" />
    <hkern u1="f" u2="&#x201e;" k="96" />
    <hkern u1="f" u2="&#x201d;" k="-41" />
    <hkern u1="f" u2="&#x201c;" k="-37" />
    <hkern u1="f" u2="&#x201a;" k="96" />
    <hkern u1="f" u2="&#x2019;" k="-41" />
    <hkern u1="f" u2="&#x2018;" k="-37" />
    <hkern u1="f" u2="&#x2014;" k="41" />
    <hkern u1="f" u2="&#x2013;" k="41" />
    <hkern u1="f" u2="&#x1ef3;" k="-12" />
    <hkern u1="f" u2="&#x1e85;" k="-10" />
    <hkern u1="f" u2="&#x1e83;" k="-10" />
    <hkern u1="f" u2="&#x1e81;" k="-10" />
    <hkern u1="f" u2="&#x219;" k="20" />
    <hkern u1="f" u2="&#x1ff;" k="31" />
    <hkern u1="f" u2="&#x1fd;" k="68" />
    <hkern u1="f" u2="&#x1fb;" k="68" />
    <hkern u1="f" u2="&#x17e;" k="4" />
    <hkern u1="f" u2="&#x17c;" k="4" />
    <hkern u1="f" u2="&#x17a;" k="4" />
    <hkern u1="f" u2="&#x177;" k="-12" />
    <hkern u1="f" u2="&#x175;" k="-10" />
    <hkern u1="f" u2="&#x173;" k="4" />
    <hkern u1="f" u2="&#x171;" k="4" />
    <hkern u1="f" u2="&#x16f;" k="4" />
    <hkern u1="f" u2="&#x16d;" k="4" />
    <hkern u1="f" u2="&#x16b;" k="4" />
    <hkern u1="f" u2="&#x169;" k="4" />
    <hkern u1="f" u2="&#x163;" k="6" />
    <hkern u1="f" u2="&#x161;" k="20" />
    <hkern u1="f" u2="&#x15f;" k="20" />
    <hkern u1="f" u2="&#x15d;" k="20" />
    <hkern u1="f" u2="&#x15b;" k="20" />
    <hkern u1="f" u2="&#x159;" k="10" />
    <hkern u1="f" u2="&#x157;" k="10" />
    <hkern u1="f" u2="&#x155;" k="10" />
    <hkern u1="f" u2="&#x151;" k="31" />
    <hkern u1="f" u2="&#x14f;" k="31" />
    <hkern u1="f" u2="&#x14d;" k="31" />
    <hkern u1="f" u2="&#x14b;" k="10" />
    <hkern u1="f" u2="&#x149;" k="-41" />
    <hkern u1="f" u2="&#x148;" k="10" />
    <hkern u1="f" u2="&#x146;" k="10" />
    <hkern u1="f" u2="&#x144;" k="10" />
    <hkern u1="f" u2="&#x123;" k="31" />
    <hkern u1="f" u2="&#x121;" k="31" />
    <hkern u1="f" u2="&#x11f;" k="31" />
    <hkern u1="f" u2="&#x11d;" k="31" />
    <hkern u1="f" u2="&#x11b;" k="31" />
    <hkern u1="f" u2="&#x119;" k="31" />
    <hkern u1="f" u2="&#x117;" k="31" />
    <hkern u1="f" u2="&#x115;" k="31" />
    <hkern u1="f" u2="&#x113;" k="31" />
    <hkern u1="f" u2="&#x111;" k="31" />
    <hkern u1="f" u2="&#x10f;" k="31" />
    <hkern u1="f" u2="&#x10d;" k="31" />
    <hkern u1="f" u2="&#x10b;" k="31" />
    <hkern u1="f" u2="&#x109;" k="31" />
    <hkern u1="f" u2="&#x107;" k="31" />
    <hkern u1="f" u2="&#x105;" k="68" />
    <hkern u1="f" u2="&#x103;" k="68" />
    <hkern u1="f" u2="&#x101;" k="68" />
    <hkern u1="f" u2="&#xff;" k="-12" />
    <hkern u1="f" u2="&#xfd;" k="-12" />
    <hkern u1="f" u2="&#xfc;" k="4" />
    <hkern u1="f" u2="&#xfb;" k="4" />
    <hkern u1="f" u2="&#xfa;" k="4" />
    <hkern u1="f" u2="&#xf9;" k="4" />
    <hkern u1="f" u2="&#xf8;" k="31" />
    <hkern u1="f" u2="&#xf6;" k="31" />
    <hkern u1="f" u2="&#xf5;" k="31" />
    <hkern u1="f" u2="&#xf4;" k="31" />
    <hkern u1="f" u2="&#xf3;" k="31" />
    <hkern u1="f" u2="&#xf2;" k="31" />
    <hkern u1="f" u2="&#xeb;" k="31" />
    <hkern u1="f" u2="&#xea;" k="31" />
    <hkern u1="f" u2="&#xe9;" k="31" />
    <hkern u1="f" u2="&#xe8;" k="31" />
    <hkern u1="f" u2="&#xe7;" k="31" />
    <hkern u1="f" u2="&#xe6;" k="68" />
    <hkern u1="f" u2="&#xe5;" k="68" />
    <hkern u1="f" u2="&#xe4;" k="68" />
    <hkern u1="f" u2="&#xe3;" k="68" />
    <hkern u1="f" u2="&#xe2;" k="68" />
    <hkern u1="f" u2="&#xe1;" k="68" />
    <hkern u1="f" u2="&#xe0;" k="68" />
    <hkern u1="f" u2="&#xba;" k="-16" />
    <hkern u1="f" u2="&#xab;" k="35" />
    <hkern u1="f" u2="&#xaa;" k="-16" />
    <hkern u1="f" u2="&#xa9;" k="-10" />
    <hkern u1="f" u2="z" k="4" />
    <hkern u1="f" u2="y" k="-12" />
    <hkern u1="f" u2="w" k="-10" />
    <hkern u1="f" u2="u" k="4" />
    <hkern u1="f" u2="t" k="6" />
    <hkern u1="f" u2="s" k="20" />
    <hkern u1="f" u2="r" k="10" />
    <hkern u1="f" u2="q" k="31" />
    <hkern u1="f" u2="p" k="10" />
    <hkern u1="f" u2="o" k="31" />
    <hkern u1="f" u2="n" k="10" />
    <hkern u1="f" u2="m" k="10" />
    <hkern u1="f" u2="g" k="31" />
    <hkern u1="f" u2="f" k="4" />
    <hkern u1="f" u2="e" k="31" />
    <hkern u1="f" u2="d" k="31" />
    <hkern u1="f" u2="c" k="31" />
    <hkern u1="f" u2="a" k="68" />
    <hkern u1="f" u2="&#x3b;" k="23" />
    <hkern u1="f" u2="&#x3a;" k="23" />
    <hkern u1="f" u2="&#x2e;" k="113" />
    <hkern u1="f" u2="&#x2d;" k="41" />
    <hkern u1="f" u2="&#x2c;" k="96" />
    <hkern u1="f" u2="&#x2122;" k="-74" />
    <hkern u1="f" u2="&#xbf;" k="41" />
    <hkern u1="f" u2="&#xae;" k="-41" />
    <hkern u1="f" u2="x" k="-6" />
    <hkern u1="f" u2="v" k="-12" />
    <hkern u1="f" u2="&#x40;" k="10" />
    <hkern u1="f" u2="&#x3f;" k="-31" />
    <hkern u1="f" u2="&#x2f;" k="92" />
    <hkern u1="f" u2="&#x2a;" k="-20" />
    <hkern u1="f" u2="&#x26;" k="10" />
    <hkern u1="g" u2="&#x2122;" k="16" />
    <hkern u1="g" u2="\" k="20" />
    <hkern u1="h" u2="&#x2122;" k="98" />
    <hkern u1="h" u2="&#xae;" k="27" />
    <hkern u1="h" u2="v" k="8" />
    <hkern u1="h" u2="\" k="109" />
    <hkern u1="h" u2="&#x2a;" k="31" />
    <hkern u1="i" u2="&#x7d;" k="10" />
    <hkern u1="i" u2="]" k="10" />
    <hkern u1="i" u2="&#x29;" k="10" />
    <hkern u1="j" u2="&#x201e;" k="10" />
    <hkern u1="j" u2="&#x201a;" k="10" />
    <hkern u1="j" u2="&#x7d;" k="10" />
    <hkern u1="j" u2="]" k="10" />
    <hkern u1="j" u2="&#x2c;" k="10" />
    <hkern u1="j" u2="&#x29;" k="10" />
    <hkern u1="j" u2="\" k="20" />
    <hkern u1="j" u2="&#x2f;" k="10" />
    <hkern u1="k" u2="&#xbf;" k="10" />
    <hkern u1="k" u2="&#xae;" k="-10" />
    <hkern u1="k" u2="x" k="-12" />
    <hkern u1="k" u2="\" k="51" />
    <hkern u1="k" u2="&#x40;" k="14" />
    <hkern u1="k" u2="&#x3f;" k="-20" />
    <hkern u1="k" u2="&#x26;" k="20" />
    <hkern u1="m" u2="&#x2122;" k="98" />
    <hkern u1="m" u2="&#xae;" k="27" />
    <hkern u1="m" u2="v" k="8" />
    <hkern u1="m" u2="\" k="109" />
    <hkern u1="m" u2="&#x2a;" k="31" />
    <hkern u1="n" u2="&#x2122;" k="98" />
    <hkern u1="n" u2="&#xae;" k="27" />
    <hkern u1="n" u2="v" k="8" />
    <hkern u1="n" u2="\" k="109" />
    <hkern u1="n" u2="&#x2a;" k="31" />
    <hkern u1="o" u2="&#x2122;" k="61" />
    <hkern u1="o" u2="&#xae;" k="16" />
    <hkern u1="o" u2="x" k="41" />
    <hkern u1="o" u2="v" k="20" />
    <hkern u1="o" u2="\" k="119" />
    <hkern u1="o" u2="&#x3f;" k="27" />
    <hkern u1="o" u2="&#x2f;" k="53" />
    <hkern u1="o" u2="&#x2a;" k="41" />
    <hkern u1="p" u2="&#x2122;" k="61" />
    <hkern u1="p" u2="&#xae;" k="16" />
    <hkern u1="p" u2="x" k="41" />
    <hkern u1="p" u2="v" k="20" />
    <hkern u1="p" u2="\" k="119" />
    <hkern u1="p" u2="&#x3f;" k="27" />
    <hkern u1="p" u2="&#x2f;" k="53" />
    <hkern u1="p" u2="&#x2a;" k="41" />
    <hkern u1="q" u2="&#x2122;" k="16" />
    <hkern u1="q" u2="\" k="20" />
    <hkern u1="r" u2="&#xbf;" k="31" />
    <hkern u1="r" u2="&#xae;" k="-20" />
    <hkern u1="r" u2="x" k="-16" />
    <hkern u1="r" u2="v" k="-41" />
    <hkern u1="r" u2="\" k="45" />
    <hkern u1="r" u2="&#x3f;" k="-20" />
    <hkern u1="r" u2="&#x2f;" k="111" />
    <hkern u1="r" u2="&#x2a;" k="-20" />
    <hkern u1="r" u2="&#x26;" k="10" />
    <hkern u1="s" u2="&#x2122;" k="66" />
    <hkern u1="s" u2="&#xae;" k="4" />
    <hkern u1="s" u2="x" k="14" />
    <hkern u1="s" u2="v" k="20" />
    <hkern u1="s" u2="\" k="121" />
    <hkern u1="s" u2="&#x3f;" k="20" />
    <hkern u1="s" u2="&#x2f;" k="47" />
    <hkern u1="s" u2="&#x2a;" k="41" />
    <hkern u1="t" u2="&#x2122;" k="12" />
    <hkern u1="t" u2="&#xae;" k="-16" />
    <hkern u1="t" u2="x" k="-18" />
    <hkern u1="t" u2="v" k="-16" />
    <hkern u1="t" u2="\" k="72" />
    <hkern u1="t" u2="&#x2f;" k="16" />
    <hkern u1="u" u2="&#x2122;" k="16" />
    <hkern u1="u" u2="\" k="20" />
    <hkern u1="v" u2="&#xfb02;" k="-12" />
    <hkern u1="v" u2="&#xfb01;" k="-12" />
    <hkern u1="v" u2="&#x203a;" k="-12" />
    <hkern u1="v" u2="&#x2039;" k="12" />
    <hkern u1="v" u2="&#x2026;" k="143" />
    <hkern u1="v" u2="&#x201e;" k="113" />
    <hkern u1="v" u2="&#x201d;" k="-51" />
    <hkern u1="v" u2="&#x201c;" k="-61" />
    <hkern u1="v" u2="&#x201a;" k="113" />
    <hkern u1="v" u2="&#x2019;" k="-51" />
    <hkern u1="v" u2="&#x2018;" k="-61" />
    <hkern u1="v" u2="&#x2014;" k="23" />
    <hkern u1="v" u2="&#x2013;" k="23" />
    <hkern u1="v" u2="&#x1ef3;" k="-14" />
    <hkern u1="v" u2="&#x1e85;" k="-4" />
    <hkern u1="v" u2="&#x1e83;" k="-4" />
    <hkern u1="v" u2="&#x1e81;" k="-4" />
    <hkern u1="v" u2="&#x219;" k="8" />
    <hkern u1="v" u2="&#x1ff;" k="20" />
    <hkern u1="v" u2="&#x1fd;" k="59" />
    <hkern u1="v" u2="&#x1fb;" k="59" />
    <hkern u1="v" u2="&#x177;" k="-14" />
    <hkern u1="v" u2="&#x175;" k="-4" />
    <hkern u1="v" u2="&#x163;" k="-16" />
    <hkern u1="v" u2="&#x161;" k="8" />
    <hkern u1="v" u2="&#x15f;" k="8" />
    <hkern u1="v" u2="&#x15d;" k="8" />
    <hkern u1="v" u2="&#x15b;" k="8" />
    <hkern u1="v" u2="&#x151;" k="20" />
    <hkern u1="v" u2="&#x14f;" k="20" />
    <hkern u1="v" u2="&#x14d;" k="20" />
    <hkern u1="v" u2="&#x149;" k="-51" />
    <hkern u1="v" u2="&#x123;" k="20" />
    <hkern u1="v" u2="&#x121;" k="20" />
    <hkern u1="v" u2="&#x11f;" k="20" />
    <hkern u1="v" u2="&#x11d;" k="20" />
    <hkern u1="v" u2="&#x11b;" k="20" />
    <hkern u1="v" u2="&#x119;" k="20" />
    <hkern u1="v" u2="&#x117;" k="20" />
    <hkern u1="v" u2="&#x115;" k="20" />
    <hkern u1="v" u2="&#x113;" k="20" />
    <hkern u1="v" u2="&#x111;" k="20" />
    <hkern u1="v" u2="&#x10f;" k="20" />
    <hkern u1="v" u2="&#x10d;" k="20" />
    <hkern u1="v" u2="&#x10b;" k="20" />
    <hkern u1="v" u2="&#x109;" k="20" />
    <hkern u1="v" u2="&#x107;" k="20" />
    <hkern u1="v" u2="&#x105;" k="59" />
    <hkern u1="v" u2="&#x103;" k="59" />
    <hkern u1="v" u2="&#x101;" k="59" />
    <hkern u1="v" u2="&#xff;" k="-14" />
    <hkern u1="v" u2="&#xfd;" k="-14" />
    <hkern u1="v" u2="&#xf8;" k="20" />
    <hkern u1="v" u2="&#xf6;" k="20" />
    <hkern u1="v" u2="&#xf5;" k="20" />
    <hkern u1="v" u2="&#xf4;" k="20" />
    <hkern u1="v" u2="&#xf3;" k="20" />
    <hkern u1="v" u2="&#xf2;" k="20" />
    <hkern u1="v" u2="&#xeb;" k="20" />
    <hkern u1="v" u2="&#xea;" k="20" />
    <hkern u1="v" u2="&#xe9;" k="20" />
    <hkern u1="v" u2="&#xe8;" k="20" />
    <hkern u1="v" u2="&#xe7;" k="20" />
    <hkern u1="v" u2="&#xe6;" k="59" />
    <hkern u1="v" u2="&#xe5;" k="59" />
    <hkern u1="v" u2="&#xe4;" k="59" />
    <hkern u1="v" u2="&#xe3;" k="59" />
    <hkern u1="v" u2="&#xe2;" k="59" />
    <hkern u1="v" u2="&#xe1;" k="59" />
    <hkern u1="v" u2="&#xe0;" k="59" />
    <hkern u1="v" u2="&#xbb;" k="-12" />
    <hkern u1="v" u2="&#xba;" k="-37" />
    <hkern u1="v" u2="&#xab;" k="12" />
    <hkern u1="v" u2="&#xaa;" k="-37" />
    <hkern u1="v" u2="&#x7d;" k="27" />
    <hkern u1="v" u2="y" k="-14" />
    <hkern u1="v" u2="w" k="-4" />
    <hkern u1="v" u2="t" k="-16" />
    <hkern u1="v" u2="s" k="8" />
    <hkern u1="v" u2="q" k="20" />
    <hkern u1="v" u2="o" k="20" />
    <hkern u1="v" u2="g" k="20" />
    <hkern u1="v" u2="f" k="-12" />
    <hkern u1="v" u2="e" k="20" />
    <hkern u1="v" u2="d" k="20" />
    <hkern u1="v" u2="c" k="20" />
    <hkern u1="v" u2="a" k="59" />
    <hkern u1="v" u2="]" k="27" />
    <hkern u1="v" u2="&#x3b;" k="31" />
    <hkern u1="v" u2="&#x3a;" k="31" />
    <hkern u1="v" u2="&#x2e;" k="143" />
    <hkern u1="v" u2="&#x2d;" k="23" />
    <hkern u1="v" u2="&#x2c;" k="113" />
    <hkern u1="v" u2="&#x29;" k="27" />
    <hkern u1="v" u2="&#x2122;" k="-20" />
    <hkern u1="v" u2="&#xbf;" k="37" />
    <hkern u1="v" u2="&#xae;" k="-37" />
    <hkern u1="v" u2="x" k="-4" />
    <hkern u1="v" u2="v" k="-4" />
    <hkern u1="v" u2="\" k="18" />
    <hkern u1="v" u2="&#x3f;" k="-39" />
    <hkern u1="v" u2="&#x2f;" k="92" />
    <hkern u1="v" u2="&#x2a;" k="-31" />
    <hkern u1="v" u2="&#x26;" k="20" />
    <hkern u1="w" u2="&#xbf;" k="37" />
    <hkern u1="w" u2="&#xae;" k="-37" />
    <hkern u1="w" u2="v" k="-4" />
    <hkern u1="w" u2="\" k="14" />
    <hkern u1="w" u2="&#x3f;" k="-39" />
    <hkern u1="w" u2="&#x2f;" k="70" />
    <hkern u1="w" u2="&#x2a;" k="-31" />
    <hkern u1="w" u2="&#x26;" k="16" />
    <hkern u1="x" u2="&#x2117;" k="20" />
    <hkern u1="x" u2="&#x2039;" k="61" />
    <hkern u1="x" u2="&#x2026;" k="10" />
    <hkern u1="x" u2="&#x201e;" k="-6" />
    <hkern u1="x" u2="&#x201d;" k="8" />
    <hkern u1="x" u2="&#x201a;" k="-6" />
    <hkern u1="x" u2="&#x2019;" k="8" />
    <hkern u1="x" u2="&#x2014;" k="72" />
    <hkern u1="x" u2="&#x2013;" k="72" />
    <hkern u1="x" u2="&#x1ef3;" k="-4" />
    <hkern u1="x" u2="&#x219;" k="10" />
    <hkern u1="x" u2="&#x1ff;" k="41" />
    <hkern u1="x" u2="&#x1fd;" k="37" />
    <hkern u1="x" u2="&#x1fb;" k="37" />
    <hkern u1="x" u2="&#x177;" k="-4" />
    <hkern u1="x" u2="&#x173;" k="10" />
    <hkern u1="x" u2="&#x171;" k="10" />
    <hkern u1="x" u2="&#x16f;" k="10" />
    <hkern u1="x" u2="&#x16d;" k="10" />
    <hkern u1="x" u2="&#x16b;" k="10" />
    <hkern u1="x" u2="&#x169;" k="10" />
    <hkern u1="x" u2="&#x163;" k="10" />
    <hkern u1="x" u2="&#x161;" k="10" />
    <hkern u1="x" u2="&#x15f;" k="10" />
    <hkern u1="x" u2="&#x15d;" k="10" />
    <hkern u1="x" u2="&#x15b;" k="10" />
    <hkern u1="x" u2="&#x151;" k="41" />
    <hkern u1="x" u2="&#x14f;" k="41" />
    <hkern u1="x" u2="&#x14d;" k="41" />
    <hkern u1="x" u2="&#x149;" k="8" />
    <hkern u1="x" u2="&#x123;" k="41" />
    <hkern u1="x" u2="&#x121;" k="41" />
    <hkern u1="x" u2="&#x11f;" k="41" />
    <hkern u1="x" u2="&#x11d;" k="41" />
    <hkern u1="x" u2="&#x11b;" k="41" />
    <hkern u1="x" u2="&#x119;" k="41" />
    <hkern u1="x" u2="&#x117;" k="41" />
    <hkern u1="x" u2="&#x115;" k="41" />
    <hkern u1="x" u2="&#x113;" k="41" />
    <hkern u1="x" u2="&#x111;" k="41" />
    <hkern u1="x" u2="&#x10f;" k="41" />
    <hkern u1="x" u2="&#x10d;" k="41" />
    <hkern u1="x" u2="&#x10b;" k="41" />
    <hkern u1="x" u2="&#x109;" k="41" />
    <hkern u1="x" u2="&#x107;" k="41" />
    <hkern u1="x" u2="&#x105;" k="37" />
    <hkern u1="x" u2="&#x103;" k="37" />
    <hkern u1="x" u2="&#x101;" k="37" />
    <hkern u1="x" u2="&#xff;" k="-4" />
    <hkern u1="x" u2="&#xfd;" k="-4" />
    <hkern u1="x" u2="&#xfc;" k="10" />
    <hkern u1="x" u2="&#xfb;" k="10" />
    <hkern u1="x" u2="&#xfa;" k="10" />
    <hkern u1="x" u2="&#xf9;" k="10" />
    <hkern u1="x" u2="&#xf8;" k="41" />
    <hkern u1="x" u2="&#xf6;" k="41" />
    <hkern u1="x" u2="&#xf5;" k="41" />
    <hkern u1="x" u2="&#xf4;" k="41" />
    <hkern u1="x" u2="&#xf3;" k="41" />
    <hkern u1="x" u2="&#xf2;" k="41" />
    <hkern u1="x" u2="&#xeb;" k="41" />
    <hkern u1="x" u2="&#xea;" k="41" />
    <hkern u1="x" u2="&#xe9;" k="41" />
    <hkern u1="x" u2="&#xe8;" k="41" />
    <hkern u1="x" u2="&#xe7;" k="41" />
    <hkern u1="x" u2="&#xe6;" k="37" />
    <hkern u1="x" u2="&#xe5;" k="37" />
    <hkern u1="x" u2="&#xe4;" k="37" />
    <hkern u1="x" u2="&#xe3;" k="37" />
    <hkern u1="x" u2="&#xe2;" k="37" />
    <hkern u1="x" u2="&#xe1;" k="37" />
    <hkern u1="x" u2="&#xe0;" k="37" />
    <hkern u1="x" u2="&#xab;" k="61" />
    <hkern u1="x" u2="&#xa9;" k="20" />
    <hkern u1="x" u2="&#x7d;" k="6" />
    <hkern u1="x" u2="y" k="-4" />
    <hkern u1="x" u2="u" k="10" />
    <hkern u1="x" u2="t" k="10" />
    <hkern u1="x" u2="s" k="10" />
    <hkern u1="x" u2="q" k="41" />
    <hkern u1="x" u2="o" k="41" />
    <hkern u1="x" u2="g" k="41" />
    <hkern u1="x" u2="e" k="41" />
    <hkern u1="x" u2="d" k="41" />
    <hkern u1="x" u2="c" k="41" />
    <hkern u1="x" u2="a" k="37" />
    <hkern u1="x" u2="]" k="6" />
    <hkern u1="x" u2="&#x3b;" k="6" />
    <hkern u1="x" u2="&#x3a;" k="6" />
    <hkern u1="x" u2="&#x2e;" k="10" />
    <hkern u1="x" u2="&#x2d;" k="72" />
    <hkern u1="x" u2="&#x2c;" k="-6" />
    <hkern u1="x" u2="&#x29;" k="6" />
    <hkern u1="x" u2="&#x2122;" k="4" />
    <hkern u1="x" u2="&#xbf;" k="27" />
    <hkern u1="x" u2="x" k="-10" />
    <hkern u1="x" u2="v" k="-4" />
    <hkern u1="x" u2="\" k="45" />
    <hkern u1="x" u2="&#x40;" k="10" />
    <hkern u1="x" u2="&#x3f;" k="-12" />
    <hkern u1="x" u2="&#x26;" k="37" />
    <hkern u1="y" u2="&#xbf;" k="33" />
    <hkern u1="y" u2="&#xae;" k="-37" />
    <hkern u1="y" u2="\" k="14" />
    <hkern u1="y" u2="&#x40;" k="10" />
    <hkern u1="y" u2="&#x3f;" k="-45" />
    <hkern u1="y" u2="&#x2f;" k="72" />
    <hkern u1="y" u2="&#x2a;" k="-31" />
    <hkern u1="z" u2="x" k="-10" />
    <hkern u1="z" u2="\" k="41" />
    <hkern u1="&#x7b;" u2="&#x20ac;" k="82" />
    <hkern u1="&#x7b;" u2="&#xbf;" k="37" />
    <hkern u1="&#x7b;" u2="&#xae;" k="20" />
    <hkern u1="&#x7b;" u2="&#xa5;" k="20" />
    <hkern u1="&#x7b;" u2="&#xa2;" k="45" />
    <hkern u1="&#x7b;" u2="x" k="6" />
    <hkern u1="&#x7b;" u2="v" k="27" />
    <hkern u1="&#x7b;" u2="j" k="-47" />
    <hkern u1="&#x7b;" u2="i" k="10" />
    <hkern u1="&#x7b;" u2="\" k="20" />
    <hkern u1="&#x7b;" u2="V" k="-6" />
    <hkern u1="&#x7b;" u2="&#x40;" k="45" />
    <hkern u1="&#x7b;" u2="&#x3f;" k="23" />
    <hkern u1="&#x7b;" u2="&#x39;" k="47" />
    <hkern u1="&#x7b;" u2="&#x38;" k="47" />
    <hkern u1="&#x7b;" u2="&#x37;" k="10" />
    <hkern u1="&#x7b;" u2="&#x36;" k="53" />
    <hkern u1="&#x7b;" u2="&#x35;" k="33" />
    <hkern u1="&#x7b;" u2="&#x34;" k="55" />
    <hkern u1="&#x7b;" u2="&#x33;" k="18" />
    <hkern u1="&#x7b;" u2="&#x32;" k="10" />
    <hkern u1="&#x7b;" u2="&#x31;" k="18" />
    <hkern u1="&#x7b;" u2="&#x30;" k="37" />
    <hkern u1="&#x7b;" u2="&#x2a;" k="51" />
    <hkern u1="&#x7b;" u2="&#x26;" k="41" />
    <hkern u1="&#x7b;" u2="&#x24;" k="20" />
    <hkern u1="&#x7b;" u2="&#x23;" k="47" />
    <hkern u1="&#x7d;" u2="&#x2122;" k="4" />
    <hkern u1="&#x7d;" u2="\" k="72" />
    <hkern u1="&#x7d;" u2="&#x2f;" k="41" />
    <hkern u1="&#x7d;" u2="&#x2a;" k="20" />
    <hkern u1="&#xa1;" u2="&#x201d;" k="18" />
    <hkern u1="&#xa1;" u2="&#x201c;" k="6" />
    <hkern u1="&#xa1;" u2="&#x2019;" k="18" />
    <hkern u1="&#xa1;" u2="&#x2018;" k="6" />
    <hkern u1="&#xa1;" u2="&#x1ef2;" k="37" />
    <hkern u1="&#xa1;" u2="&#x1e84;" k="20" />
    <hkern u1="&#xa1;" u2="&#x1e82;" k="20" />
    <hkern u1="&#xa1;" u2="&#x1e80;" k="20" />
    <hkern u1="&#xa1;" u2="&#x178;" k="37" />
    <hkern u1="&#xa1;" u2="&#x176;" k="37" />
    <hkern u1="&#xa1;" u2="&#x174;" k="20" />
    <hkern u1="&#xa1;" u2="&#x149;" k="18" />
    <hkern u1="&#xa1;" u2="&#xdd;" k="37" />
    <hkern u1="&#xa1;" u2="Y" k="37" />
    <hkern u1="&#xa1;" u2="W" k="20" />
    <hkern u1="&#xa1;" u2="\" k="20" />
    <hkern u1="&#xa1;" u2="V" k="37" />
    <hkern u1="&#xa1;" u2="&#x2a;" k="37" />
    <hkern u1="&#xa2;" u2="&#x203a;" k="10" />
    <hkern u1="&#xa2;" u2="&#x2026;" k="37" />
    <hkern u1="&#xa2;" u2="&#x201e;" k="37" />
    <hkern u1="&#xa2;" u2="&#x201a;" k="37" />
    <hkern u1="&#xa2;" u2="&#xbb;" k="10" />
    <hkern u1="&#xa2;" u2="&#x7d;" k="37" />
    <hkern u1="&#xa2;" u2="]" k="37" />
    <hkern u1="&#xa2;" u2="&#x3b;" k="10" />
    <hkern u1="&#xa2;" u2="&#x3a;" k="10" />
    <hkern u1="&#xa2;" u2="&#x2e;" k="37" />
    <hkern u1="&#xa2;" u2="&#x2c;" k="37" />
    <hkern u1="&#xa2;" u2="&#x29;" k="37" />
    <hkern u1="&#xa2;" u2="&#x2122;" k="37" />
    <hkern u1="&#xa2;" u2="&#xae;" k="10" />
    <hkern u1="&#xa2;" u2="\" k="100" />
    <hkern u1="&#xa2;" u2="&#x3f;" k="20" />
    <hkern u1="&#xa2;" u2="&#x37;" k="10" />
    <hkern u1="&#xa2;" u2="&#x2f;" k="78" />
    <hkern u1="&#xa3;" u2="&#x2039;" k="37" />
    <hkern u1="&#xa3;" u2="&#x2026;" k="29" />
    <hkern u1="&#xa3;" u2="&#x201e;" k="20" />
    <hkern u1="&#xa3;" u2="&#x201a;" k="20" />
    <hkern u1="&#xa3;" u2="&#x2014;" k="41" />
    <hkern u1="&#xa3;" u2="&#x2013;" k="41" />
    <hkern u1="&#xa3;" u2="&#xab;" k="37" />
    <hkern u1="&#xa3;" u2="&#x7d;" k="27" />
    <hkern u1="&#xa3;" u2="]" k="27" />
    <hkern u1="&#xa3;" u2="&#x3b;" k="20" />
    <hkern u1="&#xa3;" u2="&#x3a;" k="20" />
    <hkern u1="&#xa3;" u2="&#x2e;" k="29" />
    <hkern u1="&#xa3;" u2="&#x2d;" k="41" />
    <hkern u1="&#xa3;" u2="&#x2c;" k="20" />
    <hkern u1="&#xa3;" u2="&#x29;" k="27" />
    <hkern u1="&#xa3;" u2="&#xbf;" k="29" />
    <hkern u1="&#xa3;" u2="&#xae;" k="-12" />
    <hkern u1="&#xa3;" u2="\" k="31" />
    <hkern u1="&#xa3;" u2="&#x40;" k="20" />
    <hkern u1="&#xa3;" u2="&#x36;" k="25" />
    <hkern u1="&#xa3;" u2="&#x35;" k="4" />
    <hkern u1="&#xa3;" u2="&#x34;" k="23" />
    <hkern u1="&#xa3;" u2="&#x31;" k="-37" />
    <hkern u1="&#xa3;" u2="&#x2f;" k="82" />
    <hkern u1="&#xa3;" u2="&#x26;" k="20" />
    <hkern u1="&#xa5;" u2="&#x2117;" k="20" />
    <hkern u1="&#xa5;" u2="&#x203a;" k="37" />
    <hkern u1="&#xa5;" u2="&#x2039;" k="27" />
    <hkern u1="&#xa5;" u2="&#x2026;" k="55" />
    <hkern u1="&#xa5;" u2="&#x201e;" k="53" />
    <hkern u1="&#xa5;" u2="&#x201d;" k="31" />
    <hkern u1="&#xa5;" u2="&#x201c;" k="25" />
    <hkern u1="&#xa5;" u2="&#x201a;" k="53" />
    <hkern u1="&#xa5;" u2="&#x2019;" k="31" />
    <hkern u1="&#xa5;" u2="&#x2018;" k="25" />
    <hkern u1="&#xa5;" u2="&#x2014;" k="20" />
    <hkern u1="&#xa5;" u2="&#x2013;" k="20" />
    <hkern u1="&#xa5;" u2="&#x149;" k="31" />
    <hkern u1="&#xa5;" u2="&#xbb;" k="37" />
    <hkern u1="&#xa5;" u2="&#xba;" k="20" />
    <hkern u1="&#xa5;" u2="&#xab;" k="27" />
    <hkern u1="&#xa5;" u2="&#xaa;" k="20" />
    <hkern u1="&#xa5;" u2="&#xa9;" k="20" />
    <hkern u1="&#xa5;" u2="&#x7d;" k="20" />
    <hkern u1="&#xa5;" u2="]" k="20" />
    <hkern u1="&#xa5;" u2="&#x3b;" k="45" />
    <hkern u1="&#xa5;" u2="&#x3a;" k="45" />
    <hkern u1="&#xa5;" u2="&#x2e;" k="55" />
    <hkern u1="&#xa5;" u2="&#x2d;" k="20" />
    <hkern u1="&#xa5;" u2="&#x2c;" k="53" />
    <hkern u1="&#xa5;" u2="&#x29;" k="20" />
    <hkern u1="&#xa5;" u2="&#xbf;" k="18" />
    <hkern u1="&#xa5;" u2="&#xae;" k="25" />
    <hkern u1="&#xa5;" u2="\" k="14" />
    <hkern u1="&#xa5;" u2="&#x40;" k="20" />
    <hkern u1="&#xa5;" u2="&#x3f;" k="25" />
    <hkern u1="&#xa5;" u2="&#x38;" k="8" />
    <hkern u1="&#xa5;" u2="&#x37;" k="8" />
    <hkern u1="&#xa5;" u2="&#x35;" k="8" />
    <hkern u1="&#xa5;" u2="&#x33;" k="10" />
    <hkern u1="&#xa5;" u2="&#x32;" k="10" />
    <hkern u1="&#xa5;" u2="&#x2f;" k="125" />
    <hkern u1="&#xa5;" u2="&#x2a;" k="20" />
    <hkern u1="&#xa5;" u2="&#x26;" k="20" />
    <hkern u1="&#xa9;" u2="&#xa5;" k="20" />
    <hkern u1="&#xa9;" u2="x" k="20" />
    <hkern u1="&#xa9;" u2="\" k="72" />
    <hkern u1="&#xa9;" u2="X" k="55" />
    <hkern u1="&#xa9;" u2="V" k="51" />
    <hkern u1="&#xa9;" u2="&#x3f;" k="10" />
    <hkern u1="&#xa9;" u2="&#x37;" k="20" />
    <hkern u1="&#xa9;" u2="&#x2f;" k="92" />
    <hkern u1="&#xaa;" u2="v" k="-37" />
    <hkern u1="&#xaa;" u2="\" k="39" />
    <hkern u1="&#xaa;" u2="X" k="20" />
    <hkern u1="&#xaa;" u2="&#x34;" k="8" />
    <hkern u1="&#xaa;" u2="&#x2f;" k="88" />
    <hkern u1="&#xab;" u2="&#x20ac;" k="20" />
    <hkern u1="&#xab;" u2="&#xa5;" k="37" />
    <hkern u1="&#xab;" u2="&#xa2;" k="10" />
    <hkern u1="&#xab;" u2="v" k="-12" />
    <hkern u1="&#xab;" u2="j" k="10" />
    <hkern u1="&#xab;" u2="\" k="80" />
    <hkern u1="&#xab;" u2="X" k="20" />
    <hkern u1="&#xab;" u2="V" k="80" />
    <hkern u1="&#xab;" u2="&#x39;" k="6" />
    <hkern u1="&#xab;" u2="&#x38;" k="12" />
    <hkern u1="&#xab;" u2="&#x37;" k="16" />
    <hkern u1="&#xab;" u2="&#x36;" k="31" />
    <hkern u1="&#xab;" u2="&#x35;" k="10" />
    <hkern u1="&#xab;" u2="&#x34;" k="16" />
    <hkern u1="&#xab;" u2="&#x33;" k="10" />
    <hkern u1="&#xab;" u2="&#x2f;" k="27" />
    <hkern u1="&#xab;" u2="&#x2a;" k="16" />
    <hkern u1="&#xab;" u2="&#x26;" k="12" />
    <hkern u1="&#xab;" u2="&#x23;" k="16" />
    <hkern u1="&#xae;" g2="Jcircumflex.salt" k="82" />
    <hkern u1="&#xae;" g2="J.salt" k="82" />
    <hkern u1="&#xae;" u2="&#xfb02;" k="-27" />
    <hkern u1="&#xae;" u2="&#xfb01;" k="-27" />
    <hkern u1="&#xae;" u2="&#x2026;" k="90" />
    <hkern u1="&#xae;" u2="&#x201d;" k="4" />
    <hkern u1="&#xae;" u2="&#x2019;" k="4" />
    <hkern u1="&#xae;" u2="&#x1ef3;" k="-37" />
    <hkern u1="&#xae;" u2="&#x1ef2;" k="37" />
    <hkern u1="&#xae;" u2="&#x1e85;" k="-37" />
    <hkern u1="&#xae;" u2="&#x1e83;" k="-37" />
    <hkern u1="&#xae;" u2="&#x1e81;" k="-37" />
    <hkern u1="&#xae;" u2="&#x219;" k="10" />
    <hkern u1="&#xae;" u2="&#x1ff;" k="16" />
    <hkern u1="&#xae;" u2="&#x1fd;" k="53" />
    <hkern u1="&#xae;" u2="&#x1fb;" k="53" />
    <hkern u1="&#xae;" u2="&#x1fa;" k="195" />
    <hkern u1="&#xae;" u2="&#x17e;" k="10" />
    <hkern u1="&#xae;" u2="&#x17d;" k="20" />
    <hkern u1="&#xae;" u2="&#x17c;" k="10" />
    <hkern u1="&#xae;" u2="&#x17b;" k="20" />
    <hkern u1="&#xae;" u2="&#x17a;" k="10" />
    <hkern u1="&#xae;" u2="&#x179;" k="20" />
    <hkern u1="&#xae;" u2="&#x178;" k="37" />
    <hkern u1="&#xae;" u2="&#x177;" k="-37" />
    <hkern u1="&#xae;" u2="&#x176;" k="37" />
    <hkern u1="&#xae;" u2="&#x175;" k="-37" />
    <hkern u1="&#xae;" u2="&#x163;" k="-16" />
    <hkern u1="&#xae;" u2="&#x161;" k="10" />
    <hkern u1="&#xae;" u2="&#x160;" k="10" />
    <hkern u1="&#xae;" u2="&#x15f;" k="10" />
    <hkern u1="&#xae;" u2="&#x15e;" k="10" />
    <hkern u1="&#xae;" u2="&#x15d;" k="10" />
    <hkern u1="&#xae;" u2="&#x15c;" k="10" />
    <hkern u1="&#xae;" u2="&#x15b;" k="10" />
    <hkern u1="&#xae;" u2="&#x15a;" k="10" />
    <hkern u1="&#xae;" u2="&#x151;" k="16" />
    <hkern u1="&#xae;" u2="&#x14f;" k="16" />
    <hkern u1="&#xae;" u2="&#x14d;" k="16" />
    <hkern u1="&#xae;" u2="&#x149;" k="4" />
    <hkern u1="&#xae;" u2="&#x134;" k="309" />
    <hkern u1="&#xae;" u2="&#x123;" k="16" />
    <hkern u1="&#xae;" u2="&#x121;" k="16" />
    <hkern u1="&#xae;" u2="&#x11f;" k="16" />
    <hkern u1="&#xae;" u2="&#x11d;" k="16" />
    <hkern u1="&#xae;" u2="&#x11b;" k="16" />
    <hkern u1="&#xae;" u2="&#x119;" k="16" />
    <hkern u1="&#xae;" u2="&#x117;" k="16" />
    <hkern u1="&#xae;" u2="&#x115;" k="16" />
    <hkern u1="&#xae;" u2="&#x113;" k="16" />
    <hkern u1="&#xae;" u2="&#x111;" k="16" />
    <hkern u1="&#xae;" u2="&#x10f;" k="16" />
    <hkern u1="&#xae;" u2="&#x10d;" k="16" />
    <hkern u1="&#xae;" u2="&#x10b;" k="16" />
    <hkern u1="&#xae;" u2="&#x109;" k="16" />
    <hkern u1="&#xae;" u2="&#x107;" k="16" />
    <hkern u1="&#xae;" u2="&#x105;" k="53" />
    <hkern u1="&#xae;" u2="&#x104;" k="195" />
    <hkern u1="&#xae;" u2="&#x103;" k="53" />
    <hkern u1="&#xae;" u2="&#x102;" k="195" />
    <hkern u1="&#xae;" u2="&#x101;" k="53" />
    <hkern u1="&#xae;" u2="&#x100;" k="195" />
    <hkern u1="&#xae;" u2="&#xff;" k="-37" />
    <hkern u1="&#xae;" u2="&#xfd;" k="-37" />
    <hkern u1="&#xae;" u2="&#xf8;" k="16" />
    <hkern u1="&#xae;" u2="&#xf6;" k="16" />
    <hkern u1="&#xae;" u2="&#xf5;" k="16" />
    <hkern u1="&#xae;" u2="&#xf4;" k="16" />
    <hkern u1="&#xae;" u2="&#xf3;" k="16" />
    <hkern u1="&#xae;" u2="&#xf2;" k="16" />
    <hkern u1="&#xae;" u2="&#xeb;" k="16" />
    <hkern u1="&#xae;" u2="&#xea;" k="16" />
    <hkern u1="&#xae;" u2="&#xe9;" k="16" />
    <hkern u1="&#xae;" u2="&#xe8;" k="16" />
    <hkern u1="&#xae;" u2="&#xe7;" k="16" />
    <hkern u1="&#xae;" u2="&#xe6;" k="53" />
    <hkern u1="&#xae;" u2="&#xe5;" k="53" />
    <hkern u1="&#xae;" u2="&#xe4;" k="53" />
    <hkern u1="&#xae;" u2="&#xe3;" k="53" />
    <hkern u1="&#xae;" u2="&#xe2;" k="53" />
    <hkern u1="&#xae;" u2="&#xe1;" k="53" />
    <hkern u1="&#xae;" u2="&#xe0;" k="53" />
    <hkern u1="&#xae;" u2="&#xdd;" k="37" />
    <hkern u1="&#xae;" u2="&#xc5;" k="195" />
    <hkern u1="&#xae;" u2="&#xc4;" k="195" />
    <hkern u1="&#xae;" u2="&#xc3;" k="195" />
    <hkern u1="&#xae;" u2="&#xc2;" k="195" />
    <hkern u1="&#xae;" u2="&#xc1;" k="195" />
    <hkern u1="&#xae;" u2="&#xc0;" k="195" />
    <hkern u1="&#xae;" u2="&#x7d;" k="20" />
    <hkern u1="&#xae;" u2="z" k="10" />
    <hkern u1="&#xae;" u2="y" k="-37" />
    <hkern u1="&#xae;" u2="w" k="-37" />
    <hkern u1="&#xae;" u2="t" k="-16" />
    <hkern u1="&#xae;" u2="s" k="10" />
    <hkern u1="&#xae;" u2="q" k="16" />
    <hkern u1="&#xae;" u2="o" k="16" />
    <hkern u1="&#xae;" u2="g" k="16" />
    <hkern u1="&#xae;" u2="f" k="-27" />
    <hkern u1="&#xae;" u2="e" k="16" />
    <hkern u1="&#xae;" u2="d" k="16" />
    <hkern u1="&#xae;" u2="c" k="16" />
    <hkern u1="&#xae;" u2="a" k="53" />
    <hkern u1="&#xae;" u2="]" k="20" />
    <hkern u1="&#xae;" u2="Z" k="20" />
    <hkern u1="&#xae;" u2="Y" k="37" />
    <hkern u1="&#xae;" u2="S" k="10" />
    <hkern u1="&#xae;" u2="J" k="309" />
    <hkern u1="&#xae;" u2="A" k="195" />
    <hkern u1="&#xae;" u2="&#x2e;" k="90" />
    <hkern u1="&#xae;" u2="&#x29;" k="20" />
    <hkern u1="&#xae;" u2="&#xa5;" k="37" />
    <hkern u1="&#xae;" u2="&#xa3;" k="41" />
    <hkern u1="&#xae;" u2="v" k="-37" />
    <hkern u1="&#xae;" u2="\" k="47" />
    <hkern u1="&#xae;" u2="X" k="72" />
    <hkern u1="&#xae;" u2="V" k="20" />
    <hkern u1="&#xae;" u2="&#x40;" k="20" />
    <hkern u1="&#xae;" u2="&#x3f;" k="-12" />
    <hkern u1="&#xae;" u2="&#x38;" k="25" />
    <hkern u1="&#xae;" u2="&#x37;" k="16" />
    <hkern u1="&#xae;" u2="&#x36;" k="51" />
    <hkern u1="&#xae;" u2="&#x35;" k="51" />
    <hkern u1="&#xae;" u2="&#x34;" k="61" />
    <hkern u1="&#xae;" u2="&#x33;" k="31" />
    <hkern u1="&#xae;" u2="&#x31;" k="-20" />
    <hkern u1="&#xae;" u2="&#x2f;" k="227" />
    <hkern u1="&#xae;" u2="&#x24;" k="10" />
    <hkern u1="&#xba;" u2="v" k="-37" />
    <hkern u1="&#xba;" u2="\" k="39" />
    <hkern u1="&#xba;" u2="X" k="20" />
    <hkern u1="&#xba;" u2="&#x34;" k="8" />
    <hkern u1="&#xba;" u2="&#x2f;" k="88" />
    <hkern u1="&#xbb;" u2="&#xa5;" k="27" />
    <hkern u1="&#xbb;" u2="x" k="61" />
    <hkern u1="&#xbb;" u2="v" k="12" />
    <hkern u1="&#xbb;" u2="\" k="115" />
    <hkern u1="&#xbb;" u2="X" k="82" />
    <hkern u1="&#xbb;" u2="V" k="102" />
    <hkern u1="&#xbb;" u2="&#x3f;" k="53" />
    <hkern u1="&#xbb;" u2="&#x39;" k="37" />
    <hkern u1="&#xbb;" u2="&#x37;" k="82" />
    <hkern u1="&#xbb;" u2="&#x36;" k="-12" />
    <hkern u1="&#xbb;" u2="&#x34;" k="-25" />
    <hkern u1="&#xbb;" u2="&#x31;" k="31" />
    <hkern u1="&#xbb;" u2="&#x2f;" k="41" />
    <hkern u1="&#xbb;" u2="&#x2a;" k="78" />
    <hkern u1="&#xbb;" u2="&#x23;" k="-53" />
    <hkern u1="&#xbb;" u2="&#x21;" k="12" />
    <hkern u1="&#xbf;" u2="&#xfb02;" k="10" />
    <hkern u1="&#xbf;" u2="&#xfb01;" k="10" />
    <hkern u1="&#xbf;" u2="&#x2030;" k="98" />
    <hkern u1="&#xbf;" u2="&#x201e;" k="-20" />
    <hkern u1="&#xbf;" u2="&#x201d;" k="197" />
    <hkern u1="&#xbf;" u2="&#x201c;" k="190" />
    <hkern u1="&#xbf;" u2="&#x201a;" k="-20" />
    <hkern u1="&#xbf;" u2="&#x2019;" k="197" />
    <hkern u1="&#xbf;" u2="&#x2018;" k="190" />
    <hkern u1="&#xbf;" u2="&#x2014;" k="37" />
    <hkern u1="&#xbf;" u2="&#x2013;" k="37" />
    <hkern u1="&#xbf;" u2="&#x1ef3;" k="74" />
    <hkern u1="&#xbf;" u2="&#x1ef2;" k="170" />
    <hkern u1="&#xbf;" u2="&#x1e85;" k="41" />
    <hkern u1="&#xbf;" u2="&#x1e84;" k="90" />
    <hkern u1="&#xbf;" u2="&#x1e83;" k="41" />
    <hkern u1="&#xbf;" u2="&#x1e82;" k="90" />
    <hkern u1="&#xbf;" u2="&#x1e81;" k="41" />
    <hkern u1="&#xbf;" u2="&#x1e80;" k="90" />
    <hkern u1="&#xbf;" u2="&#x178;" k="170" />
    <hkern u1="&#xbf;" u2="&#x177;" k="74" />
    <hkern u1="&#xbf;" u2="&#x176;" k="170" />
    <hkern u1="&#xbf;" u2="&#x175;" k="41" />
    <hkern u1="&#xbf;" u2="&#x174;" k="90" />
    <hkern u1="&#xbf;" u2="&#x164;" k="141" />
    <hkern u1="&#xbf;" u2="&#x163;" k="31" />
    <hkern u1="&#xbf;" u2="&#x162;" k="141" />
    <hkern u1="&#xbf;" u2="&#x149;" k="197" />
    <hkern u1="&#xbf;" u2="&#xff;" k="74" />
    <hkern u1="&#xbf;" u2="&#xfd;" k="74" />
    <hkern u1="&#xbf;" u2="&#xdd;" k="170" />
    <hkern u1="&#xbf;" u2="&#x7d;" k="27" />
    <hkern u1="&#xbf;" u2="y" k="74" />
    <hkern u1="&#xbf;" u2="w" k="41" />
    <hkern u1="&#xbf;" u2="t" k="31" />
    <hkern u1="&#xbf;" u2="f" k="10" />
    <hkern u1="&#xbf;" u2="]" k="27" />
    <hkern u1="&#xbf;" u2="Y" k="170" />
    <hkern u1="&#xbf;" u2="W" k="90" />
    <hkern u1="&#xbf;" u2="T" k="141" />
    <hkern u1="&#xbf;" u2="&#x2d;" k="37" />
    <hkern u1="&#xbf;" u2="&#x2c;" k="-20" />
    <hkern u1="&#xbf;" u2="&#x29;" k="27" />
    <hkern u1="&#xbf;" u2="&#x25;" k="98" />
    <hkern u1="&#xbf;" u2="&#xbf;" k="-33" />
    <hkern u1="&#xbf;" u2="&#xa5;" k="20" />
    <hkern u1="&#xbf;" u2="x" k="12" />
    <hkern u1="&#xbf;" u2="v" k="51" />
    <hkern u1="&#xbf;" u2="\" k="170" />
    <hkern u1="&#xbf;" u2="X" k="18" />
    <hkern u1="&#xbf;" u2="V" k="121" />
    <hkern u1="&#xbf;" u2="&#x39;" k="61" />
    <hkern u1="&#xbf;" u2="&#x37;" k="25" />
    <hkern u1="&#xbf;" u2="&#x31;" k="61" />
    <hkern u1="&#xbf;" u2="&#x2f;" k="16" />
    <hkern u1="&#xbf;" u2="&#x2a;" k="178" />
    <hkern u1="&#xc0;" u2="&#x2122;" k="225" />
    <hkern u1="&#xc0;" u2="&#xae;" k="195" />
    <hkern u1="&#xc0;" u2="x" k="-20" />
    <hkern u1="&#xc0;" u2="v" k="102" />
    <hkern u1="&#xc0;" u2="\" k="154" />
    <hkern u1="&#xc0;" u2="X" k="-31" />
    <hkern u1="&#xc0;" u2="V" k="195" />
    <hkern u1="&#xc0;" u2="&#x40;" k="10" />
    <hkern u1="&#xc0;" u2="&#x3f;" k="131" />
    <hkern u1="&#xc0;" u2="&#x2a;" k="215" />
    <hkern u1="&#xc0;" u2="&#x26;" k="20" />
    <hkern u1="&#xc1;" u2="&#x2122;" k="225" />
    <hkern u1="&#xc1;" u2="&#xae;" k="195" />
    <hkern u1="&#xc1;" u2="x" k="-20" />
    <hkern u1="&#xc1;" u2="v" k="102" />
    <hkern u1="&#xc1;" u2="\" k="154" />
    <hkern u1="&#xc1;" u2="X" k="-31" />
    <hkern u1="&#xc1;" u2="V" k="195" />
    <hkern u1="&#xc1;" u2="&#x40;" k="10" />
    <hkern u1="&#xc1;" u2="&#x3f;" k="131" />
    <hkern u1="&#xc1;" u2="&#x2a;" k="215" />
    <hkern u1="&#xc1;" u2="&#x26;" k="20" />
    <hkern u1="&#xc2;" u2="&#x2122;" k="225" />
    <hkern u1="&#xc2;" u2="&#xae;" k="195" />
    <hkern u1="&#xc2;" u2="x" k="-20" />
    <hkern u1="&#xc2;" u2="v" k="102" />
    <hkern u1="&#xc2;" u2="\" k="154" />
    <hkern u1="&#xc2;" u2="X" k="-31" />
    <hkern u1="&#xc2;" u2="V" k="195" />
    <hkern u1="&#xc2;" u2="&#x40;" k="10" />
    <hkern u1="&#xc2;" u2="&#x3f;" k="131" />
    <hkern u1="&#xc2;" u2="&#x2a;" k="215" />
    <hkern u1="&#xc2;" u2="&#x26;" k="20" />
    <hkern u1="&#xc3;" u2="&#x2122;" k="225" />
    <hkern u1="&#xc3;" u2="&#xae;" k="195" />
    <hkern u1="&#xc3;" u2="x" k="-20" />
    <hkern u1="&#xc3;" u2="v" k="102" />
    <hkern u1="&#xc3;" u2="\" k="154" />
    <hkern u1="&#xc3;" u2="X" k="-31" />
    <hkern u1="&#xc3;" u2="V" k="195" />
    <hkern u1="&#xc3;" u2="&#x40;" k="10" />
    <hkern u1="&#xc3;" u2="&#x3f;" k="131" />
    <hkern u1="&#xc3;" u2="&#x2a;" k="215" />
    <hkern u1="&#xc3;" u2="&#x26;" k="20" />
    <hkern u1="&#xc4;" u2="&#x2122;" k="225" />
    <hkern u1="&#xc4;" u2="&#xae;" k="195" />
    <hkern u1="&#xc4;" u2="x" k="-20" />
    <hkern u1="&#xc4;" u2="v" k="102" />
    <hkern u1="&#xc4;" u2="\" k="154" />
    <hkern u1="&#xc4;" u2="X" k="-31" />
    <hkern u1="&#xc4;" u2="V" k="195" />
    <hkern u1="&#xc4;" u2="&#x40;" k="10" />
    <hkern u1="&#xc4;" u2="&#x3f;" k="131" />
    <hkern u1="&#xc4;" u2="&#x2a;" k="215" />
    <hkern u1="&#xc4;" u2="&#x26;" k="20" />
    <hkern u1="&#xc5;" u2="&#x2122;" k="225" />
    <hkern u1="&#xc5;" u2="&#xae;" k="195" />
    <hkern u1="&#xc5;" u2="x" k="-20" />
    <hkern u1="&#xc5;" u2="v" k="102" />
    <hkern u1="&#xc5;" u2="\" k="154" />
    <hkern u1="&#xc5;" u2="X" k="-31" />
    <hkern u1="&#xc5;" u2="V" k="195" />
    <hkern u1="&#xc5;" u2="&#x40;" k="10" />
    <hkern u1="&#xc5;" u2="&#x3f;" k="131" />
    <hkern u1="&#xc5;" u2="&#x2a;" k="215" />
    <hkern u1="&#xc5;" u2="&#x26;" k="20" />
    <hkern u1="&#xc6;" u2="&#x2122;" k="-12" />
    <hkern u1="&#xc6;" u2="x" k="-10" />
    <hkern u1="&#xc6;" u2="\" k="4" />
    <hkern u1="&#xc6;" u2="X" k="-6" />
    <hkern u1="&#xc7;" u2="x" k="12" />
    <hkern u1="&#xc7;" u2="v" k="-6" />
    <hkern u1="&#xc7;" u2="\" k="61" />
    <hkern u1="&#xc7;" u2="X" k="39" />
    <hkern u1="&#xc7;" u2="V" k="41" />
    <hkern u1="&#xc7;" u2="&#x2f;" k="70" />
    <hkern u1="&#xc8;" u2="&#x2122;" k="-12" />
    <hkern u1="&#xc8;" u2="x" k="-10" />
    <hkern u1="&#xc8;" u2="\" k="4" />
    <hkern u1="&#xc8;" u2="X" k="-6" />
    <hkern u1="&#xc9;" u2="&#x2122;" k="-12" />
    <hkern u1="&#xc9;" u2="x" k="-10" />
    <hkern u1="&#xc9;" u2="\" k="4" />
    <hkern u1="&#xc9;" u2="X" k="-6" />
    <hkern u1="&#xca;" u2="&#x2122;" k="-12" />
    <hkern u1="&#xca;" u2="x" k="-10" />
    <hkern u1="&#xca;" u2="\" k="4" />
    <hkern u1="&#xca;" u2="X" k="-6" />
    <hkern u1="&#xcb;" u2="&#x2122;" k="-12" />
    <hkern u1="&#xcb;" u2="x" k="-10" />
    <hkern u1="&#xcb;" u2="\" k="4" />
    <hkern u1="&#xcb;" u2="X" k="-6" />
    <hkern u1="&#xd0;" u2="&#x2122;" k="31" />
    <hkern u1="&#xd0;" u2="x" k="31" />
    <hkern u1="&#xd0;" u2="\" k="76" />
    <hkern u1="&#xd0;" u2="X" k="68" />
    <hkern u1="&#xd0;" u2="V" k="53" />
    <hkern u1="&#xd0;" u2="&#x2f;" k="57" />
    <hkern u1="&#xd0;" u2="&#x2a;" k="10" />
    <hkern u1="&#xd2;" u2="&#x2122;" k="31" />
    <hkern u1="&#xd2;" u2="x" k="31" />
    <hkern u1="&#xd2;" u2="\" k="76" />
    <hkern u1="&#xd2;" u2="X" k="68" />
    <hkern u1="&#xd2;" u2="V" k="53" />
    <hkern u1="&#xd2;" u2="&#x2f;" k="57" />
    <hkern u1="&#xd2;" u2="&#x2a;" k="10" />
    <hkern u1="&#xd3;" u2="&#x2122;" k="31" />
    <hkern u1="&#xd3;" u2="x" k="31" />
    <hkern u1="&#xd3;" u2="\" k="76" />
    <hkern u1="&#xd3;" u2="X" k="68" />
    <hkern u1="&#xd3;" u2="V" k="53" />
    <hkern u1="&#xd3;" u2="&#x2f;" k="57" />
    <hkern u1="&#xd3;" u2="&#x2a;" k="10" />
    <hkern u1="&#xd4;" u2="&#x2122;" k="31" />
    <hkern u1="&#xd4;" u2="x" k="31" />
    <hkern u1="&#xd4;" u2="\" k="76" />
    <hkern u1="&#xd4;" u2="X" k="68" />
    <hkern u1="&#xd4;" u2="V" k="53" />
    <hkern u1="&#xd4;" u2="&#x2f;" k="57" />
    <hkern u1="&#xd4;" u2="&#x2a;" k="10" />
    <hkern u1="&#xd5;" u2="&#x2122;" k="31" />
    <hkern u1="&#xd5;" u2="x" k="31" />
    <hkern u1="&#xd5;" u2="\" k="76" />
    <hkern u1="&#xd5;" u2="X" k="68" />
    <hkern u1="&#xd5;" u2="V" k="53" />
    <hkern u1="&#xd5;" u2="&#x2f;" k="57" />
    <hkern u1="&#xd5;" u2="&#x2a;" k="10" />
    <hkern u1="&#xd6;" u2="&#x2122;" k="31" />
    <hkern u1="&#xd6;" u2="x" k="31" />
    <hkern u1="&#xd6;" u2="\" k="76" />
    <hkern u1="&#xd6;" u2="X" k="68" />
    <hkern u1="&#xd6;" u2="V" k="53" />
    <hkern u1="&#xd6;" u2="&#x2f;" k="57" />
    <hkern u1="&#xd6;" u2="&#x2a;" k="10" />
    <hkern u1="&#xd8;" u2="&#x2122;" k="31" />
    <hkern u1="&#xd8;" u2="x" k="31" />
    <hkern u1="&#xd8;" u2="\" k="76" />
    <hkern u1="&#xd8;" u2="X" k="68" />
    <hkern u1="&#xd8;" u2="V" k="53" />
    <hkern u1="&#xd8;" u2="&#x2f;" k="57" />
    <hkern u1="&#xd8;" u2="&#x2a;" k="10" />
    <hkern u1="&#xd9;" u2="x" k="10" />
    <hkern u1="&#xd9;" u2="X" k="10" />
    <hkern u1="&#xd9;" u2="&#x2f;" k="49" />
    <hkern u1="&#xda;" u2="x" k="10" />
    <hkern u1="&#xda;" u2="X" k="10" />
    <hkern u1="&#xda;" u2="&#x2f;" k="49" />
    <hkern u1="&#xdb;" u2="x" k="10" />
    <hkern u1="&#xdb;" u2="X" k="10" />
    <hkern u1="&#xdb;" u2="&#x2f;" k="49" />
    <hkern u1="&#xdc;" u2="x" k="10" />
    <hkern u1="&#xdc;" u2="X" k="10" />
    <hkern u1="&#xdc;" u2="&#x2f;" k="49" />
    <hkern u1="&#xdd;" u2="&#x2122;" k="-43" />
    <hkern u1="&#xdd;" u2="&#xbf;" k="154" />
    <hkern u1="&#xdd;" u2="&#xae;" k="37" />
    <hkern u1="&#xdd;" u2="&#xa1;" k="37" />
    <hkern u1="&#xdd;" u2="x" k="94" />
    <hkern u1="&#xdd;" u2="v" k="66" />
    <hkern u1="&#xdd;" u2="i" k="4" />
    <hkern u1="&#xdd;" u2="&#x40;" k="135" />
    <hkern u1="&#xdd;" u2="&#x3f;" k="10" />
    <hkern u1="&#xdd;" u2="&#x2f;" k="225" />
    <hkern u1="&#xdd;" u2="&#x2a;" k="31" />
    <hkern u1="&#xdd;" u2="&#x26;" k="100" />
    <hkern u1="&#xdf;" u2="&#x2122;" k="66" />
    <hkern u1="&#xdf;" u2="&#xae;" k="4" />
    <hkern u1="&#xdf;" u2="x" k="14" />
    <hkern u1="&#xdf;" u2="v" k="20" />
    <hkern u1="&#xdf;" u2="\" k="121" />
    <hkern u1="&#xdf;" u2="&#x3f;" k="20" />
    <hkern u1="&#xdf;" u2="&#x2f;" k="47" />
    <hkern u1="&#xdf;" u2="&#x2a;" k="41" />
    <hkern u1="&#xe0;" u2="&#x2122;" k="115" />
    <hkern u1="&#xe0;" u2="&#xae;" k="18" />
    <hkern u1="&#xe0;" u2="x" k="-4" />
    <hkern u1="&#xe0;" u2="v" k="23" />
    <hkern u1="&#xe0;" u2="\" k="117" />
    <hkern u1="&#xe0;" u2="&#x3f;" k="74" />
    <hkern u1="&#xe0;" u2="&#x2a;" k="20" />
    <hkern u1="&#xe1;" u2="&#x2122;" k="115" />
    <hkern u1="&#xe1;" u2="&#xae;" k="18" />
    <hkern u1="&#xe1;" u2="x" k="-4" />
    <hkern u1="&#xe1;" u2="v" k="23" />
    <hkern u1="&#xe1;" u2="\" k="117" />
    <hkern u1="&#xe1;" u2="&#x3f;" k="74" />
    <hkern u1="&#xe1;" u2="&#x2a;" k="20" />
    <hkern u1="&#xe2;" u2="&#x2122;" k="115" />
    <hkern u1="&#xe2;" u2="&#xae;" k="18" />
    <hkern u1="&#xe2;" u2="x" k="-4" />
    <hkern u1="&#xe2;" u2="v" k="23" />
    <hkern u1="&#xe2;" u2="\" k="117" />
    <hkern u1="&#xe2;" u2="&#x3f;" k="74" />
    <hkern u1="&#xe2;" u2="&#x2a;" k="20" />
    <hkern u1="&#xe3;" u2="&#x2122;" k="115" />
    <hkern u1="&#xe3;" u2="&#xae;" k="18" />
    <hkern u1="&#xe3;" u2="x" k="-4" />
    <hkern u1="&#xe3;" u2="v" k="23" />
    <hkern u1="&#xe3;" u2="\" k="117" />
    <hkern u1="&#xe3;" u2="&#x3f;" k="74" />
    <hkern u1="&#xe3;" u2="&#x2a;" k="20" />
    <hkern u1="&#xe4;" u2="&#x2122;" k="115" />
    <hkern u1="&#xe4;" u2="&#xae;" k="18" />
    <hkern u1="&#xe4;" u2="x" k="-4" />
    <hkern u1="&#xe4;" u2="v" k="23" />
    <hkern u1="&#xe4;" u2="\" k="117" />
    <hkern u1="&#xe4;" u2="&#x3f;" k="74" />
    <hkern u1="&#xe4;" u2="&#x2a;" k="20" />
    <hkern u1="&#xe5;" u2="&#x2122;" k="115" />
    <hkern u1="&#xe5;" u2="&#xae;" k="18" />
    <hkern u1="&#xe5;" u2="x" k="-4" />
    <hkern u1="&#xe5;" u2="v" k="23" />
    <hkern u1="&#xe5;" u2="\" k="117" />
    <hkern u1="&#xe5;" u2="&#x3f;" k="74" />
    <hkern u1="&#xe5;" u2="&#x2a;" k="20" />
    <hkern u1="&#xe6;" u2="&#x2122;" k="74" />
    <hkern u1="&#xe6;" u2="&#xae;" k="16" />
    <hkern u1="&#xe6;" u2="x" k="25" />
    <hkern u1="&#xe6;" u2="v" k="20" />
    <hkern u1="&#xe6;" u2="\" k="111" />
    <hkern u1="&#xe6;" u2="&#x3f;" k="31" />
    <hkern u1="&#xe6;" u2="&#x2f;" k="27" />
    <hkern u1="&#xe6;" u2="&#x2a;" k="20" />
    <hkern u1="&#xe7;" u2="&#x2122;" k="61" />
    <hkern u1="&#xe7;" u2="&#xae;" k="10" />
    <hkern u1="&#xe7;" u2="x" k="12" />
    <hkern u1="&#xe7;" u2="v" k="6" />
    <hkern u1="&#xe7;" u2="\" k="111" />
    <hkern u1="&#xe7;" u2="&#x3f;" k="31" />
    <hkern u1="&#xe7;" u2="&#x2f;" k="20" />
    <hkern u1="&#xe7;" u2="&#x2a;" k="20" />
    <hkern u1="&#xe8;" u2="&#x2122;" k="74" />
    <hkern u1="&#xe8;" u2="&#xae;" k="16" />
    <hkern u1="&#xe8;" u2="x" k="25" />
    <hkern u1="&#xe8;" u2="v" k="20" />
    <hkern u1="&#xe8;" u2="\" k="111" />
    <hkern u1="&#xe8;" u2="&#x3f;" k="31" />
    <hkern u1="&#xe8;" u2="&#x2f;" k="27" />
    <hkern u1="&#xe8;" u2="&#x2a;" k="20" />
    <hkern u1="&#xe9;" u2="&#x2122;" k="74" />
    <hkern u1="&#xe9;" u2="&#xae;" k="16" />
    <hkern u1="&#xe9;" u2="x" k="25" />
    <hkern u1="&#xe9;" u2="v" k="20" />
    <hkern u1="&#xe9;" u2="\" k="111" />
    <hkern u1="&#xe9;" u2="&#x3f;" k="31" />
    <hkern u1="&#xe9;" u2="&#x2f;" k="27" />
    <hkern u1="&#xe9;" u2="&#x2a;" k="20" />
    <hkern u1="&#xea;" u2="&#x2122;" k="74" />
    <hkern u1="&#xea;" u2="&#xae;" k="16" />
    <hkern u1="&#xea;" u2="x" k="25" />
    <hkern u1="&#xea;" u2="v" k="20" />
    <hkern u1="&#xea;" u2="\" k="111" />
    <hkern u1="&#xea;" u2="&#x3f;" k="31" />
    <hkern u1="&#xea;" u2="&#x2f;" k="27" />
    <hkern u1="&#xea;" u2="&#x2a;" k="20" />
    <hkern u1="&#xeb;" u2="&#x2122;" k="74" />
    <hkern u1="&#xeb;" u2="&#xae;" k="16" />
    <hkern u1="&#xeb;" u2="x" k="25" />
    <hkern u1="&#xeb;" u2="v" k="20" />
    <hkern u1="&#xeb;" u2="\" k="111" />
    <hkern u1="&#xeb;" u2="&#x3f;" k="31" />
    <hkern u1="&#xeb;" u2="&#x2f;" k="27" />
    <hkern u1="&#xeb;" u2="&#x2a;" k="20" />
    <hkern u1="&#xf1;" u2="&#x2122;" k="98" />
    <hkern u1="&#xf1;" u2="&#xae;" k="27" />
    <hkern u1="&#xf1;" u2="v" k="8" />
    <hkern u1="&#xf1;" u2="\" k="109" />
    <hkern u1="&#xf1;" u2="&#x2a;" k="31" />
    <hkern u1="&#xf2;" u2="&#x2122;" k="61" />
    <hkern u1="&#xf2;" u2="&#xae;" k="16" />
    <hkern u1="&#xf2;" u2="x" k="41" />
    <hkern u1="&#xf2;" u2="v" k="20" />
    <hkern u1="&#xf2;" u2="\" k="119" />
    <hkern u1="&#xf2;" u2="&#x3f;" k="27" />
    <hkern u1="&#xf2;" u2="&#x2f;" k="53" />
    <hkern u1="&#xf2;" u2="&#x2a;" k="41" />
    <hkern u1="&#xf3;" u2="&#x2122;" k="61" />
    <hkern u1="&#xf3;" u2="&#xae;" k="16" />
    <hkern u1="&#xf3;" u2="x" k="41" />
    <hkern u1="&#xf3;" u2="v" k="20" />
    <hkern u1="&#xf3;" u2="\" k="119" />
    <hkern u1="&#xf3;" u2="&#x3f;" k="27" />
    <hkern u1="&#xf3;" u2="&#x2f;" k="53" />
    <hkern u1="&#xf3;" u2="&#x2a;" k="41" />
    <hkern u1="&#xf4;" u2="&#x2122;" k="61" />
    <hkern u1="&#xf4;" u2="&#xae;" k="16" />
    <hkern u1="&#xf4;" u2="x" k="41" />
    <hkern u1="&#xf4;" u2="v" k="20" />
    <hkern u1="&#xf4;" u2="\" k="119" />
    <hkern u1="&#xf4;" u2="&#x3f;" k="27" />
    <hkern u1="&#xf4;" u2="&#x2f;" k="53" />
    <hkern u1="&#xf4;" u2="&#x2a;" k="41" />
    <hkern u1="&#xf5;" u2="&#x2122;" k="61" />
    <hkern u1="&#xf5;" u2="&#xae;" k="16" />
    <hkern u1="&#xf5;" u2="x" k="41" />
    <hkern u1="&#xf5;" u2="v" k="20" />
    <hkern u1="&#xf5;" u2="\" k="119" />
    <hkern u1="&#xf5;" u2="&#x3f;" k="27" />
    <hkern u1="&#xf5;" u2="&#x2f;" k="53" />
    <hkern u1="&#xf5;" u2="&#x2a;" k="41" />
    <hkern u1="&#xf6;" u2="&#x2122;" k="61" />
    <hkern u1="&#xf6;" u2="&#xae;" k="16" />
    <hkern u1="&#xf6;" u2="x" k="41" />
    <hkern u1="&#xf6;" u2="v" k="20" />
    <hkern u1="&#xf6;" u2="\" k="119" />
    <hkern u1="&#xf6;" u2="&#x3f;" k="27" />
    <hkern u1="&#xf6;" u2="&#x2f;" k="53" />
    <hkern u1="&#xf6;" u2="&#x2a;" k="41" />
    <hkern u1="&#xf8;" u2="&#x2122;" k="61" />
    <hkern u1="&#xf8;" u2="&#xae;" k="16" />
    <hkern u1="&#xf8;" u2="x" k="41" />
    <hkern u1="&#xf8;" u2="v" k="20" />
    <hkern u1="&#xf8;" u2="\" k="119" />
    <hkern u1="&#xf8;" u2="&#x3f;" k="27" />
    <hkern u1="&#xf8;" u2="&#x2f;" k="53" />
    <hkern u1="&#xf8;" u2="&#x2a;" k="41" />
    <hkern u1="&#xf9;" u2="&#x2122;" k="16" />
    <hkern u1="&#xf9;" u2="\" k="20" />
    <hkern u1="&#xfa;" u2="&#x2122;" k="16" />
    <hkern u1="&#xfa;" u2="\" k="20" />
    <hkern u1="&#xfb;" u2="&#x2122;" k="16" />
    <hkern u1="&#xfb;" u2="\" k="20" />
    <hkern u1="&#xfc;" u2="&#x2122;" k="16" />
    <hkern u1="&#xfc;" u2="\" k="20" />
    <hkern u1="&#xfd;" u2="&#xbf;" k="33" />
    <hkern u1="&#xfd;" u2="&#xae;" k="-37" />
    <hkern u1="&#xfd;" u2="\" k="14" />
    <hkern u1="&#xfd;" u2="&#x40;" k="10" />
    <hkern u1="&#xfd;" u2="&#x3f;" k="-45" />
    <hkern u1="&#xfd;" u2="&#x2f;" k="72" />
    <hkern u1="&#xfd;" u2="&#x2a;" k="-31" />
    <hkern u1="&#xfe;" u2="&#x2122;" k="61" />
    <hkern u1="&#xfe;" u2="&#xae;" k="16" />
    <hkern u1="&#xfe;" u2="x" k="41" />
    <hkern u1="&#xfe;" u2="v" k="20" />
    <hkern u1="&#xfe;" u2="\" k="119" />
    <hkern u1="&#xfe;" u2="&#x3f;" k="27" />
    <hkern u1="&#xfe;" u2="&#x2f;" k="53" />
    <hkern u1="&#xfe;" u2="&#x2a;" k="41" />
    <hkern u1="&#xff;" u2="&#xbf;" k="33" />
    <hkern u1="&#xff;" u2="&#xae;" k="-37" />
    <hkern u1="&#xff;" u2="\" k="14" />
    <hkern u1="&#xff;" u2="&#x40;" k="10" />
    <hkern u1="&#xff;" u2="&#x3f;" k="-45" />
    <hkern u1="&#xff;" u2="&#x2f;" k="72" />
    <hkern u1="&#xff;" u2="&#x2a;" k="-31" />
    <hkern u1="&#x100;" u2="&#x2122;" k="225" />
    <hkern u1="&#x100;" u2="&#xae;" k="195" />
    <hkern u1="&#x100;" u2="x" k="-20" />
    <hkern u1="&#x100;" u2="v" k="102" />
    <hkern u1="&#x100;" u2="\" k="154" />
    <hkern u1="&#x100;" u2="X" k="-31" />
    <hkern u1="&#x100;" u2="V" k="195" />
    <hkern u1="&#x100;" u2="&#x40;" k="10" />
    <hkern u1="&#x100;" u2="&#x3f;" k="131" />
    <hkern u1="&#x100;" u2="&#x2a;" k="215" />
    <hkern u1="&#x100;" u2="&#x26;" k="20" />
    <hkern u1="&#x101;" u2="&#x2122;" k="115" />
    <hkern u1="&#x101;" u2="&#xae;" k="18" />
    <hkern u1="&#x101;" u2="x" k="-4" />
    <hkern u1="&#x101;" u2="v" k="23" />
    <hkern u1="&#x101;" u2="\" k="117" />
    <hkern u1="&#x101;" u2="&#x3f;" k="74" />
    <hkern u1="&#x101;" u2="&#x2a;" k="20" />
    <hkern u1="&#x102;" u2="&#x2122;" k="225" />
    <hkern u1="&#x102;" u2="&#xae;" k="195" />
    <hkern u1="&#x102;" u2="x" k="-20" />
    <hkern u1="&#x102;" u2="v" k="102" />
    <hkern u1="&#x102;" u2="\" k="154" />
    <hkern u1="&#x102;" u2="X" k="-31" />
    <hkern u1="&#x102;" u2="V" k="195" />
    <hkern u1="&#x102;" u2="&#x40;" k="10" />
    <hkern u1="&#x102;" u2="&#x3f;" k="131" />
    <hkern u1="&#x102;" u2="&#x2a;" k="215" />
    <hkern u1="&#x102;" u2="&#x26;" k="20" />
    <hkern u1="&#x103;" u2="&#x2122;" k="115" />
    <hkern u1="&#x103;" u2="&#xae;" k="18" />
    <hkern u1="&#x103;" u2="x" k="-4" />
    <hkern u1="&#x103;" u2="v" k="23" />
    <hkern u1="&#x103;" u2="\" k="117" />
    <hkern u1="&#x103;" u2="&#x3f;" k="74" />
    <hkern u1="&#x103;" u2="&#x2a;" k="20" />
    <hkern u1="&#x104;" u2="&#x2122;" k="225" />
    <hkern u1="&#x104;" u2="&#xae;" k="195" />
    <hkern u1="&#x104;" u2="x" k="-20" />
    <hkern u1="&#x104;" u2="v" k="102" />
    <hkern u1="&#x104;" u2="\" k="154" />
    <hkern u1="&#x104;" u2="X" k="-31" />
    <hkern u1="&#x104;" u2="V" k="195" />
    <hkern u1="&#x104;" u2="&#x40;" k="10" />
    <hkern u1="&#x104;" u2="&#x3f;" k="131" />
    <hkern u1="&#x104;" u2="&#x2a;" k="215" />
    <hkern u1="&#x104;" u2="&#x26;" k="20" />
    <hkern u1="&#x105;" u2="&#x2122;" k="115" />
    <hkern u1="&#x105;" u2="&#xae;" k="18" />
    <hkern u1="&#x105;" u2="x" k="-4" />
    <hkern u1="&#x105;" u2="v" k="23" />
    <hkern u1="&#x105;" u2="\" k="117" />
    <hkern u1="&#x105;" u2="&#x3f;" k="74" />
    <hkern u1="&#x105;" u2="&#x2a;" k="20" />
    <hkern u1="&#x106;" u2="x" k="12" />
    <hkern u1="&#x106;" u2="v" k="-6" />
    <hkern u1="&#x106;" u2="\" k="61" />
    <hkern u1="&#x106;" u2="X" k="39" />
    <hkern u1="&#x106;" u2="V" k="41" />
    <hkern u1="&#x106;" u2="&#x2f;" k="70" />
    <hkern u1="&#x107;" u2="&#x2122;" k="61" />
    <hkern u1="&#x107;" u2="&#xae;" k="10" />
    <hkern u1="&#x107;" u2="x" k="12" />
    <hkern u1="&#x107;" u2="v" k="6" />
    <hkern u1="&#x107;" u2="\" k="111" />
    <hkern u1="&#x107;" u2="&#x3f;" k="31" />
    <hkern u1="&#x107;" u2="&#x2f;" k="20" />
    <hkern u1="&#x107;" u2="&#x2a;" k="20" />
    <hkern u1="&#x108;" u2="x" k="12" />
    <hkern u1="&#x108;" u2="v" k="-6" />
    <hkern u1="&#x108;" u2="\" k="61" />
    <hkern u1="&#x108;" u2="X" k="39" />
    <hkern u1="&#x108;" u2="V" k="41" />
    <hkern u1="&#x108;" u2="&#x2f;" k="70" />
    <hkern u1="&#x109;" u2="&#x2122;" k="61" />
    <hkern u1="&#x109;" u2="&#xae;" k="10" />
    <hkern u1="&#x109;" u2="x" k="12" />
    <hkern u1="&#x109;" u2="v" k="6" />
    <hkern u1="&#x109;" u2="\" k="111" />
    <hkern u1="&#x109;" u2="&#x3f;" k="31" />
    <hkern u1="&#x109;" u2="&#x2f;" k="20" />
    <hkern u1="&#x109;" u2="&#x2a;" k="20" />
    <hkern u1="&#x10a;" u2="x" k="12" />
    <hkern u1="&#x10a;" u2="v" k="-6" />
    <hkern u1="&#x10a;" u2="\" k="61" />
    <hkern u1="&#x10a;" u2="X" k="39" />
    <hkern u1="&#x10a;" u2="V" k="41" />
    <hkern u1="&#x10a;" u2="&#x2f;" k="70" />
    <hkern u1="&#x10b;" u2="&#x2122;" k="61" />
    <hkern u1="&#x10b;" u2="&#xae;" k="10" />
    <hkern u1="&#x10b;" u2="x" k="12" />
    <hkern u1="&#x10b;" u2="v" k="6" />
    <hkern u1="&#x10b;" u2="\" k="111" />
    <hkern u1="&#x10b;" u2="&#x3f;" k="31" />
    <hkern u1="&#x10b;" u2="&#x2f;" k="20" />
    <hkern u1="&#x10b;" u2="&#x2a;" k="20" />
    <hkern u1="&#x10c;" u2="x" k="12" />
    <hkern u1="&#x10c;" u2="v" k="-6" />
    <hkern u1="&#x10c;" u2="\" k="61" />
    <hkern u1="&#x10c;" u2="X" k="39" />
    <hkern u1="&#x10c;" u2="V" k="41" />
    <hkern u1="&#x10c;" u2="&#x2f;" k="70" />
    <hkern u1="&#x10d;" u2="&#x2122;" k="61" />
    <hkern u1="&#x10d;" u2="&#xae;" k="10" />
    <hkern u1="&#x10d;" u2="x" k="12" />
    <hkern u1="&#x10d;" u2="v" k="6" />
    <hkern u1="&#x10d;" u2="\" k="111" />
    <hkern u1="&#x10d;" u2="&#x3f;" k="31" />
    <hkern u1="&#x10d;" u2="&#x2f;" k="20" />
    <hkern u1="&#x10d;" u2="&#x2a;" k="20" />
    <hkern u1="&#x10e;" u2="&#x2122;" k="31" />
    <hkern u1="&#x10e;" u2="x" k="31" />
    <hkern u1="&#x10e;" u2="\" k="76" />
    <hkern u1="&#x10e;" u2="X" k="68" />
    <hkern u1="&#x10e;" u2="V" k="53" />
    <hkern u1="&#x10e;" u2="&#x2f;" k="57" />
    <hkern u1="&#x10e;" u2="&#x2a;" k="10" />
    <hkern u1="&#x10f;" u2="&#x2122;" k="-8" />
    <hkern u1="&#x10f;" u2="&#x20ac;" k="49" />
    <hkern u1="&#x10f;" u2="&#xbf;" k="184" />
    <hkern u1="&#x10f;" u2="&#xae;" k="10" />
    <hkern u1="&#x10f;" u2="&#xa3;" k="115" />
    <hkern u1="&#x10f;" u2="&#xa2;" k="61" />
    <hkern u1="&#x10f;" u2="x" k="35" />
    <hkern u1="&#x10f;" u2="j" k="6" />
    <hkern u1="&#x10f;" u2="X" k="6" />
    <hkern u1="&#x10f;" u2="V" k="-27" />
    <hkern u1="&#x10f;" u2="&#x40;" k="90" />
    <hkern u1="&#x10f;" u2="&#x39;" k="16" />
    <hkern u1="&#x10f;" u2="&#x38;" k="63" />
    <hkern u1="&#x10f;" u2="&#x37;" k="-6" />
    <hkern u1="&#x10f;" u2="&#x36;" k="168" />
    <hkern u1="&#x10f;" u2="&#x35;" k="74" />
    <hkern u1="&#x10f;" u2="&#x34;" k="168" />
    <hkern u1="&#x10f;" u2="&#x33;" k="29" />
    <hkern u1="&#x10f;" u2="&#x32;" k="31" />
    <hkern u1="&#x10f;" u2="&#x31;" k="-31" />
    <hkern u1="&#x10f;" u2="&#x30;" k="43" />
    <hkern u1="&#x10f;" u2="&#x2f;" k="250" />
    <hkern u1="&#x10f;" u2="&#x2a;" k="18" />
    <hkern u1="&#x10f;" u2="&#x26;" k="109" />
    <hkern u1="&#x10f;" u2="&#x24;" k="27" />
    <hkern u1="&#x10f;" u2="&#x23;" k="53" />
    <hkern u1="&#x110;" u2="&#x2122;" k="31" />
    <hkern u1="&#x110;" u2="x" k="31" />
    <hkern u1="&#x110;" u2="\" k="76" />
    <hkern u1="&#x110;" u2="X" k="68" />
    <hkern u1="&#x110;" u2="V" k="53" />
    <hkern u1="&#x110;" u2="&#x2f;" k="57" />
    <hkern u1="&#x110;" u2="&#x2a;" k="10" />
    <hkern u1="&#x112;" u2="&#x2122;" k="-12" />
    <hkern u1="&#x112;" u2="x" k="-10" />
    <hkern u1="&#x112;" u2="\" k="4" />
    <hkern u1="&#x112;" u2="X" k="-6" />
    <hkern u1="&#x113;" u2="&#x2122;" k="74" />
    <hkern u1="&#x113;" u2="&#xae;" k="16" />
    <hkern u1="&#x113;" u2="x" k="25" />
    <hkern u1="&#x113;" u2="v" k="20" />
    <hkern u1="&#x113;" u2="\" k="111" />
    <hkern u1="&#x113;" u2="&#x3f;" k="31" />
    <hkern u1="&#x113;" u2="&#x2f;" k="27" />
    <hkern u1="&#x113;" u2="&#x2a;" k="20" />
    <hkern u1="&#x114;" u2="&#x2122;" k="-12" />
    <hkern u1="&#x114;" u2="x" k="-10" />
    <hkern u1="&#x114;" u2="\" k="4" />
    <hkern u1="&#x114;" u2="X" k="-6" />
    <hkern u1="&#x115;" u2="&#x2122;" k="74" />
    <hkern u1="&#x115;" u2="&#xae;" k="16" />
    <hkern u1="&#x115;" u2="x" k="25" />
    <hkern u1="&#x115;" u2="v" k="20" />
    <hkern u1="&#x115;" u2="\" k="111" />
    <hkern u1="&#x115;" u2="&#x3f;" k="31" />
    <hkern u1="&#x115;" u2="&#x2f;" k="27" />
    <hkern u1="&#x115;" u2="&#x2a;" k="20" />
    <hkern u1="&#x116;" u2="&#x2122;" k="-12" />
    <hkern u1="&#x116;" u2="x" k="-10" />
    <hkern u1="&#x116;" u2="\" k="4" />
    <hkern u1="&#x116;" u2="X" k="-6" />
    <hkern u1="&#x117;" u2="&#x2122;" k="74" />
    <hkern u1="&#x117;" u2="&#xae;" k="16" />
    <hkern u1="&#x117;" u2="x" k="25" />
    <hkern u1="&#x117;" u2="v" k="20" />
    <hkern u1="&#x117;" u2="\" k="111" />
    <hkern u1="&#x117;" u2="&#x3f;" k="31" />
    <hkern u1="&#x117;" u2="&#x2f;" k="27" />
    <hkern u1="&#x117;" u2="&#x2a;" k="20" />
    <hkern u1="&#x118;" u2="&#x2122;" k="-12" />
    <hkern u1="&#x118;" u2="x" k="-10" />
    <hkern u1="&#x118;" u2="\" k="4" />
    <hkern u1="&#x118;" u2="X" k="-6" />
    <hkern u1="&#x119;" u2="&#x2122;" k="74" />
    <hkern u1="&#x119;" u2="&#xae;" k="16" />
    <hkern u1="&#x119;" u2="x" k="25" />
    <hkern u1="&#x119;" u2="v" k="20" />
    <hkern u1="&#x119;" u2="\" k="111" />
    <hkern u1="&#x119;" u2="&#x3f;" k="31" />
    <hkern u1="&#x119;" u2="&#x2f;" k="27" />
    <hkern u1="&#x119;" u2="&#x2a;" k="20" />
    <hkern u1="&#x11a;" u2="&#x2122;" k="-12" />
    <hkern u1="&#x11a;" u2="x" k="-10" />
    <hkern u1="&#x11a;" u2="\" k="4" />
    <hkern u1="&#x11a;" u2="X" k="-6" />
    <hkern u1="&#x11b;" u2="&#x2122;" k="74" />
    <hkern u1="&#x11b;" u2="&#xae;" k="16" />
    <hkern u1="&#x11b;" u2="x" k="25" />
    <hkern u1="&#x11b;" u2="v" k="20" />
    <hkern u1="&#x11b;" u2="\" k="111" />
    <hkern u1="&#x11b;" u2="&#x3f;" k="31" />
    <hkern u1="&#x11b;" u2="&#x2f;" k="27" />
    <hkern u1="&#x11b;" u2="&#x2a;" k="20" />
    <hkern u1="&#x11c;" u2="&#x2122;" k="10" />
    <hkern u1="&#x11c;" u2="x" k="10" />
    <hkern u1="&#x11c;" u2="\" k="80" />
    <hkern u1="&#x11c;" u2="X" k="31" />
    <hkern u1="&#x11c;" u2="V" k="37" />
    <hkern u1="&#x11c;" u2="&#x2f;" k="53" />
    <hkern u1="&#x11d;" u2="&#x2122;" k="16" />
    <hkern u1="&#x11d;" u2="\" k="20" />
    <hkern u1="&#x11e;" u2="&#x2122;" k="10" />
    <hkern u1="&#x11e;" u2="x" k="10" />
    <hkern u1="&#x11e;" u2="\" k="80" />
    <hkern u1="&#x11e;" u2="X" k="31" />
    <hkern u1="&#x11e;" u2="V" k="37" />
    <hkern u1="&#x11e;" u2="&#x2f;" k="53" />
    <hkern u1="&#x11f;" u2="&#x2122;" k="16" />
    <hkern u1="&#x11f;" u2="\" k="20" />
    <hkern u1="&#x120;" u2="&#x2122;" k="10" />
    <hkern u1="&#x120;" u2="x" k="10" />
    <hkern u1="&#x120;" u2="\" k="80" />
    <hkern u1="&#x120;" u2="X" k="31" />
    <hkern u1="&#x120;" u2="V" k="37" />
    <hkern u1="&#x120;" u2="&#x2f;" k="53" />
    <hkern u1="&#x121;" u2="&#x2122;" k="16" />
    <hkern u1="&#x121;" u2="\" k="20" />
    <hkern u1="&#x122;" u2="&#x2122;" k="10" />
    <hkern u1="&#x122;" u2="x" k="10" />
    <hkern u1="&#x122;" u2="\" k="80" />
    <hkern u1="&#x122;" u2="X" k="31" />
    <hkern u1="&#x122;" u2="V" k="37" />
    <hkern u1="&#x122;" u2="&#x2f;" k="53" />
    <hkern u1="&#x123;" u2="&#x2122;" k="16" />
    <hkern u1="&#x123;" u2="\" k="20" />
    <hkern u1="&#x125;" u2="&#x2122;" k="98" />
    <hkern u1="&#x125;" u2="&#xae;" k="27" />
    <hkern u1="&#x125;" u2="v" k="8" />
    <hkern u1="&#x125;" u2="\" k="109" />
    <hkern u1="&#x125;" u2="&#x2a;" k="31" />
    <hkern u1="&#x127;" u2="&#x2122;" k="98" />
    <hkern u1="&#x127;" u2="&#xae;" k="27" />
    <hkern u1="&#x127;" u2="v" k="8" />
    <hkern u1="&#x127;" u2="\" k="109" />
    <hkern u1="&#x127;" u2="&#x2a;" k="31" />
    <hkern u1="&#x132;" u2="&#x2f;" k="37" />
    <hkern u1="&#x134;" u2="&#x2f;" k="37" />
    <hkern u1="&#x136;" u2="&#x2122;" k="-12" />
    <hkern u1="&#x136;" u2="&#xbf;" k="16" />
    <hkern u1="&#x136;" u2="&#xae;" k="68" />
    <hkern u1="&#x136;" u2="x" k="-29" />
    <hkern u1="&#x136;" u2="v" k="82" />
    <hkern u1="&#x136;" u2="X" k="-23" />
    <hkern u1="&#x136;" u2="&#x40;" k="31" />
    <hkern u1="&#x136;" u2="&#x3f;" k="31" />
    <hkern u1="&#x136;" u2="&#x2f;" k="-20" />
    <hkern u1="&#x136;" u2="&#x2a;" k="37" />
    <hkern u1="&#x136;" u2="&#x26;" k="20" />
    <hkern u1="&#x137;" u2="&#xbf;" k="10" />
    <hkern u1="&#x137;" u2="&#xae;" k="-10" />
    <hkern u1="&#x137;" u2="x" k="-12" />
    <hkern u1="&#x137;" u2="\" k="51" />
    <hkern u1="&#x137;" u2="&#x40;" k="14" />
    <hkern u1="&#x137;" u2="&#x3f;" k="-20" />
    <hkern u1="&#x137;" u2="&#x26;" k="20" />
    <hkern u1="&#x138;" u2="&#xbf;" k="10" />
    <hkern u1="&#x138;" u2="&#xae;" k="-10" />
    <hkern u1="&#x138;" u2="x" k="-12" />
    <hkern u1="&#x138;" u2="\" k="51" />
    <hkern u1="&#x138;" u2="&#x40;" k="14" />
    <hkern u1="&#x138;" u2="&#x3f;" k="-20" />
    <hkern u1="&#x138;" u2="&#x26;" k="20" />
    <hkern u1="&#x139;" u2="&#x2122;" k="418" />
    <hkern u1="&#x139;" u2="&#xae;" k="358" />
    <hkern u1="&#x139;" u2="v" k="123" />
    <hkern u1="&#x139;" u2="\" k="193" />
    <hkern u1="&#x139;" u2="X" k="-10" />
    <hkern u1="&#x139;" u2="V" k="190" />
    <hkern u1="&#x139;" u2="&#x40;" k="16" />
    <hkern u1="&#x139;" u2="&#x3f;" k="133" />
    <hkern u1="&#x139;" u2="&#x2f;" k="-16" />
    <hkern u1="&#x139;" u2="&#x2a;" k="420" />
    <hkern u1="&#x139;" u2="&#x26;" k="10" />
    <hkern u1="&#x13b;" u2="&#x2122;" k="418" />
    <hkern u1="&#x13b;" u2="&#xae;" k="358" />
    <hkern u1="&#x13b;" u2="v" k="123" />
    <hkern u1="&#x13b;" u2="\" k="193" />
    <hkern u1="&#x13b;" u2="X" k="-10" />
    <hkern u1="&#x13b;" u2="V" k="190" />
    <hkern u1="&#x13b;" u2="&#x40;" k="16" />
    <hkern u1="&#x13b;" u2="&#x3f;" k="133" />
    <hkern u1="&#x13b;" u2="&#x2f;" k="-16" />
    <hkern u1="&#x13b;" u2="&#x2a;" k="420" />
    <hkern u1="&#x13b;" u2="&#x26;" k="10" />
    <hkern u1="&#x13e;" u2="&#x2122;" k="-8" />
    <hkern u1="&#x13e;" u2="&#x20ac;" k="49" />
    <hkern u1="&#x13e;" u2="&#xbf;" k="184" />
    <hkern u1="&#x13e;" u2="&#xae;" k="10" />
    <hkern u1="&#x13e;" u2="&#xa3;" k="115" />
    <hkern u1="&#x13e;" u2="&#xa2;" k="61" />
    <hkern u1="&#x13e;" u2="x" k="35" />
    <hkern u1="&#x13e;" u2="j" k="6" />
    <hkern u1="&#x13e;" u2="X" k="6" />
    <hkern u1="&#x13e;" u2="V" k="-27" />
    <hkern u1="&#x13e;" u2="&#x40;" k="90" />
    <hkern u1="&#x13e;" u2="&#x39;" k="16" />
    <hkern u1="&#x13e;" u2="&#x38;" k="63" />
    <hkern u1="&#x13e;" u2="&#x37;" k="-6" />
    <hkern u1="&#x13e;" u2="&#x36;" k="168" />
    <hkern u1="&#x13e;" u2="&#x35;" k="74" />
    <hkern u1="&#x13e;" u2="&#x34;" k="168" />
    <hkern u1="&#x13e;" u2="&#x33;" k="29" />
    <hkern u1="&#x13e;" u2="&#x32;" k="31" />
    <hkern u1="&#x13e;" u2="&#x31;" k="-31" />
    <hkern u1="&#x13e;" u2="&#x30;" k="43" />
    <hkern u1="&#x13e;" u2="&#x2f;" k="250" />
    <hkern u1="&#x13e;" u2="&#x2a;" k="18" />
    <hkern u1="&#x13e;" u2="&#x26;" k="109" />
    <hkern u1="&#x13e;" u2="&#x24;" k="27" />
    <hkern u1="&#x13e;" u2="&#x23;" k="53" />
    <hkern u1="&#x144;" u2="&#x2122;" k="98" />
    <hkern u1="&#x144;" u2="&#xae;" k="27" />
    <hkern u1="&#x144;" u2="v" k="8" />
    <hkern u1="&#x144;" u2="\" k="109" />
    <hkern u1="&#x144;" u2="&#x2a;" k="31" />
    <hkern u1="&#x146;" u2="&#x2122;" k="98" />
    <hkern u1="&#x146;" u2="&#xae;" k="27" />
    <hkern u1="&#x146;" u2="v" k="8" />
    <hkern u1="&#x146;" u2="\" k="109" />
    <hkern u1="&#x146;" u2="&#x2a;" k="31" />
    <hkern u1="&#x148;" u2="&#x2122;" k="98" />
    <hkern u1="&#x148;" u2="&#xae;" k="27" />
    <hkern u1="&#x148;" u2="v" k="8" />
    <hkern u1="&#x148;" u2="\" k="109" />
    <hkern u1="&#x148;" u2="&#x2a;" k="31" />
    <hkern u1="&#x14b;" u2="&#x2122;" k="98" />
    <hkern u1="&#x14b;" u2="&#xae;" k="27" />
    <hkern u1="&#x14b;" u2="v" k="8" />
    <hkern u1="&#x14b;" u2="\" k="109" />
    <hkern u1="&#x14b;" u2="&#x2a;" k="31" />
    <hkern u1="&#x14c;" u2="&#x2122;" k="31" />
    <hkern u1="&#x14c;" u2="x" k="31" />
    <hkern u1="&#x14c;" u2="\" k="76" />
    <hkern u1="&#x14c;" u2="X" k="68" />
    <hkern u1="&#x14c;" u2="V" k="53" />
    <hkern u1="&#x14c;" u2="&#x2f;" k="57" />
    <hkern u1="&#x14c;" u2="&#x2a;" k="10" />
    <hkern u1="&#x14d;" u2="&#x2122;" k="61" />
    <hkern u1="&#x14d;" u2="&#xae;" k="16" />
    <hkern u1="&#x14d;" u2="x" k="41" />
    <hkern u1="&#x14d;" u2="v" k="20" />
    <hkern u1="&#x14d;" u2="\" k="119" />
    <hkern u1="&#x14d;" u2="&#x3f;" k="27" />
    <hkern u1="&#x14d;" u2="&#x2f;" k="53" />
    <hkern u1="&#x14d;" u2="&#x2a;" k="41" />
    <hkern u1="&#x14e;" u2="&#x2122;" k="31" />
    <hkern u1="&#x14e;" u2="x" k="31" />
    <hkern u1="&#x14e;" u2="\" k="76" />
    <hkern u1="&#x14e;" u2="X" k="68" />
    <hkern u1="&#x14e;" u2="V" k="53" />
    <hkern u1="&#x14e;" u2="&#x2f;" k="57" />
    <hkern u1="&#x14e;" u2="&#x2a;" k="10" />
    <hkern u1="&#x14f;" u2="&#x2122;" k="61" />
    <hkern u1="&#x14f;" u2="&#xae;" k="16" />
    <hkern u1="&#x14f;" u2="x" k="41" />
    <hkern u1="&#x14f;" u2="v" k="20" />
    <hkern u1="&#x14f;" u2="\" k="119" />
    <hkern u1="&#x14f;" u2="&#x3f;" k="27" />
    <hkern u1="&#x14f;" u2="&#x2f;" k="53" />
    <hkern u1="&#x14f;" u2="&#x2a;" k="41" />
    <hkern u1="&#x150;" u2="&#x2122;" k="31" />
    <hkern u1="&#x150;" u2="x" k="31" />
    <hkern u1="&#x150;" u2="\" k="76" />
    <hkern u1="&#x150;" u2="X" k="68" />
    <hkern u1="&#x150;" u2="V" k="53" />
    <hkern u1="&#x150;" u2="&#x2f;" k="57" />
    <hkern u1="&#x150;" u2="&#x2a;" k="10" />
    <hkern u1="&#x151;" u2="&#x2122;" k="61" />
    <hkern u1="&#x151;" u2="&#xae;" k="16" />
    <hkern u1="&#x151;" u2="x" k="41" />
    <hkern u1="&#x151;" u2="v" k="20" />
    <hkern u1="&#x151;" u2="\" k="119" />
    <hkern u1="&#x151;" u2="&#x3f;" k="27" />
    <hkern u1="&#x151;" u2="&#x2f;" k="53" />
    <hkern u1="&#x151;" u2="&#x2a;" k="41" />
    <hkern u1="&#x153;" u2="&#x2122;" k="74" />
    <hkern u1="&#x153;" u2="&#xae;" k="16" />
    <hkern u1="&#x153;" u2="x" k="25" />
    <hkern u1="&#x153;" u2="v" k="20" />
    <hkern u1="&#x153;" u2="\" k="111" />
    <hkern u1="&#x153;" u2="&#x3f;" k="31" />
    <hkern u1="&#x153;" u2="&#x2f;" k="27" />
    <hkern u1="&#x153;" u2="&#x2a;" k="20" />
    <hkern u1="&#x154;" u2="&#x2122;" k="31" />
    <hkern u1="&#x154;" u2="&#xbf;" k="10" />
    <hkern u1="&#x154;" u2="&#xae;" k="10" />
    <hkern u1="&#x154;" u2="x" k="-25" />
    <hkern u1="&#x154;" u2="\" k="51" />
    <hkern u1="&#x154;" u2="X" k="-20" />
    <hkern u1="&#x154;" u2="V" k="47" />
    <hkern u1="&#x154;" u2="&#x40;" k="10" />
    <hkern u1="&#x154;" u2="&#x2a;" k="20" />
    <hkern u1="&#x154;" u2="&#x26;" k="10" />
    <hkern u1="&#x155;" u2="&#xbf;" k="31" />
    <hkern u1="&#x155;" u2="&#xae;" k="-20" />
    <hkern u1="&#x155;" u2="x" k="-16" />
    <hkern u1="&#x155;" u2="v" k="-41" />
    <hkern u1="&#x155;" u2="\" k="45" />
    <hkern u1="&#x155;" u2="&#x3f;" k="-20" />
    <hkern u1="&#x155;" u2="&#x2f;" k="111" />
    <hkern u1="&#x155;" u2="&#x2a;" k="-20" />
    <hkern u1="&#x155;" u2="&#x26;" k="10" />
    <hkern u1="&#x156;" u2="&#x2122;" k="31" />
    <hkern u1="&#x156;" u2="&#xbf;" k="10" />
    <hkern u1="&#x156;" u2="&#xae;" k="10" />
    <hkern u1="&#x156;" u2="x" k="-25" />
    <hkern u1="&#x156;" u2="\" k="51" />
    <hkern u1="&#x156;" u2="X" k="-20" />
    <hkern u1="&#x156;" u2="V" k="47" />
    <hkern u1="&#x156;" u2="&#x40;" k="10" />
    <hkern u1="&#x156;" u2="&#x2a;" k="20" />
    <hkern u1="&#x156;" u2="&#x26;" k="10" />
    <hkern u1="&#x157;" u2="&#xbf;" k="31" />
    <hkern u1="&#x157;" u2="&#xae;" k="-20" />
    <hkern u1="&#x157;" u2="x" k="-16" />
    <hkern u1="&#x157;" u2="v" k="-41" />
    <hkern u1="&#x157;" u2="\" k="45" />
    <hkern u1="&#x157;" u2="&#x3f;" k="-20" />
    <hkern u1="&#x157;" u2="&#x2f;" k="111" />
    <hkern u1="&#x157;" u2="&#x2a;" k="-20" />
    <hkern u1="&#x157;" u2="&#x26;" k="10" />
    <hkern u1="&#x158;" u2="&#x2122;" k="31" />
    <hkern u1="&#x158;" u2="&#xbf;" k="10" />
    <hkern u1="&#x158;" u2="&#xae;" k="10" />
    <hkern u1="&#x158;" u2="x" k="-25" />
    <hkern u1="&#x158;" u2="\" k="51" />
    <hkern u1="&#x158;" u2="X" k="-20" />
    <hkern u1="&#x158;" u2="V" k="47" />
    <hkern u1="&#x158;" u2="&#x40;" k="10" />
    <hkern u1="&#x158;" u2="&#x2a;" k="20" />
    <hkern u1="&#x158;" u2="&#x26;" k="10" />
    <hkern u1="&#x159;" u2="&#xbf;" k="31" />
    <hkern u1="&#x159;" u2="&#xae;" k="-20" />
    <hkern u1="&#x159;" u2="x" k="-16" />
    <hkern u1="&#x159;" u2="v" k="-41" />
    <hkern u1="&#x159;" u2="\" k="45" />
    <hkern u1="&#x159;" u2="&#x3f;" k="-20" />
    <hkern u1="&#x159;" u2="&#x2f;" k="111" />
    <hkern u1="&#x159;" u2="&#x2a;" k="-20" />
    <hkern u1="&#x159;" u2="&#x26;" k="10" />
    <hkern u1="&#x15a;" u2="&#x2122;" k="51" />
    <hkern u1="&#x15a;" u2="&#xae;" k="10" />
    <hkern u1="&#x15a;" u2="x" k="27" />
    <hkern u1="&#x15a;" u2="v" k="10" />
    <hkern u1="&#x15a;" u2="\" k="84" />
    <hkern u1="&#x15a;" u2="X" k="37" />
    <hkern u1="&#x15a;" u2="V" k="74" />
    <hkern u1="&#x15a;" u2="&#x2f;" k="63" />
    <hkern u1="&#x15a;" u2="&#x2a;" k="20" />
    <hkern u1="&#x15b;" u2="&#x2122;" k="66" />
    <hkern u1="&#x15b;" u2="&#xae;" k="4" />
    <hkern u1="&#x15b;" u2="x" k="14" />
    <hkern u1="&#x15b;" u2="v" k="20" />
    <hkern u1="&#x15b;" u2="\" k="121" />
    <hkern u1="&#x15b;" u2="&#x3f;" k="20" />
    <hkern u1="&#x15b;" u2="&#x2f;" k="47" />
    <hkern u1="&#x15b;" u2="&#x2a;" k="41" />
    <hkern u1="&#x15c;" u2="&#x2122;" k="51" />
    <hkern u1="&#x15c;" u2="&#xae;" k="10" />
    <hkern u1="&#x15c;" u2="x" k="27" />
    <hkern u1="&#x15c;" u2="v" k="10" />
    <hkern u1="&#x15c;" u2="\" k="84" />
    <hkern u1="&#x15c;" u2="X" k="37" />
    <hkern u1="&#x15c;" u2="V" k="74" />
    <hkern u1="&#x15c;" u2="&#x2f;" k="63" />
    <hkern u1="&#x15c;" u2="&#x2a;" k="20" />
    <hkern u1="&#x15d;" u2="&#x2122;" k="66" />
    <hkern u1="&#x15d;" u2="&#xae;" k="4" />
    <hkern u1="&#x15d;" u2="x" k="14" />
    <hkern u1="&#x15d;" u2="v" k="20" />
    <hkern u1="&#x15d;" u2="\" k="121" />
    <hkern u1="&#x15d;" u2="&#x3f;" k="20" />
    <hkern u1="&#x15d;" u2="&#x2f;" k="47" />
    <hkern u1="&#x15d;" u2="&#x2a;" k="41" />
    <hkern u1="&#x15e;" u2="&#x2122;" k="51" />
    <hkern u1="&#x15e;" u2="&#xae;" k="10" />
    <hkern u1="&#x15e;" u2="x" k="27" />
    <hkern u1="&#x15e;" u2="v" k="10" />
    <hkern u1="&#x15e;" u2="\" k="84" />
    <hkern u1="&#x15e;" u2="X" k="37" />
    <hkern u1="&#x15e;" u2="V" k="74" />
    <hkern u1="&#x15e;" u2="&#x2f;" k="63" />
    <hkern u1="&#x15e;" u2="&#x2a;" k="20" />
    <hkern u1="&#x15f;" u2="&#x2122;" k="66" />
    <hkern u1="&#x15f;" u2="&#xae;" k="4" />
    <hkern u1="&#x15f;" u2="x" k="14" />
    <hkern u1="&#x15f;" u2="v" k="20" />
    <hkern u1="&#x15f;" u2="\" k="121" />
    <hkern u1="&#x15f;" u2="&#x3f;" k="20" />
    <hkern u1="&#x15f;" u2="&#x2f;" k="47" />
    <hkern u1="&#x15f;" u2="&#x2a;" k="41" />
    <hkern u1="&#x160;" u2="&#x2122;" k="51" />
    <hkern u1="&#x160;" u2="&#xae;" k="10" />
    <hkern u1="&#x160;" u2="x" k="27" />
    <hkern u1="&#x160;" u2="v" k="10" />
    <hkern u1="&#x160;" u2="\" k="84" />
    <hkern u1="&#x160;" u2="X" k="37" />
    <hkern u1="&#x160;" u2="V" k="74" />
    <hkern u1="&#x160;" u2="&#x2f;" k="63" />
    <hkern u1="&#x160;" u2="&#x2a;" k="20" />
    <hkern u1="&#x161;" u2="&#x2122;" k="66" />
    <hkern u1="&#x161;" u2="&#xae;" k="4" />
    <hkern u1="&#x161;" u2="x" k="14" />
    <hkern u1="&#x161;" u2="v" k="20" />
    <hkern u1="&#x161;" u2="\" k="121" />
    <hkern u1="&#x161;" u2="&#x3f;" k="20" />
    <hkern u1="&#x161;" u2="&#x2f;" k="47" />
    <hkern u1="&#x161;" u2="&#x2a;" k="41" />
    <hkern u1="&#x162;" u2="&#x2122;" k="-16" />
    <hkern u1="&#x162;" u2="&#xbf;" k="109" />
    <hkern u1="&#x162;" u2="x" k="102" />
    <hkern u1="&#x162;" u2="v" k="72" />
    <hkern u1="&#x162;" u2="i" k="10" />
    <hkern u1="&#x162;" u2="X" k="-8" />
    <hkern u1="&#x162;" u2="V" k="-23" />
    <hkern u1="&#x162;" u2="&#x40;" k="57" />
    <hkern u1="&#x162;" u2="&#x3f;" k="-12" />
    <hkern u1="&#x162;" u2="&#x2f;" k="178" />
    <hkern u1="&#x162;" u2="&#x26;" k="16" />
    <hkern u1="&#x162;" u2="&#x21;" k="-10" />
    <hkern u1="&#x163;" u2="&#x2122;" k="12" />
    <hkern u1="&#x163;" u2="&#xae;" k="-16" />
    <hkern u1="&#x163;" u2="x" k="-18" />
    <hkern u1="&#x163;" u2="v" k="-16" />
    <hkern u1="&#x163;" u2="\" k="72" />
    <hkern u1="&#x163;" u2="&#x2f;" k="16" />
    <hkern u1="&#x164;" u2="&#x2122;" k="-16" />
    <hkern u1="&#x164;" u2="&#xbf;" k="109" />
    <hkern u1="&#x164;" u2="x" k="102" />
    <hkern u1="&#x164;" u2="v" k="72" />
    <hkern u1="&#x164;" u2="i" k="10" />
    <hkern u1="&#x164;" u2="X" k="-8" />
    <hkern u1="&#x164;" u2="V" k="-23" />
    <hkern u1="&#x164;" u2="&#x40;" k="57" />
    <hkern u1="&#x164;" u2="&#x3f;" k="-12" />
    <hkern u1="&#x164;" u2="&#x2f;" k="178" />
    <hkern u1="&#x164;" u2="&#x26;" k="16" />
    <hkern u1="&#x164;" u2="&#x21;" k="-10" />
    <hkern u1="&#x168;" u2="x" k="10" />
    <hkern u1="&#x168;" u2="X" k="10" />
    <hkern u1="&#x168;" u2="&#x2f;" k="49" />
    <hkern u1="&#x169;" u2="&#x2122;" k="16" />
    <hkern u1="&#x169;" u2="\" k="20" />
    <hkern u1="&#x16a;" u2="x" k="10" />
    <hkern u1="&#x16a;" u2="X" k="10" />
    <hkern u1="&#x16a;" u2="&#x2f;" k="49" />
    <hkern u1="&#x16b;" u2="&#x2122;" k="16" />
    <hkern u1="&#x16b;" u2="\" k="20" />
    <hkern u1="&#x16c;" u2="x" k="10" />
    <hkern u1="&#x16c;" u2="X" k="10" />
    <hkern u1="&#x16c;" u2="&#x2f;" k="49" />
    <hkern u1="&#x16d;" u2="&#x2122;" k="16" />
    <hkern u1="&#x16d;" u2="\" k="20" />
    <hkern u1="&#x16e;" u2="x" k="10" />
    <hkern u1="&#x16e;" u2="X" k="10" />
    <hkern u1="&#x16e;" u2="&#x2f;" k="49" />
    <hkern u1="&#x16f;" u2="&#x2122;" k="16" />
    <hkern u1="&#x16f;" u2="\" k="20" />
    <hkern u1="&#x170;" u2="x" k="10" />
    <hkern u1="&#x170;" u2="X" k="10" />
    <hkern u1="&#x170;" u2="&#x2f;" k="49" />
    <hkern u1="&#x171;" u2="&#x2122;" k="16" />
    <hkern u1="&#x171;" u2="\" k="20" />
    <hkern u1="&#x172;" u2="x" k="10" />
    <hkern u1="&#x172;" u2="X" k="10" />
    <hkern u1="&#x172;" u2="&#x2f;" k="49" />
    <hkern u1="&#x173;" u2="&#x2122;" k="16" />
    <hkern u1="&#x173;" u2="\" k="20" />
    <hkern u1="&#x174;" u2="&#x2122;" k="-33" />
    <hkern u1="&#x174;" u2="&#xbf;" k="59" />
    <hkern u1="&#x174;" u2="&#xa1;" k="20" />
    <hkern u1="&#x174;" u2="x" k="31" />
    <hkern u1="&#x174;" u2="v" k="10" />
    <hkern u1="&#x174;" u2="i" k="6" />
    <hkern u1="&#x174;" u2="\" k="-10" />
    <hkern u1="&#x174;" u2="&#x40;" k="37" />
    <hkern u1="&#x174;" u2="&#x2f;" k="125" />
    <hkern u1="&#x174;" u2="&#x2a;" k="16" />
    <hkern u1="&#x174;" u2="&#x26;" k="23" />
    <hkern u1="&#x175;" u2="&#xbf;" k="37" />
    <hkern u1="&#x175;" u2="&#xae;" k="-37" />
    <hkern u1="&#x175;" u2="v" k="-4" />
    <hkern u1="&#x175;" u2="\" k="14" />
    <hkern u1="&#x175;" u2="&#x3f;" k="-39" />
    <hkern u1="&#x175;" u2="&#x2f;" k="70" />
    <hkern u1="&#x175;" u2="&#x2a;" k="-31" />
    <hkern u1="&#x175;" u2="&#x26;" k="16" />
    <hkern u1="&#x176;" u2="&#x2122;" k="-43" />
    <hkern u1="&#x176;" u2="&#xbf;" k="154" />
    <hkern u1="&#x176;" u2="&#xae;" k="37" />
    <hkern u1="&#x176;" u2="&#xa1;" k="37" />
    <hkern u1="&#x176;" u2="x" k="94" />
    <hkern u1="&#x176;" u2="v" k="66" />
    <hkern u1="&#x176;" u2="i" k="4" />
    <hkern u1="&#x176;" u2="&#x40;" k="135" />
    <hkern u1="&#x176;" u2="&#x3f;" k="10" />
    <hkern u1="&#x176;" u2="&#x2f;" k="225" />
    <hkern u1="&#x176;" u2="&#x2a;" k="31" />
    <hkern u1="&#x176;" u2="&#x26;" k="100" />
    <hkern u1="&#x177;" u2="&#xbf;" k="33" />
    <hkern u1="&#x177;" u2="&#xae;" k="-37" />
    <hkern u1="&#x177;" u2="\" k="14" />
    <hkern u1="&#x177;" u2="&#x40;" k="10" />
    <hkern u1="&#x177;" u2="&#x3f;" k="-45" />
    <hkern u1="&#x177;" u2="&#x2f;" k="72" />
    <hkern u1="&#x177;" u2="&#x2a;" k="-31" />
    <hkern u1="&#x178;" u2="&#x2122;" k="-43" />
    <hkern u1="&#x178;" u2="&#xbf;" k="154" />
    <hkern u1="&#x178;" u2="&#xae;" k="37" />
    <hkern u1="&#x178;" u2="&#xa1;" k="37" />
    <hkern u1="&#x178;" u2="x" k="94" />
    <hkern u1="&#x178;" u2="v" k="66" />
    <hkern u1="&#x178;" u2="i" k="4" />
    <hkern u1="&#x178;" u2="&#x40;" k="135" />
    <hkern u1="&#x178;" u2="&#x3f;" k="10" />
    <hkern u1="&#x178;" u2="&#x2f;" k="225" />
    <hkern u1="&#x178;" u2="&#x2a;" k="31" />
    <hkern u1="&#x178;" u2="&#x26;" k="100" />
    <hkern u1="&#x179;" u2="&#x2122;" k="4" />
    <hkern u1="&#x179;" u2="&#xbf;" k="8" />
    <hkern u1="&#x179;" u2="&#xae;" k="25" />
    <hkern u1="&#x179;" u2="x" k="-6" />
    <hkern u1="&#x179;" u2="v" k="10" />
    <hkern u1="&#x179;" u2="X" k="-10" />
    <hkern u1="&#x179;" u2="&#x3f;" k="18" />
    <hkern u1="&#x179;" u2="&#x2a;" k="20" />
    <hkern u1="&#x17a;" u2="x" k="-10" />
    <hkern u1="&#x17a;" u2="\" k="41" />
    <hkern u1="&#x17b;" u2="&#x2122;" k="4" />
    <hkern u1="&#x17b;" u2="&#xbf;" k="8" />
    <hkern u1="&#x17b;" u2="&#xae;" k="25" />
    <hkern u1="&#x17b;" u2="x" k="-6" />
    <hkern u1="&#x17b;" u2="v" k="10" />
    <hkern u1="&#x17b;" u2="X" k="-10" />
    <hkern u1="&#x17b;" u2="&#x3f;" k="18" />
    <hkern u1="&#x17b;" u2="&#x2a;" k="20" />
    <hkern u1="&#x17c;" u2="x" k="-10" />
    <hkern u1="&#x17c;" u2="\" k="41" />
    <hkern u1="&#x17d;" u2="&#x2122;" k="4" />
    <hkern u1="&#x17d;" u2="&#xbf;" k="8" />
    <hkern u1="&#x17d;" u2="&#xae;" k="25" />
    <hkern u1="&#x17d;" u2="x" k="-6" />
    <hkern u1="&#x17d;" u2="v" k="10" />
    <hkern u1="&#x17d;" u2="X" k="-10" />
    <hkern u1="&#x17d;" u2="&#x3f;" k="18" />
    <hkern u1="&#x17d;" u2="&#x2a;" k="20" />
    <hkern u1="&#x17e;" u2="x" k="-10" />
    <hkern u1="&#x17e;" u2="\" k="41" />
    <hkern u1="&#x1fa;" u2="&#x2122;" k="225" />
    <hkern u1="&#x1fa;" u2="&#xae;" k="195" />
    <hkern u1="&#x1fa;" u2="x" k="-20" />
    <hkern u1="&#x1fa;" u2="v" k="102" />
    <hkern u1="&#x1fa;" u2="\" k="154" />
    <hkern u1="&#x1fa;" u2="X" k="-31" />
    <hkern u1="&#x1fa;" u2="V" k="195" />
    <hkern u1="&#x1fa;" u2="&#x40;" k="10" />
    <hkern u1="&#x1fa;" u2="&#x3f;" k="131" />
    <hkern u1="&#x1fa;" u2="&#x2a;" k="215" />
    <hkern u1="&#x1fa;" u2="&#x26;" k="20" />
    <hkern u1="&#x1fb;" u2="&#x2122;" k="115" />
    <hkern u1="&#x1fb;" u2="&#xae;" k="18" />
    <hkern u1="&#x1fb;" u2="x" k="-4" />
    <hkern u1="&#x1fb;" u2="v" k="23" />
    <hkern u1="&#x1fb;" u2="\" k="117" />
    <hkern u1="&#x1fb;" u2="&#x3f;" k="74" />
    <hkern u1="&#x1fb;" u2="&#x2a;" k="20" />
    <hkern u1="&#x1fc;" u2="&#x2122;" k="-12" />
    <hkern u1="&#x1fc;" u2="x" k="-10" />
    <hkern u1="&#x1fc;" u2="\" k="4" />
    <hkern u1="&#x1fc;" u2="X" k="-6" />
    <hkern u1="&#x1fd;" u2="&#x2122;" k="74" />
    <hkern u1="&#x1fd;" u2="&#xae;" k="16" />
    <hkern u1="&#x1fd;" u2="x" k="25" />
    <hkern u1="&#x1fd;" u2="v" k="20" />
    <hkern u1="&#x1fd;" u2="\" k="111" />
    <hkern u1="&#x1fd;" u2="&#x3f;" k="31" />
    <hkern u1="&#x1fd;" u2="&#x2f;" k="27" />
    <hkern u1="&#x1fd;" u2="&#x2a;" k="20" />
    <hkern u1="&#x1fe;" u2="&#x2122;" k="31" />
    <hkern u1="&#x1fe;" u2="x" k="31" />
    <hkern u1="&#x1fe;" u2="\" k="76" />
    <hkern u1="&#x1fe;" u2="X" k="68" />
    <hkern u1="&#x1fe;" u2="V" k="53" />
    <hkern u1="&#x1fe;" u2="&#x2f;" k="57" />
    <hkern u1="&#x1fe;" u2="&#x2a;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x2122;" k="61" />
    <hkern u1="&#x1ff;" u2="&#xae;" k="16" />
    <hkern u1="&#x1ff;" u2="x" k="41" />
    <hkern u1="&#x1ff;" u2="v" k="20" />
    <hkern u1="&#x1ff;" u2="\" k="119" />
    <hkern u1="&#x1ff;" u2="&#x3f;" k="27" />
    <hkern u1="&#x1ff;" u2="&#x2f;" k="53" />
    <hkern u1="&#x1ff;" u2="&#x2a;" k="41" />
    <hkern u1="&#x219;" u2="&#x2122;" k="66" />
    <hkern u1="&#x219;" u2="&#xae;" k="4" />
    <hkern u1="&#x219;" u2="x" k="14" />
    <hkern u1="&#x219;" u2="v" k="20" />
    <hkern u1="&#x219;" u2="\" k="121" />
    <hkern u1="&#x219;" u2="&#x3f;" k="20" />
    <hkern u1="&#x219;" u2="&#x2f;" k="47" />
    <hkern u1="&#x219;" u2="&#x2a;" k="41" />
    <hkern u1="&#x1e80;" u2="&#x2122;" k="-33" />
    <hkern u1="&#x1e80;" u2="&#xbf;" k="59" />
    <hkern u1="&#x1e80;" u2="&#xa1;" k="20" />
    <hkern u1="&#x1e80;" u2="x" k="31" />
    <hkern u1="&#x1e80;" u2="v" k="10" />
    <hkern u1="&#x1e80;" u2="i" k="6" />
    <hkern u1="&#x1e80;" u2="\" k="-10" />
    <hkern u1="&#x1e80;" u2="&#x40;" k="37" />
    <hkern u1="&#x1e80;" u2="&#x2f;" k="125" />
    <hkern u1="&#x1e80;" u2="&#x2a;" k="16" />
    <hkern u1="&#x1e80;" u2="&#x26;" k="23" />
    <hkern u1="&#x1e81;" u2="&#xbf;" k="37" />
    <hkern u1="&#x1e81;" u2="&#xae;" k="-37" />
    <hkern u1="&#x1e81;" u2="v" k="-4" />
    <hkern u1="&#x1e81;" u2="\" k="14" />
    <hkern u1="&#x1e81;" u2="&#x3f;" k="-39" />
    <hkern u1="&#x1e81;" u2="&#x2f;" k="70" />
    <hkern u1="&#x1e81;" u2="&#x2a;" k="-31" />
    <hkern u1="&#x1e81;" u2="&#x26;" k="16" />
    <hkern u1="&#x1e82;" u2="&#x2122;" k="-33" />
    <hkern u1="&#x1e82;" u2="&#xbf;" k="59" />
    <hkern u1="&#x1e82;" u2="&#xa1;" k="20" />
    <hkern u1="&#x1e82;" u2="x" k="31" />
    <hkern u1="&#x1e82;" u2="v" k="10" />
    <hkern u1="&#x1e82;" u2="i" k="6" />
    <hkern u1="&#x1e82;" u2="\" k="-10" />
    <hkern u1="&#x1e82;" u2="&#x40;" k="37" />
    <hkern u1="&#x1e82;" u2="&#x2f;" k="125" />
    <hkern u1="&#x1e82;" u2="&#x2a;" k="16" />
    <hkern u1="&#x1e82;" u2="&#x26;" k="23" />
    <hkern u1="&#x1e83;" u2="&#xbf;" k="37" />
    <hkern u1="&#x1e83;" u2="&#xae;" k="-37" />
    <hkern u1="&#x1e83;" u2="v" k="-4" />
    <hkern u1="&#x1e83;" u2="\" k="14" />
    <hkern u1="&#x1e83;" u2="&#x3f;" k="-39" />
    <hkern u1="&#x1e83;" u2="&#x2f;" k="70" />
    <hkern u1="&#x1e83;" u2="&#x2a;" k="-31" />
    <hkern u1="&#x1e83;" u2="&#x26;" k="16" />
    <hkern u1="&#x1e84;" u2="&#x2122;" k="-33" />
    <hkern u1="&#x1e84;" u2="&#xbf;" k="59" />
    <hkern u1="&#x1e84;" u2="&#xa1;" k="20" />
    <hkern u1="&#x1e84;" u2="x" k="31" />
    <hkern u1="&#x1e84;" u2="v" k="10" />
    <hkern u1="&#x1e84;" u2="i" k="6" />
    <hkern u1="&#x1e84;" u2="\" k="-10" />
    <hkern u1="&#x1e84;" u2="&#x40;" k="37" />
    <hkern u1="&#x1e84;" u2="&#x2f;" k="125" />
    <hkern u1="&#x1e84;" u2="&#x2a;" k="16" />
    <hkern u1="&#x1e84;" u2="&#x26;" k="23" />
    <hkern u1="&#x1e85;" u2="&#xbf;" k="37" />
    <hkern u1="&#x1e85;" u2="&#xae;" k="-37" />
    <hkern u1="&#x1e85;" u2="v" k="-4" />
    <hkern u1="&#x1e85;" u2="\" k="14" />
    <hkern u1="&#x1e85;" u2="&#x3f;" k="-39" />
    <hkern u1="&#x1e85;" u2="&#x2f;" k="70" />
    <hkern u1="&#x1e85;" u2="&#x2a;" k="-31" />
    <hkern u1="&#x1e85;" u2="&#x26;" k="16" />
    <hkern u1="&#x1ef2;" u2="&#x2122;" k="-43" />
    <hkern u1="&#x1ef2;" u2="&#xbf;" k="154" />
    <hkern u1="&#x1ef2;" u2="&#xae;" k="37" />
    <hkern u1="&#x1ef2;" u2="&#xa1;" k="37" />
    <hkern u1="&#x1ef2;" u2="x" k="94" />
    <hkern u1="&#x1ef2;" u2="v" k="66" />
    <hkern u1="&#x1ef2;" u2="i" k="4" />
    <hkern u1="&#x1ef2;" u2="&#x40;" k="135" />
    <hkern u1="&#x1ef2;" u2="&#x3f;" k="10" />
    <hkern u1="&#x1ef2;" u2="&#x2f;" k="225" />
    <hkern u1="&#x1ef2;" u2="&#x2a;" k="31" />
    <hkern u1="&#x1ef2;" u2="&#x26;" k="100" />
    <hkern u1="&#x1ef3;" u2="&#xbf;" k="33" />
    <hkern u1="&#x1ef3;" u2="&#xae;" k="-37" />
    <hkern u1="&#x1ef3;" u2="\" k="14" />
    <hkern u1="&#x1ef3;" u2="&#x40;" k="10" />
    <hkern u1="&#x1ef3;" u2="&#x3f;" k="-45" />
    <hkern u1="&#x1ef3;" u2="&#x2f;" k="72" />
    <hkern u1="&#x1ef3;" u2="&#x2a;" k="-31" />
    <hkern u1="&#x2013;" u2="&#x2122;" k="37" />
    <hkern u1="&#x2013;" u2="&#xa5;" k="20" />
    <hkern u1="&#x2013;" u2="x" k="72" />
    <hkern u1="&#x2013;" u2="v" k="23" />
    <hkern u1="&#x2013;" u2="\" k="92" />
    <hkern u1="&#x2013;" u2="X" k="129" />
    <hkern u1="&#x2013;" u2="V" k="102" />
    <hkern u1="&#x2013;" u2="&#x3f;" k="20" />
    <hkern u1="&#x2013;" u2="&#x39;" k="18" />
    <hkern u1="&#x2013;" u2="&#x37;" k="68" />
    <hkern u1="&#x2013;" u2="&#x35;" k="10" />
    <hkern u1="&#x2013;" u2="&#x33;" k="14" />
    <hkern u1="&#x2013;" u2="&#x32;" k="20" />
    <hkern u1="&#x2013;" u2="&#x31;" k="33" />
    <hkern u1="&#x2013;" u2="&#x2f;" k="102" />
    <hkern u1="&#x2013;" u2="&#x2a;" k="10" />
    <hkern u1="&#x2014;" u2="&#x2122;" k="37" />
    <hkern u1="&#x2014;" u2="&#xa5;" k="20" />
    <hkern u1="&#x2014;" u2="x" k="72" />
    <hkern u1="&#x2014;" u2="v" k="23" />
    <hkern u1="&#x2014;" u2="\" k="92" />
    <hkern u1="&#x2014;" u2="X" k="129" />
    <hkern u1="&#x2014;" u2="V" k="102" />
    <hkern u1="&#x2014;" u2="&#x3f;" k="20" />
    <hkern u1="&#x2014;" u2="&#x39;" k="18" />
    <hkern u1="&#x2014;" u2="&#x37;" k="68" />
    <hkern u1="&#x2014;" u2="&#x35;" k="10" />
    <hkern u1="&#x2014;" u2="&#x33;" k="14" />
    <hkern u1="&#x2014;" u2="&#x32;" k="20" />
    <hkern u1="&#x2014;" u2="&#x31;" k="33" />
    <hkern u1="&#x2014;" u2="&#x2f;" k="102" />
    <hkern u1="&#x2014;" u2="&#x2a;" k="10" />
    <hkern u1="&#x2018;" u2="&#x2122;" k="-8" />
    <hkern u1="&#x2018;" u2="&#x20ac;" k="31" />
    <hkern u1="&#x2018;" u2="&#xbf;" k="160" />
    <hkern u1="&#x2018;" u2="&#xae;" k="10" />
    <hkern u1="&#x2018;" u2="&#xa3;" k="102" />
    <hkern u1="&#x2018;" u2="&#xa2;" k="37" />
    <hkern u1="&#x2018;" u2="x" k="16" />
    <hkern u1="&#x2018;" u2="v" k="-10" />
    <hkern u1="&#x2018;" u2="i" k="6" />
    <hkern u1="&#x2018;" u2="X" k="18" />
    <hkern u1="&#x2018;" u2="V" k="-14" />
    <hkern u1="&#x2018;" u2="&#x40;" k="78" />
    <hkern u1="&#x2018;" u2="&#x39;" k="10" />
    <hkern u1="&#x2018;" u2="&#x38;" k="51" />
    <hkern u1="&#x2018;" u2="&#x36;" k="143" />
    <hkern u1="&#x2018;" u2="&#x35;" k="68" />
    <hkern u1="&#x2018;" u2="&#x34;" k="154" />
    <hkern u1="&#x2018;" u2="&#x33;" k="29" />
    <hkern u1="&#x2018;" u2="&#x32;" k="31" />
    <hkern u1="&#x2018;" u2="&#x30;" k="41" />
    <hkern u1="&#x2018;" u2="&#x2f;" k="238" />
    <hkern u1="&#x2018;" u2="&#x2a;" k="12" />
    <hkern u1="&#x2018;" u2="&#x26;" k="84" />
    <hkern u1="&#x2018;" u2="&#x24;" k="20" />
    <hkern u1="&#x2018;" u2="&#x23;" k="29" />
    <hkern u1="&#x2019;" u2="&#x2122;" k="-8" />
    <hkern u1="&#x2019;" u2="&#x20ac;" k="49" />
    <hkern u1="&#x2019;" u2="&#xbf;" k="184" />
    <hkern u1="&#x2019;" u2="&#xae;" k="10" />
    <hkern u1="&#x2019;" u2="&#xa3;" k="115" />
    <hkern u1="&#x2019;" u2="&#xa2;" k="61" />
    <hkern u1="&#x2019;" u2="x" k="35" />
    <hkern u1="&#x2019;" u2="j" k="6" />
    <hkern u1="&#x2019;" u2="X" k="6" />
    <hkern u1="&#x2019;" u2="V" k="-27" />
    <hkern u1="&#x2019;" u2="&#x40;" k="90" />
    <hkern u1="&#x2019;" u2="&#x39;" k="16" />
    <hkern u1="&#x2019;" u2="&#x38;" k="63" />
    <hkern u1="&#x2019;" u2="&#x37;" k="-6" />
    <hkern u1="&#x2019;" u2="&#x36;" k="168" />
    <hkern u1="&#x2019;" u2="&#x35;" k="74" />
    <hkern u1="&#x2019;" u2="&#x34;" k="168" />
    <hkern u1="&#x2019;" u2="&#x33;" k="29" />
    <hkern u1="&#x2019;" u2="&#x32;" k="31" />
    <hkern u1="&#x2019;" u2="&#x31;" k="-31" />
    <hkern u1="&#x2019;" u2="&#x30;" k="43" />
    <hkern u1="&#x2019;" u2="&#x2f;" k="250" />
    <hkern u1="&#x2019;" u2="&#x2a;" k="18" />
    <hkern u1="&#x2019;" u2="&#x26;" k="109" />
    <hkern u1="&#x2019;" u2="&#x24;" k="27" />
    <hkern u1="&#x2019;" u2="&#x23;" k="53" />
    <hkern u1="&#x201a;" u2="&#xa5;" k="51" />
    <hkern u1="&#x201a;" u2="&#xa2;" k="35" />
    <hkern u1="&#x201a;" u2="x" k="43" />
    <hkern u1="&#x201a;" u2="v" k="111" />
    <hkern u1="&#x201a;" u2="j" k="18" />
    <hkern u1="&#x201a;" u2="\" k="184" />
    <hkern u1="&#x201a;" u2="X" k="33" />
    <hkern u1="&#x201a;" u2="V" k="197" />
    <hkern u1="&#x201a;" u2="&#x40;" k="10" />
    <hkern u1="&#x201a;" u2="&#x3f;" k="113" />
    <hkern u1="&#x201a;" u2="&#x39;" k="145" />
    <hkern u1="&#x201a;" u2="&#x38;" k="16" />
    <hkern u1="&#x201a;" u2="&#x37;" k="55" />
    <hkern u1="&#x201a;" u2="&#x36;" k="8" />
    <hkern u1="&#x201a;" u2="&#x35;" k="8" />
    <hkern u1="&#x201a;" u2="&#x34;" k="16" />
    <hkern u1="&#x201a;" u2="&#x31;" k="72" />
    <hkern u1="&#x201a;" u2="&#x30;" k="10" />
    <hkern u1="&#x201a;" u2="&#x2a;" k="215" />
    <hkern u1="&#x201a;" u2="&#x23;" k="8" />
    <hkern u1="&#x201a;" u2="&#x21;" k="12" />
    <hkern u1="&#x201c;" u2="&#x2122;" k="-8" />
    <hkern u1="&#x201c;" u2="&#x20ac;" k="31" />
    <hkern u1="&#x201c;" u2="&#xbf;" k="160" />
    <hkern u1="&#x201c;" u2="&#xae;" k="10" />
    <hkern u1="&#x201c;" u2="&#xa3;" k="102" />
    <hkern u1="&#x201c;" u2="&#xa2;" k="37" />
    <hkern u1="&#x201c;" u2="x" k="16" />
    <hkern u1="&#x201c;" u2="v" k="-10" />
    <hkern u1="&#x201c;" u2="i" k="6" />
    <hkern u1="&#x201c;" u2="X" k="18" />
    <hkern u1="&#x201c;" u2="V" k="-14" />
    <hkern u1="&#x201c;" u2="&#x40;" k="78" />
    <hkern u1="&#x201c;" u2="&#x39;" k="10" />
    <hkern u1="&#x201c;" u2="&#x38;" k="51" />
    <hkern u1="&#x201c;" u2="&#x36;" k="143" />
    <hkern u1="&#x201c;" u2="&#x35;" k="68" />
    <hkern u1="&#x201c;" u2="&#x34;" k="154" />
    <hkern u1="&#x201c;" u2="&#x33;" k="29" />
    <hkern u1="&#x201c;" u2="&#x32;" k="31" />
    <hkern u1="&#x201c;" u2="&#x30;" k="41" />
    <hkern u1="&#x201c;" u2="&#x2f;" k="238" />
    <hkern u1="&#x201c;" u2="&#x2a;" k="12" />
    <hkern u1="&#x201c;" u2="&#x26;" k="84" />
    <hkern u1="&#x201c;" u2="&#x24;" k="20" />
    <hkern u1="&#x201c;" u2="&#x23;" k="29" />
    <hkern u1="&#x201d;" u2="&#x2122;" k="-8" />
    <hkern u1="&#x201d;" u2="&#x20ac;" k="49" />
    <hkern u1="&#x201d;" u2="&#xbf;" k="184" />
    <hkern u1="&#x201d;" u2="&#xae;" k="10" />
    <hkern u1="&#x201d;" u2="&#xa3;" k="115" />
    <hkern u1="&#x201d;" u2="&#xa2;" k="61" />
    <hkern u1="&#x201d;" u2="x" k="35" />
    <hkern u1="&#x201d;" u2="j" k="6" />
    <hkern u1="&#x201d;" u2="X" k="6" />
    <hkern u1="&#x201d;" u2="V" k="-27" />
    <hkern u1="&#x201d;" u2="&#x40;" k="90" />
    <hkern u1="&#x201d;" u2="&#x39;" k="16" />
    <hkern u1="&#x201d;" u2="&#x38;" k="63" />
    <hkern u1="&#x201d;" u2="&#x37;" k="-6" />
    <hkern u1="&#x201d;" u2="&#x36;" k="168" />
    <hkern u1="&#x201d;" u2="&#x35;" k="74" />
    <hkern u1="&#x201d;" u2="&#x34;" k="168" />
    <hkern u1="&#x201d;" u2="&#x33;" k="29" />
    <hkern u1="&#x201d;" u2="&#x32;" k="31" />
    <hkern u1="&#x201d;" u2="&#x31;" k="-31" />
    <hkern u1="&#x201d;" u2="&#x30;" k="43" />
    <hkern u1="&#x201d;" u2="&#x2f;" k="250" />
    <hkern u1="&#x201d;" u2="&#x2a;" k="18" />
    <hkern u1="&#x201d;" u2="&#x26;" k="109" />
    <hkern u1="&#x201d;" u2="&#x24;" k="27" />
    <hkern u1="&#x201d;" u2="&#x23;" k="53" />
    <hkern u1="&#x201e;" u2="&#xa5;" k="51" />
    <hkern u1="&#x201e;" u2="&#xa2;" k="35" />
    <hkern u1="&#x201e;" u2="x" k="43" />
    <hkern u1="&#x201e;" u2="v" k="111" />
    <hkern u1="&#x201e;" u2="j" k="18" />
    <hkern u1="&#x201e;" u2="\" k="184" />
    <hkern u1="&#x201e;" u2="X" k="33" />
    <hkern u1="&#x201e;" u2="V" k="197" />
    <hkern u1="&#x201e;" u2="&#x40;" k="10" />
    <hkern u1="&#x201e;" u2="&#x3f;" k="113" />
    <hkern u1="&#x201e;" u2="&#x39;" k="145" />
    <hkern u1="&#x201e;" u2="&#x38;" k="16" />
    <hkern u1="&#x201e;" u2="&#x37;" k="55" />
    <hkern u1="&#x201e;" u2="&#x36;" k="8" />
    <hkern u1="&#x201e;" u2="&#x35;" k="8" />
    <hkern u1="&#x201e;" u2="&#x34;" k="16" />
    <hkern u1="&#x201e;" u2="&#x31;" k="72" />
    <hkern u1="&#x201e;" u2="&#x30;" k="10" />
    <hkern u1="&#x201e;" u2="&#x2a;" k="215" />
    <hkern u1="&#x201e;" u2="&#x23;" k="8" />
    <hkern u1="&#x201e;" u2="&#x21;" k="12" />
    <hkern u1="&#x2026;" u2="&#x2122;" k="225" />
    <hkern u1="&#x2026;" u2="&#x20ac;" k="53" />
    <hkern u1="&#x2026;" u2="&#xae;" k="143" />
    <hkern u1="&#x2026;" u2="&#xa5;" k="55" />
    <hkern u1="&#x2026;" u2="&#xa2;" k="53" />
    <hkern u1="&#x2026;" u2="x" k="10" />
    <hkern u1="&#x2026;" u2="v" k="143" />
    <hkern u1="&#x2026;" u2="\" k="215" />
    <hkern u1="&#x2026;" u2="X" k="18" />
    <hkern u1="&#x2026;" u2="V" k="276" />
    <hkern u1="&#x2026;" u2="&#x3f;" k="82" />
    <hkern u1="&#x2026;" u2="&#x39;" k="143" />
    <hkern u1="&#x2026;" u2="&#x37;" k="41" />
    <hkern u1="&#x2026;" u2="&#x36;" k="10" />
    <hkern u1="&#x2026;" u2="&#x35;" k="20" />
    <hkern u1="&#x2026;" u2="&#x34;" k="35" />
    <hkern u1="&#x2026;" u2="&#x31;" k="143" />
    <hkern u1="&#x2026;" u2="&#x30;" k="10" />
    <hkern u1="&#x2026;" u2="&#x2a;" k="205" />
    <hkern u1="&#x2026;" u2="&#x26;" k="10" />
    <hkern u1="&#x2030;" u2="&#x2122;" k="137" />
    <hkern u1="&#x2030;" u2="&#xbf;" k="-45" />
    <hkern u1="&#x2030;" u2="&#xae;" k="61" />
    <hkern u1="&#x2030;" u2="\" k="143" />
    <hkern u1="&#x2030;" u2="&#x3f;" k="33" />
    <hkern u1="&#x2030;" u2="&#x39;" k="8" />
    <hkern u1="&#x2030;" u2="&#x38;" k="-12" />
    <hkern u1="&#x2030;" u2="&#x37;" k="-12" />
    <hkern u1="&#x2030;" u2="&#x36;" k="-25" />
    <hkern u1="&#x2030;" u2="&#x35;" k="-25" />
    <hkern u1="&#x2030;" u2="&#x34;" k="-25" />
    <hkern u1="&#x2030;" u2="&#x33;" k="-25" />
    <hkern u1="&#x2030;" u2="&#x32;" k="-25" />
    <hkern u1="&#x2030;" u2="&#x31;" k="12" />
    <hkern u1="&#x2030;" u2="&#x30;" k="-18" />
    <hkern u1="&#x2030;" u2="&#x2f;" k="16" />
    <hkern u1="&#x2030;" u2="&#x2a;" k="68" />
    <hkern u1="&#x2039;" u2="&#x20ac;" k="20" />
    <hkern u1="&#x2039;" u2="&#xa5;" k="37" />
    <hkern u1="&#x2039;" u2="&#xa2;" k="10" />
    <hkern u1="&#x2039;" u2="v" k="-12" />
    <hkern u1="&#x2039;" u2="j" k="10" />
    <hkern u1="&#x2039;" u2="\" k="80" />
    <hkern u1="&#x2039;" u2="X" k="20" />
    <hkern u1="&#x2039;" u2="V" k="80" />
    <hkern u1="&#x2039;" u2="&#x39;" k="6" />
    <hkern u1="&#x2039;" u2="&#x38;" k="12" />
    <hkern u1="&#x2039;" u2="&#x37;" k="16" />
    <hkern u1="&#x2039;" u2="&#x36;" k="31" />
    <hkern u1="&#x2039;" u2="&#x35;" k="10" />
    <hkern u1="&#x2039;" u2="&#x34;" k="16" />
    <hkern u1="&#x2039;" u2="&#x33;" k="10" />
    <hkern u1="&#x2039;" u2="&#x2f;" k="27" />
    <hkern u1="&#x2039;" u2="&#x2a;" k="16" />
    <hkern u1="&#x2039;" u2="&#x26;" k="12" />
    <hkern u1="&#x2039;" u2="&#x23;" k="16" />
    <hkern u1="&#x203a;" u2="&#xa5;" k="27" />
    <hkern u1="&#x203a;" u2="x" k="61" />
    <hkern u1="&#x203a;" u2="v" k="12" />
    <hkern u1="&#x203a;" u2="\" k="115" />
    <hkern u1="&#x203a;" u2="X" k="82" />
    <hkern u1="&#x203a;" u2="V" k="102" />
    <hkern u1="&#x203a;" u2="&#x3f;" k="53" />
    <hkern u1="&#x203a;" u2="&#x39;" k="37" />
    <hkern u1="&#x203a;" u2="&#x37;" k="82" />
    <hkern u1="&#x203a;" u2="&#x36;" k="-12" />
    <hkern u1="&#x203a;" u2="&#x34;" k="-25" />
    <hkern u1="&#x203a;" u2="&#x31;" k="31" />
    <hkern u1="&#x203a;" u2="&#x2f;" k="41" />
    <hkern u1="&#x203a;" u2="&#x2a;" k="78" />
    <hkern u1="&#x203a;" u2="&#x23;" k="-53" />
    <hkern u1="&#x203a;" u2="&#x21;" k="12" />
    <hkern u1="&#x20ac;" u2="&#x203a;" k="12" />
    <hkern u1="&#x20ac;" u2="&#x201e;" k="10" />
    <hkern u1="&#x20ac;" u2="&#x201a;" k="10" />
    <hkern u1="&#x20ac;" u2="&#x2014;" k="10" />
    <hkern u1="&#x20ac;" u2="&#x2013;" k="10" />
    <hkern u1="&#x20ac;" u2="&#xbb;" k="12" />
    <hkern u1="&#x20ac;" u2="&#x7d;" k="23" />
    <hkern u1="&#x20ac;" u2="]" k="23" />
    <hkern u1="&#x20ac;" u2="&#x2d;" k="10" />
    <hkern u1="&#x20ac;" u2="&#x2c;" k="10" />
    <hkern u1="&#x20ac;" u2="&#x29;" k="23" />
    <hkern u1="&#x20ac;" u2="\" k="53" />
    <hkern u1="&#x20ac;" u2="&#x31;" k="-20" />
    <hkern u1="&#x20ac;" u2="&#x2f;" k="63" />
    <hkern u1="&#x2117;" u2="&#xa5;" k="20" />
    <hkern u1="&#x2117;" u2="x" k="20" />
    <hkern u1="&#x2117;" u2="\" k="72" />
    <hkern u1="&#x2117;" u2="X" k="55" />
    <hkern u1="&#x2117;" u2="V" k="51" />
    <hkern u1="&#x2117;" u2="&#x3f;" k="10" />
    <hkern u1="&#x2117;" u2="&#x37;" k="20" />
    <hkern u1="&#x2117;" u2="&#x2f;" k="92" />
    <hkern u1="&#x2122;" g2="Jcircumflex.salt" k="37" />
    <hkern u1="&#x2122;" g2="J.salt" k="37" />
    <hkern u1="&#x2122;" u2="&#xfb02;" k="-37" />
    <hkern u1="&#x2122;" u2="&#xfb01;" k="-37" />
    <hkern u1="&#x2122;" u2="&#x2030;" k="-37" />
    <hkern u1="&#x2122;" u2="&#x2026;" k="53" />
    <hkern u1="&#x2122;" u2="&#x1ef3;" k="-37" />
    <hkern u1="&#x2122;" u2="&#x1ef2;" k="-16" />
    <hkern u1="&#x2122;" u2="&#x1e85;" k="-37" />
    <hkern u1="&#x2122;" u2="&#x1e84;" k="-20" />
    <hkern u1="&#x2122;" u2="&#x1e83;" k="-37" />
    <hkern u1="&#x2122;" u2="&#x1e82;" k="-20" />
    <hkern u1="&#x2122;" u2="&#x1e81;" k="-37" />
    <hkern u1="&#x2122;" u2="&#x1e80;" k="-20" />
    <hkern u1="&#x2122;" u2="&#x1fd;" k="37" />
    <hkern u1="&#x2122;" u2="&#x1fb;" k="37" />
    <hkern u1="&#x2122;" u2="&#x1fa;" k="154" />
    <hkern u1="&#x2122;" u2="&#x17d;" k="8" />
    <hkern u1="&#x2122;" u2="&#x17b;" k="8" />
    <hkern u1="&#x2122;" u2="&#x179;" k="8" />
    <hkern u1="&#x2122;" u2="&#x178;" k="-16" />
    <hkern u1="&#x2122;" u2="&#x177;" k="-37" />
    <hkern u1="&#x2122;" u2="&#x176;" k="-16" />
    <hkern u1="&#x2122;" u2="&#x175;" k="-37" />
    <hkern u1="&#x2122;" u2="&#x174;" k="-20" />
    <hkern u1="&#x2122;" u2="&#x164;" k="-16" />
    <hkern u1="&#x2122;" u2="&#x162;" k="-16" />
    <hkern u1="&#x2122;" u2="&#x134;" k="324" />
    <hkern u1="&#x2122;" u2="&#x105;" k="37" />
    <hkern u1="&#x2122;" u2="&#x104;" k="154" />
    <hkern u1="&#x2122;" u2="&#x103;" k="37" />
    <hkern u1="&#x2122;" u2="&#x102;" k="154" />
    <hkern u1="&#x2122;" u2="&#x101;" k="37" />
    <hkern u1="&#x2122;" u2="&#x100;" k="154" />
    <hkern u1="&#x2122;" u2="&#xff;" k="-37" />
    <hkern u1="&#x2122;" u2="&#xfd;" k="-37" />
    <hkern u1="&#x2122;" u2="&#xe6;" k="37" />
    <hkern u1="&#x2122;" u2="&#xe5;" k="37" />
    <hkern u1="&#x2122;" u2="&#xe4;" k="37" />
    <hkern u1="&#x2122;" u2="&#xe3;" k="37" />
    <hkern u1="&#x2122;" u2="&#xe2;" k="37" />
    <hkern u1="&#x2122;" u2="&#xe1;" k="37" />
    <hkern u1="&#x2122;" u2="&#xe0;" k="37" />
    <hkern u1="&#x2122;" u2="&#xdd;" k="-16" />
    <hkern u1="&#x2122;" u2="&#xc5;" k="154" />
    <hkern u1="&#x2122;" u2="&#xc4;" k="154" />
    <hkern u1="&#x2122;" u2="&#xc3;" k="154" />
    <hkern u1="&#x2122;" u2="&#xc2;" k="154" />
    <hkern u1="&#x2122;" u2="&#xc1;" k="154" />
    <hkern u1="&#x2122;" u2="&#xc0;" k="154" />
    <hkern u1="&#x2122;" u2="y" k="-37" />
    <hkern u1="&#x2122;" u2="w" k="-37" />
    <hkern u1="&#x2122;" u2="f" k="-37" />
    <hkern u1="&#x2122;" u2="a" k="37" />
    <hkern u1="&#x2122;" u2="Z" k="8" />
    <hkern u1="&#x2122;" u2="Y" k="-16" />
    <hkern u1="&#x2122;" u2="W" k="-20" />
    <hkern u1="&#x2122;" u2="T" k="-16" />
    <hkern u1="&#x2122;" u2="J" k="324" />
    <hkern u1="&#x2122;" u2="A" k="154" />
    <hkern u1="&#x2122;" u2="&#x2e;" k="53" />
    <hkern u1="&#x2122;" u2="&#x25;" k="-37" />
    <hkern u1="&#x2122;" u2="x" k="-37" />
    <hkern u1="&#x2122;" u2="v" k="-57" />
    <hkern u1="&#x2122;" u2="V" k="-20" />
    <hkern u1="&#x2122;" u2="&#x36;" k="37" />
    <hkern u1="&#x2122;" u2="&#x35;" k="20" />
    <hkern u1="&#x2122;" u2="&#x34;" k="33" />
    <hkern u1="&#x2122;" u2="&#x31;" k="-37" />
    <hkern u1="&#x2122;" u2="&#x2f;" k="207" />
    <hkern g1="J.salt" u2="&#x2f;" k="37" />
    <hkern g1="IJ.salt" u2="&#x2f;" k="37" />
    <hkern g1="Jcircumflex.salt" u2="&#x2f;" k="37" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="parenright,bracketright,braceright"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="ordfeminine,ordmasculine"
	k="66" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="hyphen,endash,emdash"
	k="82" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="comma,quotesinglbase,quotedblbase"
	k="-20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="J.salt,Jcircumflex.salt"
	k="6" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="napostrophe,quoteright,quotedblright"
	k="246" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="copyright,published"
	k="51" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="quoteleft,quotedblleft"
	k="227" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="guillemotright,guilsinglright"
	k="18" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="J,Jcircumflex"
	k="6" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="51" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="12" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="61" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="T,Tcommaaccent,Tcaron"
	k="154" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="123" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="195" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="14" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="f,fi,fl"
	k="41" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="guillemotleft,guilsinglleft"
	k="43" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="29" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="12" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="37" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="t,tcommaaccent"
	k="70" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="74" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="102" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="parenright,bracketright,braceright"
	k="16" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="ordfeminine,ordmasculine"
	k="-20" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="comma,quotesinglbase,quotedblbase"
	k="45" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="J.salt,Jcircumflex.salt"
	k="4" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="41" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="quoteleft,quotedblleft"
	k="-10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="J,Jcircumflex"
	k="4" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="T,Tcommaaccent,Tcaron"
	k="31" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="16" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="61" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="4" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="period,ellipsis"
	k="47" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,AEacute"
	g2="parenright,bracketright,braceright"
	k="-6" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,AEacute"
	g2="hyphen,endash,emdash"
	k="6" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,AEacute"
	g2="comma,quotesinglbase,quotedblbase"
	k="-14" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,AEacute"
	g2="J.salt,Jcircumflex.salt"
	k="8" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,AEacute"
	g2="napostrophe,quoteright,quotedblright"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,AEacute"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-12" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,AEacute"
	g2="quoteleft,quotedblleft"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,AEacute"
	g2="J,Jcircumflex"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,AEacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="14" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,AEacute"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,AEacute"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="6" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,AEacute"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="parenright,bracketright,braceright"
	k="27" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="hyphen,endash,emdash"
	k="-10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="comma,quotesinglbase,quotedblbase"
	k="41" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="napostrophe,quoteright,quotedblright"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="61" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="quoteleft,quotedblleft"
	k="4" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="guillemotright,guilsinglright"
	k="6" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="T,Tcommaaccent,Tcaron"
	k="33" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="72" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="period,ellipsis"
	k="51" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="J,IJ,Jcircumflex,J.salt,IJ.salt,Jcircumflex.salt"
	g2="comma,quotesinglbase,quotedblbase"
	k="45" />
    <hkern g1="J,IJ,Jcircumflex,J.salt,IJ.salt,Jcircumflex.salt"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="57" />
    <hkern g1="J,IJ,Jcircumflex,J.salt,IJ.salt,Jcircumflex.salt"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="J,IJ,Jcircumflex,J.salt,IJ.salt,Jcircumflex.salt"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="J,IJ,Jcircumflex,J.salt,IJ.salt,Jcircumflex.salt"
	g2="period,ellipsis"
	k="31" />
    <hkern g1="K,Kcommaaccent"
	g2="ordfeminine,ordmasculine"
	k="20" />
    <hkern g1="K,Kcommaaccent"
	g2="hyphen,endash,emdash"
	k="154" />
    <hkern g1="K,Kcommaaccent"
	g2="comma,quotesinglbase,quotedblbase"
	k="-20" />
    <hkern g1="K,Kcommaaccent"
	g2="J.salt,Jcircumflex.salt"
	k="37" />
    <hkern g1="K,Kcommaaccent"
	g2="napostrophe,quoteright,quotedblright"
	k="45" />
    <hkern g1="K,Kcommaaccent"
	g2="copyright,published"
	k="61" />
    <hkern g1="K,Kcommaaccent"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-31" />
    <hkern g1="K,Kcommaaccent"
	g2="quoteleft,quotedblleft"
	k="55" />
    <hkern g1="K,Kcommaaccent"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="K,Kcommaaccent"
	g2="J,Jcircumflex"
	k="37" />
    <hkern g1="K,Kcommaaccent"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="61" />
    <hkern g1="K,Kcommaaccent"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="25" />
    <hkern g1="K,Kcommaaccent"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="K,Kcommaaccent"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="27" />
    <hkern g1="K,Kcommaaccent"
	g2="f,fi,fl"
	k="16" />
    <hkern g1="K,Kcommaaccent"
	g2="guillemotleft,guilsinglleft"
	k="72" />
    <hkern g1="K,Kcommaaccent"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="59" />
    <hkern g1="K,Kcommaaccent"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="31" />
    <hkern g1="K,Kcommaaccent"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="41" />
    <hkern g1="K,Kcommaaccent"
	g2="t,tcommaaccent"
	k="51" />
    <hkern g1="K,Kcommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="66" />
    <hkern g1="K,Kcommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="82" />
    <hkern g1="K,Kcommaaccent"
	g2="colon,semicolon"
	k="16" />
    <hkern g1="K,Kcommaaccent"
	g2="period,ellipsis"
	k="10" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="parenright,bracketright,braceright"
	k="10" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="ordfeminine,ordmasculine"
	k="133" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="hyphen,endash,emdash"
	k="236" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="comma,quotesinglbase,quotedblbase"
	k="-20" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="J.salt,Jcircumflex.salt"
	k="12" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="napostrophe,quoteright,quotedblright"
	k="471" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="copyright,published"
	k="41" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-20" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="quoteleft,quotedblleft"
	k="471" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="guillemotright,guilsinglright"
	k="4" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="J,Jcircumflex"
	k="12" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="61" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="27" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="68" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="T,Tcommaaccent,Tcaron"
	k="184" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="123" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="236" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="f,fi,fl"
	k="41" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="guillemotleft,guilsinglleft"
	k="41" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="25" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="14" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="35" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="t,tcommaaccent"
	k="82" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="82" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="123" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="parenright,bracketright,braceright"
	k="35" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="comma,quotesinglbase,quotedblbase"
	k="53" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="J.salt,Jcircumflex.salt"
	k="4" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="51" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="J,Jcircumflex"
	k="4" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="T,Tcommaaccent,Tcaron"
	k="51" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="31" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="86" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="guillemotleft,guilsinglleft"
	k="-12" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="period,ellipsis"
	k="61" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="6" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="parenright,bracketright,braceright"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="ordfeminine,ordmasculine"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="hyphen,endash,emdash"
	k="33" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="J.salt,Jcircumflex.salt"
	k="16" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="napostrophe,quoteright,quotedblright"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-20" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="J,Jcircumflex"
	k="31" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="4" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="T,Tcommaaccent,Tcaron"
	k="41" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="14" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="70" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="25" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="guillemotleft,guilsinglleft"
	k="20" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="20" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="6" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="period,ellipsis"
	k="39" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="parenright,bracketright,braceright"
	k="37" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="hyphen,endash,emdash"
	k="6" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="comma,quotesinglbase,quotedblbase"
	k="27" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="napostrophe,quoteright,quotedblright"
	k="39" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="45" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="quoteleft,quotedblleft"
	k="25" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="guillemotright,guilsinglright"
	k="16" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="T,Tcommaaccent,Tcaron"
	k="55" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="35" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="72" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="6" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="6" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="t,tcommaaccent"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="6" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="14" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="colon,semicolon"
	k="18" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="hyphen,endash,emdash"
	k="195" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="comma,quotesinglbase,quotedblbase"
	k="229" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="J.salt,Jcircumflex.salt"
	k="57" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="copyright,published"
	k="31" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="154" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="quoteleft,quotedblleft"
	k="6" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="guillemotright,guilsinglright"
	k="74" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="J,Jcircumflex"
	k="174" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="51" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="16" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-29" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-29" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="205" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="f,fi,fl"
	k="39" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="guillemotleft,guilsinglleft"
	k="154" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="174" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="164" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="102" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="t,tcommaaccent"
	k="31" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="82" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="72" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="colon,semicolon"
	k="195" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="period,ellipsis"
	k="274" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="92" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="m,n,p,r,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="123" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="parenright,bracketright,braceright"
	k="10" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="comma,quotesinglbase,quotedblbase"
	k="41" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="61" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="6" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="period,ellipsis"
	k="72" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="parenright,bracketright,braceright"
	k="-6" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="ordfeminine,ordmasculine"
	k="-10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen,endash,emdash"
	k="51" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="comma,quotesinglbase,quotedblbase"
	k="127" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="J.salt,Jcircumflex.salt"
	k="41" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="napostrophe,quoteright,quotedblright"
	k="12" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="copyright,published"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="123" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="quoteleft,quotedblleft"
	k="12" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guillemotright,guilsinglright"
	k="51" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="J,Jcircumflex"
	k="94" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="31" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="T,Tcommaaccent,Tcaron"
	k="-29" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-8" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="102" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="f,fi,fl"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guillemotleft,guilsinglleft"
	k="51" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="72" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="68" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="41" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="t,tcommaaccent"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="colon,semicolon"
	k="82" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="period,ellipsis"
	k="184" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="z,zacute,zdotaccent,zcaron"
	k="41" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="m,n,p,r,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="31" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="parenright,bracketright,braceright"
	k="-10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="ordfeminine,ordmasculine"
	k="29" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="hyphen,endash,emdash"
	k="178" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="comma,quotesinglbase,quotedblbase"
	k="266" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="J.salt,Jcircumflex.salt"
	k="72" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="napostrophe,quoteright,quotedblright"
	k="25" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="copyright,published"
	k="47" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="195" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="quoteleft,quotedblleft"
	k="37" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guillemotright,guilsinglright"
	k="123" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="J,Jcircumflex"
	k="215" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="86" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="57" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="T,Tcommaaccent,Tcaron"
	k="-29" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-8" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-8" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="215" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="f,fi,fl"
	k="57" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guillemotleft,guilsinglleft"
	k="164" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="174" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="154" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="100" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="t,tcommaaccent"
	k="53" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="74" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="66" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="colon,semicolon"
	k="195" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="period,ellipsis"
	k="307" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="23" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="z,zacute,zdotaccent,zcaron"
	k="90" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="m,n,p,r,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="113" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="hyphen,endash,emdash"
	k="51" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="napostrophe,quoteright,quotedblright"
	k="25" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="4" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="quoteleft,quotedblleft"
	k="27" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="J,Jcircumflex"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="43" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="6" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="14" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="f,fi,fl"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="31" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="t,tcommaaccent"
	k="18" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="25" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute"
	g2="parenright,bracketright,braceright"
	k="27" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute"
	g2="napostrophe,quoteright,quotedblright"
	k="47" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute"
	g2="quoteleft,quotedblleft"
	k="37" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute"
	g2="f,fi,fl"
	k="4" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute"
	g2="t,tcommaaccent"
	k="25" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="12" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="29" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="parenright,bracketright,braceright"
	k="27" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="comma,quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="napostrophe,quoteright,quotedblright"
	k="16" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="quoteleft,quotedblleft"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="guillemotright,guilsinglright"
	k="6" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="guillemotleft,guilsinglleft"
	k="-12" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="t,tcommaaccent"
	k="6" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="6" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="period,ellipsis"
	k="8" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="parenright,bracketright,braceright"
	k="27" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="ordfeminine,ordmasculine"
	k="-16" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="hyphen,endash,emdash"
	k="-6" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="comma,quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="napostrophe,quoteright,quotedblright"
	k="37" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="quoteleft,quotedblleft"
	k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="guillemotright,guilsinglright"
	k="6" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="4" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="f,fi,fl"
	k="8" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="guillemotleft,guilsinglleft"
	k="-12" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="t,tcommaaccent"
	k="6" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="23" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="period,ellipsis"
	k="8" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="z,zacute,zdotaccent,zcaron"
	k="6" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="parenright,bracketright,braceright"
	k="16" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="hyphen,endash,emdash"
	k="51" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="comma,quotesinglbase,quotedblbase"
	k="-10" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="copyright,published"
	k="4" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="23" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="f,fi,fl"
	k="8" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="guillemotleft,guilsinglleft"
	k="41" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="41" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="20" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="t,tcommaaccent"
	k="10" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="period,ellipsis"
	k="10" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="parenright,bracketright,braceright"
	k="10" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="napostrophe,quoteright,quotedblright"
	k="20" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="quoteleft,quotedblleft"
	k="8" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="T,Tcommaaccent,Tcaron"
	k="119" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="t,tcommaaccent"
	k="10" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="6" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="8" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="parenright,bracketright,braceright"
	k="37" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="comma,quotesinglbase,quotedblbase"
	k="14" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="napostrophe,quoteright,quotedblright"
	k="29" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="quoteleft,quotedblleft"
	k="16" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="guillemotright,guilsinglright"
	k="6" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="f,fi,fl"
	k="4" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="guillemotleft,guilsinglleft"
	k="-12" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="t,tcommaaccent"
	k="14" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="18" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="18" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="period,ellipsis"
	k="33" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="parenright,bracketright,braceright"
	k="27" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="ordfeminine,ordmasculine"
	k="-20" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="hyphen,endash,emdash"
	k="12" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="comma,quotesinglbase,quotedblbase"
	k="113" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="napostrophe,quoteright,quotedblright"
	k="-35" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="copyright,published"
	k="-16" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="quoteleft,quotedblleft"
	k="-35" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="45" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="guillemotleft,guilsinglleft"
	k="8" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="20" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="6" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="-6" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-27" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-37" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="colon,semicolon"
	k="18" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="period,ellipsis"
	k="143" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="parenright,bracketright,braceright"
	k="37" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="comma,quotesinglbase,quotedblbase"
	k="6" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="napostrophe,quoteright,quotedblright"
	k="37" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="quoteleft,quotedblleft"
	k="12" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="f,fi,fl"
	k="10" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="t,tcommaaccent"
	k="20" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="20" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="25" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="t,tcommaaccent"
	g2="parenright,bracketright,braceright"
	k="10" />
    <hkern g1="t,tcommaaccent"
	g2="ordfeminine,ordmasculine"
	k="-20" />
    <hkern g1="t,tcommaaccent"
	g2="hyphen,endash,emdash"
	k="29" />
    <hkern g1="t,tcommaaccent"
	g2="quoteleft,quotedblleft"
	k="-23" />
    <hkern g1="t,tcommaaccent"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="4" />
    <hkern g1="t,tcommaaccent"
	g2="guillemotleft,guilsinglleft"
	k="10" />
    <hkern g1="t,tcommaaccent"
	g2="t,tcommaaccent"
	k="4" />
    <hkern g1="t,tcommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-16" />
    <hkern g1="t,tcommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-16" />
    <hkern g1="t,tcommaaccent"
	g2="period,ellipsis"
	k="10" />
    <hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	g2="parenright,bracketright,braceright"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="parenright,bracketright,braceright"
	k="16" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="ordfeminine,ordmasculine"
	k="-31" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="hyphen,endash,emdash"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="comma,quotesinglbase,quotedblbase"
	k="78" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="napostrophe,quoteright,quotedblright"
	k="-45" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="quoteleft,quotedblleft"
	k="-51" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="49" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="f,fi,fl"
	k="-10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="guillemotleft,guilsinglleft"
	k="12" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="18" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="colon,semicolon"
	k="25" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="period,ellipsis"
	k="102" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="parenright,bracketright,braceright"
	k="10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="ordfeminine,ordmasculine"
	k="-20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="hyphen,endash,emdash"
	k="16" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="comma,quotesinglbase,quotedblbase"
	k="113" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="napostrophe,quoteright,quotedblright"
	k="-47" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="quoteleft,quotedblleft"
	k="-61" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="guillemotright,guilsinglright"
	k="-6" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="55" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="guillemotleft,guilsinglleft"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="12" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="6" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="t,tcommaaccent"
	k="-10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="colon,semicolon"
	k="31" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="period,ellipsis"
	k="133" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="z,zacute,zdotaccent,zcaron"
	k="8" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="hyphen,endash,emdash"
	k="12" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="comma,quotesinglbase,quotedblbase"
	k="-16" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="quoteleft,quotedblleft"
	k="-10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="8" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="period,ellipsis"
	g2="parenright,bracketright,braceright"
	k="29" />
    <hkern g1="period,ellipsis"
	g2="ordfeminine,ordmasculine"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="hyphen,endash,emdash"
	k="55" />
    <hkern g1="period,ellipsis"
	g2="J.salt,Jcircumflex.salt"
	k="18" />
    <hkern g1="period,ellipsis"
	g2="napostrophe,quoteright,quotedblright"
	k="174" />
    <hkern g1="period,ellipsis"
	g2="quoteleft,quotedblleft"
	k="170" />
    <hkern g1="period,ellipsis"
	g2="J,Jcircumflex"
	k="10" />
    <hkern g1="period,ellipsis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="61" />
    <hkern g1="period,ellipsis"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="72" />
    <hkern g1="period,ellipsis"
	g2="T,Tcommaaccent,Tcaron"
	k="274" />
    <hkern g1="period,ellipsis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="184" />
    <hkern g1="period,ellipsis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="307" />
    <hkern g1="period,ellipsis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="4" />
    <hkern g1="period,ellipsis"
	g2="f,fi,fl"
	k="63" />
    <hkern g1="period,ellipsis"
	g2="guillemotleft,guilsinglleft"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="33" />
    <hkern g1="period,ellipsis"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="25" />
    <hkern g1="period,ellipsis"
	g2="t,tcommaaccent"
	k="63" />
    <hkern g1="period,ellipsis"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="102" />
    <hkern g1="period,ellipsis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="154" />
    <hkern g1="period,ellipsis"
	g2="parenleft,bracketleft,braceleft"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="percent,perthousand"
	k="162" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="parenright,bracketright,braceright"
	k="25" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="hyphen,endash,emdash"
	k="29" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="napostrophe,quoteright,quotedblright"
	k="160" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="quoteleft,quotedblleft"
	k="131" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="35" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="4" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="41" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="T,Tcommaaccent,Tcaron"
	k="242" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="143" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="266" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="f,fi,fl"
	k="63" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="guillemotleft,guilsinglleft"
	k="12" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="23" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="8" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="16" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="t,tcommaaccent"
	k="63" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="92" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="131" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="parenleft,bracketleft,braceleft"
	k="20" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="percent,perthousand"
	k="143" />
    <hkern g1="colon,semicolon"
	g2="parenright,bracketright,braceright"
	k="53" />
    <hkern g1="colon,semicolon"
	g2="J.salt,Jcircumflex.salt"
	k="10" />
    <hkern g1="colon,semicolon"
	g2="J,Jcircumflex"
	k="10" />
    <hkern g1="colon,semicolon"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="10" />
    <hkern g1="colon,semicolon"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="10" />
    <hkern g1="colon,semicolon"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="colon,semicolon"
	g2="T,Tcommaaccent,Tcaron"
	k="195" />
    <hkern g1="colon,semicolon"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="82" />
    <hkern g1="colon,semicolon"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="195" />
    <hkern g1="colon,semicolon"
	g2="f,fi,fl"
	k="8" />
    <hkern g1="colon,semicolon"
	g2="t,tcommaaccent"
	k="4" />
    <hkern g1="colon,semicolon"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="25" />
    <hkern g1="colon,semicolon"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="29" />
    <hkern g1="colon,semicolon"
	g2="parenleft,bracketleft,braceleft"
	k="10" />
    <hkern g1="hyphen,endash,emdash"
	g2="parenright,bracketright,braceright"
	k="102" />
    <hkern g1="hyphen,endash,emdash"
	g2="J.salt,Jcircumflex.salt"
	k="16" />
    <hkern g1="hyphen,endash,emdash"
	g2="napostrophe,quoteright,quotedblright"
	k="8" />
    <hkern g1="hyphen,endash,emdash"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="82" />
    <hkern g1="hyphen,endash,emdash"
	g2="quoteleft,quotedblleft"
	k="8" />
    <hkern g1="hyphen,endash,emdash"
	g2="guillemotright,guilsinglright"
	k="53" />
    <hkern g1="hyphen,endash,emdash"
	g2="J,Jcircumflex"
	k="25" />
    <hkern g1="hyphen,endash,emdash"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="6" />
    <hkern g1="hyphen,endash,emdash"
	g2="T,Tcommaaccent,Tcaron"
	k="195" />
    <hkern g1="hyphen,endash,emdash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="51" />
    <hkern g1="hyphen,endash,emdash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="178" />
    <hkern g1="hyphen,endash,emdash"
	g2="f,fi,fl"
	k="16" />
    <hkern g1="hyphen,endash,emdash"
	g2="guillemotleft,guilsinglleft"
	k="-20" />
    <hkern g1="hyphen,endash,emdash"
	g2="t,tcommaaccent"
	k="20" />
    <hkern g1="hyphen,endash,emdash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="hyphen,endash,emdash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="23" />
    <hkern g1="hyphen,endash,emdash"
	g2="period,ellipsis"
	k="55" />
    <hkern g1="hyphen,endash,emdash"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="14" />
    <hkern g1="hyphen,endash,emdash"
	g2="z,zacute,zdotaccent,zcaron"
	k="12" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="parenright,bracketright,braceright"
	k="37" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="hyphen,endash,emdash"
	k="53" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="J.salt,Jcircumflex.salt"
	k="12" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="18" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="J,Jcircumflex"
	k="18" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="T,Tcommaaccent,Tcaron"
	k="74" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="51" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="123" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="12" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="6" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-6" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="parenleft,bracketleft,braceleft"
	k="18" />
    <hkern g1="guillemotright,guilsinglright"
	g2="parenright,bracketright,braceright"
	k="78" />
    <hkern g1="guillemotright,guilsinglright"
	g2="hyphen,endash,emdash"
	k="-20" />
    <hkern g1="guillemotright,guilsinglright"
	g2="comma,quotesinglbase,quotedblbase"
	k="20" />
    <hkern g1="guillemotright,guilsinglright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="43" />
    <hkern g1="guillemotright,guilsinglright"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="-12" />
    <hkern g1="guillemotright,guilsinglright"
	g2="T,Tcommaaccent,Tcaron"
	k="154" />
    <hkern g1="guillemotright,guilsinglright"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="51" />
    <hkern g1="guillemotright,guilsinglright"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="164" />
    <hkern g1="guillemotright,guilsinglright"
	g2="f,fi,fl"
	k="8" />
    <hkern g1="guillemotright,guilsinglright"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="-12" />
    <hkern g1="guillemotright,guilsinglright"
	g2="t,tcommaaccent"
	k="4" />
    <hkern g1="guillemotright,guilsinglright"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="12" />
    <hkern g1="guillemotright,guilsinglright"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="16" />
    <hkern g1="guillemotright,guilsinglright"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="guillemotright,guilsinglright"
	g2="percent,perthousand"
	k="16" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="ordfeminine,ordmasculine"
	k="16" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="hyphen,endash,emdash"
	k="102" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="comma,quotesinglbase,quotedblbase"
	k="-41" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="J.salt,Jcircumflex.salt"
	k="53" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="napostrophe,quoteright,quotedblright"
	k="27" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="copyright,published"
	k="37" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="quoteleft,quotedblleft"
	k="33" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="guillemotright,guilsinglright"
	k="33" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="J,Jcircumflex"
	k="53" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="35" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="37" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-6" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-10" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="63" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="f,fi,fl"
	k="25" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="guillemotleft,guilsinglleft"
	k="78" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="37" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="37" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="27" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="t,tcommaaccent"
	k="20" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="16" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="16" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="colon,semicolon"
	k="53" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="period,ellipsis"
	k="29" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="m,n,p,r,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="20" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="percent,perthousand"
	k="20" />
    <hkern g1="parenright,bracketright,braceright"
	g2="comma,quotesinglbase,quotedblbase"
	k="20" />
    <hkern g1="parenright,bracketright,braceright"
	g2="napostrophe,quoteright,quotedblright"
	k="14" />
    <hkern g1="parenright,bracketright,braceright"
	g2="quoteleft,quotedblleft"
	k="4" />
    <hkern g1="parenright,bracketright,braceright"
	g2="guillemotright,guilsinglright"
	k="37" />
    <hkern g1="parenright,bracketright,braceright"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="parenright,bracketright,braceright"
	g2="period,ellipsis"
	k="31" />
    <hkern g1="percent,perthousand"
	g2="parenright,bracketright,braceright"
	k="27" />
    <hkern g1="percent,perthousand"
	g2="napostrophe,quoteright,quotedblright"
	k="111" />
    <hkern g1="percent,perthousand"
	g2="quoteleft,quotedblleft"
	k="104" />
    <hkern g1="percent,perthousand"
	g2="guillemotleft,guilsinglleft"
	k="-20" />
    <hkern g1="copyright,published"
	g2="parenright,bracketright,braceright"
	k="37" />
    <hkern g1="copyright,published"
	g2="J.salt,Jcircumflex.salt"
	k="4" />
    <hkern g1="copyright,published"
	g2="napostrophe,quoteright,quotedblright"
	k="12" />
    <hkern g1="copyright,published"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="51" />
    <hkern g1="copyright,published"
	g2="J,Jcircumflex"
	k="10" />
    <hkern g1="copyright,published"
	g2="T,Tcommaaccent,Tcaron"
	k="31" />
    <hkern g1="copyright,published"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="copyright,published"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="47" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="parenright,bracketright,braceright"
	k="16" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="J.salt,Jcircumflex.salt"
	k="8" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="51" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="J,Jcircumflex"
	k="25" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-10" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="25" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-31" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-20" />
    <hkern g1="quoteleft,quotedblleft"
	g2="hyphen,endash,emdash"
	k="18" />
    <hkern g1="quoteleft,quotedblleft"
	g2="comma,quotesinglbase,quotedblbase"
	k="178" />
    <hkern g1="quoteleft,quotedblleft"
	g2="J.salt,Jcircumflex.salt"
	k="98" />
    <hkern g1="quoteleft,quotedblleft"
	g2="copyright,published"
	k="31" />
    <hkern g1="quoteleft,quotedblleft"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="256" />
    <hkern g1="quoteleft,quotedblleft"
	g2="J,Jcircumflex"
	k="440" />
    <hkern g1="quoteleft,quotedblleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="37" />
    <hkern g1="quoteleft,quotedblleft"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="31" />
    <hkern g1="quoteleft,quotedblleft"
	g2="T,Tcommaaccent,Tcaron"
	k="-20" />
    <hkern g1="quoteleft,quotedblleft"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-10" />
    <hkern g1="quoteleft,quotedblleft"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-20" />
    <hkern g1="quoteleft,quotedblleft"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="137" />
    <hkern g1="quoteleft,quotedblleft"
	g2="f,fi,fl"
	k="20" />
    <hkern g1="quoteleft,quotedblleft"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="88" />
    <hkern g1="quoteleft,quotedblleft"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="82" />
    <hkern g1="quoteleft,quotedblleft"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="31" />
    <hkern g1="quoteleft,quotedblleft"
	g2="t,tcommaaccent"
	k="10" />
    <hkern g1="quoteleft,quotedblleft"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-10" />
    <hkern g1="quoteleft,quotedblleft"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-10" />
    <hkern g1="quoteleft,quotedblleft"
	g2="colon,semicolon"
	k="41" />
    <hkern g1="quoteleft,quotedblleft"
	g2="period,ellipsis"
	k="223" />
    <hkern g1="quoteleft,quotedblleft"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="23" />
    <hkern g1="quoteleft,quotedblleft"
	g2="z,zacute,zdotaccent,zcaron"
	k="20" />
    <hkern g1="quoteleft,quotedblleft"
	g2="m,n,p,r,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="66" />
    <hkern g1="quoteleft,quotedblleft"
	g2="parenleft,bracketleft,braceleft"
	k="18" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="hyphen,endash,emdash"
	k="49" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="comma,quotesinglbase,quotedblbase"
	k="154" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="J.salt,Jcircumflex.salt"
	k="86" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="copyright,published"
	k="49" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="262" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="J,Jcircumflex"
	k="471" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="39" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="39" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="T,Tcommaaccent,Tcaron"
	k="-27" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-16" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-45" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="168" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="f,fi,fl"
	k="39" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="113" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="102" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="49" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="t,tcommaaccent"
	k="23" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="8" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="colon,semicolon"
	k="66" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="period,ellipsis"
	k="252" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="23" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="z,zacute,zdotaccent,zcaron"
	k="39" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="m,n,p,r,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="82" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="parenleft,bracketleft,braceleft"
	k="20" />
  </font>
</defs></svg>
