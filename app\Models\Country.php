<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use Illuminate\Database\Eloquent\Model;

class Country extends BaseModel
{
    protected $fillable = [
        'name','iso','iso3','num_code','phone_code',
    ];

    public function getDisplayNameAttribute() {
        return $this->name." (".$this->iso.")";
    }

    public static function getCountryFromCountryCode($country_code) {
        return self::where('iso', $country_code)->first();
    }

    public static function defaultQuery() {
        return self::query()->orderBy("name");
    }
}
