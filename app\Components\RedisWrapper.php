<?php

namespace App\Components;

use App\Base\Provider\BaseProvider;
use App\Constants\CommonConstants;
use App\Models\Log;
use App\Models\User;
use Illuminate\Support\Facades\Http;

class RedisWrapper
{
    const API_URL = "http://144.126.202.198/api/";
    public static function saveRecord($key,$data,$callbackUrl=null,$isBackground=false) {
        try {
            $postData = [
                'key' => $key,
                'value' => $data,
            ];
            if($callbackUrl) {
                $postData['callback_url']=$callbackUrl;
                if($isBackground) {
                    $postData['is_background']=1;
                }
            }

            file_put_contents('redis_log_'.$key.'_'.time().'.txt',json_encode($postData));
            $url = self::API_URL . "redis/store";
            $response = Http::post($url, $postData);
            $result = $response->body();
            if($result!="") {
                $decode=json_decode($result,true);
                if(array_key_exists('message',$decode)) {
                    if(strtolower(trim($decode['message']))==trim(strtolower("Stored in Redis"))) {
                        return $decode;
                    }
                }
            }
        } catch (\Exception $e) {

        }
        return null;
    }
    public static function fetchRecord($key) {
        try {
            $url = self::API_URL . "redis/" . $key;
            $response = Http::get($url);
            $data=$response->body();
            if($data!="") {
                $decodeData=json_decode($data,true);
                if(is_array($decodeData) && array_key_exists('value',$decodeData)) {
                    return $decodeData['value'];
                }
            }
        } catch (\Exception $e) {

        }
        return null;
    }
    public static function deleteRecord($key) {
        try {
            $url = self::API_URL . "redis/" . $key;
            $response = Http::delete($url);
            $result=$response->body();
            if($result!="") {
                $decode=json_decode($result,true);
                if(array_key_exists('message',$decode)) {
                    if(strtolower(trim($decode['message']))==trim(strtolower("Deleted from Redis"))) {
                        return true;
                    }
                }
            }
        } catch (\Exception $e) {

        }
        return null;
    }
}
