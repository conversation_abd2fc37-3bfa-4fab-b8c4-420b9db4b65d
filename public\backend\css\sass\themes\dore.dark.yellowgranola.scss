$separator-color-light: #313131;
$separator-color: #424242;
$background-color: #1f1e1c;
$foreground-color: #242321;
$input-background: #232223;

$dark-btn-background: #8d8d8d;
$light-btn-background: #e4e4e4;
$button-text-color: #d0d0d0;

$theme-color-1: #8a722c;
$theme-color-2: #a88048;
$theme-color-3: #ac9c57;
$theme-color-4: #665218;
$theme-color-5: #7c715f;
$theme-color-6: #8d7a24;

$primary-color: #8f8f8f;
$secondary-color: #707070;
$muted-color: #696969;

$gradient-color-1 : #a08a47;
$gradient-color-2 : #7a6525;
$gradient-color-3 : #8b773a;

$lp-bg-color-1 : #2b2411;
$lp-bg-color-2 : #52451e;
$lp-bg-color-3 : #685828;
$lp-bg-color-4 : #7a672d;

$shadow-offsets-top : 1 3 10 14 19;
$shadow-blurs-top: 2 6 10 14 19;
$shadow-opacities-top: 0.1 0.3 0.6 0.7 0.8;

$shadow-offsets-bottom : 1 3 6 10 15;
$shadow-blurs-bottom: 3 6 6 5 6;
$shadow-opacities-bottom: 0.1 0.3 0.6 0.7 0.8;

$logo-path: "../logos/white.svg";
$logo-path-mobile: "../logos/mobile.svg";

$lp-logo-path-pinned: "../logos/white-full.svg";
$lp-logo-path: "../logos/white-full.svg";

@import "../_mixins.scss";
@import "../_dore.style.scss";
