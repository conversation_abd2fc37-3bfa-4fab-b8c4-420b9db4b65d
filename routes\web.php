<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;

Route::get('/', [App\Http\Controllers\SiteController::class, 'index'])->name('home');
Route::get('/testing', [App\Http\Controllers\TestingController::class, 'index'])->name('testing');

Route::any('/api/place-order', [App\Http\Controllers\ApiController::class, 'placeOrder'])->name('api.placeOrder');
Route::any('/api/get-order', [App\Http\Controllers\ApiController::class, 'getOrder'])->name('api.getOrder');
Route::any('/api/place-bulk-order', [App\Http\Controllers\ApiController::class, 'placeBulkOrder'])->name('api.placeBulkOrder');
Route::any('/api/get-bulk-order', [App\Http\Controllers\ApiController::class, 'getBulkOrder'])->name('api.getBulkOrder');
Route::any('/api/get-services', [App\Http\Controllers\ApiController::class, 'services'])->name('api.services');
Route::any('/api/get-categories', [App\Http\Controllers\ApiController::class, 'categories'])->name('api.categories');
Route::any('/api/get-balance', [App\Http\Controllers\ApiController::class, 'balance'])->name('api.balance');
Route::any('/api/place-batch-order', [App\Http\Controllers\ApiController::class, 'placeBatchOrder'])->name('api.placeBatchOrder');

Route::get('/order-api/place-order/{orderKey}', [App\Http\Controllers\OrderApiController::class, 'placeOrder'])->name('order-api.placeOrder');
Route::any('/order-api/place-batch-order/{batchKey}', [App\Http\Controllers\OrderApiController::class, 'placeBatchOrder'])->name('order-api.placeBatchOrder');
Route::any('/order-api/test-batch-order/{batchKey}', [App\Http\Controllers\OrderApiController::class, 'testBatchOrder'])->name('order-api.testBatchOrder');

Route::middleware([\App\Http\Middleware\AdminIsValid::class])->group(function () {
    Route::get('/admin/dashboard', [App\Http\Controllers\AdminController::class, 'dashboard'])->name('admin.dashboard');

    Route::resource('user', \App\Http\Controllers\UserController::class);
    Route::get('/user/list/data-table', [\App\Http\Controllers\UserController::class, 'dataTable'])->name('user.dataTable');

    Route::resource('operating-system', \App\Http\Controllers\OperatingSystemController::class);
    Route::get('/operating-system/list/data-table', [\App\Http\Controllers\OperatingSystemController::class, 'dataTable'])->name('operating-system.dataTable');

    Route::resource('transaction', \App\Http\Controllers\TransactionController::class);
    Route::get('/transaction/list/data-table', [\App\Http\Controllers\TransactionController::class, 'dataTable'])->name('transaction.dataTable');

    Route::resource('provider', \App\Http\Controllers\ProviderController::class);
    Route::get('/provider/list/data-table', [\App\Http\Controllers\ProviderController::class, 'dataTable'])->name('provider.dataTable');

    Route::resource('provider-service', \App\Http\Controllers\ProviderServiceController::class);
    Route::get('/provider-service/list/data-table', [\App\Http\Controllers\ProviderServiceController::class, 'dataTable'])->name('provider-service.dataTable');

    Route::resource('service', \App\Http\Controllers\ServiceController::class);
    Route::get('/service/list/data-table', [\App\Http\Controllers\ServiceController::class, 'dataTable'])->name('service.dataTable');

    Route::resource('service-plan-price', \App\Http\Controllers\ServicePlanPriceController::class);
    Route::get('/service-plan-price/list/data-table', [\App\Http\Controllers\ServicePlanPriceController::class, 'dataTable'])->name('service-plan-price.dataTable');

    Route::resource('service-user-price', \App\Http\Controllers\ServiceUserPriceController::class);
    Route::get('/service-user-price/list/data-table', [\App\Http\Controllers\ServiceUserPriceController::class, 'dataTable'])->name('service-user-price.dataTable');

    Route::resource('category', \App\Http\Controllers\CategoryController::class);
    Route::get('/category/list/data-table', [\App\Http\Controllers\CategoryController::class, 'dataTable'])->name('category.dataTable');

    Route::resource('admin-adjustment', \App\Http\Controllers\AdminAdjustmentController::class);
    Route::get('/admin-adjustment/list/data-table', [\App\Http\Controllers\AdminAdjustmentController::class, 'dataTable'])->name('admin-adjustment.dataTable');

    Route::resource('user-login-history', \App\Http\Controllers\UserLoginHistoryController::class);
    Route::get('/user-login-history/list/data-table', [\App\Http\Controllers\UserLoginHistoryController::class, 'dataTable'])->name('user-login-history.dataTable');

    Route::resource('user-invoice', \App\Http\Controllers\UserInvoiceController::class);
    Route::get('/user-invoice/list/data-table', [\App\Http\Controllers\UserInvoiceController::class, 'dataTable'])->name('user-invoice.dataTable');

    Route::resource('user-order', \App\Http\Controllers\UserOrderController::class);
    Route::get('/user-order/list/data-table', [\App\Http\Controllers\UserOrderController::class, 'dataTable'])->name('user-order.dataTable');

    Route::resource('user-order-log', \App\Http\Controllers\UserOrderLogController::class);
    Route::get('/user-order-log/list/data-table', [\App\Http\Controllers\UserOrderLogController::class, 'dataTable'])->name('user-order-log.dataTable');
    Route::get('/user-order-log/top/user', [\App\Http\Controllers\UserOrderLogController::class, 'topUser'])->name('user-order-log.topUser');
    Route::get('/user-order-log/top/service', [\App\Http\Controllers\UserOrderLogController::class, 'topService'])->name('user-order-log.topService');

    Route::resource('plan', \App\Http\Controllers\PlanController::class);
    Route::get('/plan/list/data-table', [\App\Http\Controllers\PlanController::class, 'dataTable'])->name('plan.dataTable');

    Route::post('/ajax/switch-update', [\App\Http\Controllers\AdminController::class, 'switchUpdate'])->name('admin.switchUpdate');
    Route::post('/ajax/single-value-update', [\App\Http\Controllers\AdminController::class, 'singleValueUpdate'])->name('admin.singleValueUpdate');
    Route::post('/ajax/fetch-user-names', [App\Http\Controllers\AdminController::class, 'fetchUserNames'])->name('admin.fetchUserNames');
    Route::post('/ajax/fetch-provider-services', [App\Http\Controllers\AdminController::class, 'fetchProviderServices'])->name('admin.fetchProviderServices');



    Route::get('/imei-stats', [App\Http\Controllers\AdminController::class, 'imeiStats'])->name('imeiStats');
    Route::get('/daily-stats', [App\Http\Controllers\AdminController::class, 'dailyStats'])->name('dailyStats');
    Route::get('/top-services', [App\Http\Controllers\AdminController::class, 'topServices'])->name('topServices');
    Route::get('/top-users', [App\Http\Controllers\AdminController::class, 'topUsers'])->name('topUsers');
    Route::get('/invalid-prices', [App\Http\Controllers\AdminController::class, 'invalidPrices'])->name('invalidPrices');
    Route::get('/system-transactions', [App\Http\Controllers\AdminController::class, 'systemTransactions'])->name('systemTransactions');
    Route::get('/custom-prices', [App\Http\Controllers\AdminController::class, 'customPrices'])->name('customPrices');

    Route::get('/db-stats', [App\Http\Controllers\AdminController::class, 'dbStats'])->name('dbStats');
});

Route::middleware(['auth','verified'])->group(function () {
    Route::get('/dashboard', [App\Http\Controllers\AppController::class, 'dashboard'])->name('dashboard');


    Route::get('/account', [ProfileController::class, 'edit'])->name('account');
    Route::patch('/account', [ProfileController::class, 'update'])->name('account.update');

    Route::get('/contact', [App\Http\Controllers\AppController::class, 'contact'])->name('contact');
});

require __DIR__.'/auth.php';

//Route::get('/login', [App\Http\Controllers\SiteController::class, 'login'])->name('login');
//Route::get('/register', [App\Http\Controllers\SiteController::class, 'register'])->name('register');
