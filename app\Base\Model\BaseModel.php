<?php

namespace App\Base\Model;

use App\Base\Traits\BaseModelTrait;
use App\Components\Helper;
use App\Constants\CommonConstants;
use App\Models\Log;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class BaseModel extends Model
{
    use BaseModelTrait;

    public $timestamps = false;

    public function getUniqueColumns() {
        return [];
    }

    public static function generateReferenceNumber($limit=8,$retry=false)
    {
        if(!$retry) {
            $number = date("ymd").rand(10, 99) . rand(10, 99);
        } else {
            $number = date("ymd").rand(1000, 9999) . rand(1000, 9999);
        }
        if($limit!=8) {
            if(!$retry) {
                $number = date("ymd").rand(100, 999) . rand(100, 999);
            } else {
                $number = rand(1000, 9999) . rand(1000, 9999) . rand(10, 99);
            }
        }
        $check=self::query()->where('ref_no','=',$number)->first();
        if($check!=null) {
            return self::generateReferenceNumber($limit,true);
        }
        return $number;
    }

    public static function buildFilterQuery(&$query, $filters, $userCondition = false, $userConditionColumn = 'user_id')
    {
        if ($userCondition) {
            $identity = \auth()->user();
            if ($identity) {
                if (!$identity->isAdmin() && !$identity->isSupport()) {
                    $query->where($userConditionColumn, $identity->id);
                }
            }
        }
        $searchFilters = request()->input('search_query');
        $searchFilters = Helper::cleanArray($searchFilters);
        $filters = Helper::cleanArray($filters);
        if (is_array($searchFilters) && count($searchFilters) > 0) {
            if (is_array($filters) && count($filters) > 0) {
                foreach ($filters as $searchKey => $column) {
                    if (is_numeric($searchKey)) {
                        $searchKey = $column;
                    }
                    if (array_key_exists($searchKey, $searchFilters)) {
                        $searchString = trim($searchFilters[$searchKey]);
                        if ($searchString != null && $searchString!='') {
                            if (is_string($column)) {
                                //$query->where($column, 'LIKE', '%' . $searchString . '%');
                                $query->where($column, $searchString);
                            } elseif (is_array($column)) {
                                if($searchKey=="date-range") {
                                    try {
                                        $dateRange = explode(":", trim($searchString));
                                        if (is_array($dateRange) && count($dateRange) === 2) {
                                            $query->whereBetween($column[0], [$dateRange[0] . " 00:00:00", $dateRange[1] . " 23:59:59"]);
                                        }
                                    } catch (\Exception $e) {

                                    }
                                } else if($searchKey=="check_is_invested") {
                                    try {
                                        if($searchString<=0) {
                                            $query->where($column[0],'<=',0);
                                        } else {
                                            $query->where($column[0],'>',0);
                                        }
                                    } catch (\Exception $e) {

                                    }
                                } else if($searchKey=="amt") {
                                    try {
                                        $query->where($column[0],'>=',$searchString);
                                    } catch (\Exception $e) {

                                    }
                                } else {
                                    $columns = $column;
                                    $query->where(function ($q) use ($columns, $searchString) {
                                        foreach ($columns as $column) {
                                            $q->orWhere($column, 'LIKE', '%' . $searchString . '%');
                                        }
                                    })->get();
                                }
                            }
                        }
                    }
                }
            }
        }
        $columns = request()->input('columns');
        $orders = request()->input('order');
        if (is_array($orders) && count($orders) > 0) {
            foreach ($orders as $order) {
                $columnKey = $order['column'];
                if (is_array($columns) && array_key_exists($columnKey, $columns)) {
                    $orderDir = $order['dir'];
                    $orderColumn = $columns[$columnKey]['name'];
                    $query->orderBy($orderColumn, $orderDir);
                }
            }
        }
    }

    public static function getTableName()
    {
        $calledClass = get_called_class();
        $class = new $calledClass();
        return $class->getTable();
    }

}
