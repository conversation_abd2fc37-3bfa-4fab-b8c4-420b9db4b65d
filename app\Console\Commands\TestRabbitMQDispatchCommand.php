<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\ProcessImeiCheckJob;
use App\Jobs\SendUserNotificationJob;
use Illuminate\Support\Facades\Queue;

class TestRabbitMQDispatchCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'rabbitmq:test-dispatch';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test RabbitMQ job dispatch and verify queue contents';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing RabbitMQ job dispatch...');

        // Check initial queue sizes
        $this->info('Initial queue sizes:');
        $this->showQueueSizes();

        // Get a test user
        $user = \App\Models\User::first();
        if (!$user) {
            $this->error('No users found. Please create a user first.');
            return 1;
        }

        $this->info("Using test user: {$user->name} (ID: {$user->id})");

        try {
            // Dispatch IMEI check job
            $testImei = '35' . str_pad(rand(0, 999999999999999), 13, '0', STR_PAD_LEFT);
            $orderId = rand(1, 99999);
            
            $this->info("Dispatching IMEI check job...");
            $this->info("  Order ID: {$orderId}");
            $this->info("  IMEI: {$testImei}");
            $this->info("  User ID: {$user->id}");

            ProcessImeiCheckJob::dispatch($orderId, $testImei, 1, $user->id)
                ->onQueue('imei_checks');

            $this->info('✓ IMEI check job dispatched');

            // Dispatch notification job
            $this->info("Dispatching notification job...");
            
            SendUserNotificationJob::dispatch(
                $user->id,
                'welcome',
                ['message' => 'Test notification from rabbitmq:test-dispatch command']
            )->onQueue('notifications');

            $this->info('✓ Notification job dispatched');

            // Wait a moment for jobs to be queued
            sleep(1);

            // Check queue sizes after dispatch
            $this->info('');
            $this->info('Queue sizes after dispatch:');
            $this->showQueueSizes();

            $this->info('');
            $this->info('🎉 Test completed successfully!');
            $this->info('');
            $this->info('To process these jobs, run:');
            $this->info('  php artisan rabbitmq:work --queue=imei_checks');
            $this->info('  php artisan rabbitmq:work --queue=notifications');

        } catch (\Exception $e) {
            $this->error('Error dispatching jobs: ' . $e->getMessage());
            $this->error('Stack trace: ' . $e->getTraceAsString());
            return 1;
        }

        return 0;
    }

    private function showQueueSizes()
    {
        $queues = ['imei_checks', 'notifications', 'default', 'queue'];
        
        foreach ($queues as $queueName) {
            try {
                $size = Queue::connection('rabbitmq')->size($queueName);
                $this->info("  {$queueName}: {$size} messages");
            } catch (\Exception $e) {
                $this->error("  {$queueName}: Error - " . $e->getMessage());
            }
        }
    }
}
