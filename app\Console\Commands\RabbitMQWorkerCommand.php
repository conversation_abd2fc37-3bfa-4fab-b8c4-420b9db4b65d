<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Queue\QueueManager;
use Illuminate\Support\Facades\Log;
use App\Queue\RabbitMQQueue;

class RabbitMQWorkerCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'rabbitmq:work 
                            {--queue=default : The queue to process}
                            {--timeout=60 : The timeout for each job}
                            {--sleep=3 : Sleep time when no jobs are available}
                            {--max-jobs=0 : Maximum number of jobs to process (0 = unlimited)}
                            {--max-time=0 : Maximum time to run (0 = unlimited)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process jobs from RabbitMQ queue';

    protected $shouldQuit = false;
    protected $jobsProcessed = 0;
    protected $startTime;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->startTime = time();
        $queue = $this->option('queue');
        $timeout = (int) $this->option('timeout');
        $sleep = (int) $this->option('sleep');
        $maxJobs = (int) $this->option('max-jobs');
        $maxTime = (int) $this->option('max-time');

        $this->info("Starting RabbitMQ worker for queue: {$queue}");
        $this->info("Configuration: timeout={$timeout}s, sleep={$sleep}s, max-jobs={$maxJobs}, max-time={$maxTime}s");

        // Set up signal handlers for graceful shutdown
        $this->listenForSignals();

        /** @var QueueManager $queueManager */
        $queueManager = app('queue');
        
        try {
            /** @var RabbitMQQueue $connection */
            $connection = $queueManager->connection('rabbitmq');
            
            $this->info("Connected to RabbitMQ successfully");

            while (!$this->shouldQuit) {
                // Check if we should stop based on limits
                if ($this->shouldStop($maxJobs, $maxTime)) {
                    break;
                }

                try {
                    // Get next job from queue
                    $job = $connection->pop($queue);

                    if ($job) {
                        $this->info("Processing job: {$job->getName()}");
                        $startTime = microtime(true);

                        try {
                            // Process the job
                            $job->fire();
                            
                            $processingTime = round((microtime(true) - $startTime) * 1000, 2);
                            $this->info("Job completed successfully in {$processingTime}ms");
                            
                            $this->jobsProcessed++;

                        } catch (\Exception $e) {
                            $processingTime = round((microtime(true) - $startTime) * 1000, 2);
                            $this->error("Job failed after {$processingTime}ms: " . $e->getMessage());
                            
                            Log::error('RabbitMQ job failed', [
                                'job' => $job->getName(),
                                'queue' => $queue,
                                'error' => $e->getMessage(),
                                'trace' => $e->getTraceAsString()
                            ]);

                            // Handle job failure
                            $job->fail($e);
                        }

                    } else {
                        // No jobs available, sleep
                        $this->comment("No jobs available, sleeping for {$sleep} seconds...");
                        sleep($sleep);
                    }

                } catch (\Exception $e) {
                    $this->error("Error processing queue: " . $e->getMessage());
                    Log::error('RabbitMQ worker error', [
                        'queue' => $queue,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    
                    // Sleep before retrying
                    sleep($sleep);
                }
            }

        } catch (\Exception $e) {
            $this->error("Failed to connect to RabbitMQ: " . $e->getMessage());
            Log::error('RabbitMQ connection failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }

        $totalTime = time() - $this->startTime;
        $this->info("Worker stopped. Processed {$this->jobsProcessed} jobs in {$totalTime} seconds.");
        
        return 0;
    }

    /**
     * Listen for signals to gracefully shutdown
     */
    protected function listenForSignals()
    {
        if (extension_loaded('pcntl')) {
            pcntl_signal(SIGTERM, function () {
                $this->info('Received SIGTERM signal, shutting down gracefully...');
                $this->shouldQuit = true;
            });

            pcntl_signal(SIGINT, function () {
                $this->info('Received SIGINT signal, shutting down gracefully...');
                $this->shouldQuit = true;
            });
        }
    }

    /**
     * Check if worker should stop based on limits
     */
    protected function shouldStop($maxJobs, $maxTime): bool
    {
        // Check job limit
        if ($maxJobs > 0 && $this->jobsProcessed >= $maxJobs) {
            $this->info("Reached maximum job limit ({$maxJobs}), stopping worker.");
            return true;
        }

        // Check time limit
        if ($maxTime > 0 && (time() - $this->startTime) >= $maxTime) {
            $this->info("Reached maximum time limit ({$maxTime}s), stopping worker.");
            return true;
        }

        // Handle signals if pcntl is available
        if (extension_loaded('pcntl')) {
            pcntl_signal_dispatch();
        }

        return false;
    }
}
