<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use PhpAmqpLib\Connection\AMQPStreamConnection;

class RabbitMQInspectCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'rabbitmq:inspect {queue=imei_checks}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Inspect RabbitMQ queue contents';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $queueName = $this->argument('queue');
        $this->info("Inspecting RabbitMQ queue: {$queueName}");

        try {
            // Get RabbitMQ configuration
            $config = config('queue.connections.rabbitmq');
            
            // Create connection
            $connection = new AMQPStreamConnection(
                $config['host'],
                $config['port'],
                $config['user'],
                $config['password'],
                $config['vhost']
            );

            $channel = $connection->channel();
            $this->info('✓ Connected to RabbitMQ');

            // Declare queue to get info
            list($queueName, $messageCount, $consumerCount) = $channel->queue_declare($queueName, true);
            
            $this->info("Queue Information:");
            $this->info("  Name: {$queueName}");
            $this->info("  Messages: {$messageCount}");
            $this->info("  Consumers: {$consumerCount}");

            if ($messageCount > 0) {
                $this->info("\nTrying to peek at first message (without consuming):");
                
                // Try to get a message without acknowledging it
                $message = $channel->basic_get($queueName, false); // false = don't auto-ack
                
                if ($message) {
                    $this->info("Message found:");
                    $this->info("  Delivery Tag: " . $message->getDeliveryTag());
                    $this->info("  Body Length: " . strlen($message->getBody()));
                    $this->info("  Body Preview: " . substr($message->getBody(), 0, 200) . '...');
                    
                    // Reject the message to put it back in the queue
                    $channel->basic_nack($message->getDeliveryTag(), false, true);
                    $this->info("  Message returned to queue");
                } else {
                    $this->warn("No message retrieved despite count > 0");
                }
            } else {
                $this->info("Queue is empty");
            }

            $channel->close();
            $connection->close();

        } catch (\Exception $e) {
            $this->error('Error inspecting queue: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
