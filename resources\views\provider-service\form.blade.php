<div class="row mb-4">
    <div class="col-lg-12 col-md-12 mb-4">
        <div class="card">
            <div class="card-body">

<div class="rounded-16 form-box bg-white -dark-bg-dark-1 shadow-4 h-100">
    <form action="{{ $providerService->id ==null ? route('provider-service.store') : route('provider-service.update', $providerService) }}" method="POST"
          class="normal-form">
        @csrf

        @if( $providerService->id != null )
            @method('PUT')
        @endif

        <div class="row">
            <div class="col-md-4 col-12">
                <div class="form-group required-field text-left @error('provider_id') is-invalid @enderror">
                    <label for="provider_id">{{ __('Provider') }}</label>
                    <select required name="provider_id" id="provider_id" class="form-control filterField">
                        <option value="">Select</option>
                        <?php
                        foreach (\App\Models\Provider::query()->orderBy("name")->get() as $provider) {
                            ?>
                            <option <?=($provider->id==$providerService->provider_id ? 'selected=""' : '')?> value="<?=$provider->id?>"><?=$provider->displayName?></option>
                            <?php
                        }
                        ?>
                    </select>
                    @error('provider_id')
                    <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                    @enderror
                </div>
            </div>

            <div class="col-md-4 col-12">
                <div class="form-group required-field text-left @error('name') is-invalid @enderror">
                    <label for="name">{{ __('Name') }}</label>
                    <input required id="name" type="text" class="form-control @error('name') is-invalid @enderror"
                           name="name" placeholder="{{ __('Enter name') }}"
                           value="{{old('name', $providerService->name)}}">
                    @error('name')
                    <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                    @enderror
                </div>
            </div>

            <div class="col-md-4 col-12">
                <div class="form-group required-field text-left @error('price') is-invalid @enderror">
                    <label for="price">{{ __('Price') }}</label>
                    <input required id="price" type="text" class="form-control @error('price') is-invalid @enderror"
                           name="price" placeholder="{{ __('Enter Price') }}"
                           value="{{old('price', $providerService->price)}}">
                    @error('price')
                    <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                    @enderror
                </div>
            </div>

            <div class="col-md-3 col-12">
                <div class="form-group required-field text-left @error('sid') is-invalid @enderror">
                    <label for="sid">{{ __('Service ID') }}</label>
                    <input required id="sid" type="text" class="form-control @error('sid') is-invalid @enderror"
                           name="sid" placeholder="{{ __('Enter Service ID') }}"
                           value="{{old('sid', $providerService->sid)}}">
                    @error('sid')
                    <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                    @enderror
                </div>
            </div>

            <div class="col-md-3 col-12">
                <div class="form-group required-field text-left @error('type') is-invalid @enderror">
                    <label for="type">{{ __('Type') }}</label>
                    <select name="type" id="type" class="form-control @error('type') is-invalid @enderror">
                        <option <?=($providerService->type==\App\Constants\CommonConstants::TYPE_BOTH ? 'selected=""' : '')?> value="<?=\App\Constants\CommonConstants::TYPE_BOTH?>">Both (IMEI/SN)</option>
                        <option <?=($providerService->type==\App\Constants\CommonConstants::TYPE_IMEI ? 'selected=""' : '')?> value="<?=\App\Constants\CommonConstants::TYPE_IMEI?>">IMEI</option>
                        <option <?=($providerService->type==\App\Constants\CommonConstants::TYPE_SN ? 'selected=""' : '')?> value="<?=\App\Constants\CommonConstants::TYPE_SN?>">SN</option>
                    </select>
                    @error('type')
                    <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                    @enderror
                </div>
            </div>

            <div class="col-md-3 col-12">
                <div class="form-group required-field text-left @error('is_active') is-invalid @enderror">
                    <label for="is_active">{{ __('Active') }}</label>
                    <select name="is_active" id="is_active" class="form-control @error('is_active') is-invalid @enderror">
                        <option <?=(!$providerService->is_active ? 'selected=""' : '')?> value="<?=\App\Constants\CommonConstants::NO?>">No</option>
                        <option <?=($providerService->is_active ? 'selected=""' : '')?> value="<?=\App\Constants\CommonConstants::YES?>">Yes</option>
                    </select>
                    @error('is_active')
                    <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                    @enderror
                </div>
            </div>

            <div class="col-md-3 col-12">
                <div class="form-group required-field text-left @error('is_chinese_result') is-invalid @enderror">
                    <label for="is_chinese_result">{{ __('Chinese Result') }}</label>
                    <select name="is_chinese_result" id="is_chinese_result" class="form-control @error('is_chinese_result') is-invalid @enderror">
                        <option <?=(!$providerService->is_chinese_result ? 'selected=""' : '')?> value="<?=\App\Constants\CommonConstants::NO?>">No</option>
                        <option <?=($providerService->is_chinese_result ? 'selected=""' : '')?> value="<?=\App\Constants\CommonConstants::YES?>">Yes</option>
                    </select>
                    @error('is_chinese_result')
                    <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                    @enderror
                </div>
            </div>
        </div>

        @if( $providerService->id == null )

        @endif

        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-12 formBtn">
                @if( $providerService->id == null )
                    <button type="submit" class="btn btn-primary">{{ __('Save') }}</button>
                @else
                    <button type="submit" class="btn btn-primary">{{ __('Update') }}</button>
                @endif
            </div>
        </div>

    </form>
</div>
            </div>
        </div>
    </div>
</div>

@section('pageJs')

    <script>

    </script>

@endsection
