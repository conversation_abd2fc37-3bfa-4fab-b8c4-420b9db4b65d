<?php
use App\Components\Helper;
?>
@extends(\App\Components\Helper::getLayoutForUser())
@section('content')
    <main>
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <h1>{{__('Invoices')}}</h1>
                    <div class="text-zero top-right-button-container">
                        <a href="{{route('user-invoice.create')}}" class="btn btn-primary btn-lg top-right-button mr-1"> <i class="glyph-icon simple-icon-plus"></i> ADD NEW</a>
                    </div>

                    <nav class="breadcrumb-container d-none d-sm-block d-lg-inline-block" aria-label="breadcrumb">
                        <ol class="breadcrumb pt-0">
                            <li class="breadcrumb-item">
                                <a href="{{\App\Components\Helper::dashboardLink()}}">{{__('Dashboard')}}</a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">{{__('Invoices')}}</li>
                        </ol>
                    </nav>
                    <div class="separator mb-5"></div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-lg-12 col-md-12 mb-4">
                    <div class="card">
                        <div class="card-body table-wrapper">
                            <div class="table-filters-ghost" style="display: none;">
                                <div class="p-b-10 mt-3 searchFiltersContainer">
                                    <div class="p-b-10 searchFilters">
                                        <div class="row">
                                            <div class="col-12 col-md">
                                                <div class="form-group text-left">
                                                    <label for="role">{{ __('Search') }}</label>
                                                    <input type="text" name="q" value="" class="form-control filterField" placeholder="Enter Search">
                                                </div>
                                            </div>
                                            <div class="col-12 col-md">
                                                <div class="form-group text-left">
                                                    <label for="user_id">{{ __('User') }}</label>
                                                    <select name="user_id" id="user_id" class="form-control filterField search-user-ajax">
                                                        <option value="">All</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-12 col-md">
                                                <div class="form-group text-left">
                                                    <label for="is_paid">{{ __('Paid') }}</label>
                                                    <select name="is_paid" id="is_paid" class="form-control filterField">
                                                        <option value="">All</option>
                                                        @foreach (\App\Constants\CommonConstants::YES_NO_PROPERTIES as $userStatus => $userStatusData)
                                                            <option value="{{$userStatus}}">{{$userStatusData['text']}}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <table id="recordsTable" class="table table-hover align-middle" style="width:100%">
                                <thead>
                                <tr>
                                    <th>{{__('ID')}}</th>
                                    <th>{{__('Time')}}</th>
                                    <th>{{__('Ref.No.')}}</th>
                                    <th>{{__('User')}}</th>
                                    <th>{{__('Adjustment')}}</th>
                                    <th>{{__('Amount')}}</th>
                                    <th>{{__('Generated Date')}}</th>
                                    <th>{{__('Paid Date')}}</th>
                                    <th>{{__('Paid')}}</th>
                                    <th>{{__('Particular')}}</th>
                                    <th>{{__('Actions')}}</th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>



@endsection
@section('pageJs')

    <script>
        $(document).ready(function () {
            let recordsTable = $('#recordsTable');
            let mainTableWrapper = recordsTable.closest(".table-wrapper");
            let filters = mainTableWrapper.find(".table-filters-ghost");
            let dataTableOptions = {!! json_encode(\App\Common\ContentHelper::getDataTableOptions()) !!};
            let recordsTableObj = recordsTable.DataTable({
                ...dataTableOptions,
                processing: false,
                order: [[0, "desc"]],
                ajax: {
                    url: "{{ route('user-invoice.dataTable') }}",
                    data: function (data) {
                        data['search_query'] = {};
                        mainTableWrapper.find(".filterField").each(function () {
                            let obj = $(this);
                            data['search_query'][obj.attr("name")] = obj.val();
                        });
                    },
                    dataSrc: function (json) {
                        let data = json.stats;
                        return json.data;
                    }
                },
                columns: [
                    {data: 'id', name: 'id'},
                    {data: 'created_at', name: 'created_at'},
                    {data: 'ref_no', name: 'ref_no'},
                    {data: 'user', name: 'user',orderable: false, searchable: false},
                    {data: 'adjustment', name: 'adjustment',orderable: false, searchable: false},
                    {data: 'amount', name: 'amount'},
                    {data: 'generated_date', name: 'generated_date'},
                    {data: 'paid_date', name: 'paid_date'},
                    {data: 'is_paid', name: 'is_paid',orderable: false, searchable: false},
                    {data: 'particular', name: 'particular',orderable: false, searchable: false},
                    {data: 'actions', name: 'actions',orderable: false, searchable: false},
                ]
            });
            mainTableWrapper.find(".tableFilters").html(filters.html());
            filters.remove();
            $(document).on('keyup change', '.filterField', function () {
                recordsTableObj.draw();
            });
        });


    </script>

@endsection
