<?php

namespace App\Components;

use App\Base\Provider\BaseProvider;
use App\Constants\CommonConstants;
use App\Models\Log;
use App\Models\User;
use Illuminate\Support\Facades\Http;

class ProviderImeiLookup extends BaseProvider
{
    public static $providerID = CommonConstants::PROVIDER_IMEI_LOOKUP;
    public static function placeOrder($orderModel) {
        $validate=self::validateRequest($orderModel);
        if($validate!==true) {
            return $validate;
        }

        //$url = self::$providerDetails['api_url'] . "?key=".self::$providerDetails['api_key']."&imei=".$orderModel->imei."&service=".self::$providerServiceDetails['sid'];
        $url = self::$providerDetails['api_url'];
        $postData = [
            'imei' => $orderModel->imei,
            'serviceId' => self::$providerServiceDetails['sid'],
            'batchName' => 'test',
        ];
        $response = Http::withHeaders([
            'Content-Type'=>'application/json',
            'accept'=>'application/json',
            'access-key'=>self::$providerDetails['api_key'],
        ])->post($url,$postData);
        $result=$response->body();

        /* $curl = curl_init ($url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 40);
        curl_setopt($curl, CURLOPT_TIMEOUT, 60);
        $result = curl_exec($curl);
        curl_close($curl); */

        if($result && $result!="") {
            return self::parseResult($orderModel,$result);
        }

        return self::defaultResult();
    }

    public static function parseResult($orderModel,$result) {
        try {
            if ($result == "" || $result == false
                || stripos($result, "Checking your browser")!==false || stripos($result, "error code")!==false) {
                return self::failedResult(self::SERVICE_MAINTENANCE_MESSAGE,$result);
            }

            $finalResult=null;
            $decode = json_decode($result, true);
            if (is_array($decode) && array_key_exists('status', $decode)) {
                if (strtolower(trim($decode['status'])) == "success") {
                    if (array_key_exists('result', $decode)) {
                        $finalResult=$decode['result'];
                    }
                }
                if (array_key_exists('serviceId', $decode)) {
                    $finalResult='Ref No : '.$decode['serviceId'];
                }
            }

            if (!$finalResult) {
                return self::failedResult(self::DEFAULT_MESSAGE,$result);
            }

            $sid=self::$providerServiceDetails['sid'];
            switch ($sid) {
                case "38":
                    $html="";
                    if(is_array($finalResult)) {
                        foreach ($finalResult as $k => $v) {
                            if (trim(strtolower($k)) == strtolower("MDM Lock")) {
                                if (trim(strtolower(strip_tags($v))) == "off") {
                                    $v = '<span style="color:green;">OFF</span>';
                                } else {
                                    $v = '<span style="color:red;">' . $v . '</span>';
                                }
                            }
                            $html .= $k . ": " . $v . "<br />";
                        }
                    }

                    $finalResult = $html;
                    if($html=="") {
                        return self::failedResult(self::DEFAULT_MESSAGE,$result);
                    }

                    return self::successResult($finalResult,$result);
                    break;
            }

            return self::successResult($finalResult, $result);
        } catch (\Exception $e) {

        }
        return self::defaultResult();
    }
}
