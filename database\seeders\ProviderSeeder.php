<?php

namespace Database\Seeders;

use App\Constants\CommonConstants;
use App\Models\Category;
use App\Models\Provider;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ProviderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $rows=[
            ['id'=>CommonConstants::PROVIDER_SICKW,'name'=>'Sickw','api_url'=>'https://sickw.com/api.php','api_key'=>'Z4L-EXM-OU1-A12-C8R-CLT-NCE-FKZ','api_username'=>''],
            ['id'=>CommonConstants::PROVIDER_FASTBULK,'name'=>'FastBulk [iCheckPhone Oliver]','api_url'=>'https://fastbulkdata.com/user/api/getdata','api_key'=>'1re9mst5pi','api_username'=>''],
            ['id'=>CommonConstants::PROVIDER_HILOTMAN,'name'=>'Hilotman','api_url'=>'https://sys.imei2check.com/api.php','api_key'=>'F3S-X81-1RS-DDW-LFW-8U7-I8F-IK9','api_username'=>''],
            ['id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name'=>'MergePython','api_url'=>'https://python.goimeicheck.com/api.php','api_key'=>'hghkvhvhvhjcgvhjcgh','api_username'=>''],
            ['id'=>CommonConstants::PROVIDER_UNLOCK_3,'name'=>'Unlock3','api_url'=>'http://www.3unlock.top/','api_key'=>'4HTN6L9PBIBO5B3RSF6CAFOSKR7UJHQY','api_username'=>''],
            ['id'=>CommonConstants::PROVIDER_PHONE_CHECK,'name'=>'PhoneCheck','api_url'=>'https://phonecheck.pro/api','api_key'=>'c9d24e9a897475b896eab228bdcc7a64','api_username'=>''],
            ['id'=>CommonConstants::PROVIDER_APPLE_CHECK,'name'=>'AppleCheck','api_url'=>'http://applecheck.info/api_processor.php','api_key'=>'YZ1JLG8VIX0QPFE9CWSB','api_username'=>'Apple'],
            ['id'=>CommonConstants::PROVIDER_ZHAOJIHUI,'name'=>'Zhaojihui','api_url'=>'http://zhaojihui.top/api/sorder/instant_v2','api_key'=>'ec322b6069db4d02be5b75b929b74eba','api_username'=>'55558888'],
            ['id'=>CommonConstants::PROVIDER_IFREE_ICLOUD,'name'=>'IfreeIcloud','api_url'=>'https://api.ifreeicloud.co.uk/','api_key'=>'2J9-MVW-ORV-NJ6-4D3-F61-PHF-DFX','api_username'=>''],
            ['id'=>CommonConstants::PROVIDER_ALPHA_IMEI_CHECK,'name'=>'AlphaImeiCheck','api_url'=>'https://alpha.imeicheck.com/api/php-api/create','api_key'=>'goV7D-BAd5d-opoBr-9lTiJ-ycUZ3-tZQqY','api_username'=>''],
            ['id'=>CommonConstants::PROVIDER_GSX_UNLOCKING,'name'=>'GsxUnlocking','api_url'=>'https://new.gsxunlocking.com/api/uapi','api_key'=>'YG19YZIHL4IYG5M9DFPE','api_username'=>''],
            ['id'=>CommonConstants::PROVIDER_IMEI_LOOKUP,'name'=>'ImeiLookup','api_url'=>'https://api.imeilookup.com/order/single','api_key'=>'774dd48a-7ee6-4971-8105-23c901264506','api_username'=>'gic'],
            ['id'=>CommonConstants::PROVIDER_UNLOCK_API,'name'=>'UnlockAPI','api_url'=>'https://imei.top/api/','api_key'=>'Nsz0hlXO4qeMuvb4rPRBfkJ6c9','api_username'=>''],
            ['id'=>CommonConstants::PROVIDER_IUNLOCK_TEAM,'name'=>'iUnlockTeam','api_url'=>'https://iunlockteam.com/api/rent-service-imei','api_key'=>'0X0-861-A8P-O95-ZK1-8MI-46O-4I9','api_username'=>'h4rru'],
            ['id'=>CommonConstants::PROVIDER_IUNLOCK_TEAM_NEW,'name'=>'iUnlockTeamNew','api_url'=>'https://iunlockteam.com/api/service','api_key'=>'PRP-824-ULO-XGA-ZQ9-9UQ-5IK-501','api_username'=>'h4rru'],
        ];

        if(count($rows)>0) {
            foreach ($rows as $row) {
                $isExist=Provider::query()->where('id','=',$row['id'])->first();
                if(!$isExist) {
                    if($row['api_key']=="") {
                        $row['api_key']=null;
                    }
                    if($row['api_username']=="") {
                        $row['api_username']=null;
                    }
                    $row['created_at'] = date(CommonConstants::PHP_DATE_FORMAT);
                    Provider::create($row);
                }
            }
        }
    }
}
