<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('services', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->foreignId('operating_system_id')->references('id')->on('operating_systems')->onDelete('cascade');
            $table->foreignId('category_id')->references('id')->on('categories')->onDelete('cascade');
            $table->foreignId('provider_id')->references('id')->on('providers')->onDelete('cascade');
            $table->foreignId('provider_service_id')->references('id')->on('provider_services')->onDelete('cascade');
            $table->string('name')->index();
            $table->bigInteger('price')->index();
            $table->integer('type')->default(0)->comment('0=both 1=imei 2=sn')->index();
            $table->longText('order_format')->nullable();
            $table->longText('download_format')->nullable();
            $table->integer('is_active')->default(\App\Constants\CommonConstants::YES)->index();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('services');
    }
};
