@extends(\App\Components\Helper::getLayoutForUser())
@section('content')
    <main>
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <h1>{{__($provider->name)}}</h1>
                    <div class="text-zero top-right-button-container">
                        <a href="{{route('provider.edit',$provider->id)}}" class="btn btn-secondary btn-lg top-right-button mr-1"> <i class="glyph-icon simple-icon-pencil"></i> UPDATE</a>
                    </div>

                    <nav class="breadcrumb-container d-none d-sm-block d-lg-inline-block" aria-label="breadcrumb">
                        <ol class="breadcrumb pt-0">
                            <li class="breadcrumb-item">
                                <a href="{{\App\Components\Helper::dashboardLink()}}">{{__('Dashboard')}}</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{route('provider.index')}}">{{__('Providers')}}</a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">{{__($provider->name)}}</li>
                        </ol>
                    </nav>
                    <div class="separator mb-5"></div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-lg-12 col-md-12 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <table class="table">
                                <tbody>
                                    <tr>
                                        <th>Name</th>
                                        <td>{{__($provider->name)}}</td>
                                    </tr>
                                    <tr>
                                        <th>API URL</th>
                                        <td>{{__($provider->api_url)}}</td>
                                    </tr>
                                    <tr>
                                        <th>API Key</th>
                                        <td>{{__($provider->api_key)}}</td>
                                    </tr>
                                    <tr>
                                        <th>API Username</th>
                                        <td>{{__($provider->api_username)}}</td>
                                    </tr>
                                    <tr>
                                        <th>Active</th>
                                        <td><?=\App\Components\Helper::onOffButton($provider,'is_active')?></td>
                                    </tr>
                                    <tr>
                                        <th>Created At</th>
                                        <td>{{__($provider->created_at)}}</td>
                                    </tr>
                                    <tr>
                                        <th>Updated At</th>
                                        <td>{{__($provider->updated_at)}}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

@endsection
