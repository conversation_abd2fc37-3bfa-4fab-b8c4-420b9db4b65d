<?php

namespace App\Components;

use App\Base\Provider\BaseProvider;
use App\Constants\CommonConstants;
use App\Models\Log;
use App\Models\User;
use Illuminate\Support\Facades\Http;

class ProviderMergePython extends BaseProvider
{
    public static $providerID = CommonConstants::PROVIDER_MERGE_PYTHON;
    public static function placeOrder($orderModel) {
        $validate=self::validateRequest($orderModel);
        if($validate!==true) {
            return $validate;
        }

        $url = self::$providerDetails['api_url'] . "?api_key=".self::$providerDetails['api_key']."&imei=".$orderModel->imei."&type=".self::$providerServiceDetails['sid'];
        $response = Http::get($url);
        /* $curl = curl_init ($url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, TRUE);
        curl_setopt($curl, CURLOPT_ENCODING , '');
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 40);
        curl_setopt($curl, CURLOPT_TIMEOUT, 0);
        curl_setopt($curl, CURLOPT_MAXREDIRS, 10);
        curl_setopt($curl, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'GET');
        $result = curl_exec($curl);
        curl_close($curl); */
        $result=$response->body();

        if($result && $result!="") {
            return self::parseResult($orderModel,$result);
        }

        return self::defaultResult();
    }

    public static function parseResult($orderModel,$result) {
        try {
            if ($result == "" || $result == false || stripos($result, "error code")!==false) {
                return self::failedResult(self::SERVICE_MAINTENANCE_MESSAGE,$result);
            }

            $finalResult="";
            $purchase_price=0;
            $decode_resp = json_decode($result, true);
            if(is_array($decode_resp) && count($decode_resp)>0) {
                if (array_key_exists('status', $decode_resp)) {
                    if (strtolower($decode_resp['status']) == "success") {
                        if (array_key_exists('result', $decode_resp)) {

                            $newHtmlResult = "";
                            foreach ($decode_resp['result'] as $key => $val) {
                                if ($val == "") {
                                    continue;
                                }
                                $newHtmlResult .= $key . " : " . $val . "<br />";
                            }
                            $finalResult = $newHtmlResult;

                            if (array_key_exists('data', $decode_resp)) {
                                if (array_key_exists('cost', $decode_resp['data'])) {
                                    $purchase_price = $decode_resp['data']['cost'];
                                }
                            }
                        }
                    }
                }
            }

            if($finalResult=="") {
                return self::failedResult(self::DEFAULT_MESSAGE,['result'=>$result,'price'=>$purchase_price]);
            }

            $sid=self::$providerServiceDetails['sid'];
            /* switch ($sid) {
                case "124":
                    if (stripos($finalResult, 'IMEI\/Serial Number') !== false) {
                        $finalResult = preg_replace('/IMEI\/Serial Number/i', 'IMEI/SN', $finalResult);
                    }
                    if (stripos($finalResult, 'IMEI/Serial Number') !== false) {
                        $finalResult = preg_replace('/IMEI\/Serial Number/i', 'IMEI/SN', $finalResult);
                    }
                    if (stripos($finalResult, 'Find My iPhone Status') !== false) {
                        $finalResult = preg_replace('/Find My iPhone Status/i', 'iCloud Status', $finalResult);
                    }
                    return self::successResult($finalResult,$result);
                    break;

                case "111":
                    if (stripos($finalResult, 'Model Name:') !== false) {
                        $finalResult = preg_replace('/Model Name:/i', 'Model :', $finalResult);
                    }
                    if (stripos($result, 'MDM Status:') !== false) {
                        $finalResult = preg_replace('/MDM Status:/i', 'MDM Status :', $finalResult);
                    }
                    return self::successResult($finalResult,$result);
                    break;
            } */

            return self::successResult($finalResult, ['result'=>$result,'price'=>$purchase_price]);
        } catch (\Exception $e) {

        }
        return self::defaultResult();
    }
}
