<?php

namespace App\Http\Controllers;

use App\Base\Model\BaseModel;
use App\Components\Helper;
use App\Constants\CommonConstants;
use App\Constants\UserConstants;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Yajra\DataTables\Facades\DataTables;

class UserController extends Controller
{
    public function index()
    {
        return view('user.index');
    }

    public function show(User $user,Request $request)
    {
        if($request->input('type')) {
            switch (trim(strtolower($request->input('type')))) {
                case "re-api":
                    if($user->generateNewApiKey()) {
                        return redirect()->route('user.show',$user->id)->with('success', 'New API key generated successfully.');
                    }
                    break;
            }
            return redirect()->route('user.show',$user->id)->with('error', 'Invalid request.');
        }
        return view('user.show', compact('user'));
    }

    public function create()
    {
        $user=new User();
        return view('user.create', compact('user'));
    }

    public function edit(User $user)
    {
        return view('user.edit', compact('user'));
    }

    public function store(Request $request)
    {
        $user = new User();
        return $this->save($request, $user);
    }

    public function update(Request $request, User $user)
    {
        return $this->save($request, $user);
    }

    private function save(Request $request, User $user)
    {
        $isNewRecord = true;
        if ($user->id != null) {
            $isNewRecord = false;
        }

        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'username' => ['required', 'string', 'lowercase', 'max:255', Rule::unique('users')->ignore($user->id),
                function ($attribute, $value, $fail) {
                    if (!empty($value) && !preg_match('/^\w{4,}$/', $value)) {
                        $fail('The username must be alphanumeric, without spaces, and at least 4 characters long.');
                    }
                },
            ],
            'email' => ['required', 'string', 'lowercase', 'max:255', Rule::unique('users')->ignore($user->id),
                function ($attribute, $value, $fail) {
                    if (!empty($value) && !preg_match('/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/', $value)) {
                        $fail('Invalid email address');
                    }
                },
            ],
            'is_api_enabled' => ['required', 'integer'],
            'is_api_protection' => ['required', 'integer'],
            'is_active' => ['required', 'integer'],
            'country_id' => ['required', 'integer'],
            'plan_id' => ['required', 'integer'],
        ];

        if ($isNewRecord) {
            $rules['password'] = ['required', 'string', 'min:6', 'confirmed'];
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            if ($isNewRecord) {
                return redirect()->route('user.create')->withErrors($validator)->withInput();
            } else {
                return redirect()->route('user.edit', $user->id)->withErrors($validator)->withInput();
            }
        }

        $ip = request()->ip();
        $ipDetails = Helper::fetchIpDetails($ip, true);

        $user->name = $request->input('name');
        $user->username = strtolower($request->input('username'));
        $user->email = $request->input('email');
        $user->is_active = (int)$request->input('is_active');
        $user->is_api_enabled = (int)$request->input('is_api_enabled');
        $user->is_active = (int)$request->input('is_active');
        $user->country_id = (int)$request->input('country_id');
        $user->plan_id = (int)$request->input('plan_id');
        $user->updated_at = date(CommonConstants::PHP_DATE_FORMAT);
        if($isNewRecord) {
            $user->user_role_id = UserConstants::ROLE_USER;
        }
        if ($isNewRecord) {
            $user->password = $request->input('password');
            $user->user_agent = request()->userAgent();
            $user->email_verified_at = date(CommonConstants::PHP_DATE_FORMAT);
            $user->save();
        } else {
            $user->update();
        }
        return redirect()->route('user.index')->with('success', 'User saved successfully.');
    }

    public function dataTable(Request $request)
    {
        try {
            if ($request->ajax()) {
                $query = User::query()->where('user_role_id', '!=', UserConstants::ROLE_ADMIN);
                BaseModel::buildFilterQuery($query, [
                    'q' => ['name', 'email'],
                ]);
                return Datatables::eloquent($query)
                    ->addColumn('checkboxes', function ($row) {
                        return '<input type="checkbox" name="pdr_checkbox[]" class="pdr_checkbox" value="' . $row->id . '" />';
                    })
                    ->addColumn('username', function ($row) {
                        return $row->username;
                    })
                    ->addColumn('balance', function ($row) {
                        return Helper::printAmount($row->balance);
                    })
                    ->addColumn('email', function ($row) {
                        return Helper::editPopupStructure($row,'email');
                    })
                    ->addColumn('plan', function ($row) {
                        return $row->plan->name;
                    })
                    ->addColumn('created_at', function ($row) {
                        return Helper::displayTime($row->created_at);
                    })
                    ->addColumn('updated_at', function ($row) {
                        return Helper::displayTime($row->updated_at);
                    })
                    ->addColumn('emailVerified', function ($row) {
                        return Helper::printYesNoBadge(!($row->email_verified_at == null));
                    })
                    ->addColumn('is_api_enabled', function ($row) use ($query) {
                        $columnName = 'is_api_enabled';
                        return Helper::onOffButton($row, $columnName, $query);
                    })
                    ->addColumn('is_active', function ($row) use ($query) {
                        $columnName = 'is_active';
                        return Helper::onOffButton($row, $columnName, $query);
                    })
                    ->addColumn('actions', function ($row) {
                        $buttons = [];
                        //$buttons['Login']=['url' => route('user.loginAs', $row->id), 'icon' => 'las la-sign-in-alt'];
                        $buttons['view'] = ['url' => route('user.show', $row->id)];
                        $buttons['edit'] = ['url' => route('user.edit', $row->id)];
                        return Helper::getActionButtons($buttons);
                    })
                    ->rawColumns(['checkboxes', 'username', 'is_api_enabled', 'actions', 'email', 'is_active'])
                    ->make(true);
            }
        } catch (\Exception $e) {
            //print_r(['message'=>$e->getMessage(),'trace'=>$e->getTraceAsString()]);
            die();
        }
    }
}
