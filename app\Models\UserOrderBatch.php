<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use App\Casts\AmountCast;
use App\Constants\CommonConstants;

class UserOrderBatch extends BaseModel
{
    protected $fillable = [
        'order_ref',
        'user_id',
        'service_id',
        'provider_id',
        'provider_service_id',
        'imeis',
        'price',
        'total_count',
        'pending_count',
        'is_scheduled',
        'status',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'price' => AmountCast::class,
    ];

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function service()
    {
        return $this->hasOne(Service::class, 'id', 'service_id');
    }

    public function provider()
    {
        return $this->hasOne(Provider::class, 'id', 'provider_id');
    }

    public function providerService()
    {
        return $this->hasOne(ProviderService::class, 'id', 'provider_service_id');
    }

    public function onSaving()
    {
        if($this->order_ref=="" || $this->order_ref==null) {
            $this->order_ref=self::generateOrderReferenceNumber($this->user_id);
        }
        parent::onSaving();
    }

    public static function generateOrderReferenceNumber($user_id=null, $timestamp=null, $refNo=null) {
        if(!$user_id) {
            $user_id=CommonConstants::ADMINISTRATIVE;
        }
        if(!$timestamp) {
            $timestamp=time();
        }
        if(!$refNo) {
            $refNo=str_pad(rand(1,999), 3, '0', STR_PAD_LEFT);
        }
        //return date("YdmH",$timestamp).str_pad($user_id, 2, '0', STR_PAD_LEFT).$refNo;
        return $timestamp.str_pad($user_id, 2, '0', STR_PAD_LEFT).$refNo;
    }

    public static function getBatchByReference($order_ref) {
        return self::query()->where('order_ref','=',$order_ref)->first();
    }
}
