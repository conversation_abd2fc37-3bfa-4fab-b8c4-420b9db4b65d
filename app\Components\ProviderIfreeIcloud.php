<?php

namespace App\Components;

use App\Base\Provider\BaseProvider;
use App\Constants\CommonConstants;
use App\Models\Log;
use App\Models\User;
use Illuminate\Support\Facades\Http;

class ProviderIfreeIcloud extends BaseProvider
{
    public static $providerID = CommonConstants::PROVIDER_IFREE_ICLOUD;
    public static function placeOrder($orderModel) {
        $validate=self::validateRequest($orderModel);
        if($validate!==true) {
            return $validate;
        }

        $url = self::$providerDetails['api_url'] . "?key=".self::$providerDetails['api_key']."&imei=".$orderModel->imei."&service=".self::$providerServiceDetails['sid'];
        $response = Http::get($url);
        $result=$response->body();

        /* $curl = curl_init ($url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 40);
        curl_setopt($curl, CURLOPT_TIMEOUT, 60);
        $result = curl_exec($curl);
        curl_close($curl); */

        if($result && $result!="") {
            return self::parseResult($orderModel,$result);
        }

        return self::defaultResult();
    }

    public static function parseResult($orderModel,$result) {
        try {
            if ($result == "" || $result == false || stripos($result, "error code")!==false) {
                return self::failedResult(self::SERVICE_MAINTENANCE_MESSAGE,$result);
            }

            $finalResult=null;
            $decode=json_decode($result,true);
            if(is_array($decode) && array_key_exists('success',$decode)) {
                if($decode['success']===true) {
                    $finalResult=$decode['response'];
                }
            }

            if (!$finalResult) {
                return self::failedResult(self::DEFAULT_MESSAGE,$result);
            }

            $sid=self::$providerServiceDetails['sid'];
            switch ($sid) {

            }

            return self::successResult($finalResult, $result);
        } catch (\Exception $e) {

        }
        return self::defaultResult();
    }
}
