@extends(\App\Components\Helper::getLayoutForUser())
@section('content')
    <main>
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <h1>{{__($plan->name)}}</h1>
                    <div class="text-zero top-right-button-container">
                        <a href="{{route('plan.edit',$plan->id)}}" class="btn btn-secondary btn-lg top-right-button mr-1"> <i class="glyph-icon simple-icon-pencil"></i> UPDATE</a>
                    </div>

                    <nav class="breadcrumb-container d-none d-sm-block d-lg-inline-block" aria-label="breadcrumb">
                        <ol class="breadcrumb pt-0">
                            <li class="breadcrumb-item">
                                <a href="{{\App\Components\Helper::dashboardLink()}}">{{__('Dashboard')}}</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{route('plan.index')}}">{{__('User Groups')}}</a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">{{__($plan->name)}}</li>
                        </ol>
                    </nav>
                    <div class="separator mb-5"></div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-lg-12 col-md-12 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <table class="table">
                                <tbody>
                                    <tr>
                                        <th>Name</th>
                                        <td>{{__($plan->name)}}</td>
                                    </tr>
                                    <tr>
                                        <th>Price</th>
                                        <td>{{__(\App\Components\Helper::printAmount($plan->price))}}</td>
                                    </tr>
                                    <tr>
                                        <th>Default</th>
                                        <td><?=\App\Components\Helper::printYesNoBadge(!($plan->is_default == null))?></td>
                                    </tr>
                                    <tr>
                                        <th>Best Seller</th>
                                        <td><?=\App\Components\Helper::onOffButton($plan,'is_best_seller')?></td>
                                    </tr>
                                    <tr>
                                        <th>Visible To Guest</th>
                                        <td><?=\App\Components\Helper::onOffButton($plan,'is_visible_to_guest')?></td>
                                    </tr>
                                    <tr>
                                        <th>Active</th>
                                        <td><?=\App\Components\Helper::onOffButton($plan,'is_active')?></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

@endsection
