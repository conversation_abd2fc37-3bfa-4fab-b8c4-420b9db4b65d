<?php
namespace App\Http\Controllers;

use App\Components\RedisWrapper;
use App\Constants\CommonConstants;
use App\Models\Service;
use App\Models\UserOrder;
use App\Models\UserOrderBatch;
use Illuminate\Http\Request;

class ApiController extends \App\Base\Controller\BaseApiController
{
    public function services(Request $request) {
        $identity=$this->getUser();
        $categories=getCategoriesCache()
            ->where('is_active','=',CommonConstants::YES);

        if($request->input('category')) {
            $categories=getCategoriesCache()
                ->where('is_active','=',CommonConstants::YES)
                ->where('id','=',$request->input('category'));
        }

        $services=[];
        if(count($categories)>0) {
            foreach ($categories as $category) {
                $categoryServices=getServicesCache()
                    ->where('category_id','=',$category->id)
                    ->where('is_active','=',CommonConstants::YES);
                if(count($categoryServices)>0) {
                    foreach ($categoryServices as $service) {
                        $services[]=[
                            "id"=> "".$service->custom_id,
                            "category_id"=>"".$category->id,
                            "category"=>$category->name,
                            "name"=>$service->name,
                            "code"=>"".$service->custom_id,
                            "service_id"=>"".$service->custom_id,
                            "amount"=>"".$service->userPrice($identity),
                            "currency"=>"USD",
                            "currency_code"=>"$",
                            "instant"=>"1",
                            "type"=>$service->typeName,
                            "user_access"=>"All",
                        ];
                    }
                }
            }
        }

        return $this->sendSuccess($services);
    }
    public function categories(Request $request) {
        $identity=$this->getUser();
        $query=getCategoriesCache()
            ->where('is_active','=',CommonConstants::YES);
        $categories=[];
        if(count($query)>0) {
            foreach ($query as $category) {
                $categories[]=[
                    'id'=>$category->id,
                    'name'=>$category->name,
                ];
            }
        }
        return $this->sendSuccess($categories);
    }
    public function balance(Request $request) {
        $identity=$this->getUser();
        $response=[
            'name'=>$identity->name,
            'username'=>$identity->username,
            'email'=>$identity->email,
            "currency_code"=>"$",
            "currency"=>"USD",
            "balance"=>"".$this->balance,
        ];
        return $this->sendSuccess($response);
    }

    public function placeOrder(Request $request) {
        $serviceDetails=null;
        if(!$request->input('service') || !$request->input('imei')) {
            return $this->sendError('Invalid request parameters');
        }

        if($request->input('service')) {
            $serviceDetails=Service::fetchDetailsFromCustomIDCache($request->input('service'));
        }

        if(!$serviceDetails) {
            return $this->sendError('Invalid service or down for maintenance');
        }
        $identity=$this->getUser();
        $service=getServicesCache()
            ->where('id','=',$serviceDetails['id'])->first();

        if(!$service) {
            return $this->sendError('Service down for maintenance');
        }

        $servicePrice=$service->userPrice($identity);
        if($this->balance>=$servicePrice) {
            $model=new UserOrder();
            $model->user_id=$identity->id;
            $model->imei=trim($request->input('imei'));
            $model->service_id=$serviceDetails['id'];
            $model->provider_id=$serviceDetails['provider_id'];
            $model->provider_service_id=$serviceDetails['provider_service_id'];
            $model->price=$servicePrice;

            $postData=[
                'order_ref'=>UserOrder::generateOrderReferenceNumber($identity->id),
                'user_id'=>$identity->id,
                'imei'=>$model->imei,
                'service_id'=>$model->service_id,
                'provider_id'=>$model->provider_id,
                'provider_service_id'=>$model->provider_service_id,
                'price'=>$model->price,
                'status'=>CommonConstants::ORDER_STATUS_PENDING,
                'is_deducted'=>CommonConstants::YES,
                'result'=>'',
            ];

            debitUserBalance($model->user_id,$model->price);

            $key='order_'.$postData['order_ref'];
            $redisRequest=RedisWrapper::saveRecord(
                $key,
                $postData,
                env('APP_URL').'/order-api/place-order/'.$key
            );
            if($redisRequest) {
                if(is_array($redisRequest)) {
                    if(array_key_exists('order_response',$redisRequest)) {
                        if(is_array($redisRequest['order_response'])) {
                            if($redisRequest['order_response']['status']=="success") {
                                return $this->sendSuccess($redisRequest['order_response']);
                            } else {
                                return $this->sendError($redisRequest['order_response']);
                            }
                        }
                    }
                }
            }

            //dd($redisRequest);

            /*
            if($model->save()) {
                $result=$model->placeOrder();
                if(array_key_exists('error',$result)) {
                    $finalResult=$model->rejectOrder($result['error'],(array_key_exists('data',$result) ? $result['data'] : null));
                    return $this->sendError([
                        'order_id'=>$finalResult->order_ref,
                        'reference_id'=>$finalResult->order_ref,
                        'imei'=>$finalResult->imei,
                        'message'=>"Result failed",
                        'result'=>$finalResult->result,
                        'status'=>'error',
                    ]);
                } else {
                    $finalResult=$model->successOrder($result['result'],(array_key_exists('data',$result) ? $result['data'] : null));
                    return $this->sendSuccess([
                        'order_id'=>$finalResult->order_ref,
                        'reference_id'=>$finalResult->order_ref,
                        'imei'=>$finalResult->imei,
                        'message'=>"Result passed",
                        'result'=>$finalResult->result,
                        'status'=>'success',
                    ]);
                }
            }
            */
            return $this->sendError('Server busy. Try after sometime.');
        } else {
            return $this->sendError('Insufficient balance');
        }
    }
    public function getOrder(Request $request) {
        $serviceDetails=null;
        if(!$request->input('tran_id')) {
            return $this->sendError('Invalid request parameters');
        }

        $identity=$this->getUser();
        $result=UserOrder::fetchOrderStatus($request->input('tran_id'),$identity);
        if(array_key_exists('order_id',$result)) {
            if($result['status']=="success") {
                return $this->sendSuccess($result);
            } else {
                return $this->sendError($result);
            }
        } else {
            return $this->sendError($result['result']);
        }
    }

    public function placeBulkOrder(Request $request) {
        $serviceDetails=null;
        if(!$request->input('service') || !$request->input('imei')) {
            return $this->sendError('Invalid request parameters');
        }

        if($request->input('service')) {
            $serviceDetails=Service::fetchDetailsFromCustomIDCache($request->input('service'));
        }

        if(!$serviceDetails) {
            return $this->sendError('Invalid service or down for maintenance');
        }
        $identity=$this->getUser();
        $service=getServicesCache()
            ->where('id','=',$serviceDetails['id'])->first();

        if(!$service) {
            return $this->sendError('Service down for maintenance');
        }
        $imeis=[];
        foreach (explode(",",$request->input('imei')) as $imei) {
            $imei=trim($imei);
            if($imei!="") {
                if(!in_array($imei,$imeis)) {
                    $imeis[]=$imei;
                }
            }
        }

        if(count($imeis)<=0) {
            return $this->sendError('Invalid IMEI/SN parameter');
        }

        $servicePrice=$service->userPrice($identity);
        if($this->balance>=round($servicePrice*count($imeis),CommonConstants::USD_PRECISION)) {
            $finalResponse=[];

            debitUserBalance($identity->id,round($servicePrice*count($imeis),CommonConstants::USD_PRECISION));

            foreach ($imeis as $imei) {

                $model=new UserOrder();
                $model->user_id=$identity->id;
                $model->imei=$imei;
                $model->service_id=$serviceDetails['id'];
                $model->provider_id=$serviceDetails['provider_id'];
                $model->provider_service_id=$serviceDetails['provider_service_id'];
                $model->price=$servicePrice;

                $postData=[
                    'order_ref'=>UserOrder::generateOrderReferenceNumber($identity->id),
                    'user_id'=>$identity->id,
                    'imei'=>$model->imei,
                    'service_id'=>$model->service_id,
                    'provider_id'=>$model->provider_id,
                    'provider_service_id'=>$model->provider_service_id,
                    'price'=>$model->price,
                    'status'=>CommonConstants::ORDER_STATUS_PENDING,
                    'is_deducted'=>CommonConstants::YES,
                    'result'=>'',
                ];

                $key='order_'.$postData['order_ref'];
                $redisRequest=RedisWrapper::saveRecord(
                    $key,
                    $postData,
                    env('APP_URL').'/order-api/place-order/'.$key
                );
                if($redisRequest) {
                    if(is_array($redisRequest)) {
                        if(array_key_exists('order_response',$redisRequest)) {
                            if(is_array($redisRequest['order_response'])) {
                                if($redisRequest['order_response']['status']=="success") {
                                    $finalResponse[''.$imei]=[
                                        'status'=>'success',
                                        'response'=>$redisRequest['order_response'],
                                    ];
                                } else {
                                    $finalResponse[''.$imei]=[
                                        'status'=>'error',
                                        'response'=>$redisRequest['order_response'],
                                    ];
                                }
                            }
                        }
                    }
                }

                /* $model = new UserOrder();
                $model->user_id = $identity->id;
                $model->imei = $imei;
                $model->service_id = $serviceDetails['id'];
                $model->provider_id = $serviceDetails['provider_id'];
                $model->provider_service_id = $serviceDetails['provider_service_id'];
                $model->price = $servicePrice;
                if ($model->save()) {
                    $result = $model->placeOrder();
                    if (array_key_exists('error', $result)) {
                        $finalResult = $model->rejectOrder($result['error'], (array_key_exists('data', $result) ? $result['data'] : null));
                        $finalResponse[''.$finalResult->imei]=['status'=>'error','response'=>[
                            'order_id' => $finalResult->order_ref,
                            'reference_id' => $finalResult->order_ref,
                            'imei' => $finalResult->imei,
                            'message' => "Result failed",
                            'result' => $finalResult->result,
                            'status'=>'error',
                        ]];
                    } else {
                        $finalResult = $model->successOrder($result['result'], (array_key_exists('data', $result) ? $result['data'] : null));
                        $finalResponse[''.$finalResult->imei]=['status'=>'success','response'=>[
                            'order_id' => $finalResult->order_ref,
                            'reference_id' => $finalResult->order_ref,
                            'imei' => $finalResult->imei,
                            'message' => "Result passed",
                            'result' => $finalResult->result,
                            'status'=>'success',
                        ]];
                    }
                } */
            }
            return $this->sendSuccess($finalResponse);
        } else {
            return $this->sendError('Insufficient balance');
        }
    }
    public function getBulkOrder(Request $request) {
        $serviceDetails=null;
        if(!$request->input('tran_id')) {
            return $this->sendError('Invalid request parameters');
        }

        $identity=$this->getUser();

        $ids=[];
        foreach (explode(",",$request->input('tran_id')) as $tranID) {
            $tranID=trim($tranID);
            if($tranID!="") {
                if(!in_array($tranID,$ids)) {
                    $ids[]=$tranID;
                }
            }
        }

        if(count($ids)<=0) {
            return $this->sendError('Invalid parameters passed');
        }

        $finalResponse=[];
        foreach ($ids as $id) {
            $result=UserOrder::fetchOrderStatus($id,$identity);
            if(array_key_exists('imei',$result)) {
                $status=$result['status'];
                $finalResponse[''.$result['imei']]=['status'=>$status,'response'=>$result];;
            } else {
                $finalResponse[''.$id]=['status'=>'error','response'=>$result];
            }
        }
        return $this->sendSuccess($finalResponse);
    }

    public function placeBatchOrder(Request $request) {
        $serviceDetails=null;
        if(!$request->input('service') || !$request->input('imei')) {
            return $this->sendError('Invalid request parameters');
        }

        if($request->input('service')) {
            $serviceDetails=Service::fetchDetailsFromCustomIDCache($request->input('service'));
        }

        if(!$serviceDetails) {
            return $this->sendError('Invalid service or down for maintenance');
        }
        $identity=$this->getUser();
        $service=getServicesCache()
            ->where('id','=',$serviceDetails['id'])->first();

        if(!$service) {
            return $this->sendError('Service down for maintenance');
        }
        $imeis=[];
        foreach (explode(",",$request->input('imei')) as $imei) {
            $imei=trim($imei);
            if($imei!="") {
                if(!in_array($imei,$imeis)) {
                    $imeis[]=$imei;
                }
            }
        }

        if(count($imeis)<=0) {
            return $this->sendError('Invalid IMEI/SN parameter');
        }

        $servicePrice=$service->userPrice($identity);
        $needBalance=round($servicePrice*count($imeis),CommonConstants::USD_PRECISION);
        if($this->balance>=$needBalance) {
            debitUserBalance($identity->id,$needBalance);

            $model=new UserOrderBatch();
            $model->order_ref=UserOrderBatch::generateOrderReferenceNumber($identity->id);
            $model->user_id=$identity->id;
            $model->imeis=json_encode($imeis);
            $model->service_id=$serviceDetails['id'];
            $model->provider_id=$serviceDetails['provider_id'];
            $model->provider_service_id=$serviceDetails['provider_service_id'];
            $model->price=$servicePrice;
            $model->total_count=count($imeis);
            $model->pending_count=$model->total_count;
            if($model->save()) {
                $postData = [
                    'order_ref' => $model->order_ref,
                    'user_id' => $identity->id,
                    'imeis' => $model->imeis,
                    'service_id' => $model->service_id,
                    'provider_id' => $model->provider_id,
                    'provider_service_id' => $model->provider_service_id,
                    'price' => $model->price,
                    'status' => CommonConstants::ORDER_STATUS_PENDING,
                    'is_deducted' => CommonConstants::YES,
                    'batch_result' => '',
                    'total_count'=>$model->total_count,
                    'pending_count'=>$model->pending_count,
                ];

                $key = 'batch_' . $postData['order_ref'];
                $redisRequest = RedisWrapper::saveRecord(
                    $key,
                    $postData
                );
                if ($redisRequest) {
                    $processKey='process_'.$key;
                    /* RedisWrapper::saveRecord(
                        $processKey,
                        ['key'=>$key],
                        env('APP_URL').'/order-api/test-batch-order/'.$processKey,
                        true
                    ); */
                    RedisWrapper::saveRecord(
                        $processKey,
                        ['key'=>$key,'counter'=>0],
                        env('APP_URL').'/order-api/place-batch-order/'.$processKey,
                        true
                    );
                    return $this->sendSuccess([
                        'order_id' => $model->order_ref,
                        'reference_id' => $model->order_ref,
                        'message' => "Batch scheduled",
                        'status'=>'success',
                    ]);
                } else {
                    return $this->sendError('Unable to process your batch at this moment.');
                }
            } else {
                return $this->sendError('System busy. Try after sometime.');
            }
        } else {
            return $this->sendError('Insufficient balance');
        }
    }
}
