<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('user_api_key_logs', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->bigInteger('user_id')->nullable()->index();
            $table->string('api_key')->nullable()->index();
            $table->integer('is_admin')->default(0)->index();
            $table->createdAtTime();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_api_key_logs');
    }
};
