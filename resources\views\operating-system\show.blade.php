@extends(\App\Components\Helper::getLayoutForUser())
@section('content')
    <main>
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <h1>{{__($user->name)}}</h1>
                    <div class="text-zero top-right-button-container">
                        <a href="{{route('user.edit',$user->id)}}" class="btn btn-secondary btn-lg top-right-button mr-1"> <i class="glyph-icon simple-icon-pencil"></i> UPDATE</a>
{{--                        <a href="{{route('user.destroy',$user->id)}}" class="btn btn-danger btn-lg top-right-button mr-1"> <i class="glyph-icon simple-icon-trash"></i> DELETE</a>--}}
                    </div>

                    <nav class="breadcrumb-container d-none d-sm-block d-lg-inline-block" aria-label="breadcrumb">
                        <ol class="breadcrumb pt-0">
                            <li class="breadcrumb-item">
                                <a href="{{\App\Components\Helper::dashboardLink()}}">{{__('Dashboard')}}</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{route('user.index')}}">{{__('Users')}}</a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">{{__($user->name)}}</li>
                        </ol>
                    </nav>
                    <div class="separator mb-5"></div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-lg-12 col-md-12 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <table class="table">
                                <tbody>
                                    <tr>
                                        <th>Name</th>
                                        <td>{{__($user->name)}}</td>
                                    </tr>
                                    <tr>
                                        <th>Username</th>
                                        <td>{{__($user->username)}}</td>
                                    </tr>
                                    <tr>
                                        <th>Email</th>
                                        <td>{{__($user->email)}}</td>
                                    </tr>
                                    <tr>
                                        <th>Country</th>
                                        <td>{{__($user->country?->name)}}</td>
                                    </tr>
                                    <tr>
                                        <th>Plan</th>
                                        <td>{{__($user->plan?->name)}}</td>
                                    </tr>
                                    <tr>
                                        <th>API Key</th>
                                        <td>{{__($user->api_key)}} <a onclick="return confirm('Are you sure?')" href="{{route('user.show',$user->id)}}?type=re-api" class="text-primary" title="Re-Generate" style="font-weight: bold;">Re-Generate</a></td>
                                    </tr>
                                    <tr>
                                        <th>Login Status</th>
                                        <td><?=\App\Components\Helper::onOffButton($user,'is_active')?></td>
                                    </tr>
                                    <tr>
                                        <th>API Enabled</th>
                                        <td><?=\App\Components\Helper::onOffButton($user,'is_api_enabled')?></td>
                                    </tr>
                                    <tr>
                                        <th>API Protection</th>
                                        <td><?=\App\Components\Helper::onOffButton($user,'is_api_protection')?></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

@endsection
