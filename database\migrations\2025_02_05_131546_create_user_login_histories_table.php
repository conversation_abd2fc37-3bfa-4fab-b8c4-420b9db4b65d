<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('user_login_histories', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->foreignId('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreignId('country_id')
                ->default(\App\Constants\CommonConstants::DEFAULT_COUNTRY)
                ->references('id')->on('countries')->onDelete('cascade');
            $table->integer('is_api')->default(\App\Constants\CommonConstants::NO)->comment('0=no 1=yes')->index();
            $table->createdAtTime();
            $table->ipAddresses();
            $table->userAgent();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_login_histories');
    }
};
