<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use App\Casts\AmountCast;
use App\Constants\CommonConstants;
use Illuminate\Database\Eloquent\Model;

class Transaction extends BaseModel
{
    protected $fillable = [
        'user_id',
        'type_id',
        'debit_amount',
        'credit_amount',
        'particular',
        'ref_data',
        'time',
    ];

    protected $casts = [
        'debit_amount' => AmountCast::class,
        'credit_amount' => AmountCast::class,
    ];

    public function onCreating()
    {
        if(!$this->time) {
            $this->time=date(CommonConstants::PHP_DATE_FORMAT);
        }
        parent::onCreating(); // TODO: Change the autogenerated stub
    }

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function type()
    {
        return $this->hasOne(TransactionType::class, 'id', 'type_id');
    }

    public static function saveTransaction($user_id,$type_id,$is_debit,$amount,$particular,$data=null) {
        try {
            if(is_array($data)){
                $data= json_encode($data);
            }
            $model=new Transaction();
            $model->user_id=$user_id;
            $model->type_id=$type_id;
            if($is_debit) {
                $model->debit_amount=round($amount,CommonConstants::USD_PRECISION);
            } else {
                $model->credit_amount=round($amount,CommonConstants::USD_PRECISION);
            }
            $model->particular=$particular;
            if($data) {
                if(is_array($data)) {
                    $data=json_encode($data);
                }
                $model->ref_data=$data;
            }
            if($model->save()) {
                return true;
            } else {
                throw new \Exception('Unable to save user transaction');
            }
        } catch (\Exception $e) {
            Log::insertLog([
                'user_id'=>CommonConstants::ADMINISTRATIVE,
                'type'=>'transaction_save_error',
                'particulars'=>'Unable to record transaction',
                'data'=>['user_id'=>$user_id,'type_id'=>$type_id,'is_debit'=>$is_debit,'amount'=>$amount,'particular'=>$particular,'data'=>$data,'message'=>$e->getMessage()],
            ]);
        }
        return false;
    }
}
