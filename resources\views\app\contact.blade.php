<?php

use App\Components\Helper;

?>
@extends(\App\Components\Helper::getLayoutForUser())
@section('content')
    <style>
        .select2-results__options {
            max-height: 300px !important;
            overflow: auto !important;
        }
    </style>
    <main>
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <h1>Contact Us</h1>
                    <nav class="breadcrumb-container d-none d-sm-block d-lg-inline-block" aria-label="breadcrumb">
                        <ol class="breadcrumb pt-0">
                            <li class="breadcrumb-item">
                                <a href="{{\App\Components\Helper::dashboardLink()}}">Home</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{\App\Components\Helper::dashboardLink()}}">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">Contact Us</li>
                        </ol>
                    </nav>
                    <div class="separator mb-5"></div>
                </div>

                <div class="col-lg-12 col-xl-8 mx-auto">
                    <div class="row">
                        <div class="col-md-12 mb-4">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Contact Us</h5>
                                    <div class="dashboard-quick-post">
                                        <form id="w0" class="form" action="" method="post">
                                            @csrf
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <div class="form-group field-contactdetails-email required">
                                                        <div class="controls"><label class=""
                                                                                     for="contactdetails-email">Email</label><input
                                                                type="text" id="contactdetails-email"
                                                                class="form-control" name="email"
                                                                value="" placeholder="Email"
                                                                >
                                                            <p class="help-block help-block-error"></p></div>
                                                    </div>
                                                </div>
                                                <div class="col-md-12">
                                                    <div class="form-group field-contactdetails-subject required">
                                                        <div class="controls"><label class=""
                                                                                     for="contactdetails-subject">Subject</label><input
                                                                type="text" id="contactdetails-subject"
                                                                class="form-control" name="subject"
                                                                placeholder="Subject" autocomplete="off"
                                                                aria-required="true">
                                                            <p class="help-block help-block-error"></p></div>
                                                    </div>
                                                </div>
                                                <div class="col-12">
                                                    <div class="form-group field-contactdetails-message required">
                                                        <div class="controls"><label class=""
                                                                                     for="contactdetails-message">Message</label><textarea
                                                                id="contactdetails-message" class="form-control"
                                                                name="message" rows="10"
                                                                placeholder="Message" autocomplete="off"
                                                                aria-required="true"></textarea>
                                                            <p class="help-block help-block-error"></p></div>
                                                    </div>
                                                </div>

                                            </div>

                                            <div class="row">
                                                <div class="col-12">
                                                    <button type="submit" class="btn btn-primary">
                                                        Send Request
                                                    </button>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
@endsection
@section('pageJs')



@endsection
