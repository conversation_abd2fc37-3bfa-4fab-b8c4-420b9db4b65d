<?php

namespace App\Http\Controllers;

use App\Base\Model\BaseModel;
use App\Components\Helper;
use App\Constants\CommonConstants;
use App\Constants\UserConstants;
use App\Models\Service;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Yajra\DataTables\Facades\DataTables;

class ServiceController extends Controller
{
    public function index()
    {
        return view('service.index');
    }

    public function show(Service $service,Request $request)
    {
        return redirect()->route('service.index');
        return view('service.show', compact('service'));
    }

    public function create()
    {
        $service=new Service();
        $service->custom_id=Service::generateCustomID();
        $service->display_order=Service::generateDisplayOrder();
        return view('service.create', compact('service'));
    }

    public function edit(Service $service)
    {
        return view('service.edit', compact('service'));
    }

    public function store(Request $request)
    {
        $service = new Service();
        return $this->save($request, $service);
    }

    public function update(Request $request, Service $service)
    {
        return $this->save($request, $service);
    }

    private function save(Request $request, Service $service)
    {
        $isNewRecord = true;
        if ($service->id != null) {
            $isNewRecord = false;
        }

        $rules = [
            'operating_system_id' => ['required', 'integer'],
            'custom_id' => ['required', 'integer',Rule::unique('services')->ignore($service->id)],
            'category_id' => ['required', 'integer'],
            'provider_id' => ['required', 'integer'],
            'display_order' => ['required', 'integer'],
            'provider_service_id' => ['required', 'integer'],
            'name' => ['required', 'string'],
            'order_format' => ['required', 'string'],
            'download_format' => ['required', 'string'],
            'price' => ['required','numeric'],
        ];

        if ($isNewRecord) {

        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            if ($isNewRecord) {
                return redirect()->route('service.create')->withErrors($validator)->withInput();
            } else {
                return redirect()->route('service.edit', $service->id)->withErrors($validator)->withInput();
            }
        }

        $service->operating_system_id = (int)$request->input('operating_system_id');
        $service->category_id = (int)$request->input('category_id');
        $service->custom_id = (int)$request->input('custom_id');
        $service->provider_id = (int)$request->input('provider_id');
        $service->display_order = (int)$request->input('display_order');
        $service->provider_service_id = (int)$request->input('provider_service_id');
        $service->name = $request->input('name');
        $service->is_active = (int)$request->input('is_active');
        $service->price = trim($request->input('price'));
        $service->order_format = trim($request->input('order_format'));
        $service->download_format = trim($request->input('download_format'));
        if ($isNewRecord) {
            $service->save();
        } else {
            $service->update();
        }

        \App\Models\Service::generateCacheFile();
        resetServicesCache();
        return redirect()->route('service.index')->with('success', 'Service saved successfully.');
    }

    public function dataTable(Request $request)
    {
        try {
            if ($request->ajax()) {
                $query = Service::query();
                BaseModel::buildFilterQuery($query, [
                    'q' => ['name','price'],
                    'operating_system_id',
                    'category_id',
                    'provider_id',
                    'is_active',
                    'type',
                ]);
                return Datatables::eloquent($query)
                    ->addColumn('checkboxes', function ($row) {
                        return '<input type="checkbox" name="pdr_checkbox[]" class="pdr_checkbox" value="' . $row->id . '" />';
                    })
                    ->addColumn('operating_system_id', function ($row) {
                        return $row->operatingSystem->displayName;
                    })
                    ->addColumn('category_id', function ($row) {
                        return $row->category->displayName;
                    })
                    ->addColumn('provider_id', function ($row) {
                        $name=$row->provider->displayName;
                        if($row->providerService) {
                            $name.=' - '.$row->providerService->displayName;;
                        }
                        return $name;
                    })
                    ->addColumn('type', function ($row) {
                        return $row->typeName;
                    })
                    ->addColumn('price', function ($row) {
                        return Helper::editPopupStructure($row,'price',null,'Price (in $)');
                    })
                    ->addColumn('custom_id', function ($row) {
                        return Helper::editPopupStructure($row,'custom_id',null,'Custom ID');
                    })
                    ->addColumn('display_order', function ($row) {
                        return Helper::editPopupStructure($row,'display_order');
                    })
                    ->addColumn('name', function ($row) {
                        return Helper::editPopupStructure($row,'name');
                    })
                    ->addColumn('created_at', function ($row) {
                        return Helper::displayTime($row->created_at);
                    })
                    ->addColumn('updated_at', function ($row) {
                        return Helper::displayTime($row->updated_at);
                    })
                    ->addColumn('is_active', function ($row) use ($query) {
                        $columnName = 'is_active';
                        return Helper::onOffButton($row, $columnName, $query);
                    })
                    ->addColumn('actions', function ($row) {
                        $buttons = [];
                        //$buttons['Login']=['url' => route('user.loginAs', $row->id), 'icon' => 'las la-sign-in-alt'];
                        //$buttons['view'] = ['url' => route('service.show', $row->id)];
                        $buttons['edit'] = ['url' => route('service.edit', $row->id)];
                        return Helper::getActionButtons($buttons);
                    })
                    ->rawColumns(['checkboxes', 'name', 'is_active','custom_id', 'price','sid', 'is_chinese_result','display_order', 'actions'])
                    ->make(true);
            }
        } catch (\Exception $e) {
            //print_r(['message'=>$e->getMessage(),'trace'=>$e->getTraceAsString()]);
            die();
        }
    }
}
