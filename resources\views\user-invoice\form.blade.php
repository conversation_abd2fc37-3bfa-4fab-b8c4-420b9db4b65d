<div class="row mb-4">
    <div class="col-lg-12 col-md-12 mb-4">
        <div class="card">
            <div class="card-body">

<div class="rounded-16 form-box bg-white -dark-bg-dark-1 shadow-4 h-100">
    <form action="{{ $userInvoice->id ==null ? route('user-invoice.store') : route('user-invoice.update', $userInvoice) }}" method="POST"
          class="normal-form">
        @csrf

        @if( $userInvoice->id != null )
            @method('PUT')
        @endif

        @if( $userInvoice->id == null )
            <div class="row">
                <div class="col-md-8 col-12">
                    <div class="form-group required-field text-left @error('user_id') is-invalid @enderror">
                        <label for="user_id">{{ __('User') }}</label>
                        <select required name="user_id" id="user_id" class="form-control filterField search-user-ajax">
                            <option value="">Search Username</option>
                        </select>
                        @error('user_id')
                        <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                        @enderror
                    </div>
                </div>

                <div class="col-md-4 col-12">
                    <div class="form-group required-field text-left @error('amount') is-invalid @enderror">
                        <label for="amount">{{ __('Amount') }}</label>
                        <input required id="amount" type="text" class="form-control @error('amount') is-invalid @enderror"
                               name="amount" placeholder="{{ __('Enter amount') }}"
                               value="{{old('amount', $userInvoice->amount)}}">
                        @error('amount')
                        <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                        @enderror
                    </div>
                </div>

                <div class="col-md-8 col-12">
                    <div class="form-group required-field text-left @error('particular') is-invalid @enderror">
                        <label for="particular">{{ __('Particular') }}</label>
                        <input required id="particular" type="text" class="form-control @error('particular') is-invalid @enderror"
                               name="particular" placeholder="{{ __('Enter particular') }}"
                               value="{{old('particular', $userInvoice->particular)}}">
                        @error('particular')
                        <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                        @enderror
                    </div>
                </div>

                <div class="col-md-4 col-12">
                    <div class="form-group required-field text-left @error('is_paid') is-invalid @enderror">
                        <label for="is_paid">{{ __('Paid Invoice') }}</label>
                        <select name="is_paid" id="is_paid" class="form-control @error('is_paid') is-invalid @enderror">
                            <option <?=(!$userInvoice->is_paid ? 'selected=""' : '')?> value="<?=\App\Constants\CommonConstants::NO?>">No</option>
                            <option <?=($userInvoice->is_paid ? 'selected=""' : '')?> value="<?=\App\Constants\CommonConstants::YES?>">Yes</option>
                        </select>
                        @error('is_paid')
                        <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                        @enderror
                    </div>
                </div>

            </div>
        @else
            <div class="row">

                <div class="col-md-6 col-12">
                    <div class="form-group required-field text-left @error('amount') is-invalid @enderror">
                        <label for="amount">{{ __('Amount') }}</label>
                        <input required id="amount" type="text" class="form-control @error('amount') is-invalid @enderror"
                               name="amount" placeholder="{{ __('Enter amount') }}"
                               value="{{old('amount', $userInvoice->amount)}}">
                        @error('amount')
                        <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                        @enderror
                    </div>
                </div>

                <div class="col-md-6 col-12">
                    <div class="form-group required-field text-left @error('is_paid') is-invalid @enderror">
                        <label for="is_paid">{{ __('Paid Invoice') }}</label>
                        <select name="is_paid" id="is_paid" class="form-control @error('is_paid') is-invalid @enderror">
                            <option <?=(!$userInvoice->is_paid ? 'selected=""' : '')?> value="<?=\App\Constants\CommonConstants::NO?>">No</option>
                            <option <?=($userInvoice->is_paid ? 'selected=""' : '')?> value="<?=\App\Constants\CommonConstants::YES?>">Yes</option>
                        </select>
                        @error('is_paid')
                        <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                        @enderror
                    </div>
                </div>

                <div class="col-md-12 col-12">
                    <div class="form-group required-field text-left @error('particular') is-invalid @enderror">
                        <label for="particular">{{ __('Particular') }}</label>
                        <input required id="particular" type="text" class="form-control @error('particular') is-invalid @enderror"
                               name="particular" placeholder="{{ __('Enter particular') }}"
                               value="{{old('particular', $userInvoice->particular)}}">
                        @error('particular')
                        <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                        @enderror
                    </div>
                </div>
                <input type="hidden" name="user_id" value="{{$userInvoice->user_id}}">
            </div>
        @endif

        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-12 formBtn">
                @if( $userInvoice->id == null )
                    <button type="submit" class="btn btn-primary">{{ __('Save') }}</button>
                @else
                    <button type="submit" class="btn btn-primary">{{ __('Update') }}</button>
                @endif
            </div>
        </div>

    </form>
</div>
            </div>
        </div>
    </div>
</div>

@section('pageJs')

    <script>

    </script>

@endsection
