<?php

namespace App\Base\Provider;

use App\Components\Helper;
use App\Constants\CommonConstants;
use App\Models\Log;

class BaseProvider
{
    const SERVICE_DOWN_MESSAGE = "Invalid service or down for maintenance";
    const SERVICE_MAINTENANCE_MESSAGE = "Service down for maintenance";
    const DEFAULT_MESSAGE = "Wrong IMEI/SN or down for maintenance";
    public static $providerID = null;
    public static $providerDetails = null;
    public static $providerServiceDetails = null;

    public static function validateRequest($orderModel) {
        $providerDetails=static::fetchProviderDetails();
        $serviceID=(isset($orderModel->provider_service_id) ? $orderModel->provider_service_id : null);
        if(!$providerDetails || !$serviceID) {
            return static::failedResult();
        }
        $providerServiceDetails=(array_key_exists($serviceID,$providerDetails['services']) ? $providerDetails['services'][$serviceID] : null);
        if(!$providerServiceDetails) {
            return static::failedResult(static::SERVICE_DOWN_MESSAGE);
        }

        if(!isset($orderModel->imei) || trim($orderModel->imei)=="") {
            return static::failedResult("Invalid IMEI entered");
        }

        if($providerServiceDetails['type']>0) {
            switch ($providerServiceDetails['type']) {
                case CommonConstants::TYPE_IMEI:
                    if (!is_numeric($orderModel->imei)) {
                        return static::failedResult("Only IMEI supported");
                    }
                    break;
                case CommonConstants::TYPE_SN:
                    if (is_numeric($orderModel->imei)) {
                        return static::failedResult("Only SN supported");
                    }
                    break;
            }
        }

        static::$providerDetails=$providerDetails;
        static::$providerServiceDetails=$providerServiceDetails;
        return true;
    }

    public static function fetchProviderDetails() {
        try {
            $cacheDirectoryName=base_path().DIRECTORY_SEPARATOR.CommonConstants::CACHE_DIRECTORY_NAME;
            $cacheFileName=$cacheDirectoryName.DIRECTORY_SEPARATOR."providers.json";
            if(is_file($cacheFileName)) {
                $records=json_decode(file_get_contents($cacheFileName),true);
                if(is_array($records) && count($records)>0) {
                    if(isset(static::$providerID)) {
                        $providerID=static::$providerID;
                        if(array_key_exists($providerID,$records)) {
                            return $records[$providerID];
                        }
                    }
                }
            }
        } catch (\Exception $e) {

        }
        return null;
    }

    public static function successResult($result,$data=null) {
        if($data) {
            return ['success'=>true,'result'=>$result,'data'=>$data];
        }
        return ['success'=>true,'result'=>$result];
    }

    public static function failedResult($message=null,$data=null) {
        if(!$message) {
            $message='Service down or not available';
        }
        if($data) {
            return ['error'=>$message,'data'=>$data];
        }
        return ['error'=>$message];
    }

    public static function defaultResult($message=null,$data=null) {
        if(!$message) {
            $message='Invalid imei/sn or service down';
        }
        if($data) {
            return self::failedResult($message,$data);
        }
        return self::failedResult($message);
    }

    public static function getCsvReport($orderResult, $strip_tags = true, $change_key = true, $force_result = false)
    {
        $result = null;
        if ($orderResult != "" || $force_result) {
            $final_result = array();
            $ex = explode("<br />", $orderResult);
            if (count($ex) <= 1) {
                $ex = explode("<br>", $orderResult);
                if (count($ex) <= 1) {
                    $ex = explode("<br/>", $orderResult);
                    if (count($ex) <= 1) {
                        $ex = explode("<div>", $orderResult);
                    }
                }
            }
            if (count($ex) >= 1) {
                foreach ($ex as $ee) {
                    $arr = explode("<br/>", $ee);
                    foreach ($arr as $e) {
                        if ($strip_tags) {
                            $e = strip_tags($e);
                        }
                        $exx = explode(":", trim($e));
                        if (count($exx) >= 2) {
                            if ($change_key) {
                                $key = trim(strtoupper($exx[0]));
                            } else {
                                $key = trim($exx[0]);
                            }
                            $value = "";
                            if (!$strip_tags) {
                                //new code start
                                $haystack = trim($e);
                                $needle = trim($exx[0]);
                                $pos = strpos($haystack, $needle);
                                $replace = "";
                                if ($pos !== false) {
                                    $value = substr_replace($haystack, $replace, $pos, strlen($needle));
                                }
                                //new code end

                                $value = preg_replace('/:/', "", $value, 1);
                            } else {
                                for ($i = 1; $i < count($exx); $i++) {
                                    $value .= trim($exx[$i]);
                                }
                            }
                            $final_result[$key] = $value;
                        }
                    }
                }
            } else {
                $final_result['MESSAGE'] = $orderResult;
            }

            if (count($final_result) > 0) {
                $result = $final_result;
            }
        }
        return $result;
    }

    public static function translateChineseWord($word, $modify = true)
    {
        try {
            $new_word = $word;

            if ($modify) {
                $key = strtolower($word);
                $key = str_replace(" ", "", $key);
            } else {
                $key = $word;
            }

            $key = trim($key);


            $change_text = false;
            $change_word = $new_word;
            if (strlen($new_word) != strlen(strip_tags($new_word))) {
                $change_text = true;
                $new_word = trim(strip_tags($new_word));
                if ($modify) {
                    $key = strtolower($new_word);
                    $key = str_replace(" ", "", $key);
                } else {
                    $key = $new_word;
                }
                $key = trim($key);
            }

            $arr = array(
                '串号' => 'IMEI',
                '串号2' => 'IMEI2',
                '串号' => 'IMEI Number',
                '串号/序列号' => 'IMEI/SN',
                '设备型号' => 'Device Model',
                '设备代码' => 'Device Code',
                '设备' => 'Device',
                '型号' => 'Model',
                '品牌' => 'BrandName',
                '生产商' => 'Manufacturer',
                '开启' => 'ON',
                '关闭' => 'OFF',
                '激活锁' => 'Find My iPhone',
                '序列号' => 'Serial',
                '序列号' => 'Serial Number',
                '状态' => 'iCloud',
                '黑白状态' => 'iCloud Status',
                '黑名单' => 'Lost',
                '白名单' => 'Clean',
                '网络锁' => 'SimLock',
                '网络锁' => 'SIM-Lock',
                '有锁' => 'Locked',
                '无锁' => 'Unlocked',
                '过保' => 'Out Of Warranty',
                '销售国家' => 'Country Of Sale',
                '激活状态' => 'Activation Status',
                '保修状态' => 'Warranty Status',
                '激活日期' => 'Activation Date',
                '预估购买日期' => 'Purchase Date',
                '生产日期' => 'Manufacture Date',
                '电话支持' => 'Telephone Technical Support',
                '电话支持到期日' => 'Telephone Technical Support Expiration Date',
                '保修服务范围' => 'Repairs And Service Coverage',
                '保修到期' => 'Repairs And Service Expiration Date',
                '剩余天数' => 'Repairs And Service Expires In',
                '有效的购买日期' => 'Valid Purchase Date',
                '是否官换机' => 'Replaced Device',
                '借出设备' => 'Loaner Device',
                '运营商' => 'Carrier',
                '国家' => 'Country',
                '黑白状态' => 'Blacklist Status',
                '移动设备识别码' => 'MEID',
                '预计购买日期' => 'Estimated Purchase Date',
                '黑名单' => 'Blacklisted',
                '黑名单日期' => 'Blacklisted On',
                '黑名单国家' => 'Blacklisted Country',
                '黑名单' => 'Blacklisted',
                '年限' => 'Unit Age',
                '组装' => 'Assembled In',
                '下次策略编号' => 'Next Policy ID',
                '删除者' => 'Removed By',
                '删除国家' => 'Removed Country',
                '删除日期' => 'Removed On',

                '金色' => 'Gold',
                '深空灰色' => 'Space Gray',
                '深空灰' => 'Space Gray',
                '红色' => 'Red',
                '黑色' => 'Black',
                '磨砂黑色' => 'Black',
                '暗夜绿色' => 'Midnight Green',
                '银色' => 'Silver',
                '玫瑰金色' => 'Rose Gold',
                '玫瑰金' => 'Rose Gold',
                '珊瑚色' => 'Coral',
                '亮黑色' => 'Jet Black',
                '白色' => 'White',
                '绿色' => 'Green',
                '紫色' => 'Purple',
                '蓝色' => 'Blue',
                '黄色' => 'Yellow',
                '已过期' => 'Expired',

                '金色' => 'Gold',
                '深空灰色' => 'Space Gray',
                '红色' => 'Red',
                '黑色' => 'Black',
                '暗夜绿色' => 'Midnight Green',
                '银色' => 'Silver',
                '玫瑰金色' => 'Rose Gold',
                '珊瑚色' => 'Coral',
                '亮黑色' => 'Jet Black',
                '白色' => 'White',
                '绿色' => 'Green',
                '紫色' => 'Purple',
                '蓝色' => 'Blue',
                '黄色' => 'Yellow',

                '苹果护理' => 'Apple Care',
                '翻新设备' => 'Refurbished Device',
                '活跃的' => 'Active',
                '不活动' => 'Inactive',
                '激活' => 'Activated',
                '不' => 'No',
                '是' => 'Yes',
                '未激活' => 'Unactivated',
                '电话技术支持过期' => 'Telephone Technical Support Expire In',
                '天' => 'Days',
                'GSMA状态' => 'GSMA Status',
                '期满' => 'Expired',

                '产品供应'=>'Product Availability',
                '是否官翻机'=>'Refurbished Device',
                '是否演示/样机'=>'Demonstration or Prototype',
                '保卡日期'=>'Warranty Date',
                '保修结束'=>'End Of Warranty',
                '电子保卡未激活'=>'Electronic warranty card not activated',
            );

            //$arr = array_replace($arr, Translation::fetchTranslation('chinese'));
            if (array_key_exists($key, $arr)) {
                if ($change_text) {
                    $new_word = str_replace($new_word, $arr[$key], $change_word);
                } else {
                    $new_word = $arr[$key];
                }
            } else {
                if ($change_text) {
                    $new_word = $change_word;
                }
            }
            return $new_word;
        } catch (\Exception $e) {
            return $word;
        }
    }

    public static function translateWord($word)
    {
        try {
            $new_word = $word;
            $key = strtolower($word);
            $key = str_replace(" ", "", $key);
            $key = trim($key);

            $change_text = false;
            $change_word = $new_word;
            if (strlen($new_word) != strlen(strip_tags($new_word))) {
                $change_text = true;
                $new_word = trim(strip_tags($new_word));
                $key = strtolower($new_word);
                $key = str_replace(" ", "", $key);
                $key = trim($key);
            }

            $arr = array(
                //'imei'=>'IMEI (串号)',
                'imei' => '串号',
                //'imei2'=>'IMEI2 (串号2)',
                'imei2' => '串号2',
                //'imeinumber'=>'IMEI Number (串号)',
                'imeinumber' => '串号',
                //'imei/sn'=>'IMEI/SN (串号/序列号)',
                'imei/sn' => '串号/序列号',
                //'modelname'=>'ModelName (型号)',
                'modelname' => '型号',
                //'device' => 'Device (设备)',
                'device' => '设备',
                //'modelnumber'=>'Model (型号)',
                'modelnumber' => '型号',
                //'brandname'=>'BrandName (品牌)',
                'brandname' => '品牌',
                //'manufacturer'=>'Manufacturer (生产商)',
                'manufacturer' => '生产商',
                //'on'=>'ON (开启)',
                'on' => '开启',
                //'off'=>'OFF(关闭)',
                'off' => '关闭',
                //'findmyiphone'=>'Find My iPhone (激活锁)',
                'findmyiphone' => '激活锁',
                //'serial'=>'Serial (序列号)',
                'serial' => '序列号',
                //'sn'=>'Serial (序列号)',
                'sn' => '序列号',
                //'serialnumber'=>'Serial Number (序列号)',
                'serialnumber' => '序列号',
                //'icloud'=>'iCloud (状态)',
                'icloud' => '状态',
                //'icloudstatus'=>'iCloud Status (黑白状态)',
                'icloudstatus' => '黑白状态',
                //'lost'=>'Lost (黑名单)',
                'lost' => '黑名单',
                //'clean'=>'Clean (白名单)',
                'clean' => '白名单',
                //'model'=>'Model (型号)',
                'model' => '型号',
                //'simlock'=>'SimLock (网络锁)',
                'simlock' => '网络锁',
                //'sim-lock'=>'SIM-Lock (网络锁)',
                'sim-lock' => '网络锁',
                //'locked'=>'Locked (有锁)',
                'locked' => '有锁',
                //'unlocked'=>'Unlocked (无锁)',
                'unlocked' => '无锁',
                //'activationstatus'=>'Activation Status (激活状态)',
                'activationstatus' => '激活状态',
                //'purchasedate'=>'Purchase Date (预估购买日期)',
                'purchasedate' => '预估购买日期',
                //'manufacturedate'=>'Manufacture Date (生产日期)',
                'manufacturedate' => '生产日期',
                //'telephonetechnicalsupport'=>'Telephone Technical Support (电话支持)',
                'telephonetechnicalsupport' => '电话支持',
                //'telephonetechnicalsupportexpirationdate'=>'Telephone Technical Support Expiration Date (电话支持到期日)',
                'telephonetechnicalsupportexpirationdate' => '电话支持到期日',
                //'repairsandservicecoverage'=>'Repairs And Service Coverage (保修服务范围)',
                'repairsandservicecoverage' => '保修服务范围',
                //'repairsandserviceexpirationdate'=>'Repairs And Service Expiration Date (保修到期)',
                'repairsandserviceexpirationdate' => '保修到期',
                //'repairsandserviceexpiresin'=>'Repairs And Service Expires In (剩余天数)',
                'repairsandserviceexpiresin' => '剩余天数',
                //'validpurchasedate'=>'Valid Purchase Date (有效的购买日期)',
                'validpurchasedate' => '有效的购买日期',
                //'replaceddevice'=>'Replaced Device (是否官换机)',
                'replaceddevice' => '是否官换机',
                //'loanerdevice'=>'Loaner Device (借出设备)',
                'loanerdevice' => '借出设备',
                //'carrier'=>'Carrier (运营商)',
                'carrier' => '运营商',
                //'country'=>'Country (国家)',
                'country' => '国家',
                //'blackliststatus'=>'Blacklist Status (黑白状态)',
                'blackliststatus' => '黑白状态',
                //'meid'=>'MEID (移动设备识别码)',
                'meid' => '移动设备识别码',
                //'estimatedpurchasedate'=>'Estimated Purchase Date (预计购买日期)',
                'estimatedpurchasedate' => '预计购买日期',
                //'blacklisted'=>'Blacklisted (黑名单)',
                'blacklisted' => '黑名单',
                //'blacklistedon'=>'Blacklisted On (黑名单日期)',
                'blacklistedon' => '黑名单日期',
                //'blacklistedcountry'=>'Blacklisted Country (黑名单国家)',
                'blacklistedcountry' => '黑名单国家',
                //'blacklist'=>'Blacklisted (黑名单)',
                'blacklist' => '黑名单',
                //'unitage' => 'Unit Age (年限)',
                'unitage' => '年限',
                //'assembledin' => 'Assembled In (组装)',
                'assembledin' => '组装',
                //'nextpolicyid' => 'Next Policy ID (下次策略编号)',
                'nextpolicyid' => '下次策略编号',
                //'removedby' => 'Removed By (删除者)',
                'removedby' => '删除者',
                //'removedcountry' => 'Removed Country (删除国家)',
                'removedcountry' => '删除国家',
                //'removedon' => 'Removed On (删除日期)',
                'removedon' => '删除日期',
                '金色' => 'Gold',
                '深空灰色' => 'Space Gray',
                '深空灰' => 'Space Gray',
                '红色' => 'Red',
                '黑色' => 'Black',
                '磨砂黑色' => 'Black',
                '暗夜绿色' => 'Midnight Green',
                '银色' => 'Silver',
                '玫瑰金色' => 'Rose Gold',
                '玫瑰金' => 'Rose Gold',
                '珊瑚色' => 'Coral',
                '亮黑色' => 'Jet Black',
                '白色' => 'White',
                '绿色' => 'Green',
                '紫色' => 'Purple',
                '蓝色' => 'Blue',
                '黄色' => 'Yellow',
                '已过期' => 'Expired',

                //'gold' => 'Gold (金色)',
                'gold' => '金色',
                //'spacegray' => 'Space Gray (深空灰色)',
                'spacegray' => '深空灰色',
                //'red' => 'Red (红色)',
                'red' => '红色',
                //'black' => 'Black (黑色)',
                'black' => '黑色',
                //'midnightgreen' => 'Midnight Green (暗夜绿色)',
                'midnightgreen' => '暗夜绿色',
                //'silver' => 'Silver (银色)',
                'silver' => '银色',
                //'rosegold' => 'Rose Gold (玫瑰金色)',
                'rosegold' => '玫瑰金色',
                //'coral' => 'Coral (珊瑚色)',
                'coral' => '珊瑚色',
                //'jetblack' => 'Jet Black (亮黑色)',
                'jetblack' => '亮黑色',
                //'white' => 'White (白色)',
                'white' => '白色',
                //'green' => 'Green (绿色)',
                'green' => '绿色',
                //'purple' => 'Purple (紫色)',
                'purple' => '紫色',
                //'blue' => 'Blue (蓝色)',
                'blue' => '蓝色',
                //'yellow' => 'Yellow (黄色)',
                'yellow' => '黄色',


                'applecare' => '苹果护理',
                'refurbisheddevice' => '翻新设备',
                'active' => '活跃的',
                'inactive' => '不活动',
                'activated' => '激活',
                'no' => '不',
                'yes' => '是',
                'unactivated' => '未激活',
                'telephonetechnicalsupportexpirein' => '电话技术支持过期',
                'days' => '天',
                'gsmastatus' => 'GSMA状态',
                'expired' => '期满',
            );
            //$arr = array_replace($arr, Translation::fetchTranslation('english'));
            if (array_key_exists($key, $arr)) {
                if ($change_text) {
                    $new_word = str_replace($new_word, $arr[$key], $change_word);
                } else {
                    $new_word = $arr[$key];
                }
            } else {
                if ($change_text) {
                    $new_word = $change_word;
                }
            }
            return $new_word;
        } catch (\Exception $e) {
            return $word;
        }
    }

    public static function isValidModelName($model) {
        $isValid=true;
        try {
            $model=trim($model);
            if($model=="") {
                $isValid=false;
            }

            if(stripos($model,"gb-")!==false) {
                $exp=explode(" ",$model);
                if(count($exp)>0) {
                    foreach ($exp as $ex) {
                        if(stripos($ex,"gb-")!==false) {
                            $gb=strtolower(trim($ex));
                            $parts=explode("gb-",$gb);
                            if(count($parts)>=2) {
                                if(trim($parts[1])!="") {
                                    $isValid = true;
                                } else {
                                    $isValid=false;
                                }
                            } else {
                                $isValid=false;
                            }
                            break;
                        }
                    }
                }
            }
        } catch (\Exception $e) {

        }
        return $isValid;
    }

    public static function fetchModelNameFromAPI($imeiSn) {
        $model="";
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://python.goimeicheck.com/api.php?api_key=hghkvhvhvhjcgvhjcgh&type=model&imei='.$imeiSn,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYHOST => FALSE,
            CURLOPT_SSL_VERIFYPEER => FALSE,
            CURLOPT_CONNECTTIMEOUT => 30,
            CURLOPT_TIMEOUT => 60,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        if($response && $response!="") {
            try {
                $decode=json_decode($response,true);
                if(is_array($decode) && array_key_exists('status',$decode)) {
                    if($decode['status']=="success") {
                        if(array_key_exists('result',$decode)) {
                            if(array_key_exists('Model',$decode['result'])) {
                                $model=$decode['result']['Model'];
                            }
                        }
                    }
                }
            } catch (\Exception $e) {

            }
        }
        return $model;
    }
}
