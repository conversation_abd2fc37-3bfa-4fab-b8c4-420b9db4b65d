<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('providers', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('api_url')->index();
            $table->string('api_key')->nullable()->index();
            $table->string('api_username')->nullable()->index();
            $table->integer('is_active')->default(\App\Constants\CommonConstants::YES)->index();
            $table->integer('is_dhru')->default(\App\Constants\CommonConstants::NO)->index();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('providers');
    }
};
