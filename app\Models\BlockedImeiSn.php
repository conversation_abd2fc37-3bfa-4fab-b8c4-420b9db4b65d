<?php

namespace App\Models;

use App\Base\Model\BaseModel;

class BlockedImeiSn extends BaseModel
{
    protected $fillable = [
        'imei_sn',
        'created_at',
    ];

    public static function isBlocked($imeiSn) {
        $model=self::query()->where('imei_sn','=',$imeiSn)->first();
        if($model) {
            return true;
        }
        return false;
    }

    public static function saveRecord($imeiSn) {
        $isAlreadyBlocked=self::isBlocked($imeiSn);
        if($isAlreadyBlocked) {
            return true;
        }

        $model=new BlockedImeiSn();
        $model->imei_sn=$imeiSn;
        if($model->save()) {
            return true;
        }

        return false;
    }
}
