<?php

namespace App\Components;

use App\Base\Provider\BaseProvider;
use App\Constants\CommonConstants;
use App\Models\Log;
use App\Models\User;
use Illuminate\Support\Facades\Http;

class ProviderIUnlockTeamNew extends BaseProvider
{
    public static $providerID = CommonConstants::PROVIDER_IUNLOCK_TEAM_NEW;
    public static function placeOrder($orderModel) {
        $validate=self::validateRequest($orderModel);
        if($validate!==true) {
            return $validate;
        }

        $url = self::$providerDetails['api_url'] . "?key=".self::$providerDetails['api_key']."&username=".self::$providerDetails['api_username']."&imei=".$orderModel->imei."&service=".self::$providerServiceDetails['sid']."&ip=**************";
        $response = Http::get($url);
        $result=$response->body();

        if($result && $result!="") {
            return self::parseResult($orderModel,$result);
        }

        return self::defaultResult();
    }

    public static function parseResult($orderModel,$result) {
        try {
            if ($result == "" || $result == false
                || stripos($result, "Checking your browser")!==false || stripos($result, "error code")!==false) {
                return self::failedResult(self::SERVICE_MAINTENANCE_MESSAGE,$result);
            }

            $finalResult=null;
            $decode = json_decode($result, true);
            if (is_array($decode) && array_key_exists('status', $decode)) {
                if (strtolower($decode['status']) == "success") {
                    if (array_key_exists('response', $decode)) {
                        $finalResult=str_replace("➔","",$decode['response']);
                    }
                }
            } else if (is_array($decode) && array_key_exists('success', $decode)) {
                if ($decode['success']) {
                    if (array_key_exists('response', $decode)) {
                        $finalResult=str_replace("➔","",$decode['response']);
                    }
                }
            }
            if (!$finalResult) {
                return self::failedResult(self::DEFAULT_MESSAGE,$result);
            }

            $sid=self::$providerServiceDetails['sid'];
            switch ($sid) {
            }

            return self::successResult($finalResult, $result);
        } catch (\Exception $e) {

        }
        return self::defaultResult();
    }
}
