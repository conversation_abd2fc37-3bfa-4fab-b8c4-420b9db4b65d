<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use Illuminate\Database\Eloquent\Model;

class UserLoginHistory extends BaseModel
{
    protected $fillable = [
        'user_id',
        'country_id',
        'user_agent',
        'is_api',
        'created_at',
        'created_ip',
    ];

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function country()
    {
        return $this->hasOne(Country::class, 'id', 'country_id');
    }

    public static function saveHistory($request) {
        try {
            $model = new UserLoginHistory();
            $model->user_id = $request->user()->id;
            $model->user_agent = $request->userAgent();
            $model->created_ip = $request->ip();
            if ($model->save()) {

            }
        } catch (\Exception $e) {
            print_r($e->getMessage());die;
        }
    }
}
