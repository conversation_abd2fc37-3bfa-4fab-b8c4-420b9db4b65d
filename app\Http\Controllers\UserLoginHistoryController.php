<?php

namespace App\Http\Controllers;

use App\Base\Model\BaseModel;
use App\Components\Helper;
use App\Constants\CommonConstants;
use App\Constants\UserConstants;
use App\Models\UserLoginHistory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Yajra\DataTables\Facades\DataTables;

class UserLoginHistoryController extends Controller
{
    public function index()
    {
        return view('user-login-history.index');
    }

    public function show(UserLoginHistory $userLoginHistory)
    {
        return redirect()->route('user-login-history.index');
        return view('user-login-history.show', compact('userLoginHistory'));
    }

    public function create()
    {
        return redirect()->route('user-login-history.index');
        $userLoginHistory=new UserLoginHistory();
        return view('user-login-history.create', compact('userLoginHistory'));
    }

    public function edit(UserLoginHistory $userLoginHistory)
    {
        return redirect()->route('user-login-history.index');
        return view('user-login-history.edit', compact('userLoginHistory'));
    }

    public function store(Request $request)
    {
        return redirect()->route('user-login-history.index');
        $userLoginHistory = new UserLoginHistory();
        return $this->save($request, $userLoginHistory);
    }

    public function update(Request $request, UserLoginHistory $userLoginHistory)
    {
        return redirect()->route('user-login-history.index');
        return $this->save($request, $userLoginHistory);
    }

    private function save(Request $request, UserLoginHistory $userLoginHistory)
    {
        $isNewRecord = true;
        if ($userLoginHistory->id != null) {
            $isNewRecord = false;
        }

        $rules = [
            'user_id' => ['required', 'integer'],
            'amount' => ['required', 'numeric',
                function ($attribute, $value, $fail) {
                    if (!empty($value) && $value<=0) {
                        $fail('Invalid amount entered');
                    }
                },
            ],
            'is_debit' => ['required', 'integer'],
            'is_paid' => ['required', 'integer'],
            'is_invoice' => ['required', 'integer'],
            'comments' => ['string'],
        ];

        if ($isNewRecord) {

        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            if ($isNewRecord) {
                return redirect()->route('user-login-history.create')->withErrors($validator)->withInput();
            } else {
                return redirect()->route('user-login-history.edit', $userLoginHistory->id)->withErrors($validator)->withInput();
            }
        }

        $ip = request()->ip();
        $ipDetails = Helper::fetchIpDetails($ip, true);

        $userLoginHistory->user_id = $request->input('user_id');
        $userLoginHistory->amount = $request->input('amount');
        $userLoginHistory->is_debit = (int)$request->input('is_debit');
        $userLoginHistory->is_paid = (int)$request->input('is_paid');
        $userLoginHistory->is_invoice = (int)$request->input('is_invoice');
        $userLoginHistory->comments = trim($request->input('comments'));
        if ($isNewRecord) {
            $userLoginHistory->save();
        } else {
            $userLoginHistory->update();
        }
        return redirect()->route('user-login-history.index')->with('success', 'Admin adjustment saved successfully.');
    }

    public function dataTable(Request $request)
    {
        try {
            if ($request->ajax()) {
                $query = UserLoginHistory::query();
                BaseModel::buildFilterQuery($query, [
                    'q' => ['created_ip','user_agent'],
                    'user_id',
                    'country_id',
                ]);
                return Datatables::eloquent($query)
                    ->addColumn('checkboxes', function ($row) {
                        return '<input type="checkbox" name="pdr_checkbox[]" class="pdr_checkbox" value="' . $row->id . '" />';
                    })
                    ->addColumn('user_id', function ($row) {
                        return $row->user->displayName;
                    })
                    ->addColumn('created_at', function ($row) {
                        return Helper::displayTime($row->created_at);
                    })
                    ->addColumn('country_id', function ($row) {
                        return $row->country->displayName;
                    })
                    ->rawColumns(['checkboxes', 'user_id', 'actions', 'is_invoice', 'is_paid'])
                    ->make(true);
            }
        } catch (\Exception $e) {
            //print_r(['message'=>$e->getMessage(),'trace'=>$e->getTraceAsString()]);
            die();
        }
    }
}
