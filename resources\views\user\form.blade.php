<div class="row mb-4">
    <div class="col-lg-12 col-md-12 mb-4">
        <div class="card">
            <div class="card-body">

<div class="rounded-16 form-box bg-white -dark-bg-dark-1 shadow-4 h-100">
    <form action="{{ $user->id ==null ? route('user.store') : route('user.update', $user) }}" method="POST"
          class="normal-form">
        @csrf

        @if( $user->id != null )
            @method('PUT')
        @endif

        <div class="row">
            <div class="col-md-4 col-12">
                <div class="form-group required-field text-left @error('name') is-invalid @enderror">
                    <label for="name">{{ __('Name') }}</label>
                    <input id="name" type="text"
                           class="form-control @error('name') is-invalid @enderror"
                           name="name" placeholder="{{ __('Enter Name') }}"
                           value="{{old('name', $user->name)}}">
                    @error('name')
                    <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                    @enderror
                </div>
            </div>

            <div class="col-md-4 col-12">
                <div class="form-group required-field text-left @error('username') is-invalid @enderror">
                    <label for="username">{{ __('Username') }}</label>
                    <input id="username" type="text" class="form-control @error('username') is-invalid @enderror"
                           name="username" placeholder="{{ __('Enter Username') }}"
                           value="{{old('username', $user->username)}}">
                    @error('username')
                    <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                    @enderror
                </div>
            </div>

            <div class="col-md-4 col-12">
                <div class="form-group required-field text-left @error('email') is-invalid @enderror">
                    <label for="email">{{ __('Email') }}</label>
                    <input id="email" type="email" class="form-control @error('email') is-invalid @enderror"
                           name="email" placeholder="{{ __('Enter Email') }}"
                           value="{{old('email', $user->email)}}">
                    @error('email')
                    <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                    @enderror
                </div>
            </div>

            <div class="col-md-4 col-12">
                <div class="form-group required-field text-left @error('is_api_enabled') is-invalid @enderror">
                    <label for="is_api_enabled">{{ __('API Enabled') }}</label>
                    <select name="is_api_enabled" id="is_api_enabled" class="form-control @error('is_api_enabled') is-invalid @enderror">
                        @foreach (\App\Constants\CommonConstants::YES_NO_PROPERTIES as $userStatus => $userStatusData)
                            <option
                                {{  $userStatus === $user->is_api_enabled ? 'selected="selected"': ''}} value="{{$userStatus}}">{{$userStatusData['text']}}</option>
                        @endforeach
                    </select>
                    @error('is_api_enabled')
                    <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                    @enderror
                </div>
            </div>

            <div class="col-md-4 col-12">
                <div class="form-group required-field text-left @error('is_api_protection') is-invalid @enderror">
                    <label for="is_api_protection">{{ __('API Protection') }}</label>
                    <select name="is_api_protection" id="is_api_protection" class="form-control @error('is_api_protection') is-invalid @enderror">
                        @foreach (\App\Constants\CommonConstants::YES_NO_PROPERTIES as $userStatus => $userStatusData)
                            <option
                                {{  $userStatus === $user->is_api_protection ? 'selected="selected"': ''}} value="{{$userStatus}}">{{$userStatusData['text']}}</option>
                        @endforeach
                    </select>
                    @error('is_api_protection')
                    <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                    @enderror
                </div>
            </div>

            <div class="col-md-4 col-12">
                <div class="form-group required-field text-left @error('is_active') is-invalid @enderror">
                    <label for="is_active">{{ __('Active') }}</label>
                    <select name="is_active" id="is_active" class="form-control @error('is_active') is-invalid @enderror">
                        @foreach (\App\Constants\CommonConstants::YES_NO_PROPERTIES as $userStatus => $userStatusData)
                            <option
                                {{  $userStatus === $user->is_active ? 'selected="selected"': ''}} value="{{$userStatus}}">{{$userStatusData['text']}}</option>
                        @endforeach
                    </select>
                    @error('is_active')
                    <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                    @enderror
                </div>
            </div>

            <div class="col-md-6 col-12">
                <div class="form-group required-field text-left @error('country_id') is-invalid @enderror">
                    <label for="country_id">{{ __('Country') }}</label>
                    <select name="country_id" id="country_id" class="form-control @error('country_id') is-invalid @enderror">
                        <option value="">Select</option>
                        @foreach (\App\Models\Country::defaultQuery()->get() as $country)
                            <option
                                {{  $country->id === $user->country_id ? 'selected="selected"': ''}} value="{{$country->id}}">{{$country->name}}</option>
                        @endforeach
                    </select>
                    @error('country_id')
                    <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                    @enderror
                </div>
            </div>

            <div class="col-md-6 col-12">
                <div class="form-group required-field text-left @error('plan_id') is-invalid @enderror">
                    <label for="plan_id">{{ __('Plan') }}</label>
                    <select name="plan_id" id="plan_id" class="form-control @error('plan_id') is-invalid @enderror">
                        <option value="">Select</option>
                        @foreach (\App\Models\Plan::defaultQuery()->get() as $plan)
                            <option
                                {{  $plan->id === $user->plan_id ? 'selected="selected"': ''}} value="{{$plan->id}}">{{$plan->name}}</option>
                        @endforeach
                    </select>
                    @error('plan_id')
                    <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                    @enderror
                </div>
            </div>

        </div>

        @if( $user->id == null )

            <hr>

            <div class="row">

                <div class="col-md-6 col-12">
                    <div class="form-group required-field text-left @error('password') is-invalid @enderror">
                        <label id="password">{{ __('New Password') }}</label>
                        <input id="password" type="password"
                               class="form-control @error('password') is-invalid @enderror" name="password"
                               value="{{ old('password') }}" placeholder="{{ __('Enter New Password') }}">
                        @error('password')
                        <span class="invalid-feedback" role="alert">
                                                        <strong>{{ $message }}</strong>
                                                    </span>
                        @enderror
                    </div>
                </div>

                <div class="col-md-6 col-12">
                    <div
                        class="form-group required-field text-left @error('password_confirmation') is-invalid @enderror">
                        <label id="password_confirmation">{{ __('Confirm Password') }}</label>
                        <input id="password_confirmation" type="password"
                               class="form-control @error('password_confirmation') is-invalid @enderror"
                               name="password_confirmation" value="{{ old('password_confirmation') }}"
                               placeholder="{{ __('Confirm Password') }}">
                        @error('password_confirmation')
                        <span class="invalid-feedback" role="alert">
                                                        <strong>{{ $message }}</strong>
                                                    </span>
                        @enderror
                    </div>
                </div>

            </div>

        @endif

        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-12 formBtn">
                @if( $user->id == null )
                    <button type="submit" class="btn btn-primary">{{ __('Save') }}</button>
                @else
                    <button type="submit" class="btn btn-primary">{{ __('Update') }}</button>
                @endif
            </div>
        </div>

    </form>
</div>
            </div>
        </div>
    </div>
</div>

@section('pageJs')

    <script>

    </script>

@endsection
