<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('categories', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->string('name')->unique();
            $table->integer('is_active')->default(\App\Constants\CommonConstants::YES)->index();
            $table->integer('display_order')->default(\App\Constants\CommonConstants::YES)->index();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('categories');
    }
};
