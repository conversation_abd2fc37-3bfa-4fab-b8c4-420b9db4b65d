<?php

namespace App\Http\Controllers;

use App\Base\Model\BaseModel;
use App\Components\Helper;
use App\Constants\CommonConstants;
use App\Constants\UserConstants;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Yajra\DataTables\Facades\DataTables;

class TransactionController extends Controller
{
    public function index()
    {
        return view('transaction.index');
    }

    public function show(Transaction $transaction,Request $request)
    {
        return redirect()->route('transaction.index');
        return view('transaction.show', compact('transaction'));
    }

    public function create()
    {
        return redirect()->route('transaction.index');
        $transaction=new Transaction();
        return view('transaction.create', compact('transaction'));
    }

    public function edit(Transaction $transaction)
    {
        return redirect()->route('transaction.index');
        return view('transaction.edit', compact('transaction'));
    }

    public function store(Request $request)
    {
        return redirect()->route('transaction.index');
        $transaction = new Transaction();
        return $this->save($request, $transaction);
    }

    public function update(Request $request, Transaction $transaction)
    {
        return redirect()->route('transaction.index');
        return $this->save($request, $transaction);
    }

    private function save(Request $request, Transaction $transaction)
    {
        $isNewRecord = true;
        if ($transaction->id != null) {
            $isNewRecord = false;
        }

        $rules = [
            'user_id' => ['required', 'integer'],
            'amount' => ['required', 'numeric',
                function ($attribute, $value, $fail) {
                    if (!empty($value) && $value<=0) {
                        $fail('Invalid amount entered');
                    }
                },
            ],
            'is_debit' => ['required', 'integer'],
            'is_paid' => ['required', 'integer'],
            'is_invoice' => ['required', 'integer'],
            'comments' => ['string'],
        ];

        if ($isNewRecord) {

        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            if ($isNewRecord) {
                return redirect()->route('transaction.create')->withErrors($validator)->withInput();
            } else {
                return redirect()->route('transaction.edit', $transaction->id)->withErrors($validator)->withInput();
            }
        }

        $ip = request()->ip();
        $ipDetails = Helper::fetchIpDetails($ip, true);

        $transaction->user_id = $request->input('user_id');
        $transaction->amount = $request->input('amount');
        $transaction->is_debit = (int)$request->input('is_debit');
        $transaction->is_paid = (int)$request->input('is_paid');
        $transaction->is_invoice = (int)$request->input('is_invoice');
        $transaction->comments = trim($request->input('comments'));
        if ($isNewRecord) {
            $transaction->save();
        } else {
            $transaction->update();
        }
        return redirect()->route('transaction.index')->with('success', 'Admin adjustment saved successfully.');
    }

    public function dataTable(Request $request)
    {
        try {
            if ($request->ajax()) {
                $query = Transaction::query();
                BaseModel::buildFilterQuery($query, [
                    'q' => ['ref_no', 'amount','comments'],
                    'user_id',
                    'type_id',
                ]);
                return Datatables::eloquent($query)
                    ->addColumn('checkboxes', function ($row) {
                        return '<input type="checkbox" name="pdr_checkbox[]" class="pdr_checkbox" value="' . $row->id . '" />';
                    })
                    ->addColumn('user', function ($row) {
                        return $row->user->displayName;
                    })
                    ->addColumn('type', function ($row) {
                        return $row->type->name;
                    })
                    ->addColumn('debit_amount', function ($row) {
                        if($row->is_debit) {
                            return Helper::printAmount($row->amount);
                        }
                        return '';
                    })
                    ->addColumn('credit_amount', function ($row) {
                        if(!$row->is_debit) {
                            return Helper::printAmount($row->amount);
                        }
                        return '';
                    })
                    ->addColumn('created_at', function ($row) {
                        return Helper::displayTime($row->created_at);
                    })
                    ->addColumn('updated_at', function ($row) {
                        return Helper::displayTime($row->updated_at);
                    })
                    ->rawColumns(['checkboxes', 'actions'])
                    ->make(true);
            }
        } catch (\Exception $e) {
            //print_r(['message'=>$e->getMessage(),'trace'=>$e->getTraceAsString()]);
            die();
        }
    }
}
