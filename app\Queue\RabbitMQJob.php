<?php

namespace App\Queue;

use Illuminate\Container\Container;
use Illuminate\Contracts\Queue\Job as JobContract;
use Illuminate\Queue\Jobs\Job;
use PhpAmqpLib\Channel\AMQPChannel;
use PhpAmqpLib\Message\AMQPMessage;

class RabbitMQJob extends Job implements JobContract
{
    protected $channel;
    protected $message;
    protected $queue;

    public function __construct(Container $container, AMQPChannel $channel, AMQPMessage $message, $connectionName, $queue)
    {
        $this->container = $container;
        $this->channel = $channel;
        $this->message = $message;
        $this->connectionName = $connectionName;
        $this->queue = $queue;
    }

    /**
     * Get the job identifier.
     *
     * @return string
     */
    public function getJobId()
    {
        return $this->message->getDeliveryTag();
    }

    /**
     * Get the raw body of the job.
     *
     * @return string
     */
    public function getRawBody()
    {
        return $this->message->getBody();
    }

    /**
     * Delete the job from the queue.
     *
     * @return void
     */
    public function delete()
    {
        parent::delete();
        $this->channel->basic_ack($this->message->getDeliveryTag());
    }

    /**
     * Release the job back into the queue.
     *
     * @param  int  $delay
     * @return void
     */
    public function release($delay = 0)
    {
        parent::release($delay);
        $this->channel->basic_nack($this->message->getDeliveryTag(), false, true);
    }

    /**
     * Get the number of times the job has been attempted.
     *
     * @return int
     */
    public function attempts()
    {
        // RabbitMQ doesn't track attempts by default
        // You would need to implement this in the message headers
        return 1;
    }

    /**
     * Get the name of the queued job class.
     *
     * @return string
     */
    public function getName()
    {
        return $this->payload()['displayName'] ?? 'Unknown';
    }

    /**
     * Get the name of the queue the job belongs to.
     *
     * @return string
     */
    public function getQueue()
    {
        return $this->queue;
    }
}
