<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use App\Components\Helper;
use App\Constants\CommonConstants;
use Illuminate\Database\Eloquent\Model;

class Provider extends BaseModel
{
    protected $fillable = [
        'name','api_url','api_key','api_username',
        'is_dhru',
        'is_active',
        'created_at',
        'updated_at',
    ];

    public function getServicesCountAttribute() {
        return ProviderService::query()->where('provider_id','=',$this->id)->count();
    }

    public function getDisplayNameAttribute() {
        return $this->name;
    }

    public static function defaultQuery() {
        return self::query()->where('is_active','=',CommonConstants::YES);
    }

    public function onSaved()
    {
        self::generateCacheFile();
        parent::onSaved();
    }

    public static function getCacheFileName() {
        try {
            $cacheDirectoryName=base_path().DIRECTORY_SEPARATOR.CommonConstants::CACHE_DIRECTORY_NAME;
            if(!is_dir($cacheDirectoryName)) {
                if(!mkdir($cacheDirectoryName, 0755, true)) {
                    throw new \Exception('Cache directory not generated');
                } else {
                    Helper::updateSystemCachePermissions();
                }
            }
            $cacheFileName=$cacheDirectoryName.DIRECTORY_SEPARATOR."providers.json";
            if(!is_file($cacheFileName)) {
                if(!file_put_contents($cacheFileName,json_encode([]))) {
                    throw new \Exception('Cache file not generated');
                } else {
                    Helper::updateSystemCachePermissions();
                }
            }
            return $cacheFileName;
        } catch (\Exception $e) {
            Log::insertLog([
                'user_id'=>CommonConstants::ADMINISTRATIVE,
                'type'=>'provider_cache_file_generate_error',
                'particulars'=>'Unable to generate provider cache file',
                'data'=>['message'=>$e->getMessage(),'trace'=>$e->getTraceAsString()],
            ]);
        }
        return null;
    }

    public static function fetchDetailsFromCache($providerID) {
        try {
            if($providerID=="") {
                return null;
            }
            $fileName=self::getCacheFileName();
            if($fileName) {
                $cacheData=json_decode(file_get_contents($fileName),true);
                if(is_array($cacheData) && count($cacheData)>0) {
                    if(array_key_exists($providerID,$cacheData)) {
                        return $cacheData[$providerID];
                    }
                }
            }
        } catch (\Exception $e) {

        }
        return null;
    }

    public static function generateCacheFile() {
        $rawData=[];
        try {
            $fileName=self::getCacheFileName();
            if($fileName) {
                $rows=self::defaultQuery()->orderBy("id")->get();
                if(count($rows)>0) {
                    foreach ($rows as $row) {
                        $cacheData=[
                            'id'=>$row->id,
                            'name'=>$row->name,
                            'api_url'=>$row->api_url,
                            'api_key'=>$row->api_key,
                            'api_username'=>$row->api_username,
                            'is_dhru'=>$row->is_dhru,
                            'component'=>null,
                        ];

                        switch ($row->id) {
                            case CommonConstants::PROVIDER_SICKW: //Sickw
                                $cacheData['component']='App\\Components\\ProviderSickw';
                                break;

                            case CommonConstants::PROVIDER_UNLOCK_3: //Unlock3
                                $cacheData['component']='App\\Components\\ProviderUnlock3';
                                break;

                            case CommonConstants::PROVIDER_PHONE_CHECK: //PhoneCheck
                                $cacheData['component']='App\\Components\\ProviderPhoneCheck';
                                break;

                            case CommonConstants::PROVIDER_APPLE_CHECK: //AppleCheck
                                $cacheData['component']='App\\Components\\ProviderAppleCheck';
                                break;

                            case CommonConstants::PROVIDER_ZHAOJIHUI: //Zhaojihui
                                $cacheData['component']='App\\Components\\ProviderZhaojihui';
                                break;

                            case CommonConstants::PROVIDER_IFREE_ICLOUD: //IfreeIcloud
                                $cacheData['component']='App\\Components\\ProviderIfreeIcloud';
                                break;

                            case CommonConstants::PROVIDER_ALPHA_IMEI_CHECK: //AlphaImeiCheck
                                $cacheData['component']='App\\Components\\ProviderAlphaImeiCheck';
                                break;

                            case CommonConstants::PROVIDER_GSX_UNLOCKING: //GsxUnlocking
                                $cacheData['component']='App\\Components\\ProviderGsxUnlocking';
                                break;

                            case CommonConstants::PROVIDER_IUNLOCK_TEAM: //iUnlockTeam
                                $cacheData['component']='App\\Components\\ProviderIUnlockTeam';
                                break;

                            case CommonConstants::PROVIDER_IUNLOCK_TEAM_NEW: //iUnlockTeamNew
                                $cacheData['component']='App\\Components\\ProviderIUnlockTeamNew';
                                break;

                            case CommonConstants::PROVIDER_IMEI_LOOKUP: //ImeiLookup
                                $cacheData['component']='App\\Components\\ProviderImeiLookup';
                                break;

                            case CommonConstants::PROVIDER_UNLOCK_API: //UnlockApi
                                $cacheData['component']='App\\Components\\ProviderUnlockApi';
                                break;

                            case CommonConstants::PROVIDER_FASTBULK: //FastBulk [iCheckPhone Oliver]
                                $cacheData['component']='App\\Components\\ProviderFastBulk';
                                break;

                            case CommonConstants::PROVIDER_HILOTMAN: //Hilotman
                                $cacheData['component']='App\\Components\\ProviderHilotman';
                                break;

                            case CommonConstants::PROVIDER_MERGE_PYTHON: //MergePython2
                                $cacheData['component']='App\\Components\\ProviderMergePython';
                                break;
                        }

                        $services=[];
                        $providerServices=ProviderService::defaultQuery()
                            ->where('provider_id','=',$row->id)
                            ->orderBy("id")->get();

                        if(count($providerServices)>0) {
                            foreach ($providerServices as $providerService) {
                                $services[$providerService->id]=[
                                    'id'=>$providerService->id,
                                    'name'=>$providerService->name,
                                    'sid'=>$providerService->sid,
                                    'type'=>$providerService->type,
                                    'price'=>$providerService->price,
                                    'is_chinese_result'=>$providerService->is_chinese_result,
                                ];
                            }
                        }
                        $cacheData['services']=$services;
                        $rawData[$row->id]=$cacheData;
                    }
                }

                if(count($rawData)>0) {
                    file_put_contents($fileName,json_encode($rawData));
                    Helper::updateSystemCachePermissions();
                }
            }

            resetServicesCache();
            return true;
        } catch (\Exception $e) {
            Log::insertLog([
                'user_id'=>CommonConstants::ADMINISTRATIVE,
                'type'=>'provider_cache_file_generate_error',
                'particulars'=>'Unable to generate provider cache file',
                'data'=>['message'=>$e->getMessage(),'trace'=>$e->getTraceAsString()],
            ]);
        }
        return false;
    }
}
