<?php
use App\Components\Helper;
?>
@extends(\App\Components\Helper::getLayoutForUser())
@section('content')
    <main>
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <h1>{{__('Service User Prices')}}</h1>
                    <div class="text-zero top-right-button-container">
                        <a href="{{route('service-user-price.create')}}" class="btn btn-primary btn-lg top-right-button mr-1"> <i class="glyph-icon simple-icon-plus"></i> ADD NEW</a>
                    </div>

                    <nav class="breadcrumb-container d-none d-sm-block d-lg-inline-block" aria-label="breadcrumb">
                        <ol class="breadcrumb pt-0">
                            <li class="breadcrumb-item">
                                <a href="{{\App\Components\Helper::dashboardLink()}}">{{__('Dashboard')}}</a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">{{__('Service User Prices')}}</li>
                        </ol>
                    </nav>
                    <div class="separator mb-5"></div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-lg-12 col-md-12 mb-4">
                    <div class="card">
                        <div class="card-body table-wrapper">
                            <div class="table-filters-ghost" style="display: none;">
                                <div class="p-b-10 mt-3 searchFiltersContainer">
                                    <div class="p-b-10 searchFilters">
                                        <div class="row">
                                            <div class="col-12 col-md">
                                                <div class="form-group text-left">
                                                    <label for="role">{{ __('Search') }}</label>
                                                    <input type="text" name="q" value="" class="form-control filterField" placeholder="Enter Search">
                                                </div>
                                            </div>
                                            <div class="col-12 col-md">
                                                <div class="form-group text-left">
                                                    <label for="user_id">{{ __('User') }}</label>
                                                    <select name="user_id" id="user_id" class="form-control filterField search-user-ajax">
                                                        <option value="">All</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-12 col-md">
                                                <div class="form-group text-left">
                                                    <label for="service_id">{{ __('Service') }}</label>
                                                    <select name="service_id" id="service_id" class="form-control filterField">
                                                        <?=\App\Models\Service::printOptionsHTML()?>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-12 col-md">
                                                <div class="form-group text-left">
                                                    <label for="is_active">{{ __('Active') }}</label>
                                                    <select name="is_active" id="is_active" class="form-control filterField">
                                                        <option value="">All</option>
                                                        @foreach (\App\Constants\CommonConstants::YES_NO_PROPERTIES as $userStatus => $userStatusData)
                                                            <option value="{{$userStatus}}">{{$userStatusData['text']}}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <table id="recordsTable" class="table table-hover align-middle" style="width:100%">
                                <thead>
                                <tr>
                                    <th>{{__('Time')}}</th>
                                    <th>{{__('User')}}</th>
                                    <th>{{__('Service')}}</th>
                                    <th>{{__('Price')}}</th>
                                    <th>{{__('Active')}}</th>
                                    <th>{{__('Actions')}}</th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>



@endsection
@section('pageJs')

    <script>
        $(document).ready(function () {
            let recordsTable = $('#recordsTable');
            let mainTableWrapper = recordsTable.closest(".table-wrapper");
            let filters = mainTableWrapper.find(".table-filters-ghost");
            let dataTableOptions = {!! json_encode(\App\Common\ContentHelper::getDataTableOptions()) !!};
            let recordsTableObj = recordsTable.DataTable({
                ...dataTableOptions,
                processing: false,
                order: [[0, "desc"]],
                ajax: {
                    url: "{{ route('service-user-price.dataTable') }}",
                    data: function (data) {
                        data['search_query'] = {};
                        mainTableWrapper.find(".filterField").each(function () {
                            let obj = $(this);
                            data['search_query'][obj.attr("name")] = obj.val();
                        });
                    },
                    dataSrc: function (json) {
                        let data = json.stats;
                        return json.data;
                    }
                },
                columns: [
                    {data: 'created_at', name: 'created_at'},
                    {data: 'user_id', name: 'user_id',orderable: false, searchable: false},
                    {data: 'service_id', name: 'service_id',orderable: false, searchable: false},
                    {data: 'price', name: 'price'},
                    {data: 'is_active', name: 'is_active',orderable: false, searchable: false},
                    {data: 'actions', name: 'actions',orderable: false, searchable: false},
                ]
            });
            mainTableWrapper.find(".tableFilters").html(filters.html());
            filters.remove();
            $(document).on('keyup change', '.filterField', function () {
                recordsTableObj.draw();
            });
        });


    </script>

@endsection
