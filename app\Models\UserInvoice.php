<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use App\Casts\AmountCast;
use App\Constants\CommonConstants;
use Illuminate\Database\Eloquent\Model;

class UserInvoice extends BaseModel
{
    protected $fillable = [
        'user_id',
        'ref_no',
        'adjustment_id',
        'amount',
        'generated_date',
        'paid_date',
        'particular',
        'is_paid',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'amount' => AmountCast::class,
    ];

    public function onCreating()
    {
        if(!$this->generated_date) {
            $this->generated_date = date(CommonConstants::PHP_DATE_FORMAT_SHORT);
        }
        if($this->is_paid) {
            $this->paid_date=date(CommonConstants::PHP_DATE_FORMAT_SHORT);
        }
        parent::onCreating(); // TODO: Change the autogenerated stub
    }

    public function onUpdating()
    {
        if($this->is_paid) {
            $this->paid_date = date(CommonConstants::PHP_DATE_FORMAT_SHORT);
        } else {
            $this->paid_date = null;
        }
        parent::onUpdating(); // TODO: Change the autogenerated stub
    }

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function adjustment()
    {
        return $this->hasOne(AdminAdjustment::class, 'id', 'adjustment_id');
    }
}
