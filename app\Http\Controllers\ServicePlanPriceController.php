<?php

namespace App\Http\Controllers;

use App\Base\Model\BaseModel;
use App\Components\Helper;
use App\Constants\CommonConstants;
use App\Constants\UserConstants;
use App\Models\ServicePlanPrice;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Yajra\DataTables\Facades\DataTables;

class ServicePlanPriceController extends Controller
{
    public function index()
    {
        return view('service-plan-price.index');
    }

    public function show(ServicePlanPrice $servicePlanPrice,Request $request)
    {
        return redirect()->route('service-plan-price.index');
        return view('service-plan-price.show', compact('servicePlanPrice'));
    }

    public function create()
    {
        $servicePlanPrice=new ServicePlanPrice();
        $servicePlanPrice->is_active=CommonConstants::YES;
        return view('service-plan-price.create', compact('servicePlanPrice'));
    }

    public function edit(ServicePlanPrice $servicePlanPrice)
    {
        return view('service-plan-price.edit', compact('servicePlanPrice'));
    }

    public function store(Request $request)
    {
        $servicePlanPrice = new ServicePlanPrice();
        return $this->save($request, $servicePlanPrice);
    }

    public function update(Request $request, ServicePlanPrice $servicePlanPrice)
    {
        return $this->save($request, $servicePlanPrice);
    }

    private function save(Request $request, ServicePlanPrice $servicePlanPrice)
    {
        $isNewRecord = true;
        if ($servicePlanPrice->id != null) {
            $isNewRecord = false;
        }

        $rules = [
            'service_id' => ['required', 'integer'],
            'plan_id' => ['required', 'integer'],
            'is_active' => ['required', 'integer'],
            'price' => ['required','numeric'],
        ];

        if ($isNewRecord) {

        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            if ($isNewRecord) {
                return redirect()->route('service-plan-price.create')->withErrors($validator)->withInput();
            } else {
                return redirect()->route('service-plan-price.edit', $servicePlanPrice->id)->withErrors($validator)->withInput();
            }
        }

        $servicePlanPrice->service_id = (int)$request->input('service_id');
        $servicePlanPrice->plan_id = (int)$request->input('plan_id');
        $isExist=ServicePlanPrice::query()
            ->where('service_id','=',$servicePlanPrice->service_id)
            ->where('plan_id','=',$servicePlanPrice->plan_id)
            ->first();
        if($isExist) {
            $servicePlanPrice=$isExist;
            $isNewRecord=false;
        }
        $servicePlanPrice->is_active = (int)$request->input('is_active');
        $servicePlanPrice->price = trim($request->input('price'));
        if ($isNewRecord) {
            $servicePlanPrice->save();
        } else {
            $servicePlanPrice->update();
        }

        \App\Models\Service::generateCacheFile();
        resetServicesCache();
        return redirect()->route('service-plan-price.index')->with('success', 'Saved successfully.');
    }

    public function dataTable(Request $request)
    {
        try {
            if ($request->ajax()) {
                $query = ServicePlanPrice::query();
                BaseModel::buildFilterQuery($query, [
                    'q' => ['price'],
                    'plan_id',
                    'service_id',
                    'is_active',
                ]);
                return Datatables::eloquent($query)
                    ->addColumn('checkboxes', function ($row) {
                        return '<input type="checkbox" name="pdr_checkbox[]" class="pdr_checkbox" value="' . $row->id . '" />';
                    })
                    ->addColumn('service_id', function ($row) {
                        return $row->service->displayName;
                    })
                    ->addColumn('plan_id', function ($row) {
                        return $row->plan->name;
                    })
                    ->addColumn('price', function ($row) {
                        return Helper::editPopupStructure($row,'price',null,'Price (in $)');
                    })
                    ->addColumn('created_at', function ($row) {
                        return Helper::displayTime($row->created_at);
                    })
                    ->addColumn('updated_at', function ($row) {
                        return Helper::displayTime($row->updated_at);
                    })
                    ->addColumn('is_active', function ($row) use ($query) {
                        $columnName = 'is_active';
                        return Helper::onOffButton($row, $columnName, $query);
                    })
                    ->addColumn('actions', function ($row) {
                        $buttons = [];
                        //$buttons['Login']=['url' => route('user.loginAs', $row->id), 'icon' => 'las la-sign-in-alt'];
                        //$buttons['view'] = ['url' => route('service-plan-price.show', $row->id)];
                        $buttons['edit'] = ['url' => route('service-plan-price.edit', $row->id)];
                        return Helper::getActionButtons($buttons);
                    })
                    ->rawColumns(['checkboxes', 'name', 'is_active','custom_id', 'price','sid', 'is_chinese_result','display_order', 'actions'])
                    ->make(true);
            }
        } catch (\Exception $e) {
            //print_r(['message'=>$e->getMessage(),'trace'=>$e->getTraceAsString()]);
            die();
        }
    }
}
