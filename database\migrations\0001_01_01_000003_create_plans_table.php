<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('plans', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->string('name')->unique();
            $table->double('price')->default(0)->index();
            $table->double('is_default')->default(0)->index();
            $table->double('is_best_seller')->default(0)->index();
            $table->double('is_visible_to_guest')->default(0)->index();
            $table->double('is_active')->default(1)->index();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plans');
    }
};
