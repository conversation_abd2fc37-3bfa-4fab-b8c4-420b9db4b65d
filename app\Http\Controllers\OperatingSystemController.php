<?php

namespace App\Http\Controllers;

use App\Base\Model\BaseModel;
use App\Components\Helper;
use App\Constants\CommonConstants;
use App\Constants\UserConstants;
use App\Models\OperatingSystem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Yajra\DataTables\Facades\DataTables;

class OperatingSystemController extends Controller
{
    public function index()
    {
        return view('operating-system.index');
    }

    public function show(OperatingSystem $operatingSystem,Request $request)
    {
        return view('operating-system.show', compact('operatingSystem'));
    }

    public function create()
    {
        $operatingSystem=new OperatingSystem();
        return view('operating-system.create', compact('operatingSystem'));
    }

    public function edit(OperatingSystem $operatingSystem)
    {
        return view('operating-system.edit', compact('operatingSystem'));
    }

    public function store(Request $request)
    {
        $operatingSystem = new OperatingSystem();
        return $this->save($request, $operatingSystem);
    }

    public function update(Request $request, OperatingSystem $operatingSystem)
    {
        return $this->save($request, $operatingSystem);
    }

    private function save(Request $request, OperatingSystem $operatingSystem)
    {
        $isNewRecord = true;
        if ($operatingSystem->id != null) {
            $isNewRecord = false;
        }

        $rules = [
            'name' => ['required', 'string', 'max:255', Rule::unique('operating_systems')->ignore($operatingSystem->id)],
        ];

        if ($isNewRecord) {

        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            if ($isNewRecord) {
                return redirect()->route('operating-system.create')->withErrors($validator)->withInput();
            } else {
                return redirect()->route('operating-system.edit', $operatingSystem->id)->withErrors($validator)->withInput();
            }
        }

        $operatingSystem->name = $request->input('name');
        if ($isNewRecord) {
            $operatingSystem->save();
        } else {
            $operatingSystem->update();
        }
        return redirect()->route('operating-system.index')->with('success', 'Operating system saved successfully.');
    }

    public function dataTable(Request $request)
    {
        try {
            if ($request->ajax()) {
                $query = OperatingSystem::query();
                BaseModel::buildFilterQuery($query, [
                    'q' => ['name'],
                ]);
                return Datatables::eloquent($query)
                    ->addColumn('checkboxes', function ($row) {
                        return '<input type="checkbox" name="pdr_checkbox[]" class="pdr_checkbox" value="' . $row->id . '" />';
                    })
                    ->addColumn('name', function ($row) {
                        return Helper::editPopupStructure($row,'name');
                    })
                    ->rawColumns(['checkboxes', 'name'])
                    ->make(true);
            }
        } catch (\Exception $e) {
            //print_r(['message'=>$e->getMessage(),'trace'=>$e->getTraceAsString()]);
            die();
        }
    }
}
