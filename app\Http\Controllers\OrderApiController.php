<?php
namespace App\Http\Controllers;

use App\Components\RedisWrapper;
use App\Constants\CommonConstants;
use App\Jobs\ProcessOrderJob;
use App\Models\Service;
use App\Models\UserOrder;
use App\Models\UserOrderBatch;
use Illuminate\Http\Request;

class OrderApiController extends \App\Base\Controller\BaseApiController
{
    public $needAuthentication = false;
    public function placeOrder($orderKey,Request $request) {
        $orderDetails=RedisWrapper::fetchRecord($orderKey);
        if(!$orderDetails) {
            return $this->sendError('Invalid order details');
        }
        if($orderDetails['status']!=CommonConstants::ORDER_STATUS_PENDING) {
            return $this->sendError('Order already processed');
        }

        $orderDetails['status']=CommonConstants::ORDER_STATUS_IN_PROGRESS;
        RedisWrapper::saveRecord($orderKey,$orderDetails);

        $model=new UserOrder();
        if(array_key_exists('batch_ref',$orderDetails)) {
            $model->batch_ref=$orderDetails['batch_ref'];
        }
        if(array_key_exists('order_ref',$orderDetails)) {
            $model->order_ref=$orderDetails['order_ref'];
        }
        $model->user_id=$orderDetails['user_id'];
        $model->imei=$orderDetails['imei'];
        $model->service_id=$orderDetails['service_id'];
        $model->provider_id=$orderDetails['provider_id'];
        $model->provider_service_id=$orderDetails['provider_service_id'];
        $model->price=$orderDetails['price'];
        $model->created_at=date(CommonConstants::PHP_DATE_FORMAT);

        $result=$model->placeOrder();

        $model->updated_at=date(CommonConstants::PHP_DATE_FORMAT);
        if(is_array($result)) {
            if(array_key_exists('data',$result)) {
                $model->raw_result=json_encode($result['data']);
            }
            if(array_key_exists('price',$result)) {
                $model->cost_price=$result['price'];
            }
        }

        $model->time_taken=(strtotime($model->updated_at)-strtotime($model->created_at));
        if(array_key_exists('error',$result)) {
            $model->result=$result['error'];
            $model->status=CommonConstants::ORDER_STATUS_FAILED;
        } else {
            $model->result=$result['result'];
            $model->status=CommonConstants::ORDER_STATUS_COMPLETED;
        }

        if($model->save()) {
            if($model->status==CommonConstants::ORDER_STATUS_FAILED) {
                creditUserBalance($model->user_id,$model->price);

                $orderDetails['status']=CommonConstants::ORDER_STATUS_FAILED;
                $orderDetails['result']=$model->result;
                $orderDetails['is_deducted']=CommonConstants::NO;
                //RedisWrapper::saveRecord($orderKey,$orderDetails);
                RedisWrapper::deleteRecord($orderKey);

                return $this->sendError([
                    'order_id'=>$model->order_ref,
                    'reference_id'=>$model->order_ref,
                    'imei'=>$model->imei,
                    'message'=>"Result failed",
                    'result'=>$model->result,
                    'status'=>'error',
                ]);
            } else {
                $model->deductBalance();

                $orderDetails['status']=CommonConstants::ORDER_STATUS_COMPLETED;
                $orderDetails['result']=$model->result;
                //RedisWrapper::saveRecord($orderKey,$orderDetails);
                RedisWrapper::deleteRecord($orderKey);

                return $this->sendSuccess([
                    'order_id'=>$model->order_ref,
                    'reference_id'=>$model->order_ref,
                    'imei'=>$model->imei,
                    'message'=>"Result passed",
                    'result'=>$model->result,
                    'status'=>'success',
                ]);
            }

        }

        return $this->sendError('Server down for maintenance');
    }
    public function testBatchOrder($batchKey,Request $request) {
        $orderDetails=RedisWrapper::fetchRecord($batchKey);
        if(!$orderDetails) {
            return $this->sendError('Invalid batch details');
        }
        $orderDetails['status']='called';
        RedisWrapper::saveRecord($batchKey,$orderDetails);
        return $this->sendSuccess(['message' => 'Batch completed', 'key' => $batchKey]);
    }
    public function placeBatchOrder($batchKey,Request $request) {
        $currentBatchDetails=RedisWrapper::fetchRecord($batchKey);
        if(!$currentBatchDetails) {
            return $this->sendError('Invalid batch details');
        }
        if(!is_array($currentBatchDetails) || !array_key_exists('key',$currentBatchDetails)) {
            return $this->sendError('No batch details found');
        }
        $batchCustomKey=$currentBatchDetails['key'];
        $orderDetails=RedisWrapper::fetchRecord($batchCustomKey);
        if(!$orderDetails) {
            return $this->sendError('Invalid batch data');
        }
        $items=[];
        $imeis=json_decode($orderDetails['imeis'],true);
        if(count($imeis)>0 && $orderDetails['pending_count']>0) {
            $counter=1;
            foreach ($imeis as $k=>$v) {
                if($counter>10) {
                    break;
                }
                $items[]=$v;
                unset($imeis[$k]);
                $orderDetails['pending_count']=$orderDetails['pending_count']-1;
                $counter++;
            }
        }

        $orderDetails['imeis']=json_encode($imeis);
        $orderDetails['status']=CommonConstants::ORDER_STATUS_IN_PROGRESS;
        if(count($imeis)<=0) {
            $orderDetails['status']=CommonConstants::ORDER_STATUS_COMPLETED;
        }
        RedisWrapper::saveRecord($batchCustomKey,$orderDetails);

        $batchDetails=UserOrderBatch::getBatchByReference(str_replace("batch_","",$batchCustomKey));
        if($batchDetails) {
            $batchDetails->is_scheduled=CommonConstants::YES;
            $batchDetails->status=CommonConstants::ORDER_STATUS_IN_PROGRESS;
            $batchDetails->pending_count=$orderDetails['pending_count'];
            $batchDetails->updated_at=date(CommonConstants::PHP_DATE_FORMAT);
            if($batchDetails->pending_count<=0) {
                $batchDetails->is_scheduled=CommonConstants::NO;
                $batchDetails->status=CommonConstants::ORDER_STATUS_COMPLETED;
            }
            if($batchDetails->update(['pending_count','status','is_scheduled','updated_at'])) {

            }
        }

        if(count($items)>0) {
            $counter=1;
            foreach ($items as $item) {
                $postData=[
                    'order_ref'=>UserOrder::generateOrderReferenceNumber($orderDetails['user_id']),
                    'user_id'=>$orderDetails['user_id'],
                    'imei'=>$item,
                    'service_id'=>$orderDetails['service_id'],
                    'provider_id'=>$orderDetails['provider_id'],
                    'provider_service_id'=>$orderDetails['provider_service_id'],
                    'price'=>$orderDetails['price'],
                    'batch_ref'=>$orderDetails['order_ref'],
                    'status'=>CommonConstants::ORDER_STATUS_PENDING,
                    'is_deducted'=>CommonConstants::YES,
                    'result'=>'',
                ];

                $key='order_'.$postData['order_ref'];

                if($counter==count($items)) {

                    $redisRequest=RedisWrapper::saveRecord(
                        $key,
                        $postData,
                        env('APP_URL').'/order-api/place-order/'.$key,
                        ($counter!=count($items) ? true : false)
                    );

                    $currentBatchDetails['counter']=$currentBatchDetails['counter']+1;
                    RedisWrapper::saveRecord(
                        $batchKey,
                        $currentBatchDetails,
                        env('APP_URL').'/order-api/place-batch-order/'.$batchKey,
                        true
                    );
                } else {
                    $redisRequest=RedisWrapper::saveRecord(
                        $key,
                        $postData
                    );
                    if($redisRequest) {
                        ProcessOrderJob::dispatch($key);
                    }
                }

                $counter++;
            }
        } else {
            return $this->sendSuccess(['message' => 'Batch completed', 'key' => $batchKey, 'custom_key' => $batchCustomKey]);
        }
        return $this->sendError(['message' => 'Batch processing is in progress', 'key' => $batchKey, 'custom_key' => $batchCustomKey]);
    }
}
