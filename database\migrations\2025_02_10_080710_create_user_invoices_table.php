<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('user_invoices', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->bigInteger('ref_no')->unique();
            $table->foreignId('user_id')->nullable()
                ->references('id')->on('users')->onDelete('cascade');
            $table->foreignId('adjustment_id')->nullable()
                ->references('id')->on('admin_adjustments')->onDelete('cascade');
            $table->bigInteger('amount')->index();
            $table->date('generated_date')->nullable()->index();
            $table->date('paid_date')->nullable()->index();
            $table->integer('is_paid')->default(0)->comment('0=no 1=yes')->index();
            $table->text('particular')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_invoices');
    }
};
