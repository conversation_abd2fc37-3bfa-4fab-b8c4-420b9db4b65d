<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use App\Casts\AmountCast;
use App\Constants\CommonConstants;
use Illuminate\Support\Facades\DB;

class UserOrderLog extends BaseModel
{
    protected $fillable = [
        'user_id',
        'service_id',
        'date',
        'last_order_id',
        'total_orders',
        'price',
        'cost_price',
        'final_pl',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'price' => AmountCast::class,
        'cost_price' => AmountCast::class,
        'final_pl' => AmountCast::class,
    ];

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function service()
    {
        return $this->hasOne(Service::class, 'id', 'service_id');
    }

    public static function saveLog($date=null) {
        try {
            if (!$date) {
                $date = date(CommonConstants::PHP_DATE_FORMAT_SHORT, strtotime("-1 day"));
            }
            $logs = ['date' => $date, 'users' => []];
            $lastOrderID=null;
            $checkLastLog = UserOrderLog::query()
                ->where('date', '=', $date)
                ->orderBy("id")->limit(1)
                ->first();
            if($checkLastLog) {
                if($checkLastLog->last_order_id) {
                    $lastOrderID=$checkLastLog->last_order_id;
                }
            }

            if($lastOrderID) {
                $orders = UserOrder::query()
                    ->select(['id','user_id', 'service_id', 'time_taken', 'price', 'cost_price'])
                    ->where('id','>',$lastOrderID)
                    ->where('status', '=', CommonConstants::ORDER_STATUS_COMPLETED)
                    ->whereBetween('created_at', [$date . " 00:00:00", $date . " 23:59:59"])
                    ->orderBy("id")
                    ->get();
                print "Fetch above ".$lastOrderID.PHP_EOL;
            } else {
                $orders = UserOrder::query()
                    ->select(['id','user_id', 'service_id', 'time_taken', 'price', 'cost_price'])
                    ->where('status', '=', CommonConstants::ORDER_STATUS_COMPLETED)
                    ->whereBetween('created_at', [$date . " 00:00:00", $date . " 23:59:59"])
                    ->orderBy("id")
                    ->get();
            }


            $users = $orders->pluck('user_id')->unique();
            $services = $orders->pluck('service_id')->unique();
            if (count($users) > 0) {
                $fetchLastRow=$orders->take(-1);
                $lastOrderID=$fetchLastRow->first()->id;

                foreach ($users as $userID) {
                    $logs['users'][$userID] = [
                        'id' => $userID,
                        'services' => [],
                    ];
                }
            } else {
                print "No records found ".$date." => ".$lastOrderID.PHP_EOL;
            }
            if (count($logs['users']) > 0 && count($services) > 0) {
                foreach ($services as $serviceID) {
                    foreach ($logs['users'] as $userID => $userData) {
                        $serviceOrders = $orders
                            ->where('user_id', '=', $userID)
                            ->where('service_id', '=', $serviceID);
                        $ordersCount = count($serviceOrders);
                        if ($ordersCount > 0) {
                            $avgTime = 0;
                            $timeTaken = $serviceOrders->sum("time_taken");
                            if ($timeTaken > 0) {
                                $avgTime = round($timeTaken / $ordersCount);
                            }
                            $logs['users'][$userID]['services'][$serviceID] = [
                                'id' => $serviceID,
                                'orders' => $ordersCount,
                                'price' => $serviceOrders
                                    ->sum('price'),
                                'cost_price' => $serviceOrders
                                    ->sum('cost_price'),
                                'time_taken' => $avgTime,
                            ];
                        }
                    }
                }
            }

            if (count($logs['users']) > 0) {
                foreach ($logs['users'] as $userID => $userData) {
                    if (count($userData['services'])>0) {
                        foreach ($userData['services'] as $serviceID=>$serviceData) {
                            $model=UserOrderLog::query()
                                ->where('user_id','=',$userID)
                                ->where('service_id','=',$serviceID)
                                ->where('date','=',$date)
                                ->first();
                            if(!$model) {
                                $model = new UserOrderLog();
                                $model->user_id = $userID;
                                $model->service_id = $serviceID;
                                $model->date = $logs['date'];
                                $model->last_order_id = $lastOrderID;
                                $model->total_orders = $serviceData['orders'];
                                $model->price = $serviceData['price'];
                                $model->cost_price = $serviceData['cost_price'];
                                $model->final_pl = round($model->price - $model->cost_price, CommonConstants::USD_PRECISION);
                                $model->save();
                            } else {
                                $model->last_order_id = $lastOrderID;
                                $model->total_orders = $serviceData['orders'];
                                $model->price = ($model->price+$serviceData['price']);
                                $model->cost_price = ($model->cost_price+$serviceData['cost_price']);
                                $model->final_pl = round($model->price - $model->cost_price, CommonConstants::USD_PRECISION);
                                $model->updated_at=date(CommonConstants::PHP_DATE_FORMAT);
                                $model->update(['last_order_id','total_orders','price','cost_price','final_pl','updated_at']);
                            }
                        }
                    }
                }

                DB::statement("update `user_order_logs` set `last_order_id`='".$lastOrderID."' where `date`='".$logs['date']."'");
            }

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}
