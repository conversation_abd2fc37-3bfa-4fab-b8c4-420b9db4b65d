<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('provider_services', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->foreignId('provider_id')->references('id')->on('providers')->onDelete('cascade');
            $table->string('name')->index();
            $table->string('sid')->index();
            $table->integer('type')->default(0)->comment('0=both 1=imei 2=sn')->index();
            $table->bigInteger('price')->index();
            $table->integer('is_active')->default(\App\Constants\CommonConstants::YES)->index();
            $table->integer('is_chinese_result')->default(\App\Constants\CommonConstants::NO)->index();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('provider_services');
    }
};
