<?php

namespace App\Components;

use App\Base\Provider\BaseProvider;
use App\Constants\CommonConstants;
use App\Models\Log;
use App\Models\User;
use Illuminate\Support\Facades\Http;

class Provider<PERSON>ilotman extends BaseProvider
{
    public static $providerID = CommonConstants::PROVIDER_HILOTMAN;
    public static function placeOrder($orderModel) {
        $validate=self::validateRequest($orderModel);
        if($validate!==true) {
            return $validate;
        }

        $url = self::$providerDetails['api_url'] . "?api=".self::$providerDetails['api_key']."&imei=".$orderModel->imei."&service=".self::$providerServiceDetails['sid']. "&json=1";
        $response = Http::get($url);
        $result=$response->body();

        /* $curl = curl_init ($url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, TRUE);
        curl_setopt($curl, CURLOPT_USERAGENT , 'Mozilla/5.0 (Windows NT 6.1; rv:19.0) Gecko/20100101 Firefox/19.0');
        curl_setopt($curl, CURLOPT_ENCODING , '');
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 40);
        curl_setopt($curl, CURLOPT_TIMEOUT, 0);
        curl_setopt($curl, CURLOPT_MAXREDIRS, 10);
        curl_setopt($curl, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'GET');
        $result = curl_exec($curl);
        curl_close($curl); */

        if($result && $result!="") {
            return self::parseResult($orderModel,$result);
        }

        return self::defaultResult();
    }

    public static function parseResult($orderModel,$result) {
        try {
            if ($result == "" || $result == false || stripos($result, "error code")!==false) {
                return self::failedResult(self::SERVICE_MAINTENANCE_MESSAGE,$result);
            }

            $finalResult="";
            $decode_resp = json_decode($result, true);
            if(is_array($decode_resp) && count($decode_resp)>0) {
                if (array_key_exists('status', $decode_resp)) {
                    if (strtolower($decode_resp['status']) == "success") {
                        if (!array_key_exists('result', $decode_resp)) {
                            if (array_key_exists('reply', $decode_resp)) {
                                $finalResult = $decode_resp['reply'];
                            }
                        }
                    }
                }
            }

            if($finalResult!="") {
                $finalResult = rtrim($finalResult, "*/");
                $finalResult = rtrim($finalResult, "*\/");

                $finalResult = self::reArrangeText($finalResult);
                if (stripos($finalResult, "SerialNumber:") !== false) {
                    $finalResult = str_replace("SerialNumber:", "Serial:", $finalResult);
                }
            }

            if($finalResult=="") {
                return self::failedResult(self::DEFAULT_MESSAGE,$result);
            }

            $sid=self::$providerServiceDetails['sid'];
            switch ($sid) {
                case "64": //soldby
                    if(isset($orderModel->user_id)) {
                        if(in_array($orderModel->user_id,[282, 472])) {
                            $finalResult = preg_replace('/<br \/><u>Blacklist Status<\/u><br \/>.*?<br \/>(?=Blacklist Status:)/s', '', $finalResult);
                        }
                    }
                    return self::successResult($finalResult,$result);
                    break;
                case "124":
                    if (stripos($finalResult, 'IMEI\/Serial Number') !== false) {
                        $finalResult = preg_replace('/IMEI\/Serial Number/i', 'IMEI/SN', $finalResult);
                    }
                    if (stripos($finalResult, 'IMEI/Serial Number') !== false) {
                        $finalResult = preg_replace('/IMEI\/Serial Number/i', 'IMEI/SN', $finalResult);
                    }
                    if (stripos($finalResult, 'Find My iPhone Status') !== false) {
                        $finalResult = preg_replace('/Find My iPhone Status/i', 'iCloud Status', $finalResult);
                    }
                    return self::successResult($finalResult,$result);
                    break;

                case "111":
                    if (stripos($finalResult, 'Model Name:') !== false) {
                        $finalResult = preg_replace('/Model Name:/i', 'Model :', $finalResult);
                    }
                    if (stripos($result, 'MDM Status:') !== false) {
                        $finalResult = preg_replace('/MDM Status:/i', 'MDM Status :', $finalResult);
                    }
                    return self::successResult($finalResult,$result);
                    break;
            }

            return self::successResult($finalResult, $result);
        } catch (\Exception $e) {

        }
        return self::defaultResult();
    }

    public static function reArrangeText($result)
    {
        $items = [
            'ProductDescription:' => 'Device Name:',
            'Product Description:' => 'Device Name:',
            'ModelDescription:' => 'Model Name:',
            'Serial:' => 'Serial Number:',
            'SerialNumber:' => 'Serial Number:',
            'Imei:' => 'IMEI Number:',
            'ImeiNumber:' => 'IMEI Number:',
            'Imei1 Number:' => 'IMEI Number:',
            'Imei2:' => 'IMEI Number2:',
            'ImeiNumber2:' => 'IMEI Number2:',
            'Imei2 Number:' => 'IMEI Number2:',
            'WirelessMacAddress:' => 'Wireless MAC Address:',
            'NextTetherPolicyDetails:' => 'Next Tether Policy Details:',
            'InitialActivationPolicyDetails:' => 'Initial Activation Policy Details:',
            'LastUnbrickOsBuild:' => 'Last Unbrick OS Build:',
            'PurchaseCountry:' => 'Purchase Country:',
            'ProductVersion:' => 'Product Version:',
            'WarrantyStatusDescription:' => 'Warranty Status Description:',
            'AppleCareProtection:' => 'Apple Care Protection:',
            'AppleCareIncidentsRemaining:' => 'Apple Care Incidents Remaining:',
            'DeviceReplaced:' => 'Device Replaced:',
            'FindMyiPhone:' => 'Find My iPhone:',
            'iCloudStatus:' => 'iCloud Status:',
            'ConfigDescription:' => 'Device Config:',
            'RegistrationDate:' => 'Registration Date:',
            'FirstActivationDate:' => 'First Activation Date:',
            'Meid:' => 'MEID:',
            'MeidNumber:' => 'MEID Number:',
            'MacAddress:' => 'MAC Address:',
            'NextTetherPolicyID:' => 'Next Tether Policy ID:',
            'AppliedActivationPolicyID:' => 'Applied Activation Policy ID:',
            'AppliedActivationDetails:' => 'Applied Activation Details:',
            'CoverageStartDate:' => 'Coverage Start Date:',
            'CoverageEndDate:' => 'Coverage End Date:',
            'PurchaseDate:' => 'Purchase Date:',
            'LastRestoreDate:' => 'Last Restore Date:',
            'LimitedWarranty:' => 'Limited Warranty:',
            'OnsiteCoverage:' => 'Onsite Coverage:',
            'PartCovered:' => 'Part Covered:',
            'SoldToName:' => 'Sold To Name:',
            'FMI' => 'Find My iPhone',
            'CaseDetails:' => 'Case Details:',
            'CaseId' => 'Case ID',
            'CreatedDateTime' => 'Created Date Time',
            'Sim-lock' => 'Sim-Lock',
            '<BR \/>' => '<br />',
            '<BR>' => '<br />',
        ];

        foreach ($items as $key => $value) {
            $result = preg_replace('/' . $key . '/i', $value, $result);
        }
        return $result;
    }
}
