<?php
//die("here");
/**
 * DHRU Fusion api standards V6.1
 */
header("Access-Control-Allow-Origin: *");
set_time_limit(0);
$debug=FALSE;
if($debug)
{
	file_put_contents("text".time().".txt", json_encode($_POST));
}
session_name("DHRUFUSION");
session_set_cookie_params(0, "/", null, false, true);
session_start();
error_reporting(0);
$apiversion = '6.1';
extract($_POST);
require_once 'json_api.php';
$imei_api_key=$apiaccesskey;
$imei_api_url="http://";
if(array_key_exists('HTTPS', $_SERVER))
{
	if(strtolower($_SERVER['HTTPS'])=="on")
	{
		$imei_api_url="https://";
	}
}

$imei_api_url.=$_SERVER['HTTP_HOST']."/";
//tech end
$apiresults = array();
if($debug)
{
	file_put_contents("paramss.txt", json_encode($parameters));
}
if ($parameters)
{
	if($action!="placeimeiorderbulk" && $action!="getimeiorderbulk")
	{
		$html = $_POST['parameters'];
		$doc = new DOMDocument();
		$doc->loadXML($html);
		$p = $doc->getElementsByTagName('PARAMETERS')->item(0);
		$opts=array();
		foreach($p->getElementsByTagName('*') as $item)
		{
		    $opts[$item->nodeName]=$item->nodeValue;
		}
		$parameters = $opts;
	    //$parameters = json_decode(base64_decode($parameters), true);
	}
	else if ($action=="placeimeiorderbulk" || $action=="getimeiorderbulk")
	{
		$parameters = json_decode(base64_decode($parameters), true);
	}
}
if($debug)
{
	file_put_contents("paramss.html", json_encode($parameters));
}
$api_user_details=false;

if ($User = validateAuth($imei_api_url, $apiaccesskey)) {
	global $api_user_details;
	global $imei_api_url;
	global $apiaccesskey;
	global $debug;
    switch ($action) {

        case "accountinfo":


            $AccoutInfo['credit'] = $api_user_details['credit'];
            $AccoutInfo['mail'] = $api_user_details['mail'];;
            $AccoutInfo['currency'] = $api_user_details['currency']; /* Currency code */
            $apiresults['SUCCESS'][] = array('message' => 'Your Accout Info', 'AccoutInfo' => $AccoutInfo);
            break;

        case "imeiservicelist":
            $ServiceList = NULL;
            $Group = 'Info Services';
            $ServiceList[$Group]['GROUPNAME'] = $Group;

			//tech start
			$imei_account_details=getServices($imei_api_url,$imei_api_key);
			if(is_array($imei_account_details) && array_key_exists('status', $imei_account_details) && $imei_account_details['status']=="success")
			{
				$res=$imei_account_details['response'];
				if(count($res)>0)
				{
					$counter=1;
					foreach($res as $s)
					{
						$SERVICEID = $s['code'];
		                $ServiceList[$Group]['GROUPTYPE'] = 'IMEI';  //IMEI OR SERVER
		                $ServiceList[$Group]['SERVICES'][$SERVICEID]['SERVICEID'] = $SERVICEID;
		                $ServiceList[$Group]['SERVICES'][$SERVICEID]['SERVICETYPE'] = 'IMEI'; //IMEI OR SERVER
		                $ServiceList[$Group]['SERVICES'][$SERVICEID]['SERVICENAME'] = $s['name'];
		                $ServiceList[$Group]['SERVICES'][$SERVICEID]['CREDIT'] = $s['amount'];
		                $ServiceList[$Group]['SERVICES'][$SERVICEID]['INFO'] = utf8_encode('');
		                $ServiceList[$Group]['SERVICES'][$SERVICEID]['TIME'] = 'Instant';

		                /*QNT*/
		                $ServiceList[$Group]['SERVICES'][$SERVICEID]['QNT'] = 1;
		                $ServiceList[$Group]['SERVICES'][$SERVICEID]['QNTOPTIONS'] = '10,20,50';
		                $ServiceList[$Group]['SERVICES'][$SERVICEID]['MINQNT'] = ''; /* QNTOPTIONS OR MIN/MAX QNT*/
		                $ServiceList[$Group]['SERVICES'][$SERVICEID]['MAXQNT'] = '';


		                /* Other Fields if required only */
		                $ServiceList[$Group]['SERVICES'][$SERVICEID]['Requires.Network'] = 'None';
		                $ServiceList[$Group]['SERVICES'][$SERVICEID]['Requires.Mobile'] = 'None';
		                $ServiceList[$Group]['SERVICES'][$SERVICEID]['Requires.Provider'] = 'None';
		                $ServiceList[$Group]['SERVICES'][$SERVICEID]['Requires.PIN'] = 'None';
		                $ServiceList[$Group]['SERVICES'][$SERVICEID]['Requires.KBH'] = 'None';
		                $ServiceList[$Group]['SERVICES'][$SERVICEID]['Requires.MEP'] = 'None';
		                $ServiceList[$Group]['SERVICES'][$SERVICEID]['Requires.PRD'] = 'None';
		                $ServiceList[$Group]['SERVICES'][$SERVICEID]['Requires.Type'] = 'None';
		                $ServiceList[$Group]['SERVICES'][$SERVICEID]['Requires.Reference'] = 'None';
		                $ServiceList[$Group]['SERVICES'][$SERVICEID]['Requires.Locks'] = 'None';
		                $ServiceList[$Group]['SERVICES'][$SERVICEID]['Requires.SN'] = 'None';
		                $ServiceList[$Group]['SERVICES'][$SERVICEID]['Requires.SecRO'] = 'None';

		                /*Custom Fields*/
		                $CUSTOM = array();
		                $ServiceList[$Group]['SERVICES'][$SERVICEID]['Requires.Custom'] = $CUSTOM;
					}
				}
			}
			//tech end

            $apiresults['SUCCESS'][] = array('MESSAGE' => 'IMEI Service List', 'LIST' => $ServiceList);
            break;

        case "placeimeiorder":
			if($debug)
			{
				file_put_contents("sending1.html", json_encode($imei_account_details));
			}
            $sid = (int)$parameters['ID'];
			$imei = trim($parameters['IMEI']);
            //$CustomField = json_decode(base64_decode($parameters['customfield']), true);

			$imei_account_details=placeOrder($imei_api_url,$imei_api_key,$sid,$imei);
			if($debug)
			{
				file_put_contents("sending1.html", json_encode($imei_account_details));
			}
			if(is_array($imei_account_details) && array_key_exists('status', $imei_account_details) && $imei_account_details['status']=="success")
			{
				if(array_key_exists('response', $imei_account_details))
				{
					if(array_key_exists('order_id', $imei_account_details['response']))
					{
						$order_reff_id = $imei_account_details['response']['order_id'];
						$apiresults['SUCCESS'][] = array('MESSAGE' => 'Order received', 'REFERENCEID' => $order_reff_id);
					}
				}
			}
			else
			{
				if($imei_account_details!=null)
				{
					$apiresults['ERROR'][] = array('MESSAGE' => $imei_account_details);
				}
				else
				{
					$apiresults['ERROR'][] = array('MESSAGE' => 'Try later.');
				}
			}
            break;

        case "placeimeiorderbulk_old":
            /* Other Fusion 31- 59 api support for bulk submit */
            /*Validate each orders in loop */
            foreach ($parameters as $bulkReqId => $OrdersDetails) {

				$sid = (int)$OrdersDetails['ID'];
				$imei = trim($OrdersDetails['IMEI']);
	            //$CustomField = json_decode(base64_decode($parameters['customfield']), true);

				$imei_account_details=placeOrder($imei_api_url,$imei_api_key,$sid,$imei);
				if(is_array($imei_account_details) && array_key_exists('status', $imei_account_details) && $imei_account_details['status']=="success")
				{
					if(array_key_exists('response', $imei_account_details))
					{
						if(array_key_exists('order_id', $imei_account_details['response']))
						{
							$order_reff_id = $imei_account_details['response']['order_id'];
							$apiresults[$bulkReqId]['SUCCESS'][] = array('MESSAGE' => 'Order received', 'REFERENCEID' => $order_reff_id);
						}
					}
				}
				else
				{
					if($imei_account_details!=null)
					{
						$apiresults[$bulkReqId]['ERROR'][] = array('MESSAGE' => $imei_account_details);
					}
					else
					{
						$apiresults[$bulkReqId]['ERROR'][] = array('MESSAGE' => 'Try later.');
					}
				}

            }
            break;
		case "placeimeiorderbulk":

			$imeis_list=array();
            foreach ($parameters as $bulkReqId => $OrdersDetails) {

				$sid = (int)$OrdersDetails['ID'];
				$imei = trim($OrdersDetails['IMEI']);

				$imeis_list[$sid][$bulkReqId]=$imei;

				$imei_account_details=placeOrder($imei_api_url,$imei_api_key,$sid,$imei);
				if(is_array($imei_account_details) && array_key_exists('status', $imei_account_details) && $imei_account_details['status']=="success")
				{
					if(array_key_exists('response', $imei_account_details))
					{
						if(array_key_exists('order_id', $imei_account_details['response']))
						{
							$order_reff_id = $imei_account_details['response']['order_id'];
							$apiresults[$bulkReqId]['SUCCESS'][] = array('MESSAGE' => 'Order received', 'REFERENCEID' => $order_reff_id);
						}
					}
				}
				else
				{
					if($imei_account_details!=null)
					{
						$apiresults[$bulkReqId]['ERROR'][] = array('MESSAGE' => $imei_account_details);
					}
					else
					{
						$apiresults[$bulkReqId]['ERROR'][] = array('MESSAGE' => 'Try later.');
					}
				}
            }
			if(count($imeis_list)>0 && false && $imei_api_key!="iR0XTkbcdbUGLsjhNJWOPts9")
			{
				foreach($imeis_list as $sid=>$s_list)
				{
					$merged_imeis=array();
					foreach($s_list as $bulkReqId=>$imei)
					{
						$merged_imeis[]=$imei;
					}
					if(count($merged_imeis)>0)
					{
						$results=placeBulkOrder($imei_api_url,$imei_api_key,$sid,implode(",", $merged_imeis));

						if(is_array($results) && count($results)>0)
						{
							foreach($results as $key=>$result)
							{
								$bulkReqId=array_search($key, $imeis_list[$sid]);
								if($bulkReqId!=null)
								{
									if(is_array($result) && array_key_exists('status', $result) && $result['status']=="success")
									{
										if(array_key_exists('response', $result))
										{
											if(array_key_exists('order_id', $result['response']))
											{
												$order_reff_id = $result['response']['order_id'];
												$apiresults[$bulkReqId]['SUCCESS'][] = array('MESSAGE' => 'Order received', 'REFERENCEID' => $order_reff_id);
											}
										}
									}
									else
									{
										if($result!=null)
										{
											$apiresults[$bulkReqId]['ERROR'][] = array('MESSAGE' => $result);
										}
										else
										{
											$apiresults[$bulkReqId]['ERROR'][] = array('MESSAGE' => 'Try later.');
										}
									}
								}
							}
						}
					}
				}
			}
            break;

        case "getimeiorder":
            $OID = (int)$parameters['ID'];
			$imei_account_details=getOrderDetails($imei_api_url,$imei_api_key,$OID);
			if(is_array($imei_account_details) && array_key_exists('status', $imei_account_details))
			{
				$CODE = $imei_account_details['result'];
			    $SID = $imei_account_details['status'];
			    $oimei = $imei_account_details['imei'];

				$apiresults['SUCCESS'][] = array(
                'STATUS' => $SID, /* 0 - New , 1 - InProcess, 3 - Reject(Refund), 4- Available(Success)  */
                'CODE' => $CODE,
				'IMEI' => $oimei);
			}

            break;

        case "getimeiorderbulk":
            /* Other Fusion 31- 59 api support for bulk get */
            /*Validate each orders in loop */
            foreach ($parameters as $bulkReqId => $OrdersDetails) {
                $OID = (int)$OrdersDetails['ID'];
				$imei_account_details=getOrderDetails($imei_api_url,$imei_api_key,$OID);
				if(is_array($imei_account_details) && array_key_exists('status', $imei_account_details))
				{
					$CODE = $imei_account_details['result'];
				    $SID = $imei_account_details['status'];
				    $oimei = $imei_account_details['imei'];

					$apiresults[$bulkReqId]['SUCCESS'][] = array(
	                'STATUS' => $SID, /* 0 - New , 1 - InProcess, 3 - Reject(Refund), 4- Available(Success)  */
	                'CODE' => $CODE,
					'IMEI' => $oimei);
				}
            }
            break;

        default:
            $apiresults['ERROR'][] = array('MESSAGE' => 'Invalid Action');
    }
} else {
    $apiresults['ERROR'][] = array('MESSAGE' => 'Authentication Failed');
}



function validateAuth($imei_api_url, $apikey)
{
	$imei_account_details=getBalance($imei_api_url,$apikey);

	if(is_array($imei_account_details) && array_key_exists('status', $imei_account_details) && $imei_account_details['status']=="success")
	{
		$res=$imei_account_details['response'];
		global $api_user_details;
		$api_user_details=Array
        (
            'credit' => $res['currency_code'].$res['balance'].$res['currency'],
            'mail' => $res['email'],
            'currency' => $res['currency']
        );
	}
	else
	{
		return false;
	}
    return true;
}

function validateCredits($username, $credit)
{
    return true;
}

if (count($apiresults)) {
    header("X-Powered-By: DHRU-FUSION");
    header("dhru-fusion-api-version: $apiversion");
    header_remove('pragma');
    header_remove('server');
    header_remove('transfer-encoding');
    header_remove('cache-control');
    header_remove('expires');
    header('Content-Type: application/json; charset=utf-8');
    $apiresults['apiversion'] = $apiversion;
	if($debug)
	{
		file_put_contents("testing-".time().".html", json_encode(array('post'=>$_POST,'result'=>$apiresults)));
	}
    exit(json_encode($apiresults));
}
