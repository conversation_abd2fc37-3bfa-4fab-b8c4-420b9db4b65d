<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use App\Casts\AmountCast;
use App\Constants\CommonConstants;


class ServicePlanPrice extends BaseModel
{
    protected $fillable = [
        'service_id',
        'plan_id',
        'price',
        'is_active',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'price' => AmountCast::class,
    ];

    public function service()
    {
        return $this->hasOne(Service::class, 'id', 'service_id');
    }

    public function plan()
    {
        return $this->hasOne(Plan::class, 'id', 'plan_id');
    }

    public static function defaultQuery() {
        return self::query()->where('is_active','=',CommonConstants::YES);
    }
}
