<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use App\Casts\AmountCast;
use App\Components\ProviderFastBulk;
use App\Constants\CommonConstants;

class UserOrder extends BaseModel
{
    protected $fillable = [
        'order_ref',
        'user_id',
        'service_id',
        'provider_id',
        'provider_service_id',
        'imei',
        'price',
        'cost_price',
        'status',
        'result',
        'raw_result',
        'time_taken',
        'batch_ref',
        'created_at',
        'updated_at',
    ];

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function service()
    {
        return $this->hasOne(Service::class, 'id', 'service_id');
    }

    public function provider()
    {
        return $this->hasOne(Provider::class, 'id', 'provider_id');
    }

    public function providerService()
    {
        return $this->hasOne(ProviderService::class, 'id', 'provider_service_id');
    }

    public function deductBalance() {
        if($this->user_id) {
            if(Transaction::saveTransaction($this->user_id, CommonConstants::TRANSACTION_TYPE_ORDER_PLACED, CommonConstants::DEBIT, $this->price, 'Order placed for ' . $this->imei . ' (' . $this->order_ref . ')', ['user_order_id' => $this->id])) {
                return true;
            }
        }
        return false;
    }

    public function printOrderStatus() {
        $orderStatus=CommonConstants::ORDER_PROPERTIES;
        if(array_key_exists($this->status,$orderStatus)) {
            $orderStatus=$orderStatus[$this->status];
            return '<span class="'.$orderStatus['class'].'">'.$orderStatus['text'].'</span>';
        }
        return '';
    }

    public function successOrder($result,$data=null) {
        return null;
        $costPrice=null;
        if($data) {
            if(is_array($data)) {
                if(array_key_exists('price',$data)) {
                    $costPrice=$data['price'];
                }
                $data=json_encode($data);
            }
        }

        $this->status=CommonConstants::ORDER_STATUS_COMPLETED;
        $this->result=$result;
        if($data) {
            $this->raw_result=$data;
        }
        if($costPrice) {
            $this->cost_price=$costPrice;
        }
        $this->updated_at=date(CommonConstants::PHP_DATE_FORMAT);
        $this->time_taken=(strtotime($this->updated_at)-strtotime($this->created_at));
        $this->update(['status','result','cost_price','raw_result','updated_at','time_taken']);
        return $this;
    }

    public function rejectOrder($result,$data=null) {
        return null;
        if($data) {
            if(is_array($data)) {
                $data=json_encode($data);
            }
        }

        $this->status=CommonConstants::ORDER_STATUS_FAILED;
        $this->result=$result;
        if($data) {
            $this->raw_result=$data;
        }
        $this->updated_at=date(CommonConstants::PHP_DATE_FORMAT);
        $this->time_taken=(strtotime($this->updated_at)-strtotime($this->created_at));
        if($this->update(['status','result','raw_result','updated_at','time_taken'])) {
            if($this->user_id) {
                Transaction::saveTransaction($this->user_id,CommonConstants::TRANSACTION_TYPE_ORDER_REFUND,CommonConstants::CREDIT,$this->price,'Order refunded for '.$this->imei.' ('.$this->order_ref.')',['user_order_id'=>$this->id]);
            }
        }
        return $this;
    }

    public function onSaving()
    {
        if($this->order_ref=="" || $this->order_ref==null) {
            $this->order_ref=self::generateOrderReferenceNumber($this->user_id);
        }
        if($this->cost_price=="" || $this->cost_price==null) {
            $providerService=null;
            $provider=Provider::fetchDetailsFromCache($this->provider_id);
            if($provider) {
                if(array_key_exists('services',$provider)) {
                    if(array_key_exists($this->provider_service_id,$provider['services'])) {
                        $providerService=$provider['services'][$this->provider_service_id];
                    }
                }
            }
            if($providerService) {
                $this->cost_price=$providerService['price'];
            }
        }
        parent::onSaving();
    }

    public function onCreated()
    {
        if($this->user_id) {
            //Transaction::saveTransaction($this->user_id,CommonConstants::TRANSACTION_TYPE_ORDER_PLACED,CommonConstants::DEBIT,$this->price,'Order placed for '.$this->imei.' ('.$this->order_ref.')',['user_order_id'=>$this->id]);
        }
        parent::onCreated();
    }

    protected $casts = [
        'price' => AmountCast::class,
        'cost_price' => AmountCast::class,
    ];

    public function placeOrder() {
        try {
            $providerDetails=Provider::fetchDetailsFromCache($this->provider_id);
            if(is_array($providerDetails) && array_key_exists('component',$providerDetails)) {
                $provider=new $providerDetails['component']();
                return $provider->placeOrder($this);
            }
        } catch (\Exception $e) {
            file_put_contents('order_log_'.$this->imei.'_'.time().'.txt',json_encode(['message'=>$e->getMessage(),'trace'=>$e->getTraceAsString()]));
        }
        return ProviderFastBulk::failedResult('Invalid request. Try later.');
    }

    public static function generateOrderReferenceNumber($user_id=null, $timestamp=null, $refNo=null) {
        if(!$user_id) {
            $user_id=CommonConstants::ADMINISTRATIVE;
        }
        if(!$timestamp) {
            $timestamp=time();
        }
        if(!$refNo) {
            $refNo=str_pad(rand(1,999), 3, '0', STR_PAD_LEFT);
        }
        //return date("YdmH",$timestamp).str_pad($user_id, 2, '0', STR_PAD_LEFT).$refNo;
        return $timestamp.str_pad($user_id, 2, '0', STR_PAD_LEFT).$refNo;
    }

    public static function getOrderByReference($order_ref) {
        return self::query()->where('order_ref','=',$order_ref)->first();
    }

    public static function fetchOrderStatus($order_ref,$user=null) {
        $orderDetails=self::getOrderByReference($order_ref);
        if($orderDetails) {
            if($user) {
                if($orderDetails->user_id!=$user->id) {
                    $orderDetails=null;
                }
            }
        }

        if($orderDetails) {
            if($orderDetails->status==CommonConstants::ORDER_STATUS_COMPLETED) {
                return [
                    'order_id'=>"".$orderDetails->order_ref,
                    'reference_id'=>"".$orderDetails->order_ref,
                    'imei'=>"".$orderDetails->imei,
                    'message'=>"Result passed",
                    'result'=>$orderDetails->result,
                    'status'=>'success',
                ];
            } else {
                return [
                    'order_id'=>"".$orderDetails->order_ref,
                    'reference_id'=>"".$orderDetails->order_ref,
                    'imei'=>"".$orderDetails->imei,
                    'message'=>"Result failed",
                    'result'=>$orderDetails->result,
                    'status'=>'error',
                ];
            }
        } else {
            return [
                'message'=>"Result failed",
                'result'=>"Invalid order reference number",
                'status'=>'error',
            ];
        }
    }
}
