<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RabbitMQ Testing Dashboard - {{ config('app.name') }}</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-800 mb-8">RabbitMQ Testing Dashboard</h1>
            
            <!-- Queue Stats -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-semibold mb-4">Queue Statistics</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h3 class="font-medium text-blue-800">IMEI Checks Queue</h3>
                        <p class="text-2xl font-bold text-blue-600" id="imei-queue-count">-</p>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg">
                        <h3 class="font-medium text-green-800">Notifications Queue</h3>
                        <p class="text-2xl font-bold text-green-600" id="notifications-queue-count">-</p>
                    </div>
                    <div class="bg-purple-50 p-4 rounded-lg">
                        <h3 class="font-medium text-purple-800">Default Queue</h3>
                        <p class="text-2xl font-bold text-purple-600" id="default-queue-count">-</p>
                    </div>
                </div>
                <button onclick="refreshStats()" class="mt-4 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    Refresh Stats
                </button>
            </div>

            <!-- Test Forms -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- IMEI Check Job Form -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold mb-4">Test IMEI Check Job</h2>
                    <form id="imei-check-form">
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">User</label>
                            <select name="user_id" class="w-full border border-gray-300 rounded-md px-3 py-2" required>
                                <option value="">Select User</option>
                                @foreach($users as $user)
                                    <option value="{{ $user->id }}">{{ $user->name }} ({{ $user->email }})</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">IMEI</label>
                            <input type="text" name="imei" class="w-full border border-gray-300 rounded-md px-3 py-2" 
                                   placeholder="Enter IMEI (15-17 digits)" required minlength="15" maxlength="17">
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Service ID</label>
                            <input type="number" name="service_id" value="1" class="w-full border border-gray-300 rounded-md px-3 py-2" required>
                        </div>
                        <button type="submit" class="w-full bg-blue-500 text-white py-2 rounded-md hover:bg-blue-600">
                            Dispatch IMEI Check Job
                        </button>
                    </form>
                </div>

                <!-- Notification Job Form -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold mb-4">Test Notification Job</h2>
                    <form id="notification-form">
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">User</label>
                            <select name="user_id" class="w-full border border-gray-300 rounded-md px-3 py-2" required>
                                <option value="">Select User</option>
                                @foreach($users as $user)
                                    <option value="{{ $user->id }}">{{ $user->name }} ({{ $user->email }})</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Notification Type</label>
                            <select name="notification_type" class="w-full border border-gray-300 rounded-md px-3 py-2" required>
                                <option value="welcome">Welcome</option>
                                <option value="order_completed">Order Completed</option>
                                <option value="balance_low">Balance Low</option>
                                <option value="generic">Generic</option>
                            </select>
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Custom Message (for generic)</label>
                            <textarea name="message" class="w-full border border-gray-300 rounded-md px-3 py-2" 
                                      placeholder="Enter custom message for generic notifications"></textarea>
                        </div>
                        <button type="submit" class="w-full bg-green-500 text-white py-2 rounded-md hover:bg-green-600">
                            Dispatch Notification Job
                        </button>
                    </form>
                </div>
            </div>

            <!-- Bulk Job Testing -->
            <div class="bg-white rounded-lg shadow-md p-6 mt-8">
                <h2 class="text-xl font-semibold mb-4">Bulk Job Testing</h2>
                <form id="bulk-job-form" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Job Type</label>
                        <select name="job_type" class="w-full border border-gray-300 rounded-md px-3 py-2" required>
                            <option value="imei_check">IMEI Check</option>
                            <option value="notification">Notification</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Number of Jobs</label>
                        <input type="number" name="count" min="1" max="50" value="5" 
                               class="w-full border border-gray-300 rounded-md px-3 py-2" required>
                    </div>
                    <div class="flex items-end">
                        <button type="submit" class="w-full bg-purple-500 text-white py-2 rounded-md hover:bg-purple-600">
                            Dispatch Bulk Jobs
                        </button>
                    </div>
                </form>
            </div>

            <!-- Results -->
            <div class="bg-white rounded-lg shadow-md p-6 mt-8">
                <h2 class="text-xl font-semibold mb-4">Results</h2>
                <div id="results" class="space-y-2 max-h-96 overflow-y-auto">
                    <p class="text-gray-500">No results yet. Dispatch some jobs to see results here.</p>
                </div>
            </div>

            <!-- Documentation -->
            <div class="bg-white rounded-lg shadow-md p-6 mt-8">
                <h2 class="text-xl font-semibold mb-4">RabbitMQ Integration Documentation</h2>
                <div class="prose max-w-none">
                    <h3 class="text-lg font-medium">Setup Instructions:</h3>
                    <ol class="list-decimal list-inside space-y-2">
                        <li>Install RabbitMQ server on your system</li>
                        <li>Start RabbitMQ service: <code class="bg-gray-100 px-2 py-1 rounded">sudo systemctl start rabbitmq-server</code></li>
                        <li>Enable RabbitMQ management plugin: <code class="bg-gray-100 px-2 py-1 rounded">sudo rabbitmq-plugins enable rabbitmq_management</code></li>
                        <li>Access RabbitMQ management UI at: <a href="http://localhost:15672" target="_blank" class="text-blue-600">http://localhost:15672</a> (guest/guest)</li>
                    </ol>
                    
                    <h3 class="text-lg font-medium mt-6">Worker Commands:</h3>
                    <ul class="list-disc list-inside space-y-2">
                        <li>Start RabbitMQ worker: <code class="bg-gray-100 px-2 py-1 rounded">php artisan rabbitmq:work</code></li>
                        <li>Process specific queue: <code class="bg-gray-100 px-2 py-1 rounded">php artisan rabbitmq:work --queue=imei_checks</code></li>
                        <li>Process with limits: <code class="bg-gray-100 px-2 py-1 rounded">php artisan rabbitmq:work --max-jobs=10 --max-time=300</code></li>
                    </ul>
                    
                    <h3 class="text-lg font-medium mt-6">Available Queues:</h3>
                    <ul class="list-disc list-inside space-y-1">
                        <li><strong>imei_checks</strong> - For IMEI processing jobs</li>
                        <li><strong>notifications</strong> - For user notification jobs</li>
                        <li><strong>default</strong> - Default queue for other jobs</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Set up CSRF token for AJAX requests
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        // Add result to results div
        function addResult(message, type = 'info') {
            const colors = {
                'success': 'text-green-600 bg-green-50',
                'error': 'text-red-600 bg-red-50',
                'info': 'text-blue-600 bg-blue-50'
            };
            
            const timestamp = new Date().toLocaleTimeString();
            const resultHtml = `
                <div class="p-3 rounded-md ${colors[type]} border-l-4 border-${type === 'success' ? 'green' : type === 'error' ? 'red' : 'blue'}-400">
                    <span class="text-sm font-medium">[${timestamp}]</span> ${message}
                </div>
            `;
            
            $('#results').prepend(resultHtml);
            
            // Remove "no results" message if it exists
            $('#results p.text-gray-500').remove();
        }

        // Refresh queue statistics
        function refreshStats() {
            $.get('{{ route("rabbitmq.test.stats") }}')
                .done(function(response) {
                    if (response.success) {
                        $('#imei-queue-count').text(response.stats.imei_checks || 0);
                        $('#notifications-queue-count').text(response.stats.notifications || 0);
                        $('#default-queue-count').text(response.stats.default || 0);
                    } else {
                        addResult('Error getting queue stats: ' + response.message, 'error');
                    }
                })
                .fail(function() {
                    addResult('Failed to get queue statistics', 'error');
                });
        }

        // IMEI Check Form
        $('#imei-check-form').on('submit', function(e) {
            e.preventDefault();
            
            const formData = $(this).serialize();
            
            $.post('{{ route("rabbitmq.test.dispatch-imei") }}', formData)
                .done(function(response) {
                    if (response.success) {
                        addResult(`IMEI check job dispatched successfully. Order ID: ${response.order_id}`, 'success');
                        refreshStats();
                    } else {
                        addResult('Error: ' + response.message, 'error');
                    }
                })
                .fail(function(xhr) {
                    const response = xhr.responseJSON;
                    addResult('Failed to dispatch IMEI check job: ' + (response?.message || 'Unknown error'), 'error');
                });
        });

        // Notification Form
        $('#notification-form').on('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData.entries());
            
            // Add custom message to data if provided
            if (data.message && data.notification_type === 'generic') {
                data.data = { message: data.message };
            }
            delete data.message;
            
            $.post('{{ route("rabbitmq.test.dispatch-notification") }}', data)
                .done(function(response) {
                    if (response.success) {
                        addResult(`Notification job dispatched successfully. Type: ${response.notification_type}`, 'success');
                        refreshStats();
                    } else {
                        addResult('Error: ' + response.message, 'error');
                    }
                })
                .fail(function(xhr) {
                    const response = xhr.responseJSON;
                    addResult('Failed to dispatch notification job: ' + (response?.message || 'Unknown error'), 'error');
                });
        });

        // Bulk Job Form
        $('#bulk-job-form').on('submit', function(e) {
            e.preventDefault();
            
            const formData = $(this).serialize();
            
            $.post('{{ route("rabbitmq.test.dispatch-bulk") }}', formData)
                .done(function(response) {
                    if (response.success) {
                        addResult(`${response.dispatched_count} bulk jobs dispatched successfully`, 'success');
                        refreshStats();
                    } else {
                        addResult('Error: ' + response.message, 'error');
                    }
                })
                .fail(function(xhr) {
                    const response = xhr.responseJSON;
                    addResult('Failed to dispatch bulk jobs: ' + (response?.message || 'Unknown error'), 'error');
                });
        });

        // Load initial stats
        $(document).ready(function() {
            refreshStats();
        });
    </script>
</body>
</html>
