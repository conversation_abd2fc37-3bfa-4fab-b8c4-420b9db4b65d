<?php

namespace App\Components;

use App\Base\Provider\BaseProvider;
use App\Constants\CommonConstants;
use App\Models\Log;
use App\Models\User;
use Illuminate\Support\Facades\Http;

class ProviderAppleCheck extends BaseProvider
{
    public static $providerID = CommonConstants::PROVIDER_APPLE_CHECK;
    public static function placeOrder($orderModel) {
        $validate=self::validateRequest($orderModel);
        if($validate!==true) {
            return $validate;
        }

        $url = self::$providerDetails['api_url'] . "?api_key=".self::$providerDetails['api_key']."&json=true&imei=".$orderModel->imei."&service_id=".self::$providerServiceDetails['sid'];
        $response = Http::get($url);
        $result=$response->body();

        /* $curl = curl_init ($url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 40);
        curl_setopt($curl, CURLOPT_TIMEOUT, 60);
        $result = curl_exec($curl);
        curl_close($curl); */

        if($result && $result!="") {
            return self::parseResult($orderModel,$result);
        }

        return self::defaultResult();
    }

    public static function parseResult($orderModel,$result) {
        try {
            if ($result == "" || $result == false || stripos($result, "Checking your browser")!==false
                || stripos($result, "error code")!==false || stripos($result, "not found")!==false) {
                return self::failedResult(self::SERVICE_MAINTENANCE_MESSAGE,$result);
            }

            $finalResult=null;
            $decode = json_decode($result, true);
            if (is_array($decode) && count($decode) > 0) {
                if(array_key_exists('SUCCESS', $decode)) {
                    if(array_key_exists('Result', $decode['SUCCESS'])) {
                        $finalResult=$decode['SUCCESS']['Result'];
                    }
                }
            }

            if (!$finalResult) {
                return self::failedResult(self::DEFAULT_MESSAGE,$result);
            }

            $sid=self::$providerServiceDetails['sid'];
            switch ($sid) {
                case "119":
                    $new_data = array(
                        'Model' => '', 'Serial' => $orderModel->imei,
                        'Activation Status' => '', 'Activation Date' => '', 'Warranty Status' => '', 'Estimated Purchase Date' => '',

                        'Device Technical Support' => '', 'Telephone Technical Support' => 'Expired',
                        'Telephone Technical Support Expiration Date' => '', 'Telephone Technical Support Expire In' => '',
                        'Telephone Technical Support Expires In' => '', 'Repairs and Service Coverage' => '',

                        'Repairs And Service Coverage' => 'Expired',
                        'Repairs And Service Expiration Date' => '', 'Repairs and Service Expiration Date' => '',
                        'Repairs And Service Expires In' => '', 'Repairs and Service Expires In' => '',
                        'AppleCare Eligible' => '', 'WW GSMA Status' => '', 'Valid Purchase Date' => '',

                        'Device Replaced' => '', 'Device Activated' => '',
                        'Device Unbricked' => '',
                        'AppleCare' => '',
                        'Apple Care' => '', 'Demo Unit' => '', 'Refurbished Device' => '',
                        'Replaced Device' => '', 'Replaced by Apple' => '',
                        'Registered Device' => 'Yes', 'Loaner Device' => 'No',
                    );

                    $new_result = self::getCsvReport($finalResult, true, false, true);
                    if (is_array($new_result) && count($new_result) > 0) {
                        foreach ($new_result as $key => $value) {
                            if (array_key_exists($key, $new_data)) {
                                if ($value != "") {
                                    $new_data[$key] = $value;
                                }
                            }
                        }
                        if (array_key_exists('Generation', $new_result)) {
                            $new_data['Model'] = $new_result['Generation'];
                        }
                        if (array_key_exists('Replaced', $new_result)) {
                            $new_data['Replaced by Apple'] = $new_result['Replaced'];
                        }
                        if (array_key_exists('Loaner', $new_result)) {
                            $new_data['Loaner Device'] = $new_result['Loaner'];
                        }

                        if ($new_data['Activation Status'] != '') {
                            if (trim(strtolower($new_data['Activation Status'])) == "not activated") {
                                $new_data['Activation Status'] = '<span style="color:red;">Unactivated</span>';
                                $new_data['Registered Device'] = "No";
                                $new_data['Telephone Technical Support'] = "";
                                $new_data['Repairs And Service Coverage'] = "";
                                $new_data['Repairs and Service Coverage'] = "";
                            }
                        }

                        if ($new_data['Repairs and Service Expiration Date'] != "") {
                            try {
                                $time = strtotime($new_data['Repairs and Service Expiration Date']);
                                if ($time) {
                                    $lastYear = strtotime("+90 days", strtotime("-1 year", $time));
                                    if ($lastYear >= time()) {
                                        $new_data['Telephone Technical Support'] = "Active";
                                    }
                                    if ($time >= time()) {
                                        $new_data['Repairs And Service Coverage'] = "Active";
                                    }
                                }
                            } catch (\Exception $e) {

                            }
                        }

                        if ($new_data['Replaced by Apple'] != "") {
                            if (trim(strtolower($new_data['Replaced by Apple'])) == "yes") {
                                $new_data['Activation Status'] = '<span style="color:red;">' . $new_data['Activation Status'] . '</span>';
                                $new_data['Replaced by Apple'] = '<span style="color:red;">' . $new_data['Replaced by Apple'] . '</span>';
                                $new_data['Telephone Technical Support'] = "";
                                $new_data['Repairs And Service Coverage'] = "";
                                $new_data['Repairs and Service Coverage'] = "";
                                $new_data['Registered Device'] = "";
                                $new_data['Loaner Device'] = "";
                                $new_data['Warranty Status'] = "";
                            }
                        }

                        if ($new_data['Activation Status'] != "") {
                            if (trim(strtolower($new_data['Activation Status'])) == "activated") {
                                $new_data['Activation Status'] = '<span style="color:green;">' . $new_data['Activation Status'] . '</span>';
                            }
                        }

                        if ($new_data['Warranty Status'] != "") {
                            if (trim(strtolower($new_data['Warranty Status'])) == "no" || stripos($new_data['Warranty Status'], 'out of') !== false) {
                                $new_data['Warranty Status'] = '<span style="color:red;">' . $new_data['Warranty Status'] . '</span>';
                            } else {
                                $new_data['Warranty Status'] = '<span style="color:green;">' . $new_data['Warranty Status'] . '</span>';
                            }
                        }

                        if ($new_data['Valid Purchase Date'] != "") {
                            if (trim(strtolower($new_data['Valid Purchase Date'])) == "no") {
                                $new_data['Valid Purchase Date'] = '<span style="color:red;">' . $new_data['Valid Purchase Date'] . '</span>';
                            } else {
                                $new_data['Valid Purchase Date'] = '<span style="color:green;">' . $new_data['Valid Purchase Date'] . '</span>';
                            }
                        }

                        if ($new_data['AppleCare Eligible'] != "") {
                            if (trim(strtolower($new_data['AppleCare Eligible'])) == "no") {
                                $new_data['AppleCare Eligible'] = '<span style="color:red;">' . $new_data['AppleCare Eligible'] . '</span>';
                            } else {
                                $new_data['AppleCare Eligible'] = '<span style="color:green;">' . $new_data['AppleCare Eligible'] . '</span>';
                            }
                        }

                        if ($new_data['Replaced by Apple'] != "") {
                            if (trim(strtolower(strip_tags($new_data['Replaced by Apple']))) == "yes") {
                                $new_data['Replaced by Apple'] = '<span style="color:red;">' . strip_tags($new_data['Replaced by Apple']) . '</span>';
                            } else {
                                $new_data['Replaced by Apple'] = '<span style="color:green;">' . strip_tags($new_data['Replaced by Apple']) . '</span>';
                            }
                        }

                        if ($new_data['Registered Device'] != "") {
                            if (trim(strtolower(strip_tags($new_data['Registered Device']))) == "no") {
                                $new_data['Registered Device'] = '<span style="color:red;">' . strip_tags($new_data['Registered Device']) . '</span>';
                            } else {
                                $new_data['Registered Device'] = '<span style="color:green;">' . strip_tags($new_data['Registered Device']) . '</span>';
                            }
                        }

                        if ($new_data['Loaner Device'] != "") {
                            if (trim(strtolower(strip_tags($new_data['Loaner Device']))) == "yes") {
                                $new_data['Loaner Device'] = '<span style="color:red;">' . strip_tags($new_data['Loaner Device']) . '</span>';
                            } else {
                                $new_data['Loaner Device'] = '<span style="color:green;">' . strip_tags($new_data['Loaner Device']) . '</span>';
                            }
                        }

                        if ($new_data['Telephone Technical Support'] != "") {
                            if (trim(strtolower(strip_tags($new_data['Telephone Technical Support']))) == "expired") {
                                $new_data['Telephone Technical Support'] = '<span style="color:red;">' . strip_tags($new_data['Telephone Technical Support']) . '</span>';
                            } else {
                                $new_data['Telephone Technical Support'] = '<span style="color:green;">' . strip_tags($new_data['Telephone Technical Support']) . '</span>';
                            }
                        }

                        if ($new_data['Repairs And Service Coverage'] != "") {
                            if (trim(strtolower(strip_tags($new_data['Repairs And Service Coverage']))) == "expired") {
                                $new_data['Repairs And Service Coverage'] = '<span style="color:red;">' . strip_tags($new_data['Repairs And Service Coverage']) . '</span>';
                            } else {
                                $new_data['Repairs And Service Coverage'] = '<span style="color:green;">' . strip_tags($new_data['Repairs And Service Coverage']) . '</span>';
                            }
                        }
                    }

                    $newHtmlResult = "";
                    foreach ($new_data as $key => $val) {
                        if ($val == "") {
                            continue;
                        }
                        $newHtmlResult .= $key . " : " . $val . "<br />";
                    }
                    $finalResult = $newHtmlResult;

                    if($finalResult=="") {
                        return self::failedResult(self::DEFAULT_MESSAGE,$result);
                    }

                    return self::successResult($finalResult,$result);
                    break;
            }

            return self::successResult($finalResult, $result);
        } catch (\Exception $e) {

        }
        return self::defaultResult();
    }
}
