<?php
use App\Components\Helper;
?>
@extends(\App\Components\Helper::getLayoutForUser())
@section('content')
    <main>
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <h1>{{__('Order Logs')}}</h1>
                    <div class="text-zero top-right-button-container">

                    </div>

                    <nav class="breadcrumb-container d-none d-sm-block d-lg-inline-block" aria-label="breadcrumb">
                        <ol class="breadcrumb pt-0">
                            <li class="breadcrumb-item">
                                <a href="{{\App\Components\Helper::dashboardLink()}}">{{__('Dashboard')}}</a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">{{__('Order Logs')}}</li>
                        </ol>
                    </nav>
                    <div class="separator mb-5"></div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-lg-12 col-md-12 mb-4">
                    <div class="card">
                        <div class="card-body table-wrapper">
                            <div class="table-filters-ghost" style="display: none;">
                                <div class="p-b-10 mt-3 searchFiltersContainer">
                                    <div class="p-b-10 searchFilters">
                                        <div class="row">
                                            <div class="col-12 col-md">
                                                <div class="form-group text-left">
                                                    <label for="role">{{ __('Search') }}</label>
                                                    <input type="text" name="q" value="" class="form-control filterField" placeholder="Enter Search">
                                                </div>
                                            </div>
                                            <div class="col-12 col-md">
                                                <div class="form-group text-left">
                                                    <label for="date">{{ __('Date') }}</label>
                                                    <input type="date" name="date" value="" class="form-control filterField" placeholder="Select Date">
                                                </div>
                                            </div>
                                            <div class="col-12 col-md">
                                                <div class="form-group text-left">
                                                    <label for="user_id">{{ __('User') }}</label>
                                                    <select name="user_id" id="user_id" class="form-control filterField search-user-ajax">
                                                        <option value="">All</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-12 col-md">
                                                <div class="form-group text-left">
                                                    <label for="service_id">{{ __('Service') }}</label>
                                                    <select name="service_id" id="service_id" class="form-control filterField select2">
                                                        <option value="">All</option>
                                                        <?php
                                                        $categories=\App\Models\Category::defaultQuery()->get();
                                                        if(count($categories)>0) {
                                                            foreach ($categories as $category) {
                                                                $services=\App\Models\Service::defaultQuery()->where('category_id','=',$category->id)->get();
                                                                ?>
                                                            <optgroup label="<?=$category->name?>">
                                                                <?php
                                                                if(count($services)>0) {
                                                                    foreach ($services as $service) {
                                                                        ?>
                                                                    <option value="<?=$service->id?>"><?=$service->name?></option>
                                                                        <?php
                                                                    }
                                                                }
                                                                ?>
                                                            </optgroup>
                                                                <?php
                                                            }
                                                        }
                                                        ?>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <table id="recordsTable" class="table table-hover align-middle" style="width:100%">
                                <thead>
                                <tr>
                                    <th>{{__('Date')}}</th>
                                    <th>{{__('User')}}</th>
                                    <th>{{__('Service')}}</th>
                                    <th>{{__('Orders')}}</th>
                                    <th>{{__('Price')}}</th>
                                    <th>{{__('Cost')}}</th>
                                    <th>{{__('P&L')}}</th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>



@endsection
@section('pageJs')

    <script>
        $(document).ready(function () {
            let recordsTable = $('#recordsTable');
            let mainTableWrapper = recordsTable.closest(".table-wrapper");
            let filters = mainTableWrapper.find(".table-filters-ghost");
            let dataTableOptions = {!! json_encode(\App\Common\ContentHelper::getDataTableOptions()) !!};
            let recordsTableObj = recordsTable.DataTable({
                ...dataTableOptions,
                processing: false,
                order: [[0, "desc"]],
                ajax: {
                    url: "{{ route('user-order-log.dataTable') }}",
                    data: function (data) {
                        data['search_query'] = {};
                        mainTableWrapper.find(".filterField").each(function () {
                            let obj = $(this);
                            data['search_query'][obj.attr("name")] = obj.val();
                        });
                    },
                    dataSrc: function (json) {
                        let data = json.stats;
                        return json.data;
                    }
                },
                columns: [
                    {data: 'date', name: 'date'},
                    {data: 'user', name: 'user',orderable: false, searchable: false},
                    {data: 'service', name: 'service',orderable: false, searchable: false},
                    {data: 'total_orders', name: 'total_orders'},
                    {data: 'price', name: 'price',orderable: false, searchable: false},
                    {data: 'cost', name: 'cost',orderable: false, searchable: false},
                    {data: 'final_pl', name: 'final_pl',orderable: false, searchable: false},
                ]
            });
            mainTableWrapper.find(".tableFilters").html(filters.html());
            filters.remove();
            $(document).on('keyup change', '.filterField', function () {
                recordsTableObj.draw();
            });
        });


    </script>

@endsection
