<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use Illuminate\Database\Eloquent\Model;

class UserApiKeyLog extends BaseModel
{
    protected $fillable = [
        'user_id',
        'api_key',
        'is_admin',
        'created_at',
    ];

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public static function saveLog($user_id,$oldKey=null) {
        try {
            $model=new UserApiKeyLog();
            $model->user_id=$user_id;
            if(!$oldKey) {
                $model->api_key=$model->user->api_key;
            }
            if($model->save()) {
                return true;
            } else {
                throw new \Exception('Unable to save old api key');
            }
        } catch (\Exception $e) {
            return false;
        }
        return false;
    }
}
