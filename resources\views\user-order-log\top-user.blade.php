<?php
use App\Components\Helper;
?>
@extends(\App\Components\Helper::getLayoutForUser())
@section('content')
    <main>
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <h1>{{__('Top Users')}}</h1>
                    <div class="text-zero top-right-button-container">

                    </div>

                    <nav class="breadcrumb-container d-none d-sm-block d-lg-inline-block" aria-label="breadcrumb">
                        <ol class="breadcrumb pt-0">
                            <li class="breadcrumb-item">
                                <a href="{{\App\Components\Helper::dashboardLink()}}">{{__('Dashboard')}}</a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">{{__('Top Users')}}</li>
                        </ol>
                    </nav>
                    <div class="separator mb-5"></div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-lg-12 col-md-12 mb-4">
                    <div class="card">
                        <div class="card-body table-wrapper">
                            <h5 class="card-title">
                                <?=date("d M, Y",strtotime($startDate))?> - <?=date("d M, Y",strtotime($endDate))?>
                                <select class="float-right" id="select-duration">
                                    <option <?=($duration=="week" ? 'selected=""' : '')?> value="week">Last Week</option>
                                    <option <?=($duration=="this-month" ? 'selected=""' : '')?> value="this-month">This Month</option>
                                    <option <?=($duration=="last-month" ? 'selected=""' : '')?> value="last-month">Last Month</option>
                                </select>
                            </h5>
                            <div class="row mt-5 table-responsive">
                                <table id="recordsTable" class="table w-100 table-hover align-middle" style="width:100%">
                                    <thead>
                                        <tr>
                                            <th>{{__('Orders')}}</th>
                                            <th>{{__('User')}}</th>
                                            <th>{{__('Order Cost')}}</th>
                                            <th>{{__('Purchase Cost')}}</th>
                                            <th>{{__('P&L')}}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        if(count($logs)>0) {
                                            $users = $logs->pluck('user_id')->unique();
                                            if(count($users)>0) {
                                                foreach ($users as $userID) {
                                                    $userLogs = $logs
                                                        ->where('user_id', '=', $userID);
                                                    $user=\App\Models\User::query()->where('id','=',$userID)->first();
                                                    ?>
                                                    <tr>
                                                        <td><?=$userLogs->sum('total_orders')?></td>
                                                        <td><?=$user->displayDetails?></td>
                                                        <td><?=$userLogs->sum('price')?></td>
                                                        <td><?=$userLogs->sum('cost_price')?></td>
                                                        <td><?=$userLogs->sum('final_pl')?></td>
                                                    </tr>
                                                    <?php
                                                }
                                            }
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>



@endsection
@section('pageJs')

    <script>
        $(document).ready(function () {
            let recordsTable = $('#recordsTable');
            let recordsTableObj = recordsTable.DataTable();
        });
        $(document).on("change","#select-duration",function (){
            document.location.href="{{route('user-order-log.topUser',['duration'=>''])}}"+$(this).val();
        });
    </script>

@endsection
