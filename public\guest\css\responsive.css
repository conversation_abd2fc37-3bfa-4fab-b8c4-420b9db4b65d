/* MEDIA QUERIES */
@media (max-width: 1500px) {
  .about__banner__bg {
    top: 50px;
  }
  .imeiCalBg {
    bottom: 50px;
  }
  .imeiCalElement3 {
    bottom: 20px;
  }
  .imeiCalElement4 {
    bottom: 45px;
  }
}

@media (max-width: 1401px) {
  .container__bannerArea {
    padding: 45px 40px 50px 40px;
  }
  .about__banner {
    padding: 80px 0 100px 0;
  }
  .about__banner__bg {
    top: 50%;
    transform: translateY(-50%);
  }
}

@media (max-width: 1199.98px) {
  .bannerText.bannerText--banner {
    padding: 70px 0;
  }
  .paidCheckField {
    padding-top: 100px;
    padding-bottom: 60px;
  }
  .bannerArea {
    padding-left: 15px;
    padding-right: 15px;
  }

  .container__bannerArea {
    padding: 45px 40px 50px 40px;
  }
  .bannerHeader {
    font-size: 50px;
  }
  .bannerText {
    padding-top: 40px;
  }
  .bannerSubHeader {
    font-size: 16px;
  }
  .sectionHeader {
    font-size: 45px;
  }

  .IphoneService {
    padding: 80px 0 60px 0;
  }
  .card__txt {
    font-size: 16px;
  }
  .service_txt h6 {
    font-size: 18px;
    margin-bottom: 10px;
  }
  .service_txt p {
    font-size: 16px;
  }
  .selling_txt {
    margin-left: 30px;
  }
  .selling_txt h6 {
    font-size: 18px;
  }

  .selling_dtls {
    padding: 85px 25px 25px;
  }
  .selling_service p {
    font-size: 18px;
  }
  .rate_txt p {
    font-size: 18px;
  }
  .price_txt h6 {
    font-size: 18px;
  }
  .price_txt h6 span {
    font-size: 26px;
  }
  .topSeller {
    padding-top: 70px;
    padding-bottom: 80px;
  }
  .package_price h2 {
    font-size: 55px;
  }
  .package_price p {
    font-size: 16px;
  }
  .price_list p {
    font-size: 16px;
  }
  .price_list h6 {
    font-size: 18px;
  }
  .price_list {
    margin-bottom: 15px;
  }
  .topPackages {
    padding: 70px 0 80px 0;
  }
  .bannerSubHeader {
    margin-bottom: 20px;
  }
  .checkAdvantage {
    padding-bottom: 40px;
  }
  .freeDelivery {
    padding: 60px 0;
  }
  .delivry_txt p {
    font-size: 16px;
  }
  .client_slider_area {
    padding-top: 20px;
  }
  .client_txt p {
    font-size: 16px;
  }
  .user_txt h6 {
    font-size: 18px;
  }
  .clientFeedback {
    padding: 90px 0 60px 0;
  }
  .blogArea {
    padding: 89px 0 60px 0;
  }
  .blg_area {
    margin-top: 30px;
  }
  .blg_txt_innr h6 {
    font-size: 25px;
  }
  .blg_txt_innr h5 {
    font-size: 25px;
    letter-spacing: 0;
  }
  .blg_txt_innr h5 {
    font-size: 14px;
  }
  .form-label {
    font-size: 18px;
  }
  .inputField {
    font-size: 14px;
  }
  .txt_area h6 {
    font-size: 20px;
  }
  .txt_area p {
    font-size: 16px;
  }
  .txt_area a {
    font-size: 16px;
  }
  .appDownload {
    padding: 100px 0 60px 0;
  }
  .footer__linkPages {
    margin-bottom: 35px;
  }
  .footer__nav {
    flex-direction: column;
  }
  .footer__dwnld__app {
    margin-left: 0;
    margin-top: 20px;
  }
  .footer {
    padding: 90px 0 20px 0;
  }
  .blogBanner {
    padding: 50px 0 0 0;
  }
  .blogTitle {
    font-size: 25px;
    margin-bottom: 15px;
  }
  .blog__text {
    font-size: 16px;
  }
  .blgTxt {
    font-size: 22px;
  }
  .blog__catagories {
    padding: 10px 0;
  }
  .blog__catagories__link {
    font-size: 16px;
  }
  .imeiCal {
    padding: 50px 0 0 0;
  }
  .imeiCalBanner {
    padding-bottom: 80px;
  }
}

@media (max-width: 991.98px) {
  .order_chk-circle {
    display: none;
  }
  .freecheckBanner {
    height: 400px;
  }
  .freecheck_banner_image {
    display: none;
  }
  .mainNav.navbar-light .navbar-nav .nav-item:first-of-type {
    margin-left: 0;
  }
  .mainNav.navbar-light .navbar-nav .nav-link::after {
    content: none;
  }
  .mainNav.navbar-light .navbar-nav .nav-link.active {
    color: #84f1e0;
  }
  .footer__bottom {
    flex-direction: column;
  }
  .bottomFooter {
    margin-top: 20px;
  }
  .appDownload {
    padding: 60px 0;
  }
  .faqPage {
    padding: 70px 0 60px 0;
  }
  .tailoredInfo {
    padding: 90px 0;
  }
  .speed_hdng h5 {
    font-size: 50px;
  }
  .parallax {
    padding: 80px 0;
  }
  .netwrk_nav_innr {
    flex-direction: column;
    padding: 18px;
  }
  .netwrk_nav_img {
    margin-right: 0;
    margin-bottom: 20px;
    margin-left: auto;
    margin-right: auto;
    padding-top: 0;
  }
  .ntwrkPhn {
    display: none;
  }
  .nextgenMobile {
    padding-bottom: 0;
  }
  .ex_area {
    padding-top: 0;
    padding-bottom: 40px;
  }
}

@media (max-width: 767.98px) {
  .tabPlan--item {
    font-size: 14px;
    padding: 8px 15px;
  }
  .plansBannerShape {
    display: none;
  }
  .plansShape {
    display: none;
  }
  .plansBanner {
    padding-top: 60px;
  }
  .txt__lg {
    font-size: 16px;
  }
  .paidCheckBanner {
    height: 450px;
  }
  .checkImei_bg_image {
    display: none;
  }
  .termsBanner {
    padding-top: 50px;
    padding-bottom: 60px;
  }
  .termsBannerCurve {
    top: 50%;
    transform: translateY(-50%);
  }
  .termsQuestions {
    padding-bottom: 50px;
  }
  .parallax {
    padding: 50px 0;
  }
  .bannerImeiCheckbtn .button {
    line-height: 40px;
  }
  .ntwrkSlider {
    box-shadow: none;
  }
  .netwrk_nav_itm {
    margin-bottom: 30px;
  }
  .netwrk_nav_innr1,
  .netwrk_nav_innr2,
  .netwrk_nav_innr3 {
    border-radius: 10px;
    border: 1px solid #000;
  }
  .nextgenMobileShape {
    display: none;
  }
  .login__element1,
  .login__element2,
  .login__element3,
  .login__element4 {
    display: none;
  }
  .button {
    line-height: 40px;
    font-size: 14px;
  }
  .inputField {
    line-height: 36px;
  }
  .bannerTab {
    flex-wrap: wrap;
  }
  .banner__col {
    flex: 0 0 50%;
  }
  .banner__col {
    margin-top: 15px;
  }
  .bannerText {
    padding: 40px 0;
  }
  .bannerArea {
    padding-top: 40px;
  }
  .bannerAction {
    margin-bottom: 30px;
  }
  .sectionHeader {
    font-size: 40px;
  }
  .bannerHeader {
    font-size: 45px;
  }
  .footer__dwnld__app {
    flex-wrap: wrap;
    justify-content: center;
  }
  .footer__dwnld__app > a {
    margin-bottom: 10px;
  }
  .blg_itm {
    width: 100%;
    margin-bottom: 20px;
  }
  .blogArea {
    padding: 89px 0 30px 0;
  }
  .topSeller {
    padding-left: 15px;
    padding-right: 15px;
  }
  .blogRegister {
    padding-left: 0;
    border-left: 0;
  }
  .blogSearch {
    display: none;
  }
  .blogBanner {
    padding: 40px 0 0 0;
  }
  .blogTab {
    margin-bottom: 30px;
  }
  .loginPage {
    margin-top: 30px;
    margin-bottom: 40px;
  }
  .signinheader {
    font-size: 40px;
    margin-bottom: 25px;
  }
  .orSignin {
    font-size: 18px;
  }
  .faqHeader {
    font-size: 40px;
  }
  .about__banner {
    padding: 50px 0;
  }
  .abt__sec {
    padding-top: 60px;
  }
  .about__grid {
    padding-bottom: 90px;
  }
  .txt__md {
    font-size: 16px;
  }
  .blogDetailsPage {
    padding: 70px 0 100px 0;
  }
  .blgDtlsHeader {
    font-size: 35px;
  }
  .back__text {
    font-size: 16px;
  }
  .imeiCalBanner {
    padding-bottom: 50px;
  }
  .imeiCalElement {
    display: none;
  }

  .delivry_txt h6 {
    font-size: 18px;
  }
  .clientTestimonial .client_itm_innr {
    min-height: auto;
  }
}

@media (max-width: 575.98px) {
  .spotlight.spotlight--business {
    min-height: 450px;
    height: 450px;
  }
  .bannerText.bannerText--banner {
    padding: 40px 0;
  }
  .orderPrm {
    padding: 50px 0;
  }
  .order_ch_head {
    font-size: 18px;
  }
  .default-li {
    font-size: 14px;
  }
  .button.button--prm {
    line-height: 60px;
  }
  .orderDtls {
    padding-bottom: 40px;
  }
  .orderDtlsBnr {
    padding-top: 70px;
    padding-bottom: 40px;
  }
  .txt__s {
    font-size: 12px;
  }
  .freecheck_banner_circle {
    display: none;
  }
  .freecheckBannerShape {
    display: none;
  }
  .freecheckBanner {
    height: 350px;
  }
  .CheckImeiSteps {
    padding: 40px 0;
  }
  .findImei {
    padding-bottom: 40px;
  }
  .unlck_phn_name {
    font-size: 16px;
  }
  .prm_srvc_header {
    font-size: 20px;
  }
  .statbox__stats {
    font-size: 18px;
  }
  .termsBanner {
    padding: 40px 0;
  }
  .termsQuestion {
    font-size: 16px;
  }
  .termsQuestionCard {
    margin-bottom: 30px;
  }
  .termsSpace {
    margin-bottom: 28px;
  }
  .termsQuestionsShape {
    display: none;
  }
  .ntwrkName {
    font-size: 18px;
    margin-bottom: 20px;
  }
  .ntwrheading {
    font-size: 28px;
    margin-bottom: 15px;
  }
  .tailoredInfo {
    padding-top: 70px;
    padding-bottom: 40px;
  }
  .nextgenMobile {
    padding-top: 50px;
    padding-bottom: 0;
  }
  .netwrk_nav_innr3 {
    margin-bottom: 0;
  }
  .netwrk_speed {
    margin-right: 0;
  }

  .netwrk_speed_itm {
    padding: 15px;
  }
  .speed_hdng {
    width: 70px;
  }
  .speed_txt h6 {
    margin-bottom: 10px;
  }
  .speed_txt {
    margin-left: 15px;
  }
  .speed_hdng h5 {
    font-size: 40px;
  }
  .ex_area {
    padding-top: 30px;
  }
  .main {
    padding-top: 166px;
  }
  .card--table {
    padding: 20px 25px;
  }
  .card--table--header {
    font-size: 25px;
    margin-bottom: 35px;
  }
  .blgDtlsHeader {
    font-size: 30px;
  }
  .container__bannerArea {
    padding: 35px 30px 40px 30px;
  }
  .topSellerSlider .slick-list {
    overflow: hidden;
    padding: 12px 0 10px 5px;
  }
  .selling_itm {
    margin-right: 10px;
  }
  .headerTop {
    padding-bottom: 15px;
  }
  .headerTop__links {
    font-size: 14px;
  }
  .headerTop__links.active,
  .headerTop__links:focus {
    color: #84f1e0;
  }
  .headerTop__links::after {
    content: none;
  }
  .headerTopRight {
    justify-content: center;
  }
  .langPicker .btn-transparent {
    font-size: 14px;
  }
  .langPicker .dropdown-item {
    font-size: 14px;
  }
  .logo__image {
    width: 80px;
  }
  .bannerHeader {
    font-size: 35px;
  }
  .bannerSubHeader {
    font-size: 14px;
  }
  .bannerArea {
    padding-top: 30px;
  }
  .sectionHeader {
    font-size: 30px;
  }
  .sectionSubheader {
    font-size: 14px;
  }
  .bannerAction {
    margin-bottom: 20px;
  }
  .bannercard__header {
    font-size: 20px;
  }
  .bannercard__text {
    font-size: 14px;
  }
  .IphoneService {
    padding: 40px 0;
  }
  .sectionHeader--line {
    margin-bottom: 20px;
  }
  .card__txt {
    font-size: 14px;
  }
  .service_txt h6 {
    font-size: 16px;
  }
  .service_txt p {
    font-size: 14px;
  }
  .selling_txt h6 {
    font-size: 16px;
  }
  .selling_txt p {
    font-size: 14px;
  }
  .selling_service p {
    font-size: 16px;
  }
  .rate_txt p {
    font-size: 16px;
  }
  .topPackages {
    padding: 60px 0 30px 0;
  }
  .advan_txt p {
    font-size: 14px;
  }
  .advan_num h6 {
    font-size: 14px;
  }
  .advan_tag p {
    font-size: 14px;
  }
  .delivry_txt p {
    font-size: 14px;
  }
  .clientFeedback {
    padding: 70px 0 30px 0;
  }
  .client_slider_area {
    padding-top: 0;
  }
  .blogArea {
    padding: 70px 0 0 0;
  }
  .contactus {
    padding: 70px 0 100px 0;
  }
  .form-label {
    font-size: 16px;
  }
  .contact_area {
    padding: 40px 30px;
  }
  .contact_area_hdng {
    margin-bottom: 25px;
  }
  .contact_list {
    margin-bottom: 20px;
  }
  .txt_area h6 {
    font-size: 18px;
  }
  .txt_area p {
    font-size: 14px;
  }
  .txt_area a {
    font-size: 14px;
  }
  .appDownload {
    padding: 40px 0 20px 0;
  }
  .footer {
    padding: 60px 0 20px 0;
  }
  .footerHeader {
    text-align: center;
  }
  .footer__items {
    text-align: center;
  }
  .footer__linkPages {
    margin-bottom: 20px;
  }
  .bannerImeiCheck {
    width: 320px;
  }
  .service_img {
    text-align: center;
  }

  .selling_img {
    margin-bottom: 10px;
  }
  .selling_img img {
    width: 100%;
  }
  .selling_txt {
    margin-left: 15px;
  }
  .selling_rate {
    flex-direction: column;
  }
  .price_area {
    flex-direction: column;
  }
  .price_txt {
    margin-bottom: 10px;
  }
  .advan_itm_innr {
    padding: 25px 20px;
  }
  .advan_num {
    margin-bottom: 10px;
  }
  .advan_num_area {
    flex-direction: column;
  }
  .footer::after {
    top: -1px;
  }
  .footer__Nav__Item {
    margin-right: 20px;
  }
  .footer__Nav__Item:last-child {
    margin-right: 0;
  }
  .blg_txt_innr {
    padding: 20px;
  }
  .blogTab {
    margin-bottom: 20px;
  }
  .nav-link--blg::after {
    content: none;
  }
  .nav-link--blg {
    padding: 8px 0;
  }
  .nav--blog .nav-item {
    margin-right: 30px;
  }
  .orSignin {
    font-size: 16px;
  }
  .faqPage {
    padding: 50px 0 30px 0;
  }
  .faqHeader {
    font-size: 30px;
  }
  .footer.footer--faq {
    padding: 70px 0 20px 0;
  }
  .faqQuestions__header {
    font-size: 16px;
  }
  .faqAnswer {
    font-size: 14px;
  }
  .contactus.contactus--faq {
    padding: 70px 0 30px 0;
  }
  .txt__md {
    font-size: 14px;
  }
  .abt__sec--shape--left {
    display: none;
  }
  .abt__sec--shape {
    display: none;
  }
  .pagination-link {
    font-size: 16px;
  }

  .contactus--imei {
    padding-bottom: 60px;
  }
  .spotlight-item-content {
    padding: 30px 20px;
  }
  .footer__item {
    margin-bottom: 8px;
  }
  .footerHeader {
    margin-bottom: 15px;
  }
  .footer__items {
    margin-bottom: 25px;
  }
}
@media (max-width: 425px) {
  .main {
    padding-top: 140px;
  }
}
/* END MEDIA QUERIES */
