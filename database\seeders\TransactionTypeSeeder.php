<?php

namespace Database\Seeders;

use App\Constants\CommonConstants;
use App\Models\Plan;
use App\Models\TransactionType;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class TransactionTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $types=[
            ['id'=>CommonConstants::TRANSACTION_TYPE_DEPOSIT,'name'=>'Deposit'],
            ['id'=>CommonConstants::TRANSACTION_TYPE_ORDER_PLACED,'name'=>'Order Placed'],
            ['id'=>CommonConstants::TRANSACTION_TYPE_ORDER_REFUND,'name'=>'Order Refund'],
            ['id'=>CommonConstants::TRANSACTION_TYPE_ADMIN_ADJUSTMENT,'name'=>'Admin Adjustment'],
        ];

        if(count($types)>0) {
            foreach ($types as $row) {
                $checkRow=TransactionType::query()->where('id','=',$row['id'])->first();
                if(!$checkRow) {
                    TransactionType::create($row);
                }
            }
        }
    }
}
