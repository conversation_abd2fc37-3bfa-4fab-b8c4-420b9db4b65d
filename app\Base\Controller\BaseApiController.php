<?php

namespace App\Base\Controller;

use App\Constants\CommonConstants;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class BaseApiController extends \App\Http\Controllers\Controller
{
    public $needAuthentication = true;
    public $user = null;
    public $balance = 0;
    public function setUser($user) {
        $this->user=$user;
        $this->setBalance($user->id);
    }
    public function setBalance($userID) {
        $balances=getUsersCache()['balances'];
        if(is_array($balances) && array_key_exists($userID,$balances)) {
            $this->balance = $balances[$userID];
        }
    }
    public function getUser() {
        return $this->user;
    }
    public function __construct(Request $request) {
        if($this->needAuthentication) {
            $rules = [
                'api-key' => ['required', 'string'],
            ];

            $validator = Validator::make($request->all(), $rules);

            if ($validator->fails()) {
                $errors = [];
                foreach ($validator->messages()->messages() as $k => $v) {
                    if (is_array($v)) {
                        foreach ($v as $message) {
                            $errors[] = $message;
                        }
                    }
                }
                $message = json_encode($this->sendError('Validation error', $errors, 403, true));
                throw new \ErrorException($message, '403');
            } else {
                $findUser = getUsersCache()['users']
                    ->where('api_key', '=', $request->input('api-key'))
                    ->where('is_active', '=', CommonConstants::YES)
                    ->where('is_api_enabled', '=', CommonConstants::YES)
                    ->first();

                if (!$findUser) {
                    $message = json_encode($this->sendError('Invalid API Key', [], 403, true));
                    throw new \ErrorException($message, '403');
                } else {
                    $this->setUser($findUser);
                }
            }
        }
    }

    public function sendError($error, $errorMessages = [], $code = 403, $arrayResponse=false)
    {
        if(is_array($error)) {
            $response = [
                'status' => 'error',
                'response' => $error,
            ];
        } else {
            $response = [
                'status' => 'error',
                'response' => [
                    'message' => $error,
                ],
            ];
        }

        if(!empty($errorMessages)){
            $response['data'] = $errorMessages;
        }
        if($this->getUser()) {
            $response['user']=$this->getUser()->getApiData();
        }

        if($arrayResponse) {
            return $response;
        }
        return response()->json($response, $code);
    }

    public function sendSuccess($data, $code = 200)
    {
        $response = [
            'status' => 'success',
            'response' => $data,
        ];
        if($this->getUser()) {
            $response['user']=$this->getUser()->getApiData();
        }
        return response()->json($response, $code);
    }
}
