<?php

namespace App\Components;

use App\Base\Provider\BaseProvider;
use App\Constants\CommonConstants;
use App\Models\Log;
use App\Models\User;
use Illuminate\Support\Facades\Http;

class ProviderGsxUnlocking extends BaseProvider
{
    public static $providerID = CommonConstants::PROVIDER_GSX_UNLOCKING;
    public static function placeOrder($orderModel) {
        $validate=self::validateRequest($orderModel);
        if($validate!==true) {
            return $validate;
        }

        $url = self::$providerDetails['api_url'] . "?key=".self::$providerDetails['api_key']."&imei=".$orderModel->imei."&srv=".self::$providerServiceDetails['sid'];
        $response = Http::get($url);
        $result=$response->body();

        /* $curl = curl_init ($url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 40);
        curl_setopt($curl, CURLOPT_TIMEOUT, 60);
        $result = curl_exec($curl);
        curl_close($curl); */

        if($result && $result!="") {
            return self::parseResult($orderModel,$result);
        }

        return self::defaultResult();
    }

    public static function parseResult($orderModel,$result) {
        try {
            if ($result == "" || $result == false
                || stripos($result, "Checking your browser")!==false || stripos($result, "error code")!==false) {
                return self::failedResult(self::SERVICE_MAINTENANCE_MESSAGE,$result);
            }

            $finalResult=null;
            $decode = json_decode($result, true);
            if (is_array($decode) && array_key_exists('flag', $decode)) {
                if (strtolower($decode['flag']) == "ok") {
                    if (array_key_exists('object', $decode)) {
                        $finalResult=$decode['object'];
                    } else if (array_key_exists('result', $decode)) {
                        $finalResult=$decode['result'];
                    }
                }
            }

            if (!$finalResult) {
                return self::failedResult(self::DEFAULT_MESSAGE,$result);
            }

            $sid=self::$providerServiceDetails['sid'];
            switch ($sid) {
                case "1175":
                    $new_data = array(
                        'Model Info' => '',
                        'Search Term' => '',
                        'IMEI Number' => '',
                        'Serial Number' => '',
                        'Model Desc' => '',
                        'Model Number' => '',
                        'Model Color' => '',
                        'Model Name' => '',
                        'Warranty Status' => '',
                        'Production Date' => '',
                        'Estimated Warranty End Date' => '',
                        'Device Age' => '',
                        'Country' => '',
                        'Carrier' => '',
                    );

                    $newResult = self::getCsvReport($finalResult, false, false, true);
                    if (is_array($newResult) && count($newResult) > 0) {
                        foreach ($newResult as $key => $val) {
                            if (strtolower($key) == "model") {
                                $new_data['Model Desc'] = $val;
                            }
                            if (strtolower($key) == "imei") {
                                $new_data['IMEI Number'] = $val;
                            }
                            if (strtolower($key) == "sn") {
                                $new_data['Serial Number'] = $val;
                            }
                            if (strtolower($key) == "name") {
                                $new_data['Model Name'] = $val;
                            }
                            if (strtolower($key) == "number") {
                                $new_data['Model Number'] = $val;
                            }
                            if (strtolower($key) == "purchase country") {
                                $new_data['Country'] = $val;
                            }
                            if (strtolower($key) == "purchase date") {
                                $new_data['Production Date'] = $val;
                            }
                            if (strtolower($key) == "coverage end date") {
                                $new_data['Estimated Warranty End Date'] = $val;
                            }
                            if (strtolower($key) == "warranty status") {
                                $new_data['Warranty Status'] = $val;
                            }

                            if (strtolower($key) == "original carrier") {
                                $new_data['Carrier'] = $val;
                            }

                            if (strtolower($key) == "color") {
                                $new_data['Model Color'] = $val;
                            }

                        }

                        if ($new_data['Warranty Status'] != "") {
                            if (trim(strtolower(strip_tags($new_data['Warranty Status']))) == "out of warranty" || trim(strtolower(strip_tags($new_data['Warranty Status']))) == "expired") {
                                $new_data['Warranty Status'] = '<span style="color:red;">' . $new_data['Warranty Status'] . '</span>';
                            } else {
                                $new_data['Warranty Status'] = '<span style="color:green;">' . $new_data['Warranty Status'] . '</span>';
                            }
                        }
                    }

                    $newHtmlResult = "";
                    foreach ($new_data as $key => $val) {
                        if ($val == "") {
                            continue;
                        }
                        $newHtmlResult .= $key . " : " . $val . "<br />";
                    }
                    $finalResult = $newHtmlResult;

                    if($finalResult=="" || $new_data['IMEI Number']=="") {
                        return self::failedResult(self::DEFAULT_MESSAGE,$result);
                    }

                    return self::successResult($finalResult,$result);
                    break;

                case "1063":
                    $new_data = array(
                        'Model' => '', 'IMEI/SN' => '', 'Find My iPhone' => '',
                    );

                    $newResult = self::getCsvReport($finalResult, false, false, true);
                    if (is_array($newResult) && count($newResult) > 0) {
                        foreach ($newResult as $key => $val) {
                            if(array_key_exists($key,$new_data) && $val!="") {
                                $new_data[$key]=$val;
                            }
                        }
                    }

                    $newHtmlResult = "";
                    foreach ($new_data as $key => $val) {
                        if ($val == "") {
                            continue;
                        }
                        $newHtmlResult .= $key . " : " . $val . "<br />";
                    }
                    $finalResult = $newHtmlResult;

                    if($finalResult=="" || $new_data['Find My iPhone']=="") {
                        return self::failedResult(self::DEFAULT_MESSAGE,$result);
                    }

                    return self::successResult($finalResult,$result);
                    break;
            }

            return self::successResult($finalResult, $result);
        } catch (\Exception $e) {

        }
        return self::defaultResult();
    }
}
