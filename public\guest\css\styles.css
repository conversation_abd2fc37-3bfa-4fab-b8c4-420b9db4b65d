@import url("https://fonts.googleapis.com/css2?family=Signika:wght@400;500;600;700&display=swap");



@font-face {

  font-family: "Colfax";

  src: url("../fonts/Colfax-Regular.woff2") format("woff2"),

    url("../fonts/Colfax-Regular.woff") format("woff"),

    url("../fonts/Colfax-Regular.ttf") format("truetype"),

    url("../fonts/Colfax-Regular.svg#Colfax-Regular") format("svg");

  font-weight: normal;

  font-style: normal;

  font-display: swap;

}



@font-face {

  font-family: "Colfax";

  src: url("../fonts/Colfax-Medium.woff2") format("woff2"),

    url("../fonts/Colfax-Medium.woff") format("woff"),

    url("../fonts/Colfax-Medium.ttf") format("truetype"),

    url("../fonts/Colfax-Medium.svg#Colfax-Medium") format("svg");

  font-weight: 500;

  font-style: normal;

  font-display: swap;

}



@font-face {

  font-family: "Colfax";

  src: url("../fonts/Colfax-Bold.woff2") format("woff2"),

    url("../fonts/Colfax-Bold.woff") format("woff"),

    url("../fonts/Colfax-Bold.ttf") format("truetype"),

    url("../fonts/Colfax-Bold.svg#Colfax-Bold") format("svg");

  font-weight: bold;

  font-style: normal;

  font-display: swap;

}

html,

body {

  height: 100%;

}

* {

  transition: all 200ms ease-in-out;

}

:focus,

button:focus {

  outline: none;

}

body {

  font-family: "Colfax";

  font-weight: normal;

  color: #000000;

  background-color: #fff;

  overflow-x: hidden;

  overflow-y: auto;

}



/* RESET */

h1,

h2,

h3,

h4,

h5,

h6,

ul,

p,

figure {

  margin: 0;

  padding: 0;

}

ul {

  list-style: none;

}



a:hover,

a:focus {

  text-decoration: none;

  color: #84f1e0;

}



/* END RESET */



/* UTILITIES */

.main {

  height: 100%;

  display: flex;

  flex-direction: column;

  padding-top: 140px;

}

.contentArea {

  flex: 1 0 auto;

}

.footer {

  margin-top: auto;

}



.para {

  font-size: 15px;

}

.txt--light {

  color: #828282;

}

.txt--red {

  color: #c84040;

}

.txt--green {

  color: #45af57;

}

.txt--orange {

  color: #ff7928;

}

.txt__s {

  font-size: 14px;

}

.txt__md {

  font-size: 16px;

}

.txt__lg {

  font-size: 18px;

}

.card__txt {

  font-size: 16px;

}

.link {

  color: #84f1e0;

}

.bg__creamy {

  background-color: #fcf6ef;

}

.bg__light {

  background-color: #f8f8f8;

}

.font-weight-med {

  font-weight: 500;

}

.sectionHeader {

  font-family: "Signika", sans-serif;

  font-size: 45px;

  font-weight: bold;

  position: relative;

}

.sectionHeader--line {

  padding-top: 25px;

  margin-bottom: 30px;

}

.sectionHeader--line::before {

  content: "";

  position: absolute;

  top: 0;

  left: 0;

  height: 3px;

  background-color: #eed9c4;

  width: 70px;

}

.sectionSubheader {

  font-weight: 500;

}

.form-label {

  color: #000000;

  font-size: 18px;

  font-weight: 500;

}



/* END UTILITIES */



/* COMPONENTS */

.button {

  display: inline-block;

  color: #272b34;

  font-size: 16px;

  font-weight: 500;

  border: 0;

  background-color: #dcdcdc;

  line-height: 49px;

  padding: 0 20px;

  transition: all 0.3s ease-in-out;

  border-radius: 5px;

  text-decoration: none;

}

.button:focus {

  color: #272b34;

  box-shadow: 0 0 0 0.2rem rgba(216, 217, 219, 0.5);

}

.button:hover {

  color: #fff;

  background-color: #222222;

}

.button.button--dark {

  color: #fff;

  background-color: #222222;

}

.button.button--dark:focus {

  color: #fff;

  box-shadow: 0 0 0 0.2rem rgba(82, 88, 93, 0.5);

}

.button.button--dark:hover {

  color: #222222;

  background-color: #dcdcdc;

}

.inputField {

  border: 2px solid #222222;

  line-height: 45px;

  border-radius: 5px;

  padding: 0 20px;

  font-size: 16px;

  color: #222222;

  width: 100%;

  background-color: #fff;

}

.inputField:focus {

  box-shadow: 0 0 0 0.2rem rgba(82, 88, 93, 0.5);

}

textarea.inputField {

  resize: none;

}

.inputField::placeholder {

  color: #bcbcbc;

}

.inputField.inputField--baner {

  padding-right: 140px;

}

.dwnld__btn {

  display: flex;

  align-items: center;

  justify-content: center;

  line-height: 40px;

  font-size: 15px;

  font-weight: 500;

  color: #000;

  border-radius: 5px;

  border: 0;

  background-color: #fff;

  padding: 0 20px;

}

.dwnld__btn .dwnld__btn__icon {

  margin-right: 15px;

}

.dwnld__btn .dwnld__btn__icon svg {

  width: 18px;

}

.dwnld__btn:hover {

  background-color: #000;

  color: #fff;

}

.dwnld__btn:hover .dwnld__btn__icon svg {

  fill: #fff;

}

.dwnld__btn.dwnld__btn--dark {

  background-color: #000;

  color: #fff;

}



.dwnld__btn.dwnld__btn--dark .dwnld__btn__icon svg {

  fill: #fff;

}

.dwnld__btn.dwnld__btn--dark:hover {

  background-color: #fff;

  color: #000;

}

.dwnld__btn.dwnld__btn--dark:hover .dwnld__btn__icon svg {

  fill: #000;

}

.pagination-items {

  display: flex;

  align-items: center;

  flex-wrap: wrap;

}

.pagination__btn {

  display: inline-block;

  text-decoration: none;

  width: 40px;

  height: 40px;

  border-radius: 10px;

  background-color: transparent;

  display: flex;

  justify-content: center;

  align-items: center;

  border: 1px solid #000000;

}

.pagination__btn svg {

  width: 10px;

  fill: #000;

}

.pagination__btn:hover {

  background-color: #000;

}

.pagination__btn:hover svg {

  fill: #fff;

}

.pagination-link {

  font-size: 20px;

  font-weight: 500;

  color: #000;

}

.socialShareBtn {

  display: inline-block;

  text-decoration: none;

  font-size: 16px;

  color: #fff;

  display: flex;

  align-items: center;

  width: 273px;

  border-radius: 5px;

  border: 0;

  line-height: 55px;

  font-size: 18px;

  cursor: pointer;

}

.socialShareBtn:hover {

  color: #fff;

}

.socialShareBtn--icon {

  width: 55px;

  height: 55px;

  border-bottom-left-radius: 5px;

  border-top-left-radius: 5px;

  margin-right: 20px;

  flex-shrink: 0;

  display: flex;

  justify-content: center;

  align-items: center;

}

.socialShareBtn.socialShareBtn--facebook {

  background-color: #364d8c;

}

.socialShareBtn.socialShareBtn--facebook .socialShareBtn--icon {

  background-color: #3c569e;

}

.socialShareBtn.socialShareBtn--linkedin {

  background-color: #c74024;

}

.socialShareBtn.socialShareBtn--linkedin .socialShareBtn--icon {

  background-color: #d24626;

}

.socialShareBtn.socialShareBtn--twitter {

  background-color: #289ad8;

}

.socialShareBtn.socialShareBtn--twitter .socialShareBtn--icon {

  background-color: #2daaf3;

}

.cmn-toggle {

  position: absolute;

  margin-left: -9999px;

  visibility: hidden;

}

.cmn-toggle + label {

  display: block;

  position: relative;

  cursor: pointer;

  outline: none;

  user-select: none;

}



input.cmn-toggle-round + label {

  width: 50px;

  height: 24px;

  background-color: #e3e3e3;

  border-radius: 20px;

  border: 0;

  margin-bottom: 0;

}

input.cmn-toggle-round + label::after {

  content: "";

  display: block;

  position: absolute;

  top: 50%;

  transform: translateY(-50%);

  left: 2px;

  width: 18px;

  height: 18px;

  background-color: #fff;

  border-radius: 100%;

  transition: margin 0.3s;

}

input.cmn-toggle-round + label::before {

  content: "";

  position: absolute;

  top: 0;

  left: 0;

  width: 100%;

  height: 100%;

  background-color: #e3e3e3;

  border-radius: 20px;

  transition: background 0.3s;

}



input.cmn-toggle-round:checked + label::before {

  background-color: #84f1e0;

}

input.cmn-toggle-round:checked + label::after {

  margin-left: calc(100% - 22px);

}

/* END COMPONENTS */

.header {

  position: fixed;

  top: 0;

  left: 0;

  width: 100%;

  background-color: #fff;

  z-index: 100;

}

.header.minimal .headerTop {

  height: 0;

  overflow: hidden;

  border: 0;

  padding-bottom: 0;

}

.headerTop {

  border-bottom: 2px solid rgba(0, 0, 0, 0.2);

}

.headerTop__items:first-of-type {

  margin-right: 30px;

}

.headerTop__links {

  padding: 10px 0;

  display: inline-block;

  color: #1f3c51;

  font-weight: 500;

  position: relative;

}

.headerTop__links::after {

  content: "";

  position: absolute;

  bottom: -2px;

  left: 0;

  width: 0%;

  height: 3px;

  background-color: #84f1e0;

  transition: width 0.3s ease-in-out;

}

.headerTop__links.active::after,

.headerTop__links:hover::after,

.headerTop__links:focus::after {

  width: 100%;

}

.headerTopRight {

  height: 100%;

  display: flex;

  justify-content: flex-end;

  align-items: center;

}

.headerTopSearch {

  margin-right: 30px;

}

.headerTopSearch__field {

  background: #fff url(../images/magnifying-glass.png) no-repeat;

  background-position: center center;

  border: solid 1px #ccc;

  padding: 5px 10px;

  width: 30px;

  border-radius: 10px;

  -webkit-transition: all 0.3s;

  -moz-transition: all 0.3s;

  transition: all 0.3s;

  color: transparent;

  cursor: pointer;

  font-size: 14px;

}

.headerTopSearch__field:hover {

  -webkit-box-shadow: 0 0 2px 1px rgb(132 241 224 / 41%);

  -moz-box-shadow: 0 0 2px 1px rgb(132 241 224 / 41%);

  box-shadow: 0 0 2px 1px rgb(132 241 224 / 41%);

  border-color: #84f1e0;

}

.headerTopSearch__field:focus {

  width: 180px;

  color: #000;

  cursor: auto;

  padding-right: 30px;

  background-position: calc(100% - 10px) center;

  -webkit-box-shadow: 0 0 2px 1px rgb(132 241 224 / 41%);

  -moz-box-shadow: 0 0 2px 1px rgb(132 241 224 / 41%);

  box-shadow: 0 0 2px 1px rgb(132 241 224 / 41%);

  border-color: #84f1e0;

}

.langPicker .btn-transparent {

  color: #1f3c51;

  font-weight: 500;

}

.langPicker .btn-transparent:focus {

  outline: none !important;

}

.langPicker .dropdown-item.active,

.langPicker .dropdown-item:active {

  background-color: #000000;

}

.header.minimal .mainNav {

  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);

}

.mainNav.navbar-light .navbar-nav .nav-item {

  margin-right: 35px;

}

.mainNav.navbar-light .navbar-nav .nav-item:first-of-type {

  margin-left: 20px;

}

.mainNav.navbar-light .navbar-nav .nav-item:last-of-type {

  margin-right: 0;

}



.mainNav.navbar-light .navbar-nav .nav-link {

  font-size: 16px;

  color: #000;

  padding-left: 0;

  padding-right: 0;

  position: relative;

}

.mainNav.navbar-light .navbar-nav .nav-link::after {

  content: "";

  position: absolute;

  bottom: -2px;

  left: 0;

  width: 0%;

  height: 3px;

  background-color: #84f1e0;

  transition: width 0.3s ease-in-out;

}

.mainNav.navbar-light .navbar-nav .nav-link:hover::after,

.mainNav.navbar-light .navbar-nav .nav-link.active::after {

  width: 100%;

}

.mainNav.navbar-light .navbar-nav .nav-link:focus {

  color: #84f1e0;

}



/* SPOTLIGHT  */

.spotlight {

  position: relative;

  min-height: 530px;

  height: 530px;

  overflow: hidden;

}

.spotlight > div {

  height: 100%;

}



.spotlight .slick-list,

.spotlight .slick-slide > div,

.spotlight .slick-slider,

.spotlight .slick-track,

.spotlight .spotlight-item {

  height: 100%;

}



.spotlight-item {

  height: 100%;

  width: 100%;

  display: -webkit-box;

  display: -ms-flexbox;

  display: flex;

  overflow: hidden;

}

element {

  opacity: 1;

}

.spotlight-image {

  position: absolute;

  left: 50%;

  -webkit-transform: translateX(-50%);

  transform: translateX(-50%);

  top: 0;

  bottom: 0;

  z-index: -1;

  background-size: auto;

  background-position: 50%;

  background-repeat: no-repeat;

  max-width: 1920px;

  width: 100%;

  height: auto;

  -webkit-transition: 0.3s ease-in-out;

  transition: 0.3s ease-in-out;

  -o-object-fit: cover;

  object-fit: cover;

  background-color: #777;

  opacity: 1;

  z-index: 0;

}

.spotlight-item .banner--container {

  display: -webkit-box;

  display: -ms-flexbox;

  display: flex;

  height: 100%;

  -webkit-box-align: center;

  -ms-flex-align: center;

  align-items: center;

  /* padding-bottom: 80px; */

  position: relative;

}

.spotlight-item-content {

  /* border: 3px solid #fff; */

  padding: 50px 40px;

  max-width: 100%;

  width: 100%;

  border-radius: 10px;

}

.spotlight-link {

  position: absolute;

  left: 0;

  right: 0;

  top: 0;

  bottom: 0;

}

.spotlight-thumb {

  position: absolute;

  left: 0;

  right: 0;

  bottom: 0;

  z-index: 1;

  cursor: pointer;

}

.spotlight-thumb-inner {

  background-color: transparent;

  overflow: visible;

  padding: 0;

}

#slick-thumb {

  overflow: hidden;

}

#slick-thumb .slick-list {

  margin: 0 -7.5px;

  overflow: visible;

}

/* END SPOTLIGHT  */



.bannerText {

  padding: 50px 0;

}

.bannerText.bannerText--banner {

  padding: 100px 0;

}

.bannerHeader {

  font-family: "Signika", sans-serif;

  font-size: 50px;

  font-weight: bold;

}

.bannerSubHeader {

  font-size: 16px;

  margin-bottom: 30px;

}



.bannerImeiCheck {

  width: 440px;

  margin: 0 auto;

  position: relative;

  z-index: 7;

}



.bannerImeiCheckbtn {

  position: absolute;

  top: 0px;

  right: 0px;

}

.bannerImeiCheckbtn .button {

  border-top-left-radius: 0;

  border-bottom-left-radius: 0;

  line-height: 49px;

}

.bannerArea {

  background: url(../images/bannerImage.png) center / cover no-repeat;

  padding-top: 20px;

  position: relative;

}

.container__bannerArea {

  padding: 45px 70px 50px 70px;

  border: 3px solid #fff;

  border-radius: 10px;

}



.bannerTab {

  display: flex;

  align-items: center;

  margin-right: -15px;

  margin-left: -15px;

  padding: 0 7.5px;

}

.banner__col {

  flex: 0 0 25%;

  padding: 0 7.5px;

}

.bannercardWrapper {

  padding: 0 7.5px;

}

.bannercard {

  padding: 12px 20px;

  background-color: rgba(0, 0, 0, 0.5);

  color: #fff;

  position: relative;

}



.bannercard:hover,

#slick-thumb .slick-current .bannercard {

  background-color: #fff;

}

.bannercard:hover .bannercard__header,

#slick-thumb .slick-current .bannercard .bannercard__header {

  color: #000;

}

.bannercard:hover .bannercard__text,

#slick-thumb .slick-current .bannercard .bannercard__text {

  color: #686868;

}

.bannercard__header {

  font-size: 18px;

  font-weight: 500;

}



.spotlight-thumb

  .slick-active.slick-current

  .bannercard

  .spotlight-timer.animation {

  -webkit-animation-name: increase;

  animation-name: increase;

  -webkit-animation-fill-mode: forwards;

  animation-fill-mode: forwards;

  -webkit-animation-timing-function: linear;

  animation-timing-function: linear;

}

.spotlight-thumb .slick-active.slick-current .bannercard .spotlight-timer {

  height: 2px;

  width: 0;

  position: absolute;

  bottom: 0;

  left: 0;

  /* background-image: linear-gradient(224deg, #753bbd, #c724b1 74%); */

  background-color: #84f1e0;

}

.spotlight-thumb .spotlight-timer.animation {

  -webkit-animation-duration: 3.5s;

  animation-duration: 3.5s;

}

@-webkit-keyframes increase {

  to {

    width: 100%;

  }

}

@keyframes increase {

  to {

    width: 100%;

  }

}

/* FOOTER */

.footer {

  padding: 120px 0 20px 0;

  background-color: #1b1b1b;

  position: relative;

  color: #fff;

}

.footer::after {

  content: "";

  position: absolute;

  top: -1px;

  left: 0;

  width: 100%;

  height: 58px;

  background: url(../images/footer_curve.png) center/cover no-repeat;

}

.footer__linkPages {

  margin-bottom: 60px;

}

.footerHeader {

  font-weight: 500;

  margin-bottom: 25px;

}

.footer__item {

  margin-bottom: 12px;

}

.footer__link {

  font-size: 15px;

  color: #fff;

}

.footer__nav {

  display: flex;

  align-items: center;

  padding-bottom: 20px;

  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

}

.footer__Nav__Item {

  margin-right: 30px;

}

.footer__Nav__link {

  color: #ffffff;

  font-weight: 500;

}

.footer__Nav__Items {

  display: flex;

  align-items: center;

}



.footer__dwnld__app {

  display: flex;

  align-items: center;

  margin-left: auto;

}

.footer__bottom {

  padding-top: 15px;

  display: flex;

  align-items: center;

  justify-content: space-between;

}

.footerLogo {

  display: flex;

  align-items: center;

}

.footerLogo__img {

  margin-right: 23px;

}

.bottomFooter {

  display: flex;

  align-items: center;

}

.bottomFooter__items {

  margin-right: 30px;

}

.copyright__txt {

  font-size: 15px;

}

.bottomFooter__items:last-child {

  margin-right: 0;

}

.bottomFooter__link {

  font-size: 15px;

  color: #fff;

}

/* END FOOTER */

.IphoneService {

  padding: 100px 0 80px 0;

}

.service_itm_innr {

  height: 100%;

  padding: 40px 30px;

  border-radius: 10px;

  box-shadow: 0px 2px 5.5px rgba(0, 0, 0, 0.11);

  background-color: #ffffff;

  /* min-height: 330px; */

  transform: scale(1);

  transition: all 0.3s ease-in-out;

}

.service_itm_innr:hover {

  transform: scale(1.05);

}

.service_img {

  margin-bottom: 20px;

}

.service_txt h6 {

  font-size: 20px;

  line-height: 32px;

  color: #000000;

  font-weight: 500;

  margin-bottom: 15px;

}

.service_txt p {

  font-size: 16px;

}



.topSeller {

  padding-top: 90px;

  padding-bottom: 120px;

  position: relative;

  overflow-x: hidden;

  overflow-y: auto;

}

.topSeller::before {

  content: "";

  position: absolute;

  left: 0;

  top: 0;

  width: 100%;

  height: 33px;

  background: url(../images/topseller_curve.png) 0 0 / cover no-repeat;

}

.topSeller--orderchk {

  background-color: #fcf6ef;

}

.topSeller--orderchk::before {

  height: 27px;

  background: url(../images/topPackage_curve.png) 0 0 / cover no-repeat;

}

.topSeller--imei::before {

  content: none;

}

.topSeller--frecheck {

  background-color: #fcf6ef;

  padding-bottom: 90px;

}

.topSeller--frecheck::before {

  background: url(../images/topPackage_curve.png) 0 0 / cover no-repeat;

  height: 27px;

  top: -1px;

}

.topSellerSlider {

  overflow: hidden;

}

.topSellerSlider .slick-list {

  padding: 12px;

  overflow: visible;

  margin: 0 -15px;

}

.topSellerSlider .slick-track {

  display: flex;

}

.selling_itm {

  padding: 0 15px;

  float: none;

  height: auto;

}

.selling_itm_innr {

  border-radius: 5px;

  box-shadow: 0px 2px 5.5px rgba(0, 0, 0, 0.11);

  background-color: #ffffff;

  position: relative;

  min-height: 460px;

  height: 100%;

  display: flex;

  flex-direction: column;

  transition: box-shadow 0.3s ease-in-out;

}



.selling_itm_innr:hover {

  box-shadow: 0 2px 10.5px 3px rgba(0, 0, 0, 0.11);

}

/* .selling_itm_innr::before,

.selling_itm_innr::after {

  content: "";

  position: absolute;

  width: 0;

  height: 0;

  border: 3px solid transparent;

  border-radius: 5px;

} */

.selling_itm_innr::before {

  top: 0;

  left: 0;

}

.selling_itm_innr::after {

  bottom: 0;

  right: 0;

}

.selling_itm_innr:hover::before,

.selling_itm_innr:hover::after {

  width: 100%;

  height: 100%;

}

.selling_itm_innr:hover::before {

  border-top-color: #84f1e0;

  border-right-color: #84f1e0;

  transition: width 0.25s ease-out, height 0.25s ease-out 0.25s;

}

.selling_itm_innr:hover::after {

  border-bottom-color: #84f1e0;

  border-left-color: #84f1e0;

  transition: border-color 0s ease-out 0.5s, width 0.25s ease-out 0.5s,

    height 0.25s ease-out 0.75s;

}



.selling_dtls {

  flex-grow: 1;

  padding: 95px 30px 25px;

  display: flex;

  align-items: flex-start;

}

.selling_img {

  width: 45px;

  text-align: center;

  display: flex;

  align-items: flex-start;

  justify-content: center;

  flex-shrink: 0;

  padding-top: 5px;

}

.selling_img img {

  width: 100%;

}

.selling_txt {

  margin-left: 28px;

}

.selling_txt h6 {

  font-size: 20px;

  margin-bottom: 10px;

  font-weight: 500;

}

.selling_txt p {

  font-size: 16px;

  line-height: 24px;

  font-weight: normal;

}

.selling_service {

  padding: 15px 30px;

  background-color: #f7f7f7;

}

.selling_service p {

  font-size: 16px;

  color: #000000;

  font-weight: normal;

}

.selling_service p span {

  font-weight: 500;

}

.selling_rate {

  display: flex;

  align-items: center;

  justify-content: space-between;

  padding: 20px 30px;

}

.rate_txt p {

  font-size: 16px;

  line-height: 32px;

  color: #000000;

  font-weight: normal;

}

.price_area {

  padding: 25px 30px;

  display: flex;

  align-items: center;

  justify-content: space-between;

  border-top: 1px solid #ededed;

}

.price_txt h6 {

  font-size: 18px;

  line-height: 32px;

  color: #000000;

  font-weight: normal;

}

.price_txt h6 span {

  font-size: 28px;

  font-weight: 500;

}



.best_seller {

  position: absolute;

  top: -12px;

  left: 50%;

  transform: translateX(-50%);

}

.slider_btn {

  padding-right: 60px;

}

.selling_hdng {

  display: flex;

  align-items: center;

  justify-content: space-between;

  padding-right: 70px;

  margin-bottom: 25px;

}

.slider_btn .slick-arrow {

  font-size: 0;

  background-color: transparent;

  background-size: 30px;

  background-repeat: no-repeat;

  background-position: center center;

  width: 37px;

  height: 31px;

  position: relative;

  z-index: 2;

  cursor: pointer;

  border: 0;

}

.slider_btn .slick-arrow.slick-disabled {

  opacity: 0.2;

}

.slider_btn .slick-prev {

  background-image: url("../images/slide_btn.png");

  padding-left: 60px;

  transform: rotate(180deg);

}

.slider_btn .slick-next {

  background-image: url("../images/slide_btn.png");

}

.papper_img2 {

  position: absolute;

  top: -40px;

  left: 0;

  right: 0;

}

.papper_img3 {

  position: absolute;

  bottom: -40px;

  left: 0;

  right: 0;

  z-index: 1;

}



.slider_btn .slick-arrow.slick-disabled {

  opacity: 0.2;

}

.slider_btn .slick-prev {

  background-image: url("../images/slide_btn.png");

  padding-left: 60px;

  transform: rotate(180deg);

}

.slider_btn .slick-next {

  background-image: url("../images/slide_btn.png");

}



.topPackages {

  padding: 80px 0 100px 0;

  position: relative;

}

.topPackages::before {

  content: "";

  position: absolute;

  top: 0;

  left: 0;

  width: 100%;

  height: 27px;

  background: url(../images/topPackage_curve.png) 0 0 / cover no-repeat;

}



.package_itm_innr {

  background-color: #ffffff;

  border: 1px solid #000000;

  box-shadow: 4.388px 4.092px 0px rgba(0, 0, 0, 0.14);

  border-radius: 10px;

  padding: 40px 25px;

  transition: all 0.3s ease-in-out;

  position: relative;

}

.package_itm_innr.package_itm_innr--best {

  padding-top: 80px;

}

.package_price {

  position: relative;

  padding-bottom: 25px;

  margin-bottom: 20px;

  text-align: center;

}

.package_price::after {

  content: "";

  position: absolute;

  bottom: 0;

  left: 50%;

  transform: translateX(-50%);

  width: 77px;

  height: 4px;

  background-color: #eed9c4;

}

.package_price h6 {

  font-size: 20px;

  color: #000000;

  margin-bottom: 10px;

}

.package_price h2 {

  font-size: 60px;

  letter-spacing: -1px;

  color: #000000;

  margin-bottom: 15px;

}

.package_price p {

  font-size: 16px;

  line-height: 26px;

  color: #000000;

}

.price_list {

  margin-bottom: 30px;

}

.price_list p {

  font-size: 16px;

  line-height: 26px;

  color: #000000;

  margin-bottom: 1px;

}

.price_list h6 {

  font-size: 18px;

  line-height: 30px;

  color: #000000;

  text-decoration: underline;

  font-weight: 500;

}

.package_btn_area {

  text-align: center;

  padding-top: 15px;

}

.package_btn_area .basic_btn {

  margin-right: 5px;

}

.package_itm_innr:hover {

  transform: scaleY(1.05);

  box-shadow: 9.508px 8.866px 12px rgba(0, 0, 0, 0.26);

}

.package_itm_innr .best_seller {

  left: 50%;

  transform: translateX(-50%);

}

.papper_img4 {

  position: absolute;

  bottom: -40px;

  left: 0;

  right: 0;

}

.checkAdvantage {

  padding-top: 80px;

  padding-bottom: 60px;

  background: url(../images/checkAdvantage_bg.png) center / cover no-repeat;

  position: relative;

}

.checkAdvantage::before {

  content: "";

  position: absolute;

  top: 0;

  width: 100%;

  height: 33px;

  background: url(../images/topseller_curve.png) center / cover no-repeat;

}

.advan_slider_area {

  margin-top: 5px;

}

.advan_itm_innr {

  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.08);

  border-radius: 15px;

  background-color: #fff;

  padding: 25px 30px;

  position: relative;

}

.advan_txt p {

  font-size: 16px;

  color: #000000;

  font-weight: normal;

  margin-bottom: 10px;

}

.advan_num_area {

  display: flex;

  align-items: center;

  justify-content: space-between;

}

.advan_num h6 {

  font-size: 16px;

  color: #000000;

  font-weight: 500;

}

.advan_tag p {

  font-size: 16px;

  color: #9e9e9e;

  font-weight: 500;

}

.check_img {

  position: absolute;

  top: -25px;

  left: 50%;

  transform: translateX(-50%);

}

.advan_itm {

  padding-right: 15px;

}

.advan_slider .slick-list {

  padding: 20px 20px 10px;

}

.freeDelivery {

  padding: 80px 0;

}

.freeDelivery--freecheck {

  position: relative;

  padding: 100px 0;

}

.freeDelivery--freecheck::before {

  content: "";

  position: absolute;

  top: -1px;

  height: 33px;

  width: 100%;

  left: 0;

  background: url(../images/topseller_curve.png) 0 0 / cover no-repeat;

}

.delivry_img {

  margin-bottom: 15px;

  min-height: 54px;

  display: flex;

  align-items: flex-end;

  justify-content: center;

  transition: all ease-in-out 0.3s;

}

.delivry_itm:hover .delivry_img {

  transform: translateY(-5px);

}

.delivry_itm:hover .delivry_txt h6 {

  color: #84f1e0;

}

.delivry_itm:hover .delivry_txt p {

  color: #84f1e0;

}

.delivry_txt {

  text-align: center;

}

.delivry_txt h6 {

  font-size: 20px;

  color: #000000;

  font-weight: 500;

  margin-bottom: 10px;

}

.delivry_txt p {

  font-size: 16px;

  color: #000000;

  font-weight: normal;

}

.papper_img8 {

  position: absolute;

  bottom: -40px;

  left: 0;

  right: 0;

  z-index: 1;

}

.clientFeedback {

  padding: 90px 0 100px 0;

  position: relative;

}

.clientFeedback::before {

  content: "";

  position: absolute;

  top: 0;

  left: 0;

  height: 81px;

  width: 100%;

  background: url(../images/clients_curve.png) center / cover no-repeat;

}

.clientFeedback.clientFeedback--nocurve::before {

  background-image: url(../images/clients_no_curve.png);

}

.papper_img7 {

  position: absolute;

  bottom: -40px;

  left: 0;

  right: 0;

}

.client_innr .page_hdng {

  max-width: 545px;

}

.client_itm_innr {

  position: relative;

  border-radius: 20px;

  background-color: #fff;

  box-shadow: 0px 6px 12px rgba(0, 0, 0, 0.07);

  padding: 30px;

  cursor: pointer;

  overflow: hidden;

  min-height: 340px;

}

.client_itm_innr:hover .hover_area {

  transform: translateX(0);

}

.client_txt p {

  font-size: 16px;

  color: #161616;

  font-weight: normal;

}

.user_area {

  margin-top: 25px;

  display: flex;

}

.user_img {

  width: 54px;

  height: 54px;

  border-radius: 50%;

  overflow: hidden;

  margin-right: 15px;

  flex-shrink: 0;

}

.user_img img {

  width: 100%;

}

.user_txt {

  width: 80%;

}

.user_txt h6 {

  font-size: 20px;

  color: #161616;

  font-weight: 500;

}

.user_txt p {

  font-size: 15px;

  color: #555555;

  font-weight: normal;

}

.client_itm {

  padding: 0 15px;

}

.hover_area {

  padding: 40px 30px;

  background-color: #84f1e0;

  text-align: center;

  position: absolute;

  top: 0;

  left: 0;

  right: 0;

  bottom: 0;

  transition: all 0.3s ease-in-out;

  transform: translateX(100%);

}

.hover_hdng h6 {

  font-size: 22px;

  color: #161616;

  font-weight: 500;

}

.hover_rate {

  margin: 15px 0;

}

.hover_rate img {

  margin: 0 auto;

}

.hover_txt {

  margin: 0 auto 35px;

}

.hover_txt p {

  font-size: 16px;

  color: #000000;

  font-weight: normal;

}



.client_slide_btn .slick-arrow {

  border: 0;

  font-size: 0;

  background-color: transparent;

  background-size: 30px;

  background-repeat: no-repeat;

  background-position: center center;

  width: 37px;

  height: 31px;

  position: relative;

  z-index: 2;

  cursor: pointer;

}

.client_slide_btn .slick-arrow.slick-disabled {

  opacity: 0.2;

}

.client_slide_btn .slick-prev {

  background-image: url("../images/slide_btn.png");

  padding-left: 60px;

  transform: rotate(180deg);

}

.client_slide_btn .slick-next {

  background-image: url("../images/slide_btn.png");

}

.client_slider .slick-list {

  padding: 15px 0;

}

.client_slider_area {

  padding-top: 30px;

}

.client_slide_btn {

  padding: 30px 0 0 10px;

}

.client_hover_btn {

  width: 180px;

}



.blogArea {

  position: relative;

  padding: 90px 0 100px 0;

}

.blogArea::before {

  content: "";

  position: absolute;

  top: 0;

  left: 0;

  height: 46px;

  width: 100%;

  background: url(../images/blog_curve.png) center / cover no-repeat;

}



.blg_area {

  margin-top: 40px;

}

.blg_row {

  display: flex;

  flex-wrap: wrap;

  margin: 0 -15px;

}

.blg_itm {

  width: 50%;

  padding: 0 15px;

}

.blg_dtls {

  border-radius: 5px;

  overflow: hidden;

  position: relative;

}

.blg_txt {

  position: absolute;

  top: 0;

  left: 0;

  right: 0;

  bottom: 0;

  background-color: rgba(0, 0, 0, 0.2);

  display: flex;

  align-items: flex-end;

  justify-content: center;

}

.blg_txt_innr {

  padding: 30px;

}

.blg_txt_innr p {

  font-size: 14px;

  letter-spacing: 1px;

  color: #ffffff;

  font-weight: 500;

  margin-bottom: 10px;

}

.blg_txt_innr h6 {

  font-size: 32px;

  color: #ffffff;

  font-weight: 500;

  display: inline-block;

  text-decoration: none;

  text-transform: none;

}

.blg_txt_innr a:hover h6 {

  color: #84f1e0;

}

.blg_itm_innr {

  margin-bottom: 25px;

}

.blg_itm_innr_row {

  display: flex;

  flex-wrap: wrap;

  margin: 0 -15px;

}

.blg_innr_itm {

  width: 50%;

  padding: 0 15px;

}

.blg_txt_innr h5 {

  font-size: 38px;

  letter-spacing: -1px;

  line-height: 50px;

  color: #ffffff;

  font-weight: 500;

}

.blg_txt_innr a {

  font-size: 16px;

  letter-spacing: 1px;

  line-height: 24px;

  color: #ffffff;

  text-decoration: underline;

  text-transform: uppercase;

  font-weight: 500;

  display: inline-block;

}

.blg_txt_innr a:hover {

  color: #84f1e0;

}

.blg_img {

  transition: all 0.5s ease-in-out;

  transform: scale(1);

}

.blg_dtls:hover .blg_img {

  transform: scale(1.3);

}

.blg_img img {

  width: 100%;

}



.contactus {

  padding: 90px 0 130px 0;

  position: relative;

}

.contactus::before {

  content: "";

  position: absolute;

  top: 0;

  left: 0;

  width: 100%;

  height: 27px;

  background: url(../images/topPackage_curve.png) 0 0 / cover no-repeat;

}

.contact_frm {

  padding: 20px 0 0;

  border-top: 1px dashed rgba(0, 0, 0, 0.3);

  margin-top: 15px;

}

.contact_area {

  border-radius: 10px;

  overflow: hidden;

  background-image: url("../images/contact_bg.png");

  background-position: center center;

  background-size: cover;

  background-repeat: no-repeat;

  padding: 50px 38px;

}

.contact_area_hdng {

  margin-bottom: 38px;

}

.contact_area_hdng h5 {

  font-size: 30px;

  line-height: 45px;

  color: #ffffff;

}

.contact_list {

  display: flex;

  margin-bottom: 30px;

}

.contact_list:last-child {

  margin-bottom: 0;

}

.img_area {

  padding-right: 15px;

  width: 45px;

}

.txt_area {

  width: 90%;

}

.txt_area h6 {

  font-size: 18px;

  color: #ffffff;

  margin-bottom: 10px;

}

.txt_area p {

  font-size: 16px;

  color: #ffffff;

  font-weight: normal;

}

.txt_area a {

  font-size: 14px;

  color: #ffffff;

  font-weight: normal;

  margin-bottom: 5px;

  transition: all 0.3s ease-in-out;

}

.txt_area a:hover {

  color: #84f1e0;

}

.papper_img5 {

  position: absolute;

  bottom: -40px;

  left: 0;

  right: 0;

}

.appDownload {

  padding: 150px 0 130px 0;

  position: relative;

}

.appDownload::after {

  content: "";

  position: absolute;

  top: -83px;

  left: 0;

  width: 100%;

  height: 83px;

  background: url(../images/downld_curve.png) 0 0 / cover no-repeat;

}

.appDownloadBgArea {

  position: absolute;

  width: 100%;

  top: 0px;

  left: 0;

}

.appDownload__content {

  position: relative;

  z-index: 1;

}



.blogBanner {

  padding: 70px 0 50px 0;

}

.blogTab {

  margin-bottom: 50px;

}

.nav--blog .nav-item {

  margin-right: 60px;

}

.nav--blog .nav-item:last-of-type {

  margin-right: 0;

}

.nav-link--blg {

  color: #000;

  font-size: 16px;

  display: inline-block;

  position: relative;

  padding: 15px 0;

}

.nav-link--blg::after {

  content: "";

  position: absolute;

  bottom: 0px;

  left: 0;

  width: 0%;

  height: 3px;

  background-color: #84f1e0;

  transition: width 0.3s ease-in-out;

}

.nav-link--blg:hover::after,

.nav-link--blg.active::after {

  width: 100%;

}

.blog {

  margin-bottom: 40px;

}

.blogImage {

  margin-bottom: 25px;

}

.blogImage__thumnail {

  width: 100%;

  max-width: 100%;

}

.blogTitle {

  font-size: 30px;

  font-weight: 500;

  margin-bottom: 20px;

}

.blog__text {

  font-size: 16px;

  color: #151515;

}

.blog__publish__date {

  position: relative;

  padding-bottom: 15px;

  margin-bottom: 15px;

}

.blog__publish__date::after {

  content: "";

  position: absolute;

  bottom: 0;

  left: 0;

  width: 50px;

  height: 3px;

  background-color: #84f1e0;

}



.blogRegister {

  padding-left: 25px;

  border-left: 1px solid rgba(0, 0, 0, 0.1);

}

.blogFields {

  padding-bottom: 35px;

  margin-bottom: 35px;

  border-bottom: 1px solid rgba(0, 0, 0, 0.1);

}

.blogSearch {

  padding-bottom: 25px;

  margin-bottom: 30px;

  border-bottom: 1px solid rgba(0, 0, 0, 0.1);

}

.blgTxt {

  font-size: 22px;

  font-weight: 500;

  margin-bottom: 25px;

}

.blogCtg {

  padding-bottom: 20px;

  border-bottom: 1px solid rgba(0, 0, 0, 0.1);

}

.blog__catagories {

  padding: 12px 0;

  border-bottom: 1px solid rgba(0, 0, 0, 0.1);

}

.blog__catagories__link {

  font-size: 18px;

  font-weight: 500;

  color: #000;

}



.blog__catagories__icon {

  margin-right: 10px;

  flex-shrink: 0;

  display: flex;

  align-items: center;

  justify-content: center;

}

.blog__catagories__icon svg {

  width: 12px;

}

.blog__catagories__link:hover .blog__catagories__icon svg {

  fill: #84f1e0;

}

.loginPage {

  margin-top: 40px;

  margin-bottom: 50px;

}

.elementWrapper {

  position: relative;

  z-index: 2;

}

.login__element1 {

  position: absolute;

  top: 130px;

  left: -15px;

  transform: translateX(-100%);

  z-index: 1;

}

.login__element2 {

  position: absolute;

  top: 120px;

  right: -15px;

  z-index: 1;

}

.login__element3 {

  position: absolute;

  bottom: 80px;

  left: -15px;

  transform: translateX(-100%);

  z-index: 1;

}

.login__element4 {

  position: absolute;

  bottom: 80px;

  right: 30px;

  z-index: 1;

}

.signinheader {

  font-size: 48px;

  margin-bottom: 30px;

  text-align: center;

}

.googleBtn {

  width: 100%;

  display: block;

  line-height: 50px;

  border: 0;

  border-radius: 5px;

  background-color: #286efa;

  color: #fff;

  display: flex;

  align-items: center;

  font-size: 16px;

  font-weight: 500;

  padding: 0;

  padding-left: 2px;

}



.googleBtn--twitter {

  background-color: #03a9f4;

}

.googleBtn--facebook {

  background-color: #3b5999;

}

.googleBtn__logo {

  width: 60px;

  height: 46px;

  display: flex;

  justify-content: center;

  align-items: center;

  flex-shrink: 0;

  background-color: #fff;

  border-bottom-left-radius: 5px;

  border-top-left-radius: 5px;

}

.googleBtn__text {

  text-align: center;

  flex-grow: 1;

  padding: 0 10px;

}

.orSignin {

  font-size: 20px;

  display: flex;

  align-items: center;

  justify-content: center;

}

.orSignin::after,

.orSignin::before {

  content: "";

  height: 2px;

  background-color: #000;

  flex-grow: 1;

  display: block;

}

.keepSignedin {

  display: flex;

  justify-content: space-between;

  align-items: center;

  margin: 16px 0;

}



.pass__reset {

  color: #000;

  text-decoration: underline;

}

.faqPage {

  padding: 70px 0 80px 0;

}

.faqHeader {

  font-size: 50px;

  margin-bottom: 15px;

}

.faqQuestion__card {

  padding: 25px 0;

  border-top: 1px solid rgba(0, 0, 0, 0.15);

}

.faqQuestion__card:last-child {

  border-bottom: 1px solid rgba(0, 0, 0, 0.15);

}

.faqQuestion__card__header {

  display: flex;

  justify-content: space-between;

  align-items: center;

  padding-right: 15px;

}

.faqQuestions__header {

  font-size: 18px;

  font-weight: 500;

  color: #000;

}



.collaspes__icon {

  width: 11px;

  flex-shrink: 0;

  display: flex;

  justify-content: center;

  align-items: center;

  padding-left: 5px;

  transform-origin: center;

  transform: rotate(0deg);

}

.faqQuestion__card__header a[aria-expanded="true"] ~ .collaspes__icon {

  transform: rotate(180deg);

}

.faqQuestion__card__body {

  margin-top: 30px;

}

.faqAnswer {

  font-size: 16px;

  color: #494949;

  margin-bottom: 30px;

}

.faqAnswer:last-child {

  margin-bottom: 25px;

}

.footer.footer--faq::after {

  background-image: url(../images/faq_footer_curve.png);

  height: 77px;

  top: -1px;

}

.about__banner {

  position: relative;

  padding: 110px 0 140px 0;

  z-index: 1;

}

.about__banner__bg {

  position: absolute;

  top: 0;

  left: 0;

  width: 100%;

  z-index: -1;

}

.aboutheader {

  margin-bottom: 30px;

}

.about__grid {

  padding-bottom: 150px;

}

.abt__sec {

  padding-top: 100px;

}

.abt__sec--Hasshape {

  position: relative;

}

.abt__sec--shape {

  position: absolute;

  right: 3%;

  top: 10px;

}

.abt__sec--shape--left {

  position: absolute;

  left: 3%;

  top: 0px;

}



.blogDetailsPage {

  padding: 90px 0 130px 0;

}

.back__icon {

  flex-shrink: 0;

  width: 7px;

  margin-right: 14px;

  display: flex;

  align-items: center;

}

.back__text {

  font-size: 18px;

  color: #000;

}

.blgDtlsHeader {

  font-size: 43px;

}



.blgWriterDetls {

  display: flex;

  align-items: center;

  margin-bottom: 35px;

}

.bgWriterProfile {

  width: 50px;

  height: 50px;

  margin-right: 8px;

  border-radius: 50%;

  flex-shrink: 0;

  display: flex;

  justify-content: center;

  overflow: hidden;

}

.bgWriterProfile > img {

  width: 100%;

  height: 100%;

}

.singleBlgContImg {

  margin-bottom: 35px;

}

.card--table {

  padding: 30px 35px;

  background-color: #fcf6ef;

}

.card--table--header {

  font-size: 35px;

  margin-bottom: 50px;

}

.blgTableBody {

  border: 1px solid #000;

  border-radius: 10px;

  background-color: #fff;

  box-shadow: 4px 4px 0px 0px rgba(0, 0, 0, 0.14);

}

.blgTable th {

  padding: 10px 20px;

  font-size: 16px;

  font-weight: 500;

  border-bottom: 1px solid #000;

  white-space: nowrap;

}

.blgTable td {

  padding: 16px 20px;

  font-size: 16px;

}

.blgTable tbody tr:nth-child(even) {

  background-color: #f2f5f6;

}

.relatedBlog {

  margin-top: 80px;

}

.imeiCalBanner {

  position: relative;

  z-index: 2;

  padding-bottom: 100px;

}

.imeiCal {

  padding: 65px 0 20px 0;

  position: relative;

  z-index: 3;

}

.imeiCal__header {

  margin-bottom: 25px;

  font-weight: bold;

}

.imeiCalHighlight {

  padding: 14px 17px;

  border-radius: 10px;

  background-color: #f6f6f6;

  margin: 22px 0;

  color: #767676;

}

.imeiSrch {

  margin-top: 35px;

}

.imeiCalElement {

  position: absolute;

  z-index: 1;

}

.imeiCalElement1 {

  left: -50px;

  top: 10px;

}

.imeiCalElement2 {

  right: 15px;

  top: 0;

}

.imeiCalElement3 {

  right: -40px;

  bottom: 85px;

}

.imeiCalElement4 {

  left: 0;

  bottom: 65px;

}

.imeiCalBg {

  position: absolute;

  bottom: 30px;

  z-index: -1;

  width: 100%;

  left: 0;

}

.spotlight-item .banner--container.banner--container__business {

  padding-bottom: 0;

}

.spotlight-item

  .banner--container.banner--container__business

  .spotlight-item-content {

  border: 0;

  padding: 0;

  border-radius: 0;

}



.spotlight.spotlight--business .slick-dots {

  position: absolute;

  bottom: 30px;

  left: 50%;

  transform: translateX(-50%);

  display: flex;

  align-items: center;

}



.spotlight.spotlight--business .slick-dots li {

  position: relative;

  display: inline-block;

  width: 17px;

  height: 17px;

  margin: 0 3px;

  padding: 0;

  cursor: pointer;

  margin: 0 2px;

}



.spotlight.spotlight--business .slick-dots li button {

  font-size: 0;

  line-height: 0;

  display: block;

  width: 17px;

  height: 17px;

  padding: 5px;

  cursor: pointer;

  color: transparent;

  border: 0;

  outline: none;

  background: transparent;

}

.spotlight.spotlight--business .slick-dots li button:hover,

.spotlight.spotlight--business .slick-dots li button:focus {

  outline: none;

}

.spotlight.spotlight--business .slick-dots li button:hover:before,

.spotlight.spotlight--business .slick-dots li button:focus:before {

  opacity: 1;

}

.spotlight.spotlight--business .slick-dots li button:before {

  line-height: 12px;

  position: absolute;

  top: 0;

  left: 0;

  width: 12px;

  height: 12px;

  border-radius: 50%;

  content: "";

  text-align: center;

  background-color: #fff;

  -webkit-font-smoothing: antialiased;

  -moz-osx-font-smoothing: grayscale;

}

.spotlight.spotlight--business .slick-dots li.slick-active button:before {

  background-color: #84f1e0;

}



.iphone__hover {

  position: relative;

  padding-top: 20px;

  padding-bottom: 60px;

  min-height: 400px;

  z-index: 2;

}

.iphone__curve__img {

  position: absolute;

  top: 50%;

  left: 50%;

  transform: translate(-50%, -50%);

  width: 100%;

  z-index: 1;

}

.iphone__hover__img {

  /* position: absolute;

  top: 50%;

  left: 50%;

  transform: translate(-50%, -50%); */

  max-width: 427px;

  margin: 0 auto;

}

.float_anim {

  animation-name: floating;

  animation-duration: 3s;

  animation-iteration-count: infinite;

  animation-timing-function: ease-in-out;

}

@keyframes floating {

  from {

    transform: translate(0, 0px);

  }

  65% {

    transform: translate(0, 15px);

  }

  to {

    transform: translate(0, -0px);

  }

}

.tailoredInfo {

  padding: 130px 0;

  position: relative;

}

.tailoredInfo::before {

  content: "";

  position: absolute;

  top: 0;

  left: 0;

  height: 33px;

  background: url(../images/topseller_curve.png) center/cover no-repeat;

  width: 100%;

}



.netwrk_speed {

  background-color: #ffffff;

  margin-right: 30px;

  box-shadow: 4px 4px 0px 0px rgba(0, 0, 0, 0.14);

  border-radius: 10px;

}

.netwrk_speed-shape {

  position: absolute;

  top: 38px;

  z-index: -1;

}

.netwrk_speed-shape1 {

  left: -60px;

}

.netwrk_speed-shape2 {

  left: -8px;

  top: auto;

  bottom: -10px;

}

.netwrk_speed_itm {

  padding: 30px 20px 30px 30px;

  display: flex;

  align-items: center;

  transition: all 0.3s ease-in-out;

  border: 1px solid #000;

  border-bottom: 0;

}

.netwrk_speed_itm:first-child {

  border-top-left-radius: 10px;

  border-top-right-radius: 10px;

}

.netwrk_speed_itm:last-of-type {

  border-bottom: 1px solid #000;

  border-bottom-left-radius: 10px;

  border-bottom-right-radius: 10px;

}

.speed_hdng {

  position: relative;

  padding-bottom: 16px;

  width: 98px;

  flex-shrink: 0;

}

.speed_hdng::after {

  content: "";

  position: absolute;

  bottom: 0;

  left: 0;

  width: 37px;

  height: 4px;

  background-color: #eed9c4;

}

.speed_hdng h5 {

  font-size: 60px;

  font-weight: bold;

}

.speed_txt {

  margin-left: 20px;

  flex-grow: 1;

}

.speed_txt h6 {

  font-size: 18px;

  font-weight: 500;

  margin-bottom: 15px;

}



.itm_active {

  margin-right: -30px;

  border-right: 1px solid #000;

  border-radius: 0 10px 10px 0;

  background-color: #fff;

  box-shadow: 9.508px 8.866px 12px rgba(0, 0, 0, 0.26);

  border-top: 1px solid #000;

  border-bottom: 1px solid #000;

}

.netwrk_txt {

  margin-left: 65px;

}

.netwrk_hdng h6 {

  font-size: 18px;

  line-height: 26px;

  color: #000000;

  font-family: "Colfax-Regular";

  margin-bottom: 20px;

}

.netwrk_hdng h2 {

  font-size: 30px;

  color: #000000;

  line-height: 35px;

  margin-bottom: 10px;

}

.netwrk_hdng p {

  font-size: 16px;

  line-height: 24px;

  color: #000000;

}

.netwrk_hdng {

  margin-bottom: 40px;

}



.ntwrkName {

  font-size: 20px;

  margin-bottom: 25px;

}

.ntwrheading {

  font-size: 35px;

  font-weight: bold;

  margin-bottom: 25px;

}

.ntwrk_txt {

  margin-bottom: 30px;

}

.ntwrkPhn {

  margin-left: -55px;

}

.parallax {

  padding: 120px 0;

  background: #000 url(../images/nxt_gen.png) center/ cover no-repeat;

  background-attachment: fixed;

}

.parallax.nextgen {

  background: #000 url(../images/bg-nextgen.jpg) center/ cover no-repeat;

}

.parallaxtxt {

  color: #fff;

}

.prlx_txt {

  margin-bottom: 45px;

}

.nextgenMobile {

  padding: 65px 0;

  background-color: #fcf6ef;

  position: relative;

  z-index: 2;

}

.nxtGenTop {

  margin-bottom: 60px;

}

.nextgenMobileShape {

  position: absolute;

}

.nextgenMobileShape1 {

  top: 75px;

  left: 3%;

}

.nextgenMobileShape2 {

  top: 150px;

  right: 4%;

}

.nextgenMobileShape3 {

  top: 280px;

  left: 3%;

}

.nextgenMobileShape4 {

  top: 450px;

  right: 4%;

}

.ntwrkSlider {

  box-shadow: 4px 4px 0px 0px rgba(0, 0, 0, 0.14);

  border-radius: 10px;

}

.netwrk_nav_innr {

  padding: 20px 20px 20px 35px;

  display: flex;

  height: 100%;

  border: 1px solid #000000;

  border-right: 0;

  transition: all 0.3s ease-in-out;

  background-color: #fff;

}

.netwrk_nav_innr1 {

  border-right: 0;

  border-radius: 10px 0 0 10px;

}

.netwrk_nav_innr3 {

  border-right: 1px solid #000000;

  border-radius: 0 10px 10px 0;

}

.netwrk_nav_txt h6 {

  font-size: 18px;

  font-weight: 500;

  margin-bottom: 15px;

}



.netwrk_nav_img {

  padding-top: 10px;

  width: 42px;

  flex-shrink: 0;

  margin-right: 30px;

  overflow: hidden;

}

.netwrk_nav_img img {

  width: 100%;

}

.netwrk_nav_txt {

  flex-grow: 1;

}



.slider_active {

  margin-top: -30px;

}

.slider_active .netwrk_nav_innr {

  border-top: 1px solid #000;

  border-radius: 10px 10px 0 0;

  background-color: #fff;

  box-shadow: 9.508px 8.866px 12px rgba(0, 0, 0, 0.26);

  border-right: 1px solid #000000;

}

.ex_area {

  padding: 55px 0;

}

.ex_image {

  height: 40px;

  margin-bottom: 18px;

}

.ex_image img {

  height: 100%;

}

.termsBanner {

  padding-top: 75px;

  padding-bottom: 85px;

  position: relative;

  z-index: 2;

}

.termsBannerCurve {

  position: absolute;

  width: 100%;

  top: 0;

  left: 0;

  z-index: -1;

}

.termsQuestionCard {

  margin-bottom: 38px;

}

.termsQuestions {

  padding-bottom: 70px;

  position: relative;

  z-index: 2;

}

.termsQuestionsShape {

  position: absolute;

  z-index: 1;

}

.termsQuestionsShape1 {

  bottom: 190px;

  left: 3%;

}

.termsQuestionsShape2 {

  bottom: 190px;

  right: 3%;

}

.termsQuestion {

  font-size: 18px;

  font-weight: bold;

  margin-bottom: 10px;

}



.termsSpace {

  margin-bottom: 35px;

}

.termsItem {

  display: flex;

  align-items: center;

  margin-bottom: 5px;

}

.termsItem:last-of-type {

  margin-bottom: 0;

}

.termsItem__arrow {

  width: 11px;

  flex-shrink: 0;

  margin-right: 15px;

  display: flex;

  align-items: center;

}

.testimonialBanner {

  padding-top: 55px;

  padding-bottom: 100px;

  background: url(../images/testimonial_bg.png) top center no-repeat;

  background-size: 100%;

  background-size: 100%;

}

.testimonialBannerText {

  text-align: center;

}

.testimonialbnrtxt {

  font-size: 20px;

  font-weight: 500;

  color: #84f1e0;

  margin-bottom: 15px;

}

.testimonialBnrVideo {

  margin-top: 70px;

}

.testimonoalPlaylist {

  margin-top: 25px;

  position: relative;

}

.testimonoalPlaylistSlide {

  display: flex;

  align-items: center;

  justify-content: center;

  overflow: hidden;

  margin: 0 10px;

}



.testimonoalPlaylistSlide img {

  width: 100%;

}

.testimonoalPlaylistSlider {

  overflow: hidden;

}

.testimonoalPlaylistSlider .testimonoalPlaylistSlide.slick-current img {

  -webkit-filter: grayscale(100%);

  filter: grayscale(100%);

}

.testimonoalPlaylistSlider .slick-list {

  overflow: visible;

  margin: 0 -10px;

}

.testimonoalsliderArrow {

  position: relative;

}

.testimonoalsliderArrow .slick-arrow {

  position: absolute;

  top: 50%;

  transform: translateY(-50%) rotate(0deg);

  left: -100px;

}

.testimonoalsliderArrow .slick-arrow.slick-prev {

  transform: translateY(-50%) rotate(180deg);

}

.testimonoalsliderArrow .slick-arrow.slick-next {

  position: absolute;

  top: 50%;

  left: auto;

  right: -100px;

}

.clientTestimonialSec {

  position: relative;

  margin-top: 35px;

}

.clientTestimonial {

  padding-bottom: 80px;

}

.clientTestimonialSlider {

  overflow: hidden;

}

.clientTestimonialSlider .slick-list {

  overflow: visible;

  margin: 0 -15px;

}

.clientTestimonialSlide {

  padding: 40px 30px 30px 30px;

  background: #f3f3f3 url(../images/quote.png) 20px 15px no-repeat;

  margin: 0 15px;

}

.clientTestimonialTxt {

  font-size: 18px;

}

.clientTestimonial__com {

  color: #646464;

}

.excellentReview {

  padding: 45px 40px;

  background-color: #84f1e0;

  border-radius: 10px;

}

.clntreview {

  font-size: 18px;

  font-weight: 500;

}

.excellentReview {

  margin: 40px 0;

}

.freecheckBanner {

  background-color: #fcf6ef;

  background-size: 100%;

  height: 520px;

  position: relative;

  display: flex;

  align-items: center;

  z-index: 4;

  overflow: hidden;

}

.freecheckBanner::after {

  content: "";

  position: absolute;

  bottom: 0;

  left: 0;

  width: 100%;

  height: 36px;

  background: url(../images/freecheck_banner_curve.png) center / cover no-repeat;

  z-index: 6;

}

.freecheckBannerShape {

  position: absolute;

  top: 80px;

  z-index: -1;

}

.freecheckBannerShape1 {

  left: 5%;

  transform: rotate(235deg);

}

.freecheckBannerShape2 {

  right: 5%;

}

.freecheckInsideBtxt {

  position: relative;

  z-index: 7;

}

.bannerFreeText {

  position: relative;

  z-index: 4;

  height: 100%;

  display: flex;

  align-items: center;

}

.freecheck_banner_image {

  position: absolute;

  bottom: 0;

  right: 20px;

  z-index: 2;

}

.freecheck_banner_circle {

  position: absolute;

  bottom: 0;

  right: 0;

  transform: translate(20%, 50%);

}

.CheckImeiSteps {

  position: relative;

  padding: 85px 0;

  z-index: 1;

}



.checkImei_bg_image {

  position: absolute;

  bottom: -60px;

  left: 0;

  width: 100%;

  z-index: -1;

}

.findImei {

  padding-top: 30px;

  padding-bottom: 100px;

}

.checkUl__items {

  padding-left: 35px;

  position: relative;

  margin-bottom: 15px;

}

.checkUl__items:last-of-type {

  margin-bottom: 0;

}

.checkUl__items::before {

  content: "";

  position: absolute;

  left: 0;

  top: 2px;

  width: 18px;

  height: 18px;

  border-radius: 50%;

  background: url(../images/checked.png) center / cover no-repeat;

}

.prm_srvc {

  padding: 30px 25px;

  background: url(../images/premium_srvc_bg.png) center / cover no-repeat;

  border-radius: 10px;

  margin: 35px 0;

}

.prm_srvc_header {

  font-size: 22px;

  font-weight: 500;

  margin-bottom: 15px;

}

.unlckStats {

  display: flex;

  align-items: center;

  padding-bottom: 20px;

  flex-wrap: wrap;

  border-bottom: 1px solid rgba(0, 0, 0, 0.1);

}

.statbox {

  background-color: #eaeaea;

  border-radius: 5px;

  display: inline-flex;

  align-items: center;

  padding: 8px 15px;

  font-weight: 500;

}

.statbox__stats {

  font-size: 20px;

}

.unlockedItem {

  padding: 15px 15px 25px 15px;

  border-bottom: 1px solid rgba(0, 0, 0, 0.1);

  display: flex;

  align-items: flex-end;

}



.unlocked__phn__img {

  width: 33px;

  overflow: hidden;

  flex-shrink: 0;

  margin-right: 20px;

}

.unlocked__phn__desc {

  padding-top: 5px;

  flex-grow: 1;

  display: flex;

  justify-content: space-between;

  flex-wrap: wrap;

}

.unlck_phn_name {

  font-size: 18px;

  font-weight: 500;

  margin-bottom: 8px;

}

.unlck_phn_off_txt {

  font-size: 14px;

  color: #8a8a8a;

}

.unlck__phn__details {

  max-width: 100px;

}

.paidCheckBanner {

  background-color: #fcf6ef;

  height: 520px;

  position: relative;

  z-index: 4;

  overflow: hidden;

}

.paidCheckField {

  padding-top: 120px;

  padding-bottom: 100px;

}

.advantageSlider--paid {

  position: relative;

  top: -60px;

  z-index: 10;

}

.faqPage--paid {

  padding: 90px 0 80px 0;

  background-color: #fcf6ef;

  position: relative;

}

.faqPage--paid::before {

  content: "";

  position: absolute;

  top: 0;

  left: 0;

  width: 100%;

  height: 27px;

  background: url(../images/topPackage_curve.png) 0 0 / cover no-repeat;

}

.plansBanner {

  padding-top: 90px;

  position: relative;

}

.plansBannerShape {

  position: absolute;

  bottom: 100px;

  z-index: -1;

}

.plansBannerShape1 {

  left: 5%;

}

.plansBannerShape2 {

  right: 5%;

}

.bannerToggle {

  padding-top: 30px;

  padding-bottom: 50px;

  display: flex;

  justify-content: center;

  align-items: center;

}

.tabPlan {

  display: inline-flex;

}

.tabPlan--item {

  text-transform: uppercase;

  font-size: 18px;

  padding: 10px 25px;

  border-top: 1px solid #f0f0f0;

  border-bottom: 1px solid #f0f0f0;

  border-right: 1px solid #f0f0f0;

  cursor: pointer;

}

.tabPlan--item:first-of-type {

  border-left: 1px solid #f0f0f0;

}

.tabPlan--item:hover,

.tabPlan--item.active {

  background-color: #fcf6ef;

  border-bottom: 1px solid #fcf6ef;

  border-top: 3px solid #eed9c4;

}

.plans {

  background-color: #fcf6ef;

  padding: 60px 0;

  position: relative;

  z-index: 2;

}

.plansShape {

  position: absolute;

  top: 50%;

  transform: translateY(-50%);

  z-index: 1;

}

.plansShape1 {

  left: 3%;

}

.plansShape2 {

  right: 3%;

}

.planBox {

  overflow-x: auto;

  overflow-y: hidden;

  padding-top: 20px;

}

.planBox__row {

  min-width: 800px;

}

.planBg {

  background-color: #fff;

  padding: 40px 25px;

  height: 100%;

}

.planBg__left {

  border-top-left-radius: 10px;

  border-bottom-left-radius: 10px;

}

.plans_underline_txt {

  position: relative;

  font-weight: 500;

  font-size: 18px;

  margin-bottom: 40px;

}

.plans_underline_txt::after {

  content: "";

  position: absolute;

  bottom: 0;

  left: 0;

  width: 128px;

  height: 3px;

  background-color: #84f1e0;

}

.arrowUl {

  list-style: none;

}

.arrowUl li {

  padding-left: 20px;

  position: relative;

  margin-bottom: 10px;

}

.arrowUl li::after {

  content: "";

  position: absolute;

  left: 0;

  top: 5px;

  width: 8px;

  height: 13px;

  background: url(../images/ul_right_arrow.png) 0 0 / cover no-repeat;

}

.arrowUl li a {

  color: #000;

}

.planCol {

  border-left: 1px solid #f7f7f7;

  background-color: #fff;

  height: 100%;

  padding-bottom: 15px;

}

.planCol--bestslr {

  position: relative;

}

.planCol--bestslr::after {

  content: "";

  position: absolute;

  top: -10px;

  left: 18px;

  width: 128px;

  height: 34px;

  background: url(../images/best_slr_plan.png) 0 0 / cover no-repeat;

}

.planName {

  font-size: 22px;

  font-weight: bold;

  margin-bottom: 5px;

}

.plan__underlineTxt {

  position: relative;

  padding-bottom: 15px;

  font-size: 15px;

}

.plan__underlineTxt::after {

  content: "";

  position: absolute;

  background-color: #eed9c4;

  width: 70px;

  height: 3px;

  left: 0;

  bottom: 0;

}

.plan__topTxt {

  padding: 40px 25px 20px 25px;

  border-bottom: 1px solid #f7f7f7;

}

.planDescription {

  padding: 25px;

}

.planDescription:nth-child(even) {

  background-color: #f7f7f7;

}

.lookupSrvc {

  padding: 100px 0 70px 0;

  position: relative;

}

.lookupSrvc::before {

  content: "";

  position: absolute;

  top: -1px;

  height: 33px;

  width: 100%;

  left: 0;

  background: url(../images/topseller_curve.png) 0 0 / cover no-repeat;

}

.resultModalContent {

  width: 100%;

  background-color: #fff;

  border-radius: 10px;

  padding: 60px 30px 35px 30px;

  pointer-events: auto;

  outline: 0;

  position: relative;

}

.modal__close {

  position: absolute;

  top: 20px;

  right: 20px;

}

.searchRes {

  font-size: 30px;

  font-weight: 500;

  margin-bottom: 60px;

}

.searchRes__off__txt {

  color: #7c7c7c;

}

.serchModel {

  font-size: 36px;

  font-weight: normal;

  margin-bottom: 30px;

}

.serchModel__variant {

  font-size: 20px;

}

.srchModelFeatures {

  margin-bottom: 40px;

}

.srchModelFeature {

  margin-bottom: 10px;

}

.srchModelFeature--stat {

  display: flex;

  align-items: center;

}

.order__smry {

  margin-bottom: 60px;

}

.button--prm {

  line-height: 60px;

}

.modal__close__btn {

  background: transparent;

  border: 0;

  padding: 5px;

  display: flex;

  justify-content: center;

  align-items: center;

}

.orderDtls {

  padding-bottom: 80px;

}

.orderDtlsBnr {

  padding-top: 100px;

  padding-bottom: 50px;

}

.border-dashed-bottom {

  border-bottom: 1px dashed #221122;

}

.border-dashed-top {

  border-top: 1px dashed #221122;

}

.border-dashed-top-off {

  border-top: 1px dashed #cccccc;

}

.border-dashed-bottom-off {

  border-bottom: 1px dashed #cccccc;

}

.order-padding {

  padding: 20px 15px;

}

.nameOrder {

  font-size: 22px;

}

.order__confidence {

  padding: 45px 0;

}

.paymentBox {

  margin-top: 15px;

  padding: 20px 25px 10px 25px;

  border-radius: 5px;

  border: 1px solid #cccccc;

  display: flex;

  justify-content: center;

  align-items: center;

  flex-wrap: wrap;

}

.paymentCard {

  display: flex;

  align-items: center;

  margin-right: 20px;

  margin-bottom: 10px;

}

.cardThumb {

  margin-left: 15px;

  width: 53px;

  overflow: hidden;

  display: flex;

  justify-content: center;

  align-items: center;

}

.acceptedCards {

  margin-bottom: 30px;

}

.take_advantage {

  font-size: 38px;

  position: relative;

  font-weight: bold;

  padding-bottom: 20px;

  margin-bottom: 10px;

}

.take_advantage::after {

  content: "";

  position: absolute;

  bottom: 0;

  height: 4px;

  width: 70px;

  background-color: #eed9c4;

  left: 0;

}

.orederChk__banner {

  padding-top: 80px;

  padding-bottom: 110px;

  background-color: #fcf6ef;

  position: relative;

  overflow: hidden;

}

.order_chk-circle {

  position: absolute;

  right: 0;

  transform: translateX(-45%);

  bottom: -10%;

  z-index: 2;

}

.orederChk__banner::after {

  content: "";

  position: absolute;

  bottom: 0;

  left: 0;

  width: 100%;

  background: url(../images/freecheck_banner_curve.png) 0 0 / cover no-repeat;

  height: 30px;

  z-index: 3;

}

.order_sm_box {

  padding: 30px 25px;

  background-color: #fff;

  border-radius: 10px;

  position: relative;

}

.order_sm_padding {

  padding: 15px 0;

}

.orderPrm {

  padding: 100px 0;

}

.order_ch_head {

  font-weight: 500;

  font-size: 22px;

  margin-bottom: 5px;

}



.default-li {

  position: relative;

  color: #434343;

  padding-left: 15px;

  margin-bottom: 5px;

}

.default-li::after {

  position: absolute;

  content: "";

  left: 0;

  top: 7px;

  width: 8px;

  height: 8px;

  border-radius: 100%;

  background-color: #010101;

}

.order_sm_wrap {

  position: relative;

}

.order_sm_wrap_shape {

  position: absolute;

  right: -14px;

  top: 11px;

  z-index: -1;

}



/* Loder */

.loader {

  position: fixed;

  top: 0;

  left: 0;

  height: 100vh;

  width: 100vw;

  background-color: #000;

  display: flex;

  justify-content: center;

  align-items: center;

  z-index: 9999;

}

@keyframes rotate {

  to {

    transform: rotate(360deg);

  }

}

.arc {

  position: relative;

  width: 100px;

  height: 100px;

}



.arc:before,

.arc:after {

  border-bottom: 4px solid #84f1e0;

  border-left: 4px solid transparent;

  border-radius: 50%;

  border-right: 4px solid #84f1e0;

  border-top: 4px solid #84f1e0;

  bottom: 0;

  box-sizing: border-box;

  content: "";

  left: 0;

  margin: auto;

  position: absolute;

  right: 0;

  top: 0;

  transform-origin: center center;

}



.arc:before {

  animation: rotate 1s ease-in-out infinite;

  height: 100%;

  width: 100%;

}



.arc:after {

  animation: rotate 1s ease-in-out infinite reverse;

  height: 50%;

  width: 50%;

}

@media only screen and (max-width: 992px) {
  .spotlight-item{
    background: #d7d6d2;
  }
    .spotlight-image{
      display: none;
    }

}

.networkTxt img {
    max-width: 200px;
    margin-bottom: 30px;
} .netwrk_speed_itm {
  cursor: pointer !important;
}