/*!
 * Cropper.js v1.4.0
 * https://fengyuanchen.github.io/cropperjs
 *
 * Copyright 2015-present <PERSON>
 * Released under the MIT license
 *
 * Date: 2018-06-01T15:18:18.692Z
 */
!function(t,i){"object"==typeof exports&&"undefined"!=typeof module?module.exports=i():"function"==typeof define&&define.amd?define(i):t.Cropper=i()}(this,function(){"use strict";var n="undefined"!=typeof window,h=n?window:{},d="cropper",k="all",T="crop",W="move",N="zoom",E="e",H="w",L="s",O="n",z="ne",Y="nw",X="se",R="sw",r=d+"-crop",t=d+"-disabled",S=d+"-hidden",l=d+"-hide",o=d+"-modal",p=d+"-move",m=d+"Action",g=d+"Preview",s="crop",c="move",u="none",a="crop",f="cropend",v="cropmove",w="cropstart",x="dblclick",b=h.PointerEvent?"pointerdown":"touchstart mousedown",y=h.PointerEvent?"pointermove":"touchmove mousemove",M=h.PointerEvent?"pointerup pointercancel":"touchend touchcancel mouseup",C="ready",D="resize",B="wheel mousewheel DOMMouseScroll",A="zoom",I=/^(?:e|w|s|n|se|sw|ne|nw|all|crop|move|zoom)$/,U=/^data:/,j=/^data:image\/jpeg;base64,/,P=/^(?:img|canvas)$/i,q={viewMode:0,dragMode:s,initialAspectRatio:NaN,aspectRatio:NaN,data:null,preview:"",responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,modal:!0,guides:!0,center:!0,highlight:!0,background:!0,autoCrop:!0,autoCropArea:.8,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,wheelZoomRatio:.1,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,minCanvasWidth:0,minCanvasHeight:0,minCropBoxWidth:0,minCropBoxHeight:0,minContainerWidth:200,minContainerHeight:100,ready:null,cropstart:null,cropmove:null,cropend:null,crop:null,zoom:null},i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},$=function(){function a(t,i){for(var e=0;e<i.length;e++){var a=i[e];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,a.key,a)}}return function(t,i,e){return i&&a(t.prototype,i),e&&a(t,e),t}}(),xt=function(t){if(Array.isArray(t)){for(var i=0,e=Array(t.length);i<t.length;i++)e[i]=t[i];return e}return Array.from(t)},e=Number.isNaN||h.isNaN;function Q(t){return"number"==typeof t&&!e(t)}function Z(t){return void 0===t}function F(t){return"object"===(void 0===t?"undefined":i(t))&&null!==t}var K=Object.prototype.hasOwnProperty;function V(t){if(!F(t))return!1;try{var i=t.constructor,e=i.prototype;return i&&e&&K.call(e,"isPrototypeOf")}catch(t){return!1}}function G(t){return"function"==typeof t}function J(i,e){if(i&&G(e))if(Array.isArray(i)||Q(i.length)){var t=i.length,a=void 0;for(a=0;a<t&&!1!==e.call(i,i[a],a,i);a+=1);}else F(i)&&Object.keys(i).forEach(function(t){e.call(i,i[t],t,i)});return i}var _=Object.assign||function(e){for(var t=arguments.length,i=Array(1<t?t-1:0),a=1;a<t;a++)i[a-1]=arguments[a];return F(e)&&0<i.length&&i.forEach(function(i){F(i)&&Object.keys(i).forEach(function(t){e[t]=i[t]})}),e},tt=/\.\d*(?:0|9){12}\d*$/i;function bt(t){var i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:1e11;return tt.test(t)?Math.round(t*i)/i:t}var it=/^(?:width|height|left|top|marginLeft|marginTop)$/;function et(t,i){var e=t.style;J(i,function(t,i){it.test(i)&&Q(t)&&(t+="px"),e[i]=t})}function at(t,i){if(i)if(Q(t.length))J(t,function(t){at(t,i)});else if(t.classList)t.classList.add(i);else{var e=t.className.trim();e?e.indexOf(i)<0&&(t.className=e+" "+i):t.className=i}}function nt(t,i){i&&(Q(t.length)?J(t,function(t){nt(t,i)}):t.classList?t.classList.remove(i):0<=t.className.indexOf(i)&&(t.className=t.className.replace(i,"")))}function ot(t,i,e){i&&(Q(t.length)?J(t,function(t){ot(t,i,e)}):e?at(t,i):nt(t,i))}var ht=/([a-z\d])([A-Z])/g;function rt(t){return t.replace(ht,"$1-$2").toLowerCase()}function st(t,i){return F(t[i])?t[i]:t.dataset?t.dataset[i]:t.getAttribute("data-"+rt(i))}function ct(t,i,e){F(e)?t[i]=e:t.dataset?t.dataset[i]=e:t.setAttribute("data-"+rt(i),e)}function dt(i,e){if(F(i[e]))try{delete i[e]}catch(t){i[e]=void 0}else if(i.dataset)try{delete i.dataset[e]}catch(t){i.dataset[e]=void 0}else i.removeAttribute("data-"+rt(e))}var lt=/\s\s*/,pt=function(){var t=!1;if(n){var i=!1,e=function(){},a=Object.defineProperty({},"once",{get:function(){return t=!0,i},set:function(t){i=t}});h.addEventListener("test",e,a),h.removeEventListener("test",e,a)}return t}();function mt(e,t,a){var n=3<arguments.length&&void 0!==arguments[3]?arguments[3]:{},o=a;t.trim().split(lt).forEach(function(t){if(!pt){var i=e.listeners;i&&i[t]&&i[t][a]&&(o=i[t][a],delete i[t][a],0===Object.keys(i[t]).length&&delete i[t],0===Object.keys(i).length&&delete e.listeners)}e.removeEventListener(t,o,n)})}function gt(o,t,h){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:{},s=h;t.trim().split(lt).forEach(function(a){if(r.once&&!pt){var t=o.listeners,n=void 0===t?{}:t;s=function(){for(var t=arguments.length,i=Array(t),e=0;e<t;e++)i[e]=arguments[e];delete n[a][h],o.removeEventListener(a,s,r),h.apply(o,i)},n[a]||(n[a]={}),n[a][h]&&o.removeEventListener(a,n[a][h],r),n[a][h]=s,o.listeners=n}o.addEventListener(a,s,r)})}function ut(t,i,e){var a=void 0;return G(Event)&&G(CustomEvent)?a=new CustomEvent(i,{detail:e,bubbles:!0,cancelable:!0}):(a=document.createEvent("CustomEvent")).initCustomEvent(i,!0,!0,e),t.dispatchEvent(a)}function ft(t){var i=t.getBoundingClientRect();return{left:i.left+(window.pageXOffset-document.documentElement.clientLeft),top:i.top+(window.pageYOffset-document.documentElement.clientTop)}}var vt=h.location,wt=/^(https?:)\/\/([^:/?#]+):?(\d*)/i;function yt(t){var i=t.match(wt);return i&&(i[1]!==vt.protocol||i[2]!==vt.hostname||i[3]!==vt.port)}function Mt(t){var i="timestamp="+(new Date).getTime();return t+(-1===t.indexOf("?")?"?":"&")+i}function Ct(t){var i=t.rotate,e=t.scaleX,a=t.scaleY,n=t.translateX,o=t.translateY,h=[];Q(n)&&0!==n&&h.push("translateX("+n+"px)"),Q(o)&&0!==o&&h.push("translateY("+o+"px)"),Q(i)&&0!==i&&h.push("rotate("+i+"deg)"),Q(e)&&1!==e&&h.push("scaleX("+e+")"),Q(a)&&1!==a&&h.push("scaleY("+a+")");var r=h.length?h.join(" "):"none";return{WebkitTransform:r,msTransform:r,transform:r}}function Dt(t,i){var e=t.pageX,a=t.pageY,n={endX:e,endY:a};return i?n:_({startX:e,startY:a},n)}var Bt=Number.isFinite||h.isFinite;function kt(t){var i=t.aspectRatio,e=t.height,a=t.width,n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"contain",o=function(t){return Bt(t)&&0<t};if(o(a)&&o(e)){var h=e*i;"contain"===n&&a<h||"cover"===n&&h<a?e=a/i:a=e*i}else o(a)?e=a/i:o(e)&&(a=e*i);return{width:a,height:e}}var Tt=String.fromCharCode;var Wt=/^data:.*,/;function Nt(t){var i=new DataView(t),e=void 0,a=void 0,n=void 0,o=void 0;if(255===i.getUint8(0)&&216===i.getUint8(1))for(var h=i.byteLength,r=2;r<h;){if(255===i.getUint8(r)&&225===i.getUint8(r+1)){n=r;break}r+=1}if(n){var s=n+10;if("Exif"===function(t,i,e){var a="",n=void 0;for(e+=i,n=i;n<e;n+=1)a+=Tt(t.getUint8(n));return a}(i,n+4,4)){var c=i.getUint16(s);if(((a=18761===c)||19789===c)&&42===i.getUint16(s+2,a)){var d=i.getUint32(s+4,a);8<=d&&(o=s+d)}}}if(o){var l=i.getUint16(o,a),p=void 0,m=void 0;for(m=0;m<l;m+=1)if(p=o+12*m+2,274===i.getUint16(p,a)){p+=8,e=i.getUint16(p,a),i.setUint16(p,1,a);break}}return e}var Et={render:function(){this.initContainer(),this.initCanvas(),this.initCropBox(),this.renderCanvas(),this.cropped&&this.renderCropBox()},initContainer:function(){var t=this.element,i=this.options,e=this.container,a=this.cropper;at(a,S),nt(t,S);var n={width:Math.max(e.offsetWidth,Number(i.minContainerWidth)||200),height:Math.max(e.offsetHeight,Number(i.minContainerHeight)||100)};et(a,{width:(this.containerData=n).width,height:n.height}),at(t,S),nt(a,S)},initCanvas:function(){var t=this.containerData,i=this.imageData,e=this.options.viewMode,a=Math.abs(i.rotate)%180==90,n=a?i.naturalHeight:i.naturalWidth,o=a?i.naturalWidth:i.naturalHeight,h=n/o,r=t.width,s=t.height;t.height*h>t.width?3===e?r=t.height*h:s=t.width/h:3===e?s=t.width/h:r=t.height*h;var c={aspectRatio:h,naturalWidth:n,naturalHeight:o,width:r,height:s};c.left=(t.width-r)/2,c.top=(t.height-s)/2,c.oldLeft=c.left,c.oldTop=c.top,this.canvasData=c,this.limited=1===e||2===e,this.limitCanvas(!0,!0),this.initialImageData=_({},i),this.initialCanvasData=_({},c)},limitCanvas:function(t,i){var e=this.options,a=this.containerData,n=this.canvasData,o=this.cropBoxData,h=e.viewMode,r=n.aspectRatio,s=this.cropped&&o;if(t){var c=Number(e.minCanvasWidth)||0,d=Number(e.minCanvasHeight)||0;1<h?(c=Math.max(c,a.width),d=Math.max(d,a.height),3===h&&(c<d*r?c=d*r:d=c/r)):0<h&&(c?c=Math.max(c,s?o.width:0):d?d=Math.max(d,s?o.height:0):s&&((c=o.width)<(d=o.height)*r?c=d*r:d=c/r));var l=kt({aspectRatio:r,width:c,height:d});c=l.width,d=l.height,n.minWidth=c,n.minHeight=d,n.maxWidth=1/0,n.maxHeight=1/0}if(i)if(h){var p=a.width-n.width,m=a.height-n.height;n.minLeft=Math.min(0,p),n.minTop=Math.min(0,m),n.maxLeft=Math.max(0,p),n.maxTop=Math.max(0,m),s&&this.limited&&(n.minLeft=Math.min(o.left,o.left+(o.width-n.width)),n.minTop=Math.min(o.top,o.top+(o.height-n.height)),n.maxLeft=o.left,n.maxTop=o.top,2===h&&(n.width>=a.width&&(n.minLeft=Math.min(0,p),n.maxLeft=Math.max(0,p)),n.height>=a.height&&(n.minTop=Math.min(0,m),n.maxTop=Math.max(0,m))))}else n.minLeft=-n.width,n.minTop=-n.height,n.maxLeft=a.width,n.maxTop=a.height},renderCanvas:function(t,i){var e=this.canvasData,a=this.imageData;if(i){var n=function(t){var i=t.width,e=t.height,a=t.degree;if(90==(a=Math.abs(a)%180))return{width:e,height:i};var n=a%90*Math.PI/180,o=Math.sin(n),h=Math.cos(n),r=i*h+e*o,s=i*o+e*h;return 90<a?{width:s,height:r}:{width:r,height:s}}({width:a.naturalWidth*Math.abs(a.scaleX||1),height:a.naturalHeight*Math.abs(a.scaleY||1),degree:a.rotate||0}),o=n.width,h=n.height,r=e.width*(o/e.naturalWidth),s=e.height*(h/e.naturalHeight);e.left-=(r-e.width)/2,e.top-=(s-e.height)/2,e.width=r,e.height=s,e.aspectRatio=o/h,e.naturalWidth=o,e.naturalHeight=h,this.limitCanvas(!0,!1)}(e.width>e.maxWidth||e.width<e.minWidth)&&(e.left=e.oldLeft),(e.height>e.maxHeight||e.height<e.minHeight)&&(e.top=e.oldTop),e.width=Math.min(Math.max(e.width,e.minWidth),e.maxWidth),e.height=Math.min(Math.max(e.height,e.minHeight),e.maxHeight),this.limitCanvas(!1,!0),e.left=Math.min(Math.max(e.left,e.minLeft),e.maxLeft),e.top=Math.min(Math.max(e.top,e.minTop),e.maxTop),e.oldLeft=e.left,e.oldTop=e.top,et(this.canvas,_({width:e.width,height:e.height},Ct({translateX:e.left,translateY:e.top}))),this.renderImage(t),this.cropped&&this.limited&&this.limitCropBox(!0,!0)},renderImage:function(t){var i=this.canvasData,e=this.imageData,a=e.naturalWidth*(i.width/i.naturalWidth),n=e.naturalHeight*(i.height/i.naturalHeight);_(e,{width:a,height:n,left:(i.width-a)/2,top:(i.height-n)/2}),et(this.image,_({width:e.width,height:e.height},Ct(_({translateX:e.left,translateY:e.top},e)))),t&&this.output()},initCropBox:function(){var t=this.options,i=this.canvasData,e=t.aspectRatio||t.initialAspectRatio,a=Number(t.autoCropArea)||.8,n={width:i.width,height:i.height};e&&(i.height*e>i.width?n.height=n.width/e:n.width=n.height*e),this.cropBoxData=n,this.limitCropBox(!0,!0),n.width=Math.min(Math.max(n.width,n.minWidth),n.maxWidth),n.height=Math.min(Math.max(n.height,n.minHeight),n.maxHeight),n.width=Math.max(n.minWidth,n.width*a),n.height=Math.max(n.minHeight,n.height*a),n.left=i.left+(i.width-n.width)/2,n.top=i.top+(i.height-n.height)/2,n.oldLeft=n.left,n.oldTop=n.top,this.initialCropBoxData=_({},n)},limitCropBox:function(t,i){var e=this.options,a=this.containerData,n=this.canvasData,o=this.cropBoxData,h=this.limited,r=e.aspectRatio;if(t){var s=Number(e.minCropBoxWidth)||0,c=Number(e.minCropBoxHeight)||0,d=Math.min(a.width,h?n.width:a.width),l=Math.min(a.height,h?n.height:a.height);s=Math.min(s,a.width),c=Math.min(c,a.height),r&&(s&&c?s<c*r?c=s/r:s=c*r:s?c=s/r:c&&(s=c*r),d<l*r?l=d/r:d=l*r),o.minWidth=Math.min(s,d),o.minHeight=Math.min(c,l),o.maxWidth=d,o.maxHeight=l}i&&(h?(o.minLeft=Math.max(0,n.left),o.minTop=Math.max(0,n.top),o.maxLeft=Math.min(a.width,n.left+n.width)-o.width,o.maxTop=Math.min(a.height,n.top+n.height)-o.height):(o.minLeft=0,o.minTop=0,o.maxLeft=a.width-o.width,o.maxTop=a.height-o.height))},renderCropBox:function(){var t=this.options,i=this.containerData,e=this.cropBoxData;(e.width>e.maxWidth||e.width<e.minWidth)&&(e.left=e.oldLeft),(e.height>e.maxHeight||e.height<e.minHeight)&&(e.top=e.oldTop),e.width=Math.min(Math.max(e.width,e.minWidth),e.maxWidth),e.height=Math.min(Math.max(e.height,e.minHeight),e.maxHeight),this.limitCropBox(!1,!0),e.left=Math.min(Math.max(e.left,e.minLeft),e.maxLeft),e.top=Math.min(Math.max(e.top,e.minTop),e.maxTop),e.oldLeft=e.left,e.oldTop=e.top,t.movable&&t.cropBoxMovable&&ct(this.face,m,e.width>=i.width&&e.height>=i.height?W:k),et(this.cropBox,_({width:e.width,height:e.height},Ct({translateX:e.left,translateY:e.top}))),this.cropped&&this.limited&&this.limitCanvas(!0,!0),this.disabled||this.output()},output:function(){this.preview(),ut(this.element,a,this.getData())}},Ht={initPreview:function(){var e=this.crossOrigin,t=this.options.preview,a=e?this.crossOriginUrl:this.url,i=document.createElement("img");if(e&&(i.crossOrigin=e),i.src=a,this.viewBox.appendChild(i),this.viewBoxImage=i,t){var n=t;"string"==typeof t?n=this.element.ownerDocument.querySelectorAll(t):t.querySelector&&(n=[t]),J(this.previews=n,function(t){var i=document.createElement("img");ct(t,g,{width:t.offsetWidth,height:t.offsetHeight,html:t.innerHTML}),e&&(i.crossOrigin=e),i.src=a,i.style.cssText='display:block;width:100%;height:auto;min-width:0!important;min-height:0!important;max-width:none!important;max-height:none!important;image-orientation:0deg!important;"',t.innerHTML="",t.appendChild(i)})}},resetPreview:function(){J(this.previews,function(t){var i=st(t,g);et(t,{width:i.width,height:i.height}),t.innerHTML=i.html,dt(t,g)})},preview:function(){var r=this.imageData,t=this.canvasData,i=this.cropBoxData,s=i.width,c=i.height,d=r.width,l=r.height,p=i.left-t.left-r.left,m=i.top-t.top-r.top;this.cropped&&!this.disabled&&(et(this.viewBoxImage,_({width:d,height:l},Ct(_({translateX:-p,translateY:-m},r)))),J(this.previews,function(t){var i=st(t,g),e=i.width,a=i.height,n=e,o=a,h=1;s&&(o=c*(h=e/s)),c&&a<o&&(n=s*(h=a/c),o=a),et(t,{width:n,height:o}),et(t.getElementsByTagName("img")[0],_({width:d*h,height:l*h},Ct(_({translateX:-p*h,translateY:-m*h},r))))}))}},Lt={bind:function(){var t=this.element,i=this.options,e=this.cropper;G(i.cropstart)&&gt(t,w,i.cropstart),G(i.cropmove)&&gt(t,v,i.cropmove),G(i.cropend)&&gt(t,f,i.cropend),G(i.crop)&&gt(t,a,i.crop),G(i.zoom)&&gt(t,A,i.zoom),gt(e,b,this.onCropStart=this.cropStart.bind(this)),i.zoomable&&i.zoomOnWheel&&gt(e,B,this.onWheel=this.wheel.bind(this)),i.toggleDragModeOnDblclick&&gt(e,x,this.onDblclick=this.dblclick.bind(this)),gt(t.ownerDocument,y,this.onCropMove=this.cropMove.bind(this)),gt(t.ownerDocument,M,this.onCropEnd=this.cropEnd.bind(this)),i.responsive&&gt(window,D,this.onResize=this.resize.bind(this))},unbind:function(){var t=this.element,i=this.options,e=this.cropper;G(i.cropstart)&&mt(t,w,i.cropstart),G(i.cropmove)&&mt(t,v,i.cropmove),G(i.cropend)&&mt(t,f,i.cropend),G(i.crop)&&mt(t,a,i.crop),G(i.zoom)&&mt(t,A,i.zoom),mt(e,b,this.onCropStart),i.zoomable&&i.zoomOnWheel&&mt(e,B,this.onWheel),i.toggleDragModeOnDblclick&&mt(e,x,this.onDblclick),mt(t.ownerDocument,y,this.onCropMove),mt(t.ownerDocument,M,this.onCropEnd),i.responsive&&mt(window,D,this.onResize)}},Ot={resize:function(){var t=this.options,i=this.container,e=this.containerData,a=Number(t.minContainerWidth)||200,n=Number(t.minContainerHeight)||100;if(!(this.disabled||e.width<=a||e.height<=n)){var o=i.offsetWidth/e.width;if(1!==o||i.offsetHeight!==e.height){var h=void 0,r=void 0;t.restore&&(h=this.getCanvasData(),r=this.getCropBoxData()),this.render(),t.restore&&(this.setCanvasData(J(h,function(t,i){h[i]=t*o})),this.setCropBoxData(J(r,function(t,i){r[i]=t*o})))}}},dblclick:function(){var t,i;this.disabled||this.options.dragMode===u||this.setDragMode((t=this.dragBox,i=r,(t.classList?t.classList.contains(i):-1<t.className.indexOf(i))?c:s))},wheel:function(t){var i=this,e=Number(this.options.wheelZoomRatio)||.1,a=1;this.disabled||(t.preventDefault(),this.wheeling||(this.wheeling=!0,setTimeout(function(){i.wheeling=!1},50),t.deltaY?a=0<t.deltaY?1:-1:t.wheelDelta?a=-t.wheelDelta/120:t.detail&&(a=0<t.detail?1:-1),this.zoom(-a*e,t)))},cropStart:function(t){if(!this.disabled){var i=this.options,e=this.pointers,a=void 0;t.changedTouches?J(t.changedTouches,function(t){e[t.identifier]=Dt(t)}):e[t.pointerId||0]=Dt(t),a=1<Object.keys(e).length&&i.zoomable&&i.zoomOnTouch?N:st(t.target,m),I.test(a)&&!1!==ut(this.element,w,{originalEvent:t,action:a})&&(t.preventDefault(),this.action=a,this.cropping=!1,a===T&&(this.cropping=!0,at(this.dragBox,o)))}},cropMove:function(t){var i=this.action;if(!this.disabled&&i){var e=this.pointers;t.preventDefault(),!1!==ut(this.element,v,{originalEvent:t,action:i})&&(t.changedTouches?J(t.changedTouches,function(t){_(e[t.identifier],Dt(t,!0))}):_(e[t.pointerId||0],Dt(t,!0)),this.change(t))}},cropEnd:function(t){if(!this.disabled){var i=this.action,e=this.pointers;t.changedTouches?J(t.changedTouches,function(t){delete e[t.identifier]}):delete e[t.pointerId||0],i&&(t.preventDefault(),Object.keys(e).length||(this.action=""),this.cropping&&(this.cropping=!1,ot(this.dragBox,o,this.cropped&&this.options.modal)),ut(this.element,f,{originalEvent:t,action:i}))}}},zt={change:function(t){var i=this.options,e=this.canvasData,a=this.containerData,n=this.cropBoxData,o=this.pointers,h=this.action,r=i.aspectRatio,s=n.left,c=n.top,d=n.width,l=n.height,p=s+d,m=c+l,g=0,u=0,f=a.width,v=a.height,w=!0,x=void 0;!r&&t.shiftKey&&(r=d&&l?d/l:1),this.limited&&(g=n.minLeft,u=n.minTop,f=g+Math.min(a.width,e.width,e.left+e.width),v=u+Math.min(a.height,e.height,e.top+e.height));var b,y,M,C=o[Object.keys(o)[0]],D={x:C.endX-C.startX,y:C.endY-C.startY},B=function(t){switch(t){case E:p+D.x>f&&(D.x=f-p);break;case H:s+D.x<g&&(D.x=g-s);break;case O:c+D.y<u&&(D.y=u-c);break;case L:m+D.y>v&&(D.y=v-m)}};switch(h){case k:s+=D.x,c+=D.y;break;case E:if(0<=D.x&&(f<=p||r&&(c<=u||v<=m))){w=!1;break}B(E),(d+=D.x)<0&&(h=H,s-=d=-d),r&&(l=d/r,c+=(n.height-l)/2);break;case O:if(D.y<=0&&(c<=u||r&&(s<=g||f<=p))){w=!1;break}B(O),l-=D.y,c+=D.y,l<0&&(h=L,c-=l=-l),r&&(d=l*r,s+=(n.width-d)/2);break;case H:if(D.x<=0&&(s<=g||r&&(c<=u||v<=m))){w=!1;break}B(H),d-=D.x,s+=D.x,d<0&&(h=E,s-=d=-d),r&&(l=d/r,c+=(n.height-l)/2);break;case L:if(0<=D.y&&(v<=m||r&&(s<=g||f<=p))){w=!1;break}B(L),(l+=D.y)<0&&(h=O,c-=l=-l),r&&(d=l*r,s+=(n.width-d)/2);break;case z:if(r){if(D.y<=0&&(c<=u||f<=p)){w=!1;break}B(O),l-=D.y,c+=D.y,d=l*r}else B(O),B(E),0<=D.x?p<f?d+=D.x:D.y<=0&&c<=u&&(w=!1):d+=D.x,D.y<=0?u<c&&(l-=D.y,c+=D.y):(l-=D.y,c+=D.y);d<0&&l<0?(h=R,c-=l=-l,s-=d=-d):d<0?(h=Y,s-=d=-d):l<0&&(h=X,c-=l=-l);break;case Y:if(r){if(D.y<=0&&(c<=u||s<=g)){w=!1;break}B(O),l-=D.y,c+=D.y,d=l*r,s+=n.width-d}else B(O),B(H),D.x<=0?g<s?(d-=D.x,s+=D.x):D.y<=0&&c<=u&&(w=!1):(d-=D.x,s+=D.x),D.y<=0?u<c&&(l-=D.y,c+=D.y):(l-=D.y,c+=D.y);d<0&&l<0?(h=X,c-=l=-l,s-=d=-d):d<0?(h=z,s-=d=-d):l<0&&(h=R,c-=l=-l);break;case R:if(r){if(D.x<=0&&(s<=g||v<=m)){w=!1;break}B(H),d-=D.x,s+=D.x,l=d/r}else B(L),B(H),D.x<=0?g<s?(d-=D.x,s+=D.x):0<=D.y&&v<=m&&(w=!1):(d-=D.x,s+=D.x),0<=D.y?m<v&&(l+=D.y):l+=D.y;d<0&&l<0?(h=z,c-=l=-l,s-=d=-d):d<0?(h=X,s-=d=-d):l<0&&(h=Y,c-=l=-l);break;case X:if(r){if(0<=D.x&&(f<=p||v<=m)){w=!1;break}B(E),l=(d+=D.x)/r}else B(L),B(E),0<=D.x?p<f?d+=D.x:0<=D.y&&v<=m&&(w=!1):d+=D.x,0<=D.y?m<v&&(l+=D.y):l+=D.y;d<0&&l<0?(h=Y,c-=l=-l,s-=d=-d):d<0?(h=R,s-=d=-d):l<0&&(h=z,c-=l=-l);break;case W:this.move(D.x,D.y),w=!1;break;case N:this.zoom((y=_({},b=o),M=[],J(b,function(r,t){delete y[t],J(y,function(t){var i=Math.abs(r.startX-t.startX),e=Math.abs(r.startY-t.startY),a=Math.abs(r.endX-t.endX),n=Math.abs(r.endY-t.endY),o=Math.sqrt(i*i+e*e),h=(Math.sqrt(a*a+n*n)-o)/o;M.push(h)})}),M.sort(function(t,i){return Math.abs(t)<Math.abs(i)}),M[0]),t),w=!1;break;case T:if(!D.x||!D.y){w=!1;break}x=ft(this.cropper),s=C.startX-x.left,c=C.startY-x.top,d=n.minWidth,l=n.minHeight,0<D.x?h=0<D.y?X:z:D.x<0&&(s-=d,h=0<D.y?R:Y),D.y<0&&(c-=l),this.cropped||(nt(this.cropBox,S),this.cropped=!0,this.limited&&this.limitCropBox(!0,!0))}w&&(n.width=d,n.height=l,n.left=s,n.top=c,this.action=h,this.renderCropBox()),J(o,function(t){t.startX=t.endX,t.startY=t.endY})}},Yt={crop:function(){return!this.ready||this.cropped||this.disabled||(this.cropped=!0,this.limitCropBox(!0,!0),this.options.modal&&at(this.dragBox,o),nt(this.cropBox,S),this.setCropBoxData(this.initialCropBoxData)),this},reset:function(){return this.ready&&!this.disabled&&(this.imageData=_({},this.initialImageData),this.canvasData=_({},this.initialCanvasData),this.cropBoxData=_({},this.initialCropBoxData),this.renderCanvas(),this.cropped&&this.renderCropBox()),this},clear:function(){return this.cropped&&!this.disabled&&(_(this.cropBoxData,{left:0,top:0,width:0,height:0}),this.cropped=!1,this.renderCropBox(),this.limitCanvas(!0,!0),this.renderCanvas(),nt(this.dragBox,o),at(this.cropBox,S)),this},replace:function(i){var t=1<arguments.length&&void 0!==arguments[1]&&arguments[1];return!this.disabled&&i&&(this.isImg&&(this.element.src=i),t?(this.url=i,this.image.src=i,this.ready&&(this.viewBoxImage.src=i,J(this.previews,function(t){t.getElementsByTagName("img")[0].src=i}))):(this.isImg&&(this.replaced=!0),this.options.data=null,this.uncreate(),this.load(i))),this},enable:function(){return this.ready&&this.disabled&&(this.disabled=!1,nt(this.cropper,t)),this},disable:function(){return this.ready&&!this.disabled&&(this.disabled=!0,at(this.cropper,t)),this},destroy:function(){var t=this.element;return st(t,d)&&(this.isImg&&this.replaced&&(t.src=this.originalUrl),this.uncreate(),dt(t,d)),this},move:function(t){var i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:t,e=this.canvasData,a=e.left,n=e.top;return this.moveTo(Z(t)?t:a+Number(t),Z(i)?i:n+Number(i))},moveTo:function(t){var i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:t,e=this.canvasData,a=!1;return t=Number(t),i=Number(i),this.ready&&!this.disabled&&this.options.movable&&(Q(t)&&(e.left=t,a=!0),Q(i)&&(e.top=i,a=!0),a&&this.renderCanvas(!0)),this},zoom:function(t,i){var e=this.canvasData;return t=(t=Number(t))<0?1/(1-t):1+t,this.zoomTo(e.width*t/e.naturalWidth,null,i)},zoomTo:function(t,i,e){var a,n,o,h=this.options,r=this.canvasData,s=r.width,c=r.height,d=r.naturalWidth,l=r.naturalHeight;if(0<=(t=Number(t))&&this.ready&&!this.disabled&&h.zoomable){var p=d*t,m=l*t;if(!1===ut(this.element,A,{ratio:t,oldRatio:s/d,originalEvent:e}))return this;if(e){var g=this.pointers,u=ft(this.cropper),f=g&&Object.keys(g).length?(o=n=a=0,J(g,function(t){var i=t.startX,e=t.startY;a+=i,n+=e,o+=1}),{pageX:a/=o,pageY:n/=o}):{pageX:e.pageX,pageY:e.pageY};r.left-=(p-s)*((f.pageX-u.left-r.left)/s),r.top-=(m-c)*((f.pageY-u.top-r.top)/c)}else V(i)&&Q(i.x)&&Q(i.y)?(r.left-=(p-s)*((i.x-r.left)/s),r.top-=(m-c)*((i.y-r.top)/c)):(r.left-=(p-s)/2,r.top-=(m-c)/2);r.width=p,r.height=m,this.renderCanvas(!0)}return this},rotate:function(t){return this.rotateTo((this.imageData.rotate||0)+Number(t))},rotateTo:function(t){return Q(t=Number(t))&&this.ready&&!this.disabled&&this.options.rotatable&&(this.imageData.rotate=t%360,this.renderCanvas(!0,!0)),this},scaleX:function(t){var i=this.imageData.scaleY;return this.scale(t,Q(i)?i:1)},scaleY:function(t){var i=this.imageData.scaleX;return this.scale(Q(i)?i:1,t)},scale:function(t){var i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:t,e=this.imageData,a=!1;return t=Number(t),i=Number(i),this.ready&&!this.disabled&&this.options.scalable&&(Q(t)&&(e.scaleX=t,a=!0),Q(i)&&(e.scaleY=i,a=!0),a&&this.renderCanvas(!0,!0)),this},getData:function(){var t=0<arguments.length&&void 0!==arguments[0]&&arguments[0],i=this.options,e=this.imageData,a=this.canvasData,n=this.cropBoxData,o=void 0;if(this.ready&&this.cropped){o={x:n.left-a.left,y:n.top-a.top,width:n.width,height:n.height};var h=e.width/e.naturalWidth;if(J(o,function(t,i){o[i]=t/h}),t){var r=Math.round(o.y+o.height),s=Math.round(o.x+o.width);o.x=Math.round(o.x),o.y=Math.round(o.y),o.width=s-o.x,o.height=r-o.y}}else o={x:0,y:0,width:0,height:0};return i.rotatable&&(o.rotate=e.rotate||0),i.scalable&&(o.scaleX=e.scaleX||1,o.scaleY=e.scaleY||1),o},setData:function(t){var i=this.options,e=this.imageData,a=this.canvasData,n={};if(this.ready&&!this.disabled&&V(t)){var o=!1;i.rotatable&&Q(t.rotate)&&t.rotate!==e.rotate&&(e.rotate=t.rotate,o=!0),i.scalable&&(Q(t.scaleX)&&t.scaleX!==e.scaleX&&(e.scaleX=t.scaleX,o=!0),Q(t.scaleY)&&t.scaleY!==e.scaleY&&(e.scaleY=t.scaleY,o=!0)),o&&this.renderCanvas(!0,!0);var h=e.width/e.naturalWidth;Q(t.x)&&(n.left=t.x*h+a.left),Q(t.y)&&(n.top=t.y*h+a.top),Q(t.width)&&(n.width=t.width*h),Q(t.height)&&(n.height=t.height*h),this.setCropBoxData(n)}return this},getContainerData:function(){return this.ready?_({},this.containerData):{}},getImageData:function(){return this.sized?_({},this.imageData):{}},getCanvasData:function(){var i=this.canvasData,e={};return this.ready&&J(["left","top","width","height","naturalWidth","naturalHeight"],function(t){e[t]=i[t]}),e},setCanvasData:function(t){var i=this.canvasData,e=i.aspectRatio;return this.ready&&!this.disabled&&V(t)&&(Q(t.left)&&(i.left=t.left),Q(t.top)&&(i.top=t.top),Q(t.width)?(i.width=t.width,i.height=t.width/e):Q(t.height)&&(i.height=t.height,i.width=t.height*e),this.renderCanvas(!0)),this},getCropBoxData:function(){var t=this.cropBoxData,i=void 0;return this.ready&&this.cropped&&(i={left:t.left,top:t.top,width:t.width,height:t.height}),i||{}},setCropBoxData:function(t){var i=this.cropBoxData,e=this.options.aspectRatio,a=void 0,n=void 0;return this.ready&&this.cropped&&!this.disabled&&V(t)&&(Q(t.left)&&(i.left=t.left),Q(t.top)&&(i.top=t.top),Q(t.width)&&t.width!==i.width&&(a=!0,i.width=t.width),Q(t.height)&&t.height!==i.height&&(n=!0,i.height=t.height),e&&(a?i.height=i.width/e:n&&(i.width=i.height*e)),this.renderCropBox()),this},getCroppedCanvas:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};if(!this.ready||!window.HTMLCanvasElement)return null;var i,e,a,n,o,h,r,s,c,d,l,p,m,g,u,f,v,w,x,b,y,M,C,D,B,k,T,W,N,E,H,L,O,z,Y,X,R,S,A,I,U,j=this.canvasData,P=(i=this.image,e=this.imageData,a=j,n=t,o=e.aspectRatio,h=e.naturalWidth,r=e.naturalHeight,s=e.rotate,c=void 0===s?0:s,d=e.scaleX,l=void 0===d?1:d,p=e.scaleY,m=void 0===p?1:p,g=a.aspectRatio,u=a.naturalWidth,f=a.naturalHeight,v=n.fillColor,w=void 0===v?"transparent":v,x=n.imageSmoothingEnabled,b=void 0===x||x,y=n.imageSmoothingQuality,M=void 0===y?"low":y,C=n.maxWidth,D=void 0===C?1/0:C,B=n.maxHeight,k=void 0===B?1/0:B,T=n.minWidth,W=void 0===T?0:T,N=n.minHeight,E=void 0===N?0:N,H=document.createElement("canvas"),L=H.getContext("2d"),O=kt({aspectRatio:g,width:D,height:k}),z=kt({aspectRatio:g,width:W,height:E},"cover"),Y=Math.min(O.width,Math.max(z.width,u)),X=Math.min(O.height,Math.max(z.height,f)),R=kt({aspectRatio:o,width:D,height:k}),S=kt({aspectRatio:o,width:W,height:E},"cover"),A=Math.min(R.width,Math.max(S.width,h)),I=Math.min(R.height,Math.max(S.height,r)),U=[-A/2,-I/2,A,I],H.width=bt(Y),H.height=bt(X),L.fillStyle=w,L.fillRect(0,0,Y,X),L.save(),L.translate(Y/2,X/2),L.rotate(c*Math.PI/180),L.scale(l,m),L.imageSmoothingEnabled=b,L.imageSmoothingQuality=M,L.drawImage.apply(L,[i].concat(xt(U.map(function(t){return Math.floor(bt(t))})))),L.restore(),H);if(!this.cropped)return P;var q=this.getData(),$=q.x,Q=q.y,Z=q.width,F=q.height,K=P.width/Math.floor(j.naturalWidth);1!==K&&($*=K,Q*=K,Z*=K,F*=K);var V=Z/F,G=kt({aspectRatio:V,width:t.maxWidth||1/0,height:t.maxHeight||1/0}),J=kt({aspectRatio:V,width:t.minWidth||0,height:t.minHeight||0},"cover"),_=kt({aspectRatio:V,width:t.width||(1!==K?P.width:Z),height:t.height||(1!==K?P.height:F)}),tt=_.width,it=_.height;tt=Math.min(G.width,Math.max(J.width,tt)),it=Math.min(G.height,Math.max(J.height,it));var et=document.createElement("canvas"),at=et.getContext("2d");et.width=bt(tt),et.height=bt(it),at.fillStyle=t.fillColor||"transparent",at.fillRect(0,0,tt,it);var nt=t.imageSmoothingEnabled,ot=void 0===nt||nt,ht=t.imageSmoothingQuality;at.imageSmoothingEnabled=ot,ht&&(at.imageSmoothingQuality=ht);var rt=P.width,st=P.height,ct=$,dt=Q,lt=void 0,pt=void 0,mt=void 0,gt=void 0,ut=void 0,ft=void 0;ct<=-Z||rt<ct?ut=mt=lt=ct=0:ct<=0?(mt=-ct,ct=0,ut=lt=Math.min(rt,Z+ct)):ct<=rt&&(mt=0,ut=lt=Math.min(Z,rt-ct)),lt<=0||dt<=-F||st<dt?ft=gt=pt=dt=0:dt<=0?(gt=-dt,dt=0,ft=pt=Math.min(st,F+dt)):dt<=st&&(gt=0,ft=pt=Math.min(F,st-dt));var vt=[ct,dt,lt,pt];if(0<ut&&0<ft){var wt=tt/Z;vt.push(mt*wt,gt*wt,ut*wt,ft*wt)}return at.drawImage.apply(at,[P].concat(xt(vt.map(function(t){return Math.floor(bt(t))})))),et},setAspectRatio:function(t){var i=this.options;return this.disabled||Z(t)||(i.aspectRatio=Math.max(0,t)||NaN,this.ready&&(this.initCropBox(),this.cropped&&this.renderCropBox())),this},setDragMode:function(t){var i=this.options,e=this.dragBox,a=this.face;if(this.ready&&!this.disabled){var n=t===s,o=i.movable&&t===c;t=n||o?t:u,i.dragMode=t,ct(e,m,t),ot(e,r,n),ot(e,p,o),i.cropBoxMovable||(ct(a,m,t),ot(a,r,n),ot(a,p,o))}return this}},Xt=h.Cropper,Rt=function(){function e(t){var i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};if(function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,e),!t||!P.test(t.tagName))throw new Error("The first argument is required and must be an <img> or <canvas> element.");this.element=t,this.options=_({},q,V(i)&&i),this.cropped=!1,this.disabled=!1,this.pointers={},this.ready=!1,this.reloading=!1,this.replaced=!1,this.sized=!1,this.sizing=!1,this.init()}return $(e,[{key:"init",value:function(){var t=this.element,i=t.tagName.toLowerCase(),e=void 0;if(!st(t,d)){if(ct(t,d,this),"img"===i){if(this.isImg=!0,e=t.getAttribute("src")||"",!(this.originalUrl=e))return;e=t.src}else"canvas"===i&&window.HTMLCanvasElement&&(e=t.toDataURL());this.load(e)}}},{key:"load",value:function(t){var i=this;if(t){this.url=t,this.imageData={};var e=this.element,a=this.options;if(a.rotatable||a.scalable||(a.checkOrientation=!1),a.checkOrientation&&window.ArrayBuffer)if(U.test(t))j.test(t)?this.read((n=t.replace(Wt,""),o=atob(n),h=new ArrayBuffer(o.length),J(r=new Uint8Array(h),function(t,i){r[i]=o.charCodeAt(i)}),h)):this.clone();else{var n,o,h,r,s=new XMLHttpRequest;this.reloading=!0,this.xhr=s;var c=function(){i.reloading=!1,i.xhr=null};s.ontimeout=c,s.onabort=c,s.onerror=function(){c(),i.clone()},s.onload=function(){c(),i.read(s.response)},a.checkCrossOrigin&&yt(t)&&e.crossOrigin&&(t=Mt(t)),s.open("get",t),s.responseType="arraybuffer",s.withCredentials="use-credentials"===e.crossOrigin,s.send()}else this.clone()}}},{key:"read",value:function(t){var i,e,a,n=this.options,o=this.imageData,h=Nt(t),r=0,s=1,c=1;if(1<h){this.url=(i="image/jpeg",e=new Uint8Array(t),a="",J(e,function(t){a+=Tt(t)}),"data:"+i+";base64,"+btoa(a));var d=function(t){var i=0,e=1,a=1;switch(t){case 2:e=-1;break;case 3:i=-180;break;case 4:a=-1;break;case 5:i=90,a=-1;break;case 6:i=90;break;case 7:i=90,e=-1;break;case 8:i=-90}return{rotate:i,scaleX:e,scaleY:a}}(h);r=d.rotate,s=d.scaleX,c=d.scaleY}n.rotatable&&(o.rotate=r),n.scalable&&(o.scaleX=s,o.scaleY=c),this.clone()}},{key:"clone",value:function(){var t=this.element,i=this.url,e=void 0,a=void 0;this.options.checkCrossOrigin&&yt(i)&&((e=t.crossOrigin)?a=i:(e="anonymous",a=Mt(i))),this.crossOrigin=e,this.crossOriginUrl=a;var n=document.createElement("img");e&&(n.crossOrigin=e),n.src=a||i,(this.image=n).onload=this.start.bind(this),n.onerror=this.stop.bind(this),at(n,l),t.parentNode.insertBefore(n,t.nextSibling)}},{key:"start",value:function(){var e=this,t=this.isImg?this.element:this.image;t.onload=null,t.onerror=null,this.sizing=!0;var i=h.navigator&&/(Macintosh|iPhone|iPod|iPad).*AppleWebKit/i.test(h.navigator.userAgent),a=function(t,i){_(e.imageData,{naturalWidth:t,naturalHeight:i,aspectRatio:t/i}),e.sizing=!1,e.sized=!0,e.build()};if(!t.naturalWidth||i){var n=document.createElement("img"),o=document.body||document.documentElement;(this.sizingImage=n).onload=function(){a(n.width,n.height),i||o.removeChild(n)},n.src=t.src,i||(n.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",o.appendChild(n))}else a(t.naturalWidth,t.naturalHeight)}},{key:"stop",value:function(){var t=this.image;t.onload=null,t.onerror=null,t.parentNode.removeChild(t),this.image=null}},{key:"build",value:function(){if(this.sized&&!this.ready){var t=this.element,i=this.options,e=this.image,a=t.parentNode,n=document.createElement("div");n.innerHTML='<div class="cropper-container" touch-action="none"><div class="cropper-wrap-box"><div class="cropper-canvas"></div></div><div class="cropper-drag-box"></div><div class="cropper-crop-box"><span class="cropper-view-box"></span><span class="cropper-dashed dashed-h"></span><span class="cropper-dashed dashed-v"></span><span class="cropper-center"></span><span class="cropper-face"></span><span class="cropper-line line-e" data-cropper-action="e"></span><span class="cropper-line line-n" data-cropper-action="n"></span><span class="cropper-line line-w" data-cropper-action="w"></span><span class="cropper-line line-s" data-cropper-action="s"></span><span class="cropper-point point-e" data-cropper-action="e"></span><span class="cropper-point point-n" data-cropper-action="n"></span><span class="cropper-point point-w" data-cropper-action="w"></span><span class="cropper-point point-s" data-cropper-action="s"></span><span class="cropper-point point-ne" data-cropper-action="ne"></span><span class="cropper-point point-nw" data-cropper-action="nw"></span><span class="cropper-point point-sw" data-cropper-action="sw"></span><span class="cropper-point point-se" data-cropper-action="se"></span></div></div>';var o=n.querySelector("."+d+"-container"),h=o.querySelector("."+d+"-canvas"),r=o.querySelector("."+d+"-drag-box"),s=o.querySelector("."+d+"-crop-box"),c=s.querySelector("."+d+"-face");this.container=a,this.cropper=o,this.canvas=h,this.dragBox=r,this.cropBox=s,this.viewBox=o.querySelector("."+d+"-view-box"),this.face=c,h.appendChild(e),at(t,S),a.insertBefore(o,t.nextSibling),this.isImg||nt(e,l),this.initPreview(),this.bind(),i.initialAspectRatio=Math.max(0,i.initialAspectRatio)||NaN,i.aspectRatio=Math.max(0,i.aspectRatio)||NaN,i.viewMode=Math.max(0,Math.min(3,Math.round(i.viewMode)))||0,at(s,S),i.guides||at(s.getElementsByClassName(d+"-dashed"),S),i.center||at(s.getElementsByClassName(d+"-center"),S),i.background&&at(o,d+"-bg"),i.highlight||at(c,"cropper-invisible"),i.cropBoxMovable&&(at(c,p),ct(c,m,k)),i.cropBoxResizable||(at(s.getElementsByClassName(d+"-line"),S),at(s.getElementsByClassName(d+"-point"),S)),this.render(),this.ready=!0,this.setDragMode(i.dragMode),i.autoCrop&&this.crop(),this.setData(i.data),G(i.ready)&&gt(t,C,i.ready,{once:!0}),ut(t,C)}}},{key:"unbuild",value:function(){this.ready&&(this.ready=!1,this.unbind(),this.resetPreview(),this.cropper.parentNode.removeChild(this.cropper),nt(this.element,S))}},{key:"uncreate",value:function(){this.ready?(this.unbuild(),this.ready=!1,this.cropped=!1):this.sizing?(this.sizingImage.onload=null,this.sizing=!1,this.sized=!1):this.reloading?this.xhr.abort():this.image&&this.stop()}}],[{key:"noConflict",value:function(){return window.Cropper=Xt,e}},{key:"setDefaults",value:function(t){_(q,V(t)&&t)}}]),e}();return _(Rt.prototype,Et,Ht,Lt,Ot,zt,Yt),Rt});