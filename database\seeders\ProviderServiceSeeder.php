<?php

namespace Database\Seeders;

use App\Constants\CommonConstants;
use App\Models\Category;
use App\Models\Provider;
use App\Models\ProviderService;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ProviderServiceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $rows=[
            ['provider_id'=>CommonConstants::PROVIDER_FASTBULK,'sid'=>'2','name'=>'IMEI ⇄ IMEI2 ⇄ Serial Convert ( ❌ Color+Storage )','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_FASTBULK,'sid'=>'3','name'=>'IMEI ⇄ IMEI2 ⇄ Serial Convert ( ✔️ Color+Storage )','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_FASTBULK,'sid'=>'7','name'=>'iCloud Clean/Lost Status (Cellular) Duplicate ☁️','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_FASTBULK,'sid'=>'11','name'=>'IMEI ⇄ IMEI2 ⇄ Serial Convert + ( ✔️ Warranty+AppleCare+DOP )','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_FASTBULK,'sid'=>'15','name'=>'Apple Part Number / MPN + DOP 👁️‍🗨️','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_FASTBULK,'sid'=>'16','name'=>'Activation Check ( By IMEI + Serial ) ✅','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_FASTBULK,'sid'=>'17','name'=>'Replacement Status ( By IMEI + Serial ) 🔄','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_FASTBULK,'sid'=>'18','name'=>'Replacement/Activation Status Check ( By IMEI + Serial )','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_FASTBULK,'sid'=>'19','name'=>'Warranty Check (by Serial) 🔧 (TesT)','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_FASTBULK,'sid'=>'21','name'=>'MacBook FMI On/Off Check (By IMEI + Serial) 💻🔒','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_FASTBULK,'sid'=>'22','name'=>'FMI On/Off Status 🔒 In Use','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_FASTBULK,'sid'=>'23','name'=>'IMEI ⇄ IMEI2 ⇄ Serial Convert + ( ✔️ Warranty+AppleCare+DOP ❌ Color+Storage )','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],

            ['provider_id'=>CommonConstants::PROVIDER_SICKW,'sid'=>'12','name'=>'IMEI-SN Convert','type'=>CommonConstants::TYPE_BOTH,'price'=>0.015,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_SICKW,'sid'=>'8','name'=>'Simlock Checker','type'=>CommonConstants::TYPE_BOTH,'price'=>0.015,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_SICKW,'sid'=>'105','name'=>'Sold By Check','type'=>CommonConstants::TYPE_BOTH,'price'=>0.015,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_SICKW,'sid'=>'103','name'=>'Carrier Simple','type'=>CommonConstants::TYPE_BOTH,'price'=>0.015,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_SICKW,'sid'=>'19','name'=>'LF Info','type'=>CommonConstants::TYPE_BOTH,'price'=>0.015,'is_chinese_result'=>0],

            ['provider_id'=>CommonConstants::PROVIDER_HILOTMAN,'sid'=>'124','name'=>'FMI ON/OFF & STATUS CLEAN/LOST → IMEI/SN','type'=>CommonConstants::TYPE_BOTH,'price'=>0.02,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_HILOTMAN,'sid'=>'111','name'=>'MDM S2','type'=>CommonConstants::TYPE_BOTH,'price'=>0.02,'is_chinese_result'=>0],

            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'IMEI ⇄ IMEI2 ⇄ Serial Convert','sid' => 'imei-to-sn','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'iCloud + FMI','sid' => 'icloud-fmi','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'Warranty','sid' => 'warranty','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'FMI','sid' => 'fmi','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'GSMA','sid' => 'gsma','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'Python Carrier FMI','sid' => 'python-carrier-fmi','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'Python Carrier FMI Warranty','sid' => 'python-carrier-fmi-warranty','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'Python Carrier Warranty','sid' => 'python-carrier-warranty','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'Python All In One','sid' => 'python-all-in-one','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'python-carrier-fmi-warranty-gsma','sid' => 'python-all-in-one-fmi','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'Python All In One with MDM','sid' => 'python-all-in-one-mdm','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'Carrier with replaced model','sid' => 'python-carrier-model','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'TAC + US-Blacklist','sid' => 'tac-us-blacklist','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'TAC Database','sid' => 'tac','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'Warranty with replaced model','sid' => 'python-warranty-model','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'Python Simlock FMI','sid' => 'python-simlock-fmi','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'Python Simlock FMI Warranty','sid' => 'python-simlock-fmi-warranty','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'Python Simlock Warranty','sid' => 'python-simlock-warranty','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'Simlock with replaced model','sid' => 'python-simlock-model','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'Python Simlock iCloud+FMI','sid' => 'python-simlock-icloudfmi','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'Python Simlock iCloud+FMI Warranty GSMA','sid' => 'python-simlock-icloudfmi-warranty-gsma','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'Python Simlock FMI Warranty GSMA','sid' => 'python-simlock-fmi-warranty-gsma','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'Python Simlock iCloud+FMI Warranty','sid' => 'python-simlock-icloudfmi-warranty','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'IMEI ⇄ IMEI2 ⇄ Serial Convert (Clone)','sid' => 'imei-to-sn-clone','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'IMEI ⇄ IMEI2 ⇄ Serial Convert (Clone2)','sid' => 'imei-to-sn-clone2','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'python-carrier-warranty-gsma-without-fmi','sid' => 'python-all-in-one-without-fmi','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'IMEI ⇄ IMEI2 ⇄ Serial Convert (Clone3)','sid' => 'imei-to-sn-clone3','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'IMEI ⇄ IMEI2 ⇄ Serial Convert (DB Only)','sid' => 'imei-to-sn-db','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'MDM','sid' => 'mdm','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'GSMA Pro','sid' => 'gsma-pro','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'Python All In One Pro','sid' => 'python-all-in-one-pro','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'EID Finder','sid' => 'eid','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'IMEI ⇄ IMEI2 ⇄ Serial Convert ⇄ Simlock (DB + Simlock)','sid' => 'python-imei-to-sn-db-simlock','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_MERGE_PYTHON,'name' => 'IMEI ⇄ IMEI2 ⇄ Serial Convert ⇄ Carrier (DB + Carrier)','sid' => 'python-imei-to-sn-db-carrier','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],

            ['provider_id'=>CommonConstants::PROVIDER_UNLOCK_3,'sid'=>'clean2','name'=>'clean2','type'=>CommonConstants::TYPE_BOTH,'price'=>0.024,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_UNLOCK_3,'sid'=>'fmia','name'=>'FMI','type'=>CommonConstants::TYPE_BOTH,'price'=>0.006,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_UNLOCK_3,'sid'=>'model','name'=>'Model Color Gb','type'=>CommonConstants::TYPE_BOTH,'price'=>0.1,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_UNLOCK_3,'sid'=>'baoxen','name'=>'Warranty Check English','type'=>CommonConstants::TYPE_BOTH,'price'=>0.0084,'is_chinese_result'=>0],

            ['provider_id'=>CommonConstants::PROVIDER_PHONE_CHECK,'sid'=>'43','name'=>'WW GSMA Blacklist [Simple] Check by IMEI 📝','type'=>CommonConstants::TYPE_BOTH,'price'=>0.008,'is_chinese_result'=>0],

            ['provider_id'=>CommonConstants::PROVIDER_APPLE_CHECK,'sid'=>'119','name'=>'Apple Warranty Check By(SN)','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],

            ['provider_id'=>CommonConstants::PROVIDER_ZHAOJIHUI,'sid'=>'55','name'=>'MDM','type'=>CommonConstants::TYPE_BOTH,'price'=>0.29,'is_chinese_result'=>1],
            ['provider_id'=>CommonConstants::PROVIDER_ZHAOJIHUI,'sid'=>'202','name'=>'MDM S2','type'=>CommonConstants::TYPE_BOTH,'price'=>0.26,'is_chinese_result'=>1],

            ['provider_id'=>CommonConstants::PROVIDER_IFREE_ICLOUD,'sid'=>'274','name'=>'Model','type'=>CommonConstants::TYPE_BOTH,'price'=>0.003,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_IFREE_ICLOUD,'sid'=>'232','name'=>'IMEI2SN','type'=>CommonConstants::TYPE_BOTH,'price'=>0.006,'is_chinese_result'=>0],

            ['provider_id'=>CommonConstants::PROVIDER_ALPHA_IMEI_CHECK,'sid'=>'14','name'=>'IMEI ⇄ IMEI2 ⇄ Serial Convert','type'=>CommonConstants::TYPE_BOTH,'price'=>0.01,'is_chinese_result'=>0],

            ['provider_id'=>CommonConstants::PROVIDER_GSX_UNLOCKING,'sid'=>'1175','name'=>'Samsung Checker','type'=>CommonConstants::TYPE_BOTH,'price'=>0.01,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_GSX_UNLOCKING,'sid'=>'1063','name'=>'Fmi','type'=>CommonConstants::TYPE_BOTH,'price'=>0.004,'is_chinese_result'=>0],

            ['provider_id'=>CommonConstants::PROVIDER_IMEI_LOOKUP,'sid'=>'38','name'=>'MDM','type'=>CommonConstants::TYPE_BOTH,'price'=>0.27,'is_chinese_result'=>0],

            ['provider_id'=>CommonConstants::PROVIDER_UNLOCK_API,'sid'=>'huawei','name'=>'Huawei','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_UNLOCK_API,'sid'=>'1086','name'=>'MDM','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_UNLOCK_API,'sid'=>'1050','name'=>'Moto','type'=>CommonConstants::TYPE_BOTH,'price'=>0.03,'is_chinese_result'=>1],

            ['provider_id'=>CommonConstants::PROVIDER_IUNLOCK_TEAM,'sid'=>'248','name'=>'Samsung KG S1','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_IUNLOCK_TEAM,'sid'=>'179','name'=>'Samsung KG S2','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_IUNLOCK_TEAM,'sid'=>'240','name'=>'Xioami','type'=>CommonConstants::TYPE_BOTH,'price'=>0,'is_chinese_result'=>0],

            ['provider_id'=>CommonConstants::PROVIDER_IUNLOCK_TEAM_NEW,'sid'=>'157','name'=>'Samsung Full Info','type'=>CommonConstants::TYPE_BOTH,'price'=>0.01,'is_chinese_result'=>0],
            ['provider_id'=>CommonConstants::PROVIDER_IUNLOCK_TEAM_NEW,'sid'=>'286','name'=>'Samsung Full Info + Knox','type'=>CommonConstants::TYPE_BOTH,'price'=>0.01,'is_chinese_result'=>0],
        ];

        if(count($rows)>0) {
            foreach ($rows as $row) {
                $isExist=ProviderService::query()
                    ->where('provider_id','=',$row['provider_id'])
                    ->where('sid','=',$row['sid'])
                    ->first();
                if(!$isExist) {
                    $row['created_at'] = date(CommonConstants::PHP_DATE_FORMAT);
                    ProviderService::create($row);
                }
            }
        }
    }
}
