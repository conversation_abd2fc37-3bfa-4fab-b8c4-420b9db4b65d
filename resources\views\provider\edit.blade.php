<?php
use App\Components\Helper;
?>
@extends(\App\Components\Helper::getLayoutForUser())
@section('content')
    <main>
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <h1>{{__('Update: '.$provider->name)}}</h1>
                    <div class="text-zero top-right-button-container">
                        <a href="{{route('provider.create')}}" class="btn btn-primary btn-lg top-right-button mr-1"> <i class="glyph-icon simple-icon-plus"></i> ADD NEW</a>
                    </div>

                    <nav class="breadcrumb-container d-none d-sm-block d-lg-inline-block" aria-label="breadcrumb">
                        <ol class="breadcrumb pt-0">
                            <li class="breadcrumb-item">
                                <a href="{{\App\Components\Helper::dashboardLink()}}">{{__('Dashboard')}}</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{route('provider.index')}}">{{__('Providers')}}</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{route('provider.show',$provider->id)}}">{{__($provider->name)}}</a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">{{__('Update')}}</li>
                        </ol>
                    </nav>
                    <div class="separator mb-5"></div>
                </div>
            </div>

            @include('provider.form', compact('provider'))
        </div>
    </main>

@endsection
