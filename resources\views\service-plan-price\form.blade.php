<div class="row mb-4">
    <div class="col-lg-12 col-md-12 mb-4">
        <div class="card">
            <div class="card-body">

<div class="rounded-16 form-box bg-white -dark-bg-dark-1 shadow-4 h-100">
    <form action="{{ $servicePlanPrice->id ==null ? route('service-plan-price.store') : route('service-plan-price.update', $servicePlanPrice) }}" method="POST"
          class="normal-form">
        @csrf

        @if( $servicePlanPrice->id != null )
            @method('PUT')
        @endif

        <div class="row">
            <div class="col-md-3 col-12">
                <div class="form-group required-field text-left @error('plan_id') is-invalid @enderror">
                    <label for="plan_id">{{ __('Plan') }}</label>
                    <select required name="plan_id" id="plan_id" class="form-control filterField">
                        <option value="">Select</option>
                        <?php
                        $plans=\App\Models\Plan::defaultQuery()->get();
                        if(count($plans)>0) {
                            foreach ($plans as $plan) {
                                ?>
                                <option <?=($plan->id==$servicePlanPrice->plan_id ? 'selected=""' : '')?> value="<?=$plan->id?>"><?=$plan->name?></option>
                                <?php
                            }
                        }
                        ?>
                    </select>
                    @error('plan_id')
                    <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                    @enderror
                </div>
            </div>

            <div class="col-md-3 col-12">
                <div class="form-group required-field text-left @error('service_id') is-invalid @enderror">
                    <label for="service_id">{{ __('Service') }}</label>
                    <select required name="service_id" id="service_id" class="form-control filterField">
                        <?=\App\Models\Service::printOptionsHTML($servicePlanPrice->service_id)?>
                    </select>
                    @error('service_id')
                    <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                    @enderror
                </div>
            </div>

            <div class="col-md-3 col-12">
                <div class="form-group required-field text-left @error('price') is-invalid @enderror">
                    <label for="price">{{ __('Price (in $)') }}</label>
                    <input required id="price" type="text" class="form-control @error('price') is-invalid @enderror"
                           name="price" placeholder="{{ __('Enter Price') }}"
                           value="{{old('price', $servicePlanPrice->price)}}">
                    @error('price')
                    <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                    @enderror
                </div>
            </div>

            <div class="col-md-3 col-12">
                <div class="form-group required-field text-left @error('is_active') is-invalid @enderror">
                    <label for="is_active">{{ __('Active') }}</label>
                    <select name="is_active" id="is_active" class="form-control @error('is_active') is-invalid @enderror">
                        <option <?=(!$servicePlanPrice->is_active ? 'selected=""' : '')?> value="<?=\App\Constants\CommonConstants::NO?>">No</option>
                        <option <?=($servicePlanPrice->is_active ? 'selected=""' : '')?> value="<?=\App\Constants\CommonConstants::YES?>">Yes</option>
                    </select>
                    @error('is_active')
                    <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                    @enderror
                </div>
            </div>
        </div>

        @if( $servicePlanPrice->id == null )

        @endif

        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-12 formBtn">
                @if( $servicePlanPrice->id == null )
                    <button type="submit" class="btn btn-primary">{{ __('Save') }}</button>
                @else
                    <button type="submit" class="btn btn-primary">{{ __('Update') }}</button>
                @endif
            </div>
        </div>

    </form>
</div>
            </div>
        </div>
    </div>
</div>

@section('pageJs')

    <script>

    </script>

@endsection
