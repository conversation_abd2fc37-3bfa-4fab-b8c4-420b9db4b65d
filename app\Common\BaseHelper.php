<?php
if(!function_exists('getUsersCache')){
    function getUsersCache() {
        return Cache::remember('usersCache', 86400, function () {
            //return DB::table('users')->where('is_active','=',1)->all();
            $users=\App\Models\User::with('plan')
                ->with('country')->orderBy("username")->get();
            $cacheData=['users'=>$users,'balances'=>[]];
            if(count($users)>0) {
                foreach ($users as $user) {
                    $cacheData['balances'][$user->id]=$user->balance;
                }
            }
            return $cacheData;
        });
    }
}
if(!function_exists('updateUserCache')){
    function updateUserCache() {
        $usersCache=getUsersCache();
        $users=\App\Models\User::with('plan')
            ->with('country')->orderBy("username")->get();
        $usersCache['users']=$users;
        if(count($users)>0) {
            if(array_key_exists('balances',$usersCache)) {
                foreach ($users as $user) {
                    if(!array_key_exists($user->id,$usersCache['balances'])) {
                        $usersCache['balances'][$user->id]=0;
                    }
                }
            }
        }
        Cache::put('usersCache',$usersCache);
        return true;
    }
}
if(!function_exists('creditUserBalance')){
    function creditUserBalance($userID,$amount) {
        $usersCache=getUsersCache();
        if(array_key_exists('balances',$usersCache)) {
            if(array_key_exists($userID,$usersCache['balances'])) {
                $usersCache['balances'][$userID]=round($usersCache['balances'][$userID]+$amount,\App\Constants\CommonConstants::USD_PRECISION);
            }
        }
        Cache::put('usersCache',$usersCache);
        return true;
    }
}
if(!function_exists('debitUserBalance')){
    function debitUserBalance($userID,$amount) {
        $usersCache=getUsersCache();
        if(array_key_exists('balances',$usersCache)) {
            if(array_key_exists($userID,$usersCache['balances'])) {
                $usersCache['balances'][$userID]=round($usersCache['balances'][$userID]-$amount,\App\Constants\CommonConstants::USD_PRECISION);
            }
        }
        Cache::put('usersCache',$usersCache);
        return true;
    }
}
if(!function_exists('updateUserBalance')){
    function updateUserBalance($userID,$balance) {
        $usersCache=getUsersCache();
        if(array_key_exists('balances',$usersCache)) {
            if(array_key_exists($userID,$usersCache['balances'])) {
                $usersCache['balances'][$userID]=round($balance,\App\Constants\CommonConstants::USD_PRECISION);
            }
        }
        Cache::put('usersCache',$usersCache);
        return true;
    }
}

if(!function_exists('getCategoriesCache')){
    function getCategoriesCache() {
        return Cache::remember('categoriesCache', 86400, function () {
            //return DB::table('categories')->where('is_active','=',1)->all();
            return \App\Models\Category::orderBy("display_order")->get();
        });
    }
}

if(!function_exists('getServicesCache')){
    function getServicesCache() {
        return Cache::remember('servicesCache', 86400, function () {
            //return DB::table('categories')->where('is_active','=',1)->all();
            return \App\Models\Service::orderBy("display_order")->get();
        });
    }
}

if(!function_exists('resetUserCache')){
    function resetUserCache() {
        Cache::forget("usersCache");
    }
}

if(!function_exists('resetServicesCache')){
    function resetServicesCache() {
        Cache::forget("servicesCache");
    }
}

if(!function_exists('resetCategoriesCache')){
    function resetCategoriesCache() {
        Cache::forget("categoriesCache");
    }
}

if(!function_exists('resetSystemCache')){
    function resetSystemCache() {
        Cache::forget("usersCache");
        Cache::forget("categoriesCache");
        Cache::forget("servicesCache");
    }
}
