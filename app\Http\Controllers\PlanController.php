<?php

namespace App\Http\Controllers;

use App\Base\Model\BaseModel;
use App\Components\Helper;
use App\Constants\CommonConstants;
use App\Constants\UserConstants;
use App\Models\Plan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Yajra\DataTables\Facades\DataTables;

class PlanController extends Controller
{
    public function index()
    {
        return view('plan.index');
    }

    public function show(Plan $plan,Request $request)
    {
        return view('plan.show', compact('plan'));
    }

    public function create()
    {
        $plan=new Plan();
        return view('plan.create', compact('plan'));
    }

    public function edit(Plan $plan)
    {
        return view('plan.edit', compact('plan'));
    }

    public function store(Request $request)
    {
        $plan = new Plan();
        return $this->save($request, $plan);
    }

    public function update(Request $request, Plan $plan)
    {
        return $this->save($request, $plan);
    }

    private function save(Request $request, Plan $plan)
    {
        $isNewRecord = true;
        if ($plan->id != null) {
            $isNewRecord = false;
        }

        $rules = [
            'name' => ['required', 'string',Rule::unique('plans')->ignore($plan->id)],
            'price' => ['required', 'numeric',
                function ($attribute, $value, $fail) {
                    if (!empty($value) && $value<0) {
                        $fail('Invalid amount entered');
                    }
                },
            ],
            'is_default' => ['required', 'integer'],
            'is_visible_to_guest' => ['required', 'integer'],
            'is_best_seller' => ['required', 'integer'],
            'is_active' => ['required', 'integer'],
        ];

        if ($isNewRecord) {

        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            if ($isNewRecord) {
                return redirect()->route('plan.create')->withErrors($validator)->withInput();
            } else {
                return redirect()->route('plan.edit', $plan->id)->withErrors($validator)->withInput();
            }
        }

        $ip = request()->ip();
        $ipDetails = Helper::fetchIpDetails($ip, true);

        $plan->name = $request->input('name');
        $plan->price = $request->input('price');
        $plan->is_default = (int)$request->input('is_default');
        $plan->is_visible_to_guest = (int)$request->input('is_visible_to_guest');
        $plan->is_best_seller = (int)$request->input('is_best_seller');
        $plan->is_active = (int)$request->input('is_active');
        if ($isNewRecord) {
            $plan->save();
        } else {
            $plan->update();
        }
        return redirect()->route('plan.index')->with('success', 'Admin adjustment saved successfully.');
    }

    public function dataTable(Request $request)
    {
        try {
            if ($request->ajax()) {
                $query = Plan::query();
                BaseModel::buildFilterQuery($query, [
                    'q' => ['name', 'price'],
                    'is_active',
                ]);
                return Datatables::eloquent($query)
                    ->addColumn('checkboxes', function ($row) {
                        return '<input type="checkbox" name="pdr_checkbox[]" class="pdr_checkbox" value="' . $row->id . '" />';
                    })
                    ->addColumn('name', function ($row) {
                        return Helper::editPopupStructure($row,'name');
                    })
                    ->addColumn('price', function ($row) {
                        return Helper::editPopupStructure($row,'price',null,'Price (in $)');
                        //return Helper::printAmount($row->price);
                    })
                    ->addColumn('created_at', function ($row) {
                        return Helper::displayTime($row->created_at);
                    })
                    ->addColumn('updated_at', function ($row) {
                        return Helper::displayTime($row->updated_at);
                    })
                    ->addColumn('is_visible_to_guest', function ($row) use ($query) {
                        $columnName = 'is_visible_to_guest';
                        return Helper::onOffButton($row, $columnName, $query);
                    })
                    ->addColumn('is_active', function ($row) use ($query) {
                        $columnName = 'is_active';
                        return Helper::onOffButton($row, $columnName, $query);
                    })
                    ->addColumn('is_default', function ($row) {
                        return Helper::printYesNoBadge(!($row->is_default == null));
                    })
                    ->addColumn('is_best_seller', function ($row) use ($query) {
                        $columnName = 'is_best_seller';
                        return Helper::onOffButton($row, $columnName, $query);
                    })
                    ->addColumn('actions', function ($row) {
                        $buttons = [];
                        $buttons['view'] = ['url' => route('plan.show', $row->id)];
                        $buttons['edit'] = ['url' => route('plan.edit', $row->id)];
                        return Helper::getActionButtons($buttons);
                    })
                    ->rawColumns(['checkboxes','name', 'is_default', 'is_best_seller','price', 'actions', 'is_visible_to_guest', 'is_active'])
                    ->make(true);
            }
        } catch (\Exception $e) {
            //print_r(['message'=>$e->getMessage(),'trace'=>$e->getTraceAsString()]);
            die();
        }
    }
}
