<?php

namespace App\Http\Controllers;

use App\Base\Model\BaseModel;
use App\Components\Helper;
use App\Constants\CommonConstants;
use App\Constants\UserConstants;
use App\Models\UserOrder;
use App\Models\UserOrderLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Yajra\DataTables\Facades\DataTables;

class UserOrderLogController extends Controller
{
    public function index()
    {
        return view('user-order-log.index');
    }

    public function topUser(Request $request)
    {
        $duration="week";
        if($request->input('duration')) {
            switch ($request->input('duration')) {
                case "week":
                case "this-month":
                case "last-month":
                    $duration=$request->input('duration');
                    break;
            }
        }
        $startDate=$endDate=null;

        switch ($duration) {
            case "week":
                $startDate=date(CommonConstants::PHP_DATE_FORMAT_SHORT,strtotime("-6 days"));
                $endDate=date(CommonConstants::PHP_DATE_FORMAT_SHORT);
                break;

            case "this-month":
                $startDate=date(CommonConstants::PHP_DATE_FORMAT_SHORT,strtotime(date("Y-m")."-01"));
                $endDate=date(CommonConstants::PHP_DATE_FORMAT_SHORT,strtotime("+1 month",strtotime($startDate))-1);
                break;
            case "last-month":
                $startDate=date(CommonConstants::PHP_DATE_FORMAT_SHORT,strtotime(date("Y-m",strtotime("-1 month"))."-01"));
                $endDate=date(CommonConstants::PHP_DATE_FORMAT_SHORT,strtotime("+1 month",strtotime($startDate))-1);
                break;
        }

        $logs = UserOrderLog::query()
            ->whereBetween('date', [$startDate . " 00:00:00", $endDate . " 23:59:59"])->get();
        return view('user-order-log.top-user',compact('logs','startDate','endDate','duration'));
    }

    public function topService(Request $request)
    {
        $duration="week";
        if($request->input('duration')) {
            switch ($request->input('duration')) {
                case "week":
                case "this-month":
                case "last-month":
                    $duration=$request->input('duration');
                    break;
            }
        }
        $startDate=$endDate=null;

        switch ($duration) {
            case "week":
                $startDate=date(CommonConstants::PHP_DATE_FORMAT_SHORT,strtotime("-6 days"));
                $endDate=date(CommonConstants::PHP_DATE_FORMAT_SHORT);
                break;

            case "this-month":
                $startDate=date(CommonConstants::PHP_DATE_FORMAT_SHORT,strtotime(date("Y-m")."-01"));
                $endDate=date(CommonConstants::PHP_DATE_FORMAT_SHORT,strtotime("+1 month",strtotime($startDate))-1);
                break;
            case "last-month":
                $startDate=date(CommonConstants::PHP_DATE_FORMAT_SHORT,strtotime(date("Y-m",strtotime("-1 month"))."-01"));
                $endDate=date(CommonConstants::PHP_DATE_FORMAT_SHORT,strtotime("+1 month",strtotime($startDate))-1);
                break;
        }

        $logs = UserOrderLog::query()
            ->whereBetween('date', [$startDate . " 00:00:00", $endDate . " 23:59:59"])->get();
        return view('user-order-log.top-service',compact('logs','startDate','endDate','duration'));
    }

    public function show(UserOrderLog $userOrderLog,Request $request)
    {
        return redirect()->route('user-order-log.index');
        return view('user-order-log.show', compact('userOrderLog'));
    }

    public function create()
    {
        return redirect()->route('user-order-log.index');
        $userOrderLog=new UserOrderLog();
        return view('user-order-log.create', compact('userOrderLog'));
    }

    public function edit(UserOrderLog $userOrderLog)
    {
        return redirect()->route('user-order-log.index');
        return view('user-order-log.edit', compact('userOrderLog'));
    }

    public function store(Request $request)
    {
        $userOrderLog = new UserOrderLog();
        return $this->save($request, $userOrderLog);
    }

    public function update(Request $request, UserOrderLog $userOrderLog)
    {
        return $this->save($request, $userOrderLog);
    }

    private function save(Request $request, UserOrderLog $userOrderLog)
    {
        return redirect()->route('user-order-log.index');
        $isNewRecord = true;
        if ($userOrderLog->id != null) {
            $isNewRecord = false;
        }

        $rules = [
            'operating_system_id' => ['required', 'integer'],
            'category_id' => ['required', 'integer'],
            'provider_id' => ['required', 'integer'],
            'display_order' => ['required', 'integer'],
            'name' => ['required', 'string'],
            'order_format' => ['required', 'string'],
            'download_format' => ['required', 'string'],
            'price' => ['required','numeric'],
        ];

        if ($isNewRecord) {

        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            if ($isNewRecord) {
                return redirect()->route('user-order-log.create')->withErrors($validator)->withInput();
            } else {
                return redirect()->route('user-order-log.edit', $userOrderLog->id)->withErrors($validator)->withInput();
            }
        }

        $userOrderLog->operating_system_id = (int)$request->input('operating_system_id');
        $userOrderLog->category_id = (int)$request->input('category_id');
        $userOrderLog->custom_id = (int)$request->input('custom_id');
        $userOrderLog->provider_id = (int)$request->input('provider_id');
        $userOrderLog->display_order = (int)$request->input('display_order');
        $userOrderLog->name = $request->input('name');
        $userOrderLog->is_active = (int)$request->input('is_active');
        $userOrderLog->price = trim($request->input('price'));
        $userOrderLog->order_format = trim($request->input('order_format'));
        $userOrderLog->download_format = trim($request->input('download_format'));
        if ($isNewRecord) {
            $userOrderLog->save();
        } else {
            $userOrderLog->update();
        }
        return redirect()->route('user-order-log.index')->with('success', 'UserOrderLog saved successfully.');
    }

    public function dataTable(Request $request)
    {
        try {
            if ($request->ajax()) {
                $query = UserOrderLog::query();
                BaseModel::buildFilterQuery($query, [
                    'q' => ['date','total_orders'],
                    'date',
                    'user_id',
                    'service_id',
                ]);
                return Datatables::eloquent($query)
                    ->addColumn('checkboxes', function ($row) {
                        return '<input type="checkbox" name="pdr_checkbox[]" class="pdr_checkbox" value="' . $row->id . '" />';
                    })
                    ->addColumn('user', function ($row) {
                        if($row->user) {
                            return $row->user->displayDetails;
                        }
                        return 'N/A';
                    })
                    ->addColumn('service', function ($row) {
                        if($row->service) {
                            return $row->service->displayDetails;
                        }
                        return 'N/A';
                    })
                    ->addColumn('cost', function ($row) {
                        return Helper::printAmount($row->cost);
                    })
                    ->addColumn('final_pl', function ($row) {
                        return Helper::printAmount($row->final_pl);
                    })
                    ->addColumn('price', function ($row) {
                        return Helper::printAmount($row->price);
                    })
                    ->addColumn('actions', function ($row) {
                        $buttons = [];
                        //$buttons['Login']=['url' => route('user.loginAs', $row->id), 'icon' => 'las la-sign-in-alt'];
                        //$buttons['view'] = ['url' => route('user-order-log.show', $row->id)];
                        //$buttons['edit'] = ['url' => route('user-order-log.edit', $row->id)];
                        return Helper::getActionButtons($buttons);
                    })
                    ->rawColumns(['checkboxes', 'status', 'result','date', 'actions'])
                    ->make(true);
            }
        } catch (\Exception $e) {
            //print_r(['message'=>$e->getMessage(),'trace'=>$e->getTraceAsString()]);
            die();
        }
    }
}
