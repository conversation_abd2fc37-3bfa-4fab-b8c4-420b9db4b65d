<div class="row mb-4">
    <div class="col-lg-12 col-md-12 mb-4">
        <div class="card">
            <div class="card-body">

<div class="rounded-16 form-box bg-white -dark-bg-dark-1 shadow-4 h-100">
    <form action="{{ $operatingSystem->id ==null ? route('operating-system.store') : route('operating-system.update', $operatingSystem) }}" method="POST"
          class="normal-form">
        @csrf

        @if( $operatingSystem->id != null )
            @method('PUT')
        @endif

        @if( $operatingSystem->id == null )
            <div class="row">
                <div class="col-md-12 col-12">
                    <div class="form-group required-field text-left @error('name') is-invalid @enderror">
                        <label for="name">{{ __('Name') }}</label>
                        <input required id="name" type="text" class="form-control @error('name') is-invalid @enderror"
                               name="name" placeholder="{{ __('Enter name') }}"
                               value="{{old('name', $operatingSystem->name)}}">
                        @error('name')
                        <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                        @enderror
                    </div>
                </div>
            </div>
        @endif

        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-12 formBtn">
                @if( $operatingSystem->id == null )
                    <button type="submit" class="btn btn-primary">{{ __('Save') }}</button>
                @else
                    <button type="submit" class="btn btn-primary">{{ __('Update') }}</button>
                @endif
            </div>
        </div>

    </form>
</div>
            </div>
        </div>
    </div>
</div>

@section('pageJs')

    <script>

    </script>

@endsection
