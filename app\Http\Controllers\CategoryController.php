<?php

namespace App\Http\Controllers;

use App\Base\Model\BaseModel;
use App\Components\Helper;
use App\Constants\CommonConstants;
use App\Constants\UserConstants;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Yajra\DataTables\Facades\DataTables;

class CategoryController extends Controller
{
    public function index()
    {
        return view('category.index');
    }

    public function show(Category $category,Request $request)
    {
        return view('category.show', compact('category'));
    }

    public function create()
    {
        $category=new Category();
        return view('category.create', compact('category'));
    }

    public function edit(Category $category)
    {
        return view('category.edit', compact('category'));
    }

    public function store(Request $request)
    {
        $category = new Category();
        return $this->save($request, $category);
    }

    public function update(Request $request, Category $category)
    {
        return $this->save($request, $category);
    }

    private function save(Request $request, Category $category)
    {
        $isNewRecord = true;
        if ($category->id != null) {
            $isNewRecord = false;
        }

        $rules = [
            'name' => ['required', 'string', 'max:255', Rule::unique('categories')->ignore($category->id)],
            'is_active' => ['required', 'integer'],
            'display_order' => ['required', 'integer'],
        ];

        if ($isNewRecord) {

        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            if ($isNewRecord) {
                return redirect()->route('category.create')->withErrors($validator)->withInput();
            } else {
                return redirect()->route('category.edit', $category->id)->withErrors($validator)->withInput();
            }
        }

        $category->name = $request->input('name');
        $category->is_active = (int)$request->input('is_active');
        $category->display_order = (int)$request->input('display_order');
        if ($isNewRecord) {
            $category->save();
        } else {
            $category->update();
        }
        return redirect()->route('category.index')->with('success', 'Admin adjustment saved successfully.');
    }

    public function dataTable(Request $request)
    {
        try {
            if ($request->ajax()) {
                $query = Category::query();
                BaseModel::buildFilterQuery($query, [
                    'q' => ['name','display_order'],
                    'is_active',
                ]);
                return Datatables::eloquent($query)
                    ->addColumn('checkboxes', function ($row) {
                        return '<input type="checkbox" name="pdr_checkbox[]" class="pdr_checkbox" value="' . $row->id . '" />';
                    })
                    ->addColumn('name', function ($row) {
                        return Helper::editPopupStructure($row,'name');
                    })
                    ->addColumn('display_order', function ($row) {
                        return Helper::editPopupStructure($row,'display_order');
                    })
                    ->addColumn('is_active', function ($row) use ($query) {
                        $columnName = 'is_active';
                        return Helper::onOffButton($row, $columnName, $query);
                    })
                    ->addColumn('created_at', function ($row) {
                        return Helper::displayTime($row->created_at);
                    })
                    ->addColumn('updated_at', function ($row) {
                        return Helper::displayTime($row->updated_at);
                    })
                    ->addColumn('actions', function ($row) {
                        $buttons = [];
                        //$buttons['Login']=['url' => route('user.loginAs', $row->id), 'icon' => 'las la-sign-in-alt'];
                        $buttons['view'] = ['url' => route('category.show', $row->id)];
                        $buttons['edit'] = ['url' => route('category.edit', $row->id)];
                        return Helper::getActionButtons($buttons);
                    })
                    ->rawColumns(['checkboxes', 'name', 'is_active', 'actions', 'display_order'])
                    ->make(true);
            }
        } catch (\Exception $e) {
            //print_r(['message'=>$e->getMessage(),'trace'=>$e->getTraceAsString()]);
            die();
        }
    }
}
