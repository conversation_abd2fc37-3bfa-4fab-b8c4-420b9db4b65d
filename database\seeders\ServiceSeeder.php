<?php

namespace Database\Seeders;

use App\Constants\CommonConstants;
use App\Models\Category;
use App\Models\Provider;
use App\Models\ProviderService;
use App\Models\Service;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ServiceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $rows=[
            ['operating_system_id'=>1,'category_id'=>1,'provider_id'=>CommonConstants::PROVIDER_SICKW,'provider_service_id'=>1,'name'=>'IMEI-SN Source 1','type'=>CommonConstants::TYPE_BOTH,'price'=>0.5],
            ['operating_system_id'=>1,'category_id'=>1,'provider_id'=>CommonConstants::PROVIDER_FASTBULK,'provider_service_id'=>2,'name'=>'IMEI-SN Source 2','type'=>CommonConstants::TYPE_BOTH,'price'=>0.5],
            ['operating_system_id'=>1,'category_id'=>1,'provider_id'=>CommonConstants::PROVIDER_HILOTMAN,'provider_service_id'=>3,'name'=>'FMI Source 1','type'=>CommonConstants::TYPE_BOTH,'price'=>0.5],
        ];

        if(count($rows)>0) {
            foreach ($rows as $row) {
                $isExist=Service::query()
                    ->where('operating_system_id','=',$row['operating_system_id'])
                    ->where('category_id','=',$row['category_id'])
                    ->where('provider_id','=',$row['provider_id'])
                    ->where('provider_service_id','=',$row['provider_service_id'])
                    ->first();
                if(!$isExist) {
                    $row['created_at'] = date(CommonConstants::PHP_DATE_FORMAT);
                    Service::create($row);
                }
            }
        }
    }
}
