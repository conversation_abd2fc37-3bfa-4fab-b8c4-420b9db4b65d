<?php

namespace App\Casts;

use App\Constants\CommonConstants;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;

class AmountCast implements CastsAttributes
{
    public function get($model, string $key, $value, array $attributes)
    {
        return round($value / CommonConstants::AMOUNT_MULTIPLIER,CommonConstants::USD_PRECISION);
    }

    public function set($model, string $key, $value, array $attributes)
    {
        return round($value * CommonConstants::AMOUNT_MULTIPLIER,CommonConstants::USD_PRECISION);
    }
}
