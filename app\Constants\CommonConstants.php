<?php

namespace App\Constants;

class CommonConstants
{
    const DISPLAY_DATE_FORMAT = 'Y-m-d';
    const DISPLAY_DATE_TIME_FORMAT = 'Y-m-d H:i:s A';

    const PHP_DATE_FORMAT = 'Y-m-d H:i:s';
    const PHP_DATE_FORMAT_SHORT = 'Y-m-d';

    const DEFAULT_REFERRAL_CODE = 'system';
    const DEFAULT_USER_ROLE = 2;

    const AFTER_LOGIN_REDIRECT_URL = 'dashboard';

    const ADMINISTRATIVE = 1;

    const USD_PRECISION = 4;

    const AMOUNT_MULTIPLIER = 10000;

    const ALLOWED_EXTENSIONS = ['jpg', 'png', 'jpeg'];

    const CURRENCY_ICON = '$';
    const CURRENCY_CODE = 'USD';

    const DATA_TYPE_STRING ="text";
    const DATA_TYPE_TEXTAREA ="textarea";

    const TYPE_IMEI = 1;
    const TYPE_SN = 2;
    const TYPE_BOTH = 0;

    const TRANSACTION_TYPE_DEPOSIT = 1;
    const TRANSACTION_TYPE_ORDER_PLACED = 2;
    const TRANSACTION_TYPE_ORDER_REFUND = 3;
    const TRANSACTION_TYPE_ADMIN_ADJUSTMENT = 4;

    const YES = 1;
    const NO = 0;

    const DEBIT = 1;
    const CREDIT = 0;

    const DEFAULT_COUNTRY = 38;

    const PROVIDER_SICKW = 1;
    const PROVIDER_FASTBULK = 2;
    const PROVIDER_HILOTMAN = 3;
    const PROVIDER_MERGE_PYTHON = 4;
    const PROVIDER_UNLOCK_3 = 5;
    const PROVIDER_PHONE_CHECK = 6;
    const PROVIDER_APPLE_CHECK = 7;
    const PROVIDER_ZHAOJIHUI = 8;
    const PROVIDER_IFREE_ICLOUD = 9;
    const PROVIDER_ALPHA_IMEI_CHECK = 10;
    const PROVIDER_GSX_UNLOCKING = 11;
    const PROVIDER_IMEI_LOOKUP = 12;
    const PROVIDER_UNLOCK_API = 13;
    const PROVIDER_IUNLOCK_TEAM = 14;
    const PROVIDER_IUNLOCK_TEAM_NEW = 15;


    const SETTING_SUPPORT_EMAIL = 1;

    const YES_STRING = 'yes';
    const NO_STRING = 'no';

    const LIVE_WIRE_THEME = 'bootstrap-4';

    const YES_NO_PROPERTIES = [
        self::YES => ['class' => 'badge badge-success', 'text' => 'Yes'],
        self::NO => ['class' => 'badge badge-danger', 'text' => 'No']
    ];

    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';

    const STATUS_PROPERTIES = [
        self::STATUS_ACTIVE => ['class' => 'badge badge-success', 'text' => 'Active'],
        self::STATUS_INACTIVE => ['class' => 'badge badge-danger', 'text' => 'In-Active']
    ];

    const ORDER_STATUS_PENDING = 'pending';
    const ORDER_STATUS_IN_PROGRESS = 'inprogress';
    const ORDER_STATUS_COMPLETED = 'completed';
    const ORDER_STATUS_FAILED = 'failed';

    const ORDER_PROPERTIES = [
        self::ORDER_STATUS_PENDING => ['class' => 'badge badge-default', 'text' => 'Pending'],
        self::ORDER_STATUS_IN_PROGRESS => ['class' => 'badge badge-info', 'text' => 'In-Progress'],
        self::ORDER_STATUS_COMPLETED => ['class' => 'badge badge-success', 'text' => 'Completed'],
        self::ORDER_STATUS_FAILED => ['class' => 'badge badge-danger', 'text' => 'Failed']
    ];

    const CACHE_DIRECTORY_NAME = 'system_cache';
}
