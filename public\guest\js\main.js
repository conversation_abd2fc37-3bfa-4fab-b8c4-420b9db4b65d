$(document).ready(function () {
  // $(".loader").hide();

  // Current Year
  const date = new Date();
  $("#year").text(date.getFullYear());

  // Initialize tooltip
  $('[data-toggle="tooltip"]').tooltip();

  //Topseller Slider
  if ($(".topSellerSlider").length) {
    $(".topSellerSlider").slick({
      infinite: false,
      slidesToShow: 3,
      slidesToScroll: 1,
      arrows: true,
      dots: false,
      adaptiveHeight: false,
      appendArrows: ".slider_btn",
      responsive: [
        {
          breakpoint: 1025,
          settings: {
            slidesToShow: 2,
          },
        },
        {
          breakpoint: 768,
          settings: {
            slidesToShow: 1,
          },
        },
      ],
    });
  }

  if ($(".advan_slider").length) {
    $(".advan_slider").slick({
      infinite: true,
      slidesToShow: 4,
      slidesToScroll: 1,
      arrows: false,
      dots: false,
      autoplay: false,
      responsive: [
        {
          breakpoint: 1367,
          settings: {
            slidesToShow: 3,
          },
        },
        {
          breakpoint: 1025,
          settings: {
            slidesToShow: 2,
          },
        },
        {
          breakpoint: 769,
          settings: {
            slidesToShow: 1,
          },
        },
      ],
    });
  }

  if ($(".client_slider").length) {
    $(".client_slider").slick({
      infinite: false,
      slidesToShow: 3,
      slidesToScroll: 1,
      arrows: true,
      dots: false,
      appendArrows: ".client_slide_btn",
      responsive: [
        {
          breakpoint: 992,
          settings: {
            slidesToShow: 2,
          },
        },
        {
          breakpoint: 768,
          settings: {
            slidesToShow: 1,
          },
        },
      ],
    });
  }

  // Banner slider
  // if ($("#slick-spotlight").length) {
  //   $("#slick-spotlight").slick({
  //     slidesToShow: 1,
  //     slidesToScroll: 1,
  //     arrows: false,
  //     fade: true,
  //     asNavFor: "#slick-thumb",
  //     autoplay: true,
  //     pauseOnHover: false,
  //     lazyLoad: "ondemand",
  //   });
  // }

  // if ($("#slick-thumb").length) {
  //   $("#slick-thumb").slick({
  //     slidesToShow: 4,
  //     slidesToScroll: 1,
  //     dots: false,
  //     arrows: false,
  //     infinite: true,
  //     autoplay: true,
  //     cssEase: "linear",
  //     focusOnSelect: true,
  //     asNavFor: "#slick-spotlight",
  //     pauseOnHover: false,
  //     responsive: [
  //       {
  //         breakpoint: 1200,
  //         settings: {
  //           slidesToShow: 3,
  //         },
  //       },
  //       {
  //         breakpoint: 767,
  //         settings: {
  //           slidesToShow: 2,
  //         },
  //       },
  //       {
  //         breakpoint: 578,
  //         settings: {
  //           slidesToShow: 1,
  //         },
  //       },
  //     ],
  //   });
  // }

  // Playlist slider
  if ($(".testimonoalPlaylistSlider").length) {
    $(".testimonoalPlaylistSlider").slick({
      infinite: true,
      slidesToShow: 4,
      slidesToScroll: 1,
      arrows: true,
      dots: false,
      focusOnSelect: true,
      appendArrows: $(".testimonoalsliderArrow")[0],

      responsive: [
        {
          breakpoint: 992,
          settings: {
            slidesToShow: 3,
            arrows: false,
          },
        },
        {
          breakpoint: 768,
          settings: {
            slidesToShow: 2,
            arrows: false,
          },
        },
        {
          breakpoint: 576,
          settings: {
            slidesToShow: 1,
            arrows: false,
          },
        },
      ],
    });

    $(".testimonoalPlaylistSlider").on(
      "beforeChange",
      function (event, slick, currentSlide, nextSlide) {
        var nextSlideDom = $(slick.$slides.get(nextSlide));
        const videoIndex = $(nextSlideDom).data("playlist");
        const videoUrl = "./images/video/video" + videoIndex + ".mp4";
        const videoFile = `<source src="${videoUrl}" type="video/mp4"></source>`;
        $("#testimonialVideo").html(videoFile);
        $("#testimonialVideo")[0].load();
      }
    );
  }

  // Play video
  $(".testimonoalPlaylistSlide").on("click", function () {
    const videoIndex = $(this).data("playlist");
    const videoUrl = "./images/video/video" + videoIndex + ".mp4";
    const videoFile = `<source src="${videoUrl}" type="video/mp4"></source>`;
    $("#testimonialVideo").html(videoFile);
    $("#testimonialVideo")[0].load();
  });

  // Client slider
  if ($(".clientTestimonialSlider").length) {
    $(".clientTestimonialSlider").slick({
      infinite: true,
      slidesToShow: 4,
      slidesToScroll: 1,
      arrows: true,
      dots: false,
      focusOnSelect: true,
      appendArrows: $(".clientTestimonialSec")[0],

      responsive: [
        {
          breakpoint: 992,
          settings: {
            slidesToShow: 3,
            arrows: false,
          },
        },
        {
          breakpoint: 768,
          settings: {
            slidesToShow: 2,
            arrows: false,
          },
        },
        {
          breakpoint: 576,
          settings: {
            slidesToShow: 1,
            arrows: false,
          },
        },
      ],
    });
  }

  // Business Slider
  // if ($("#spotlight-business").length) {
  //   $("#spotlight-business").slick({
  //     dots: true,
  //     appendDots: $("#businessSpotlight"),
  //     slidesToShow: 1,
  //     slidesToScroll: 1,
  //     arrows: false,
  //     fade: true,
  //     dots: true,
  //     autoplay: false,
  //     autoplaySpeed: 4000,
  //     pauseOnHover: false,
  //     lazyLoad: "ondemand",
  //   });
  // }

  // Always initialize aos after slick
  AOS.init({ disable: "mobile" });

  $(".netwrk_nav_itm").mouseover(function () {
    $(this).addClass("slider_active");
  });
  $(".netwrk_nav_itm").mouseout(function () {
    $(this).removeClass("slider_active");
  });
  $(".netwrk_speed_itm").mouseover(function () {
    $(this).addClass("itm_active");
  });
  $(".netwrk_speed_itm").mouseout(function () {
    $(this).removeClass("itm_active");
  });
});
