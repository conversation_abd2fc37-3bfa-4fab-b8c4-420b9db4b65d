<?php

namespace App\Components;

use App\Base\Provider\BaseProvider;
use App\Constants\CommonConstants;
use App\Models\Log;
use App\Models\User;
use Illuminate\Support\Facades\Http;

class ProviderUnlockApi extends BaseProvider
{
    public static $providerID = CommonConstants::PROVIDER_UNLOCK_API;
    public static function placeOrder($orderModel) {
        $validate=self::validateRequest($orderModel);
        if($validate!==true) {
            return $validate;
        }

        switch (self::$providerServiceDetails['sid']) {
            case "huawei":
                $url=self::$providerDetails['api_url'] . "generic/hwinfo?key=" . self::$providerDetails['api_key'] . "&imei=" . $orderModel->imei . "&format=json";
                break;
            case "1086":
                $url=self::$providerDetails['api_url'] . "submit?key=" . self::$providerDetails['api_key'] . "&api_id=" . self::$providerServiceDetails['sid']. "&imei=" . $orderModel->imei . "&format=json";
                break;
            default:
                $url = self::$providerDetails['api_url'] . "?key=".self::$providerDetails['api_key']."&imei=".$orderModel->imei."&format=json&api_id=".self::$providerServiceDetails['sid'];
                break;
        }
        $response = Http::get($url);
        $result=$response->body();

        /* $curl = curl_init ($url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 40);
        curl_setopt($curl, CURLOPT_TIMEOUT, 60);
        $result = curl_exec($curl);
        curl_close($curl); */

        if($result && $result!="") {
            return self::parseResult($orderModel,$result);
        }

        return self::defaultResult();
    }

    public static function parseResult($orderModel,$result) {
        try {
            if ($result == "" || $result == false
                || stripos($result, "Checking your browser")!==false || stripos($result, "error code")!==false) {
                return self::failedResult(self::SERVICE_MAINTENANCE_MESSAGE,$result);
            }

            $finalResult=null;
            $decode_resp = json_decode($result, true);
            if (array_key_exists('code', $decode_resp) && array_key_exists('result', $decode_resp)) {
                if ($decode_resp['code'] != "0") {
                    return self::failedResult(self::DEFAULT_MESSAGE,$result);
                } else {
                    $finalResult=$decode_resp['result'];
                }
            }
            if (!$finalResult) {
                return self::failedResult(self::DEFAULT_MESSAGE,$result);
            }

            $sid=self::$providerServiceDetails['sid'];
            switch ($sid) {
                case "huawei":
                    $new_data = array();

                    $new_csv_result = self::getCsvReport($finalResult, false, false, true);
                    if (is_array($new_csv_result) && count($new_csv_result) > 0) {
                        foreach ($new_csv_result as $key => $value) {
                            $key = trim($key);
                            if($key=="WarrStartDate") {
                                $key="Warr StartDate";
                            }
                            switch(strtolower($key)) {
                                case "serialnumber":
                                    $key="Serial Number";
                                    break;

                                case "skuname":
                                    $key="SKU Name";
                                    break;

                                case "displayname":
                                    $key="Display Name";
                                    break;

                                case "productoffering":
                                    $key="Product Offering";
                                    break;

                                case "official flipping machine":
                                    $key="Official Flipping Machine";
                                    break;

                                case "is it a prototype":
                                    $key="Is It A Prototype";
                                    break;

                                case "warranty status":
                                    $key="Warranty Status";
                                    break;

                                case "warr startdate":
                                    $key="Warr Start Date";
                                    break;

                                case "warrstartdate":
                                    $key="Warranty Start Date";
                                    break;

                                case "warrenddate":
                                    $key="Warranty End Date";
                                    break;

                                case "productiondate":
                                    $key="Production Date";
                                    break;

                                case "onlinetime":
                                    $key="Online Time";
                                    break;

                                case "7-day refund":
                                    $key="7-Day Refund";
                                    break;

                                case "change in 15 days":
                                    $key="Change In 15 Days";
                                    break;
                            }
                            $value = trim($value);
                            $new_data[$key]=$value;
                        }
                    }

                    $newHtmlResult = "";
                    foreach ($new_data as $key => $val) {
                        if ($val == "") {
                            continue;
                        }
                        $newHtmlResult .= $key . " : " . $val . "<br />";
                    }
                    $finalResult = $newHtmlResult;

                    if($finalResult=="") {
                        return self::failedResult(self::DEFAULT_MESSAGE,$result);
                    }
                    return self::successResult($finalResult,$result);
                    break;

                case "mdm":
                    $new_data = array('Model'=>'',
                        'IMEI/SN'=>'',
                        'MDM Status'=>'',);

                    $new_csv_result = self::getCsvReport($finalResult, false, false, true);
                    if (is_array($new_csv_result) && count($new_csv_result) > 0) {
                        foreach ($new_csv_result as $key => $value) {
                            if(trim(strtolower($key))=="imei/sn") {
                                $key = "IMEI/SN";
                            }
                            if(trim(strtolower($key))=="model") {
                                $key = "Model";
                            }
                            if(trim(strtolower($key))=="mdm status") {
                                $key="MDM Status";
                                if(trim(strtolower(strip_tags($value)))=="on") {
                                    $value='<span style="color:red;">ON</span>';
                                } else {
                                    $value='<span style="color:green;">OFF</span>';
                                }
                            }

                            if (array_key_exists($key, $new_data) && $value != "") {
                                if($new_data[$key]=="") {
                                    $new_data[$key] = $value;
                                }
                            }
                        }
                    }

                    $newHtmlResult = "";
                    foreach ($new_data as $key => $val) {
                        if ($val == "") {
                            continue;
                        }
                        $newHtmlResult .= $key . " : " . $val . "<br />";
                    }
                    $finalResult = $newHtmlResult;

                    if($finalResult=="") {
                        return self::failedResult(self::DEFAULT_MESSAGE,$result);
                    }
                    return self::successResult($finalResult,$result);
                    break;
            }

            return self::successResult($finalResult, $result);
        } catch (\Exception $e) {

        }
        return self::defaultResult();
    }
}
