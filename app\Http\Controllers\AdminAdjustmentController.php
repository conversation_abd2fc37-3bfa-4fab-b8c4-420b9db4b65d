<?php

namespace App\Http\Controllers;

use App\Base\Model\BaseModel;
use App\Components\Helper;
use App\Constants\CommonConstants;
use App\Constants\UserConstants;
use App\Models\AdminAdjustment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Yajra\DataTables\Facades\DataTables;

class AdminAdjustmentController extends Controller
{
    public function index()
    {
        return view('admin-adjustment.index');
    }

    public function show(AdminAdjustment $adminAdjustment,Request $request)
    {
        if($request->input('type')) {
            switch (trim(strtolower($request->input('type')))) {
                case "re-api":
                    if($adminAdjustment->generateNewApiKey()) {
                        return redirect()->route('admin-adjustment.show',$adminAdjustment->id)->with('success', 'New API key generated successfully.');
                    }
                    break;
            }
            return redirect()->route('admin-adjustment.show',$adminAdjustment->id)->with('error', 'Invalid request.');
        }
        return view('admin-adjustment.show', compact('adminAdjustment'));
    }

    public function create()
    {
        $adminAdjustment=new AdminAdjustment();
        return view('admin-adjustment.create', compact('adminAdjustment'));
    }

    public function edit(AdminAdjustment $adminAdjustment)
    {
        return view('admin-adjustment.edit', compact('adminAdjustment'));
    }

    public function store(Request $request)
    {
        $adminAdjustment = new AdminAdjustment();
        return $this->save($request, $adminAdjustment);
    }

    public function update(Request $request, AdminAdjustment $adminAdjustment)
    {
        return $this->save($request, $adminAdjustment);
    }

    private function save(Request $request, AdminAdjustment $adminAdjustment)
    {
        $isNewRecord = true;
        if ($adminAdjustment->id != null) {
            $isNewRecord = false;
        }

        $rules = [
            'user_id' => ['required', 'integer'],
            'amount' => ['required', 'numeric',
                function ($attribute, $value, $fail) {
                    if (!empty($value) && $value<=0) {
                        $fail('Invalid amount entered');
                    }
                },
            ],
            'is_debit' => ['required', 'integer'],
            'is_paid' => ['required', 'integer'],
            'is_invoice' => ['required', 'integer'],
            'comments' => ['string'],
        ];

        if ($isNewRecord) {

        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            if ($isNewRecord) {
                return redirect()->route('admin-adjustment.create')->withErrors($validator)->withInput();
            } else {
                return redirect()->route('admin-adjustment.edit', $adminAdjustment->id)->withErrors($validator)->withInput();
            }
        }

        $ip = request()->ip();
        $ipDetails = Helper::fetchIpDetails($ip, true);

        $adminAdjustment->user_id = $request->input('user_id');
        $adminAdjustment->amount = $request->input('amount');
        $adminAdjustment->is_debit = (int)$request->input('is_debit');
        $adminAdjustment->is_paid = (int)$request->input('is_paid');
        $adminAdjustment->is_invoice = (int)$request->input('is_invoice');
        $adminAdjustment->comments = trim($request->input('comments'));
        if ($isNewRecord) {
            $adminAdjustment->save();
        } else {
            $adminAdjustment->update();
        }

        updateUserBalance($adminAdjustment->user_id,$adminAdjustment->user->balance);
        return redirect()->route('admin-adjustment.index')->with('success', 'Admin adjustment saved successfully.');
    }

    public function dataTable(Request $request)
    {
        try {
            if ($request->ajax()) {
                $query = AdminAdjustment::query();
                BaseModel::buildFilterQuery($query, [
                    'q' => ['ref_no', 'amount','comments'],
                    'user_id',
                    'is_paid',
                    'is_invoice',
                ]);
                return Datatables::eloquent($query)
                    ->addColumn('checkboxes', function ($row) {
                        return '<input type="checkbox" name="pdr_checkbox[]" class="pdr_checkbox" value="' . $row->id . '" />';
                    })
                    ->addColumn('user', function ($row) {
                        return $row->user->displayName;
                    })
                    ->addColumn('debit_amount', function ($row) {
                        if($row->is_debit) {
                            return Helper::printAmount($row->amount);
                        }
                        return '';
                    })
                    ->addColumn('credit_amount', function ($row) {
                        if(!$row->is_debit) {
                            return Helper::printAmount($row->amount);
                        }
                        return '';
                    })
                    ->addColumn('created_at', function ($row) {
                        return Helper::displayTime($row->created_at);
                    })
                    ->addColumn('updated_at', function ($row) {
                        return Helper::displayTime($row->updated_at);
                    })
                    ->addColumn('is_paid', function ($row) {
                        return Helper::printYesNoBadge(!($row->is_paid == null));
                    })
                    ->addColumn('is_invoice', function ($row) {
                        return Helper::printYesNoBadge(!($row->is_invoice == null));
                    })
                    ->rawColumns(['checkboxes', 'AdminAdjustmentname', 'is_api_enabled', 'actions', 'is_invoice', 'is_paid'])
                    ->make(true);
            }
        } catch (\Exception $e) {
            //print_r(['message'=>$e->getMessage(),'trace'=>$e->getTraceAsString()]);
            die();
        }
    }
}
