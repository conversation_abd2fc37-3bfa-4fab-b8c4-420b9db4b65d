<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('users', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->string('name');
            $table->string('username')->unique();
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password');


            $table->foreignId('country_id')
                ->default(\App\Constants\CommonConstants::DEFAULT_COUNTRY)
                ->references('id')->on('countries')->onDelete('cascade');
            $table->foreignId('plan_id')
                ->nullable()
                ->references('id')->on('plans')->onDelete('cascade');

            $table->integer('is_active')->default(\App\Constants\CommonConstants::YES)->index();
            $table->string('api_key')->nullable()->unique();

            $table->integer('is_api_enabled')->default(\App\Constants\CommonConstants::YES)->index();
            $table->integer('is_api_protection')->default(\App\Constants\CommonConstants::NO)->index();
            $table->foreignId('user_role_id')
                ->default(\App\Constants\CommonConstants::DEFAULT_USER_ROLE)
                ->references('id')->on('user_roles')
                ->onDelete('cascade');

            $table->text('user_agent')->nullable();
            $table->string('created_ip')->nullable();
            $table->rememberToken();
            $table->timestamps();
        });

        $this->schema->create('password_reset_tokens', function (\App\Base\Database\BlueprintBase $table) {
            $table->string('email')->primary();
            $table->string('token');
            $table->timestamp('created_at')->nullable();
        });

        $this->schema->create('sessions', function (\App\Base\Database\BlueprintBase $table) {
            $table->string('id')->primary();
            $table->foreignId('user_id')->nullable()->index();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->longText('payload');
            $table->integer('last_activity')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('sessions');
    }
};
