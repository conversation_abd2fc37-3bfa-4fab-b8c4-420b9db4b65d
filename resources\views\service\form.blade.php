<div class="row mb-4">
    <div class="col-lg-12 col-md-12 mb-4">
        <div class="card">
            <div class="card-body">

<div class="rounded-16 form-box bg-white -dark-bg-dark-1 shadow-4 h-100">
    <form action="{{ $service->id ==null ? route('service.store') : route('service.update', $service) }}" method="POST"
          class="normal-form">
        @csrf

        @if( $service->id != null )
            @method('PUT')
        @endif

        <div class="row">
            <div class="col-md-3 col-12">
                <div class="form-group required-field text-left @error('operating_system_id') is-invalid @enderror">
                    <label for="operating_system_id">{{ __('Operating System') }}</label>
                    <select required name="operating_system_id" id="operating_system_id" class="form-control filterField">
                        <option value="">Select</option>
                        <?php
                        foreach (\App\Models\OperatingSystem::query()->orderBy("name")->get() as $provider) {
                            ?>
                            <option <?=($provider->id==$service->operating_system_id ? 'selected=""' : '')?> value="<?=$provider->id?>"><?=$provider->displayName?></option>
                            <?php
                        }
                        ?>
                    </select>
                    @error('operating_system_id')
                    <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                    @enderror
                </div>
            </div>
            <div class="col-md-3 col-12">
                <div class="form-group required-field text-left @error('category_id') is-invalid @enderror">
                    <label for="category_id">{{ __('Category') }}</label>
                    <select required name="category_id" id="category_id" class="form-control filterField">
                        <option value="">Select</option>
                        <?php
                        foreach (\App\Models\Category::query()->orderBy("name")->get() as $provider) {
                            ?>
                            <option <?=($provider->id==$service->category_id ? 'selected=""' : '')?> value="<?=$provider->id?>"><?=$provider->displayName?></option>
                            <?php
                        }
                        ?>
                    </select>
                    @error('category_id')
                    <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                    @enderror
                </div>
            </div>

            <div class="col-md-3 col-12">
                <div class="form-group required-field text-left @error('provider_id') is-invalid @enderror">
                    <label for="provider_id">{{ __('Provider') }}</label>
                    <select required name="provider_id" id="provider_id" class="form-control filterField fetch-provider-services">
                        <option value="">Select</option>
                        <?php
                        foreach (\App\Models\Provider::query()->orderBy("name")->get() as $provider) {
                            ?>
                            <option <?=($provider->id==$service->provider_id ? 'selected=""' : '')?> value="<?=$provider->id?>"><?=$provider->displayName?></option>
                            <?php
                        }
                        ?>
                    </select>
                    @error('provider_id')
                    <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                    @enderror
                </div>
            </div>

            <div class="col-md-3 col-12">
                <div class="form-group required-field text-left @error('provider_service_id') is-invalid @enderror">
                    <label for="provider_id">{{ __('Provider Service') }}</label>
                    <select required name="provider_service_id" id="provider_service_id" class="form-control filterField set-provider-services">
                        <option value="">Select</option>
                        <?php
                        if($service->provider_id) {
                            foreach (\App\Models\ProviderService::query()->where('provider_id','=',$service->provider_id)->orderBy("name")->get() as $provider) {
                                ?>
                                <option <?=($provider->id==$service->provider_service_id ? 'selected=""' : '')?> value="<?=$provider->id?>"><?=$provider->displayName?></option>
                                <?php
                            }
                        }
                        ?>
                    </select>
                    @error('provider_service_id')
                    <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                    @enderror
                </div>
            </div>

            <div class="col-md-4 col-12">
                <div class="form-group required-field text-left @error('name') is-invalid @enderror">
                    <label for="name">{{ __('Name') }}</label>
                    <input required id="name" type="text" class="form-control @error('name') is-invalid @enderror"
                           name="name" placeholder="{{ __('Enter name') }}"
                           value="{{old('name', $service->name)}}">
                    @error('name')
                    <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                    @enderror
                </div>
            </div>

            <div class="col-md-2 col-12">
                <div class="form-group required-field text-left @error('price') is-invalid @enderror">
                    <label for="price">{{ __('Price (in $)') }}</label>
                    <input required id="price" type="text" class="form-control @error('price') is-invalid @enderror"
                           name="price" placeholder="{{ __('Enter Price') }}"
                           value="{{old('price', $service->price)}}">
                    @error('price')
                    <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                    @enderror
                </div>
            </div>

            <div class="col-md-2 col-12">
                <div class="form-group required-field text-left @error('display_order') is-invalid @enderror">
                    <label for="display_order">{{ __('Display Order') }}</label>
                    <input required id="display_order" type="text" class="form-control @error('display_order') is-invalid @enderror"
                           name="display_order" placeholder="{{ __('Enter display order') }}"
                           value="{{old('display_order', $service->display_order)}}">
                    @error('display_order')
                    <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                    @enderror
                </div>
            </div>

            <div class="col-md-2 col-12">
                <div class="form-group required-field text-left @error('custom_id') is-invalid @enderror">
                    <label for="custom_id">{{ __('Custom ID') }}</label>
                    <input required id="custom_id" type="number" min="1" class="form-control @error('custom_id') is-invalid @enderror"
                           name="custom_id" placeholder="{{ __('Enter Custom ID') }}"
                           value="{{old('custom_id', $service->custom_id)}}">
                    @error('custom_id')
                    <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                    @enderror
                </div>
            </div>

            <div class="col-md-2 col-12">
                <div class="form-group required-field text-left @error('is_active') is-invalid @enderror">
                    <label for="is_active">{{ __('Active') }}</label>
                    <select name="is_active" id="is_active" class="form-control @error('is_active') is-invalid @enderror">
                        <option <?=(!$service->is_active ? 'selected=""' : '')?> value="<?=\App\Constants\CommonConstants::NO?>">No</option>
                        <option <?=($service->is_active ? 'selected=""' : '')?> value="<?=\App\Constants\CommonConstants::YES?>">Yes</option>
                    </select>
                    @error('is_active')
                    <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                    @enderror
                </div>
            </div>

            <div class="col-md-6 col-12">
                <div class="form-group required-field text-left @error('order_format') is-invalid @enderror">
                    <label for="order_format">{{ __('Order Format') }}</label>
                    <textarea required id="order_format" rows="6" type="text" class="form-control @error('order_format') is-invalid @enderror"
                           name="order_format" placeholder="{{ __('Enter order format') }}">{{old('order_format', $service->order_format)}}</textarea>
                    @error('order_format')
                    <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                    @enderror
                </div>
            </div>

            <div class="col-md-6 col-12">
                <div class="form-group required-field text-left @error('download_format') is-invalid @enderror">
                    <label for="download_format">{{ __('Download Format') }}</label>
                    <textarea required id="download_format" rows="6" type="text" class="form-control @error('download_format') is-invalid @enderror"
                           name="download_format" placeholder="{{ __('Enter download format') }}">{{old('download_format', $service->download_format)}}</textarea>
                    @error('download_format')
                    <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                    @enderror
                </div>
            </div>

        </div>

        @if( $service->id == null )

        @endif

        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-12 formBtn">
                @if( $service->id == null )
                    <button type="submit" class="btn btn-primary">{{ __('Save') }}</button>
                @else
                    <button type="submit" class="btn btn-primary">{{ __('Update') }}</button>
                @endif
            </div>
        </div>

    </form>
</div>
            </div>
        </div>
    </div>
</div>

@section('pageJs')

    <script>

    </script>

@endsection
