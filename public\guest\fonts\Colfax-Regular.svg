<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20170731 at Sat Sep 26 15:10:29 2020
 By Aleksey,,,
Copyright (c) 2012 by Process Type Foundry, LLC. All rights reserved.
</metadata>
<defs>
<font id="Colfax-Regular" horiz-adv-x="1165" >
  <font-face 
    font-family="Colfax"
    font-weight="400"
    font-stretch="normal"
    units-per-em="2048"
    panose-1="2 11 3 4 0 0 0 1 0 2"
    ascent="1536"
    descent="-512"
    x-height="1049"
    cap-height="1434"
    bbox="-106 -512 2406 1972"
    underline-thickness="73"
    underline-position="-277"
    unicode-range="U+0020-FB02"
  />
<missing-glyph horiz-adv-x="1462" 
d="M213 0v1434h1036v-1434h-1036zM240 25h983v1384h-983v-1384zM541 475l-52 51l191 191l-191 192l50 52l192 -193l195 193l49 -52l-193 -192l193 -191l-51 -51l-193 191z" />
    <glyph glyph-name=".notdef" horiz-adv-x="1462" 
d="M213 0v1434h1036v-1434h-1036zM240 25h983v1384h-983v-1384zM541 475l-52 51l191 191l-191 192l50 52l192 -193l195 193l49 -52l-193 -192l193 -191l-51 -51l-193 191z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="682" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="491" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="536" 
d="M227 377l-41 514v543h164v-543l-41 -514h-82zM164 88q0 44 29 72t75 28t75.5 -28t29.5 -72t-30 -72t-75 -28t-74.5 28t-29.5 72z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="913" 
d="M604 893v541h133v-541h-133zM176 893v541h133v-541h-133z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="1390" 
d="M162 911v111h241l48 412h122l-47 -412h422l47 412h123l-47 -412h211v-111h-223l-41 -366h221v-111h-233l-50 -434h-122l49 434h-422l-49 -434h-123l49 434h-219v111h231l41 366h-229zM514 911l-41 -366h422l41 366h-422z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="1218" 
d="M545 -225v229q-184 19 -293 122.5t-109 270.5v37h144v-37q0 -131 85.5 -204.5t248.5 -73.5q149 0 234 71.5t85 190.5q0 160 -180 227l-318 121q-139 52 -211.5 129.5t-72.5 208.5q0 155 103.5 250t273.5 113v229h153v-232q167 -21 266 -117.5t99 -252.5v-53h-144v49
q0 120 -80.5 191t-224.5 71q-137 0 -218 -64t-81 -178q0 -85 46 -133t138 -82l314 -121q143 -56 213.5 -138t70.5 -210q0 -157 -109 -260.5t-280 -124.5v-229h-153z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="1738" 
d="M434 657q-141 0 -225 85.5t-84 222.5v178q0 137 84 222t225 85t225 -85t84 -222v-178q0 -137 -84 -222.5t-225 -85.5zM434 756q92 0 142.5 54.5t50.5 145.5v195q0 91 -50.5 146t-142.5 55t-142 -55t-50 -146v-195q0 -91 50 -145.5t142 -54.5zM1315 -16q-141 0 -225 85
t-84 222v178q0 137 84 222t225 85t225 -85t84 -222v-178q0 -137 -84 -222t-225 -85zM1315 82q92 0 142 55t50 146v194q0 91 -50 146t-142 55t-142.5 -55t-50.5 -146v-194q0 -91 50.5 -146t142.5 -55zM444 0l744 1434h119l-744 -1434h-119z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="1419" 
d="M1032 1059h-129v78q0 92 -68.5 153.5t-172.5 61.5q-111 0 -177.5 -52.5t-66.5 -146.5q0 -121 114 -238l566 -585q37 92 37 207v145h131v-150q0 -171 -74 -301l166 -170l-92 -90l-152 156q-159 -156 -455 -156q-143 0 -262.5 56t-191.5 161.5t-72 239.5q0 276 297 410
l-8 8q-139 147 -139 295q0 149 102 235t274 86q160 0 266.5 -89.5t106.5 -233.5v-80zM692 84q218 0 336 133l-520 539q-234 -101 -234 -328q0 -158 113.5 -251t304.5 -93z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="485" 
d="M176 893v541h133v-541h-133z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="632" 
d="M403 1602h152q-128 -269 -186 -490t-58 -448t58 -448t186 -490h-152q-128 245 -187.5 468.5t-59.5 469.5t59.5 469.5t187.5 468.5z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="632" 
d="M78 1602h151q128 -245 188 -469t60 -469t-60 -469t-188 -469h-151q128 269 186 490t58 448t-58 448t-186 490z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="870" 
d="M709 895l-234 133v-268h-78v268l-235 -133l-39 68l233 133l-233 133l39 67l235 -135v273h78v-271l234 133l39 -67l-234 -133l234 -133z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="1241" 
d="M551 258v418h-387v123h387v418h139v-418h387v-123h-387v-418h-139z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="487" 
d="M72 -233l98 458h174l-141 -458h-131z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="845" 
d="M137 571v138h572v-138h-572z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="487" 
d="M139 88q0 44 29.5 72t75.5 28t75 -28t29 -72t-29.5 -72t-74.5 -28t-75 28t-30 72z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="866" 
d="M72 -215l577 1751h146l-578 -1751h-145z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="1290" 
d="M645 -29q-231 0 -366.5 134.5t-135.5 343.5v536q0 209 135.5 343t366.5 134t366.5 -134t135.5 -343v-536q0 -209 -135.5 -343.5t-366.5 -134.5zM645 100q164 0 254 93t90 249v549q0 156 -90 249t-254 93t-254 -93t-90 -249v-549q0 -156 90 -249t254 -93z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="716" 
d="M362 0v1272l-299 -142v144l326 160h131v-1434h-158z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="1222" 
d="M143 0v123l607 637q80 83 119.5 156.5t39.5 152.5q0 118 -82.5 191t-210.5 73q-138 0 -219.5 -76.5t-81.5 -212.5v-32h-149v39q0 193 123.5 302t326.5 109q198 0 324.5 -107.5t126.5 -285.5q0 -62 -19 -121t-57.5 -114.5t-72.5 -95t-87 -93.5l-505 -516h743v-129h-926z
" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="1245" 
d="M225 1307v127h836v-129l-459 -502h55q200 0 325.5 -111t125.5 -293q0 -122 -67.5 -221t-181.5 -153t-249 -54q-126 0 -232 47t-176 144.5t-75 228.5h145q10 -135 103 -214t237 -79q153 0 247.5 84t94.5 213q0 131 -90 211t-241 80h-195v123l442 498h-645z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="1253" 
d="M805 0v342h-680v117l477 975h143l-466 -973h526v340h145v-340h199v-119h-199v-342h-145z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="1277" 
d="M635 -29q-183 0 -317.5 102t-167.5 279h149q32 -115 118.5 -183.5t217.5 -68.5q163 0 262.5 99t99.5 254q0 147 -96 247.5t-246 100.5q-201 0 -325 -168h-150l135 801h715v-129h-590l-90 -514q51 62 142 100.5t194 38.5q201 0 333 -136t132 -337q0 -207 -146.5 -346.5
t-369.5 -139.5z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="1228" 
d="M629 1434h168l-416 -633v-2q106 127 289 127q194 0 322 -133t128 -338q0 -96 -37.5 -184.5t-102.5 -154.5t-159 -105.5t-200 -39.5q-219 0 -359.5 135.5t-140.5 342.5q0 110 40 209.5t111 211.5zM274 446q0 -153 94 -251.5t250 -98.5q155 0 252 101t97 254
q0 154 -92.5 254t-247.5 100q-152 0 -252.5 -102.5t-100.5 -256.5z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="1144" 
d="M242 0l628 1305h-737v129h899v-111l-633 -1323h-157z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="1290" 
d="M645 -29q-219 0 -358.5 114t-139.5 296q0 79 25.5 144.5t68 109t92 71t103.5 39.5q-107 48 -168 133t-61 201q0 176 121.5 279.5t316.5 103.5t316.5 -103.5t121.5 -279.5q0 -116 -61 -201t-168 -133q54 -12 103.5 -39.5t92 -71t68 -109t25.5 -144.5q0 -182 -139.5 -296
t-358.5 -114zM645 90q150 0 248 81t98 210q0 138 -95 217.5t-251 79.5t-251 -79.5t-95 -217.5q0 -129 98 -210t248 -81zM354 1069q0 -114 83 -193t208 -79t208 79t83 193q0 126 -79.5 200t-211.5 74t-211.5 -74t-79.5 -200z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="1228" 
d="M600 0h-170l404 618v3q-36 -47 -113.5 -80t-161.5 -33q-194 0 -322 134.5t-128 336.5q0 207 139 345t360 138q219 0 359.5 -134t140.5 -343q0 -183 -102 -346zM954 987q0 151 -97 250.5t-247 99.5q-156 0 -252 -101.5t-96 -254.5q0 -162 90 -257t250 -95q148 0 250 102
t102 256z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="487" 
d="M139 88q0 44 29.5 72t75.5 28t75 -28t29 -72t-29.5 -72t-74.5 -28t-75 28t-30 72zM139 766q0 44 29.5 72t75.5 28t75 -28t29 -72t-29.5 -72t-74.5 -28t-75 28t-30 72z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="487" 
d="M61 -233l99 458h174l-141 -458h-132zM160 766q0 44 29 72t75 28t75.5 -28t29.5 -72t-30 -72t-75 -28t-74.5 28t-29.5 72z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="1210" 
d="M1036 238l-913 450v98l913 451v-137l-743 -363l743 -362v-137z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="1257" 
d="M184 920v122h889v-122h-889zM184 432v123h889v-123h-889z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="1210" 
d="M1087 688l-913 -450v137l744 362l-744 363v137l913 -451v-98z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="1038" 
d="M399 358v97q0 133 31 209.5t109 138.5l102 80q71 56 104 106.5t33 122.5q0 104 -73.5 163.5t-198.5 59.5q-126 0 -197 -64t-71 -184v-38h-144v41q0 181 110.5 276.5t303.5 95.5q191 0 308.5 -97t117.5 -253q0 -179 -162 -303l-108 -84q-76 -59 -101.5 -112.5
t-25.5 -168.5v-86h-138zM365 88q0 44 29 72t75 28t75 -28t29 -72t-29.5 -72t-74.5 -28t-74.5 28t-29.5 72z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="1945" 
d="M954 -236q-182 0 -335.5 63.5t-255.5 171.5t-159 253.5t-57 308.5q0 185 62 349t174 286.5t280 194t369 71.5q180 0 329 -59.5t245 -159t148 -226.5t52 -265q0 -73 -13 -145.5t-43 -144t-73.5 -126t-110.5 -88t-149 -33.5q-98 0 -163 58.5t-78 138.5
q-41 -71 -116.5 -119.5t-174.5 -48.5q-134 0 -211.5 85.5t-77.5 219.5q0 37 6 78l10 59q27 161 117 242.5t225 81.5q92 0 156.5 -46t89.5 -108l25 137h116l-75 -424q-6 -38 -6 -67q0 -88 43.5 -142.5t128.5 -54.5q84 0 147 63.5t94 163t31 218.5q0 127 -46 239t-130 197
t-213 134.5t-285 49.5q-229 0 -405.5 -102t-273.5 -286t-97 -421q0 -146 47.5 -273.5t136 -223.5t225 -151.5t304.5 -55.5q196 0 430 63l-13 -90q-176 -66 -430 -66zM905 344q92 0 157 64.5t81 160.5l12 72q4 20 4 45q0 92 -58.5 157.5t-148.5 65.5q-179 0 -219 -233l-8 -47
q-8 -46 -8 -80q0 -99 50.5 -152t137.5 -53z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="1392" 
d="M776 1434l539 -1434h-158l-131 365h-661l-129 -365h-158l538 1434h160zM696 1288l-286 -796h571z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="1357" 
d="M186 0v1434h592q186 0 288.5 -98.5t102.5 -258.5q0 -120 -57.5 -206.5t-153.5 -129.5q130 -29 212.5 -117t82.5 -235q0 -180 -124 -284.5t-336 -104.5h-607zM340 125h440q154 0 236 71t82 203q0 122 -84.5 196.5t-231.5 74.5h-442v-545zM340 795h403q128 0 199.5 76
t71.5 186q0 112 -72 182t-199 70h-403v-514z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="1435" 
d="M739 -29q-145 0 -259.5 47.5t-187.5 132t-111 197.5t-38 248v242q0 181 65.5 320t202 221.5t328.5 82.5q121 0 224.5 -40t175 -109t114.5 -159.5t52 -192.5h-154q-10 76 -39.5 141t-79.5 116.5t-125.5 81t-169.5 29.5q-212 0 -323 -132.5t-111 -352.5v-254
q0 -220 111 -353t323 -133q94 0 169.5 29.5t125.5 81.5t79.5 117t39.5 141h154q-9 -102 -52 -192.5t-114.5 -159.5t-175 -109.5t-224.5 -40.5z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="1431" 
d="M186 1434h539q267 0 416 -152.5t149 -398.5v-332q0 -246 -149 -398.5t-416 -152.5h-539v1434zM702 131q208 0 319.5 114t111.5 306v332q0 192 -111.5 306t-319.5 114h-358v-1172h358z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="1247" 
d="M186 0v1434h947v-134h-787v-507h727v-134h-727v-526h789v-133h-949z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="1198" 
d="M186 0v1434h928v-134h-768v-528h709v-133h-709v-639h-160z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="1452" 
d="M1294 963h-151q-5 157 -115 261.5t-297 104.5q-144 0 -241.5 -63.5t-142 -171t-44.5 -254.5v-238q0 -87 15.5 -161t49.5 -137.5t84.5 -108.5t124 -70t164.5 -25q195 0 304.5 109t109.5 313v37h-401v133h555v-151q0 -166 -65 -294t-195.5 -202t-309.5 -74
q-146 0 -261 49.5t-187.5 137t-110 201.5t-37.5 247v228q0 185 66.5 325.5t200.5 221.5t319 81q256 0 408 -140.5t157 -358.5z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="1449" 
d="M186 0v1434h160v-643h758v643h160v-1434h-160v647h-758v-647h-160z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="552" 
d="M197 0v1434h159v-1434h-159z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="1206" 
d="M559 -29q-211 0 -339 115t-128 301v12h152v-12q0 -128 81 -205.5t232 -77.5q148 0 229.5 75.5t81.5 209.5v1045h160v-1045q0 -184 -129 -301t-340 -117z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="1253" 
d="M186 0v1434h160v-699l647 699h193l-666 -709l688 -725h-192l-670 713v-713h-160z" />
    <glyph glyph-name="L" unicode="L" 
d="M186 0v1434h160v-1301h735v-133h-895z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="1617" 
d="M1432 1434v-1434h-152v1126l-395 -780h-152l-395 780v-1126h-152v1434h150l473 -947l473 947h150z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="1466" 
d="M186 0v1434h150l788 -1178v1178h156v-1434h-150l-788 1178v-1178h-156z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="1490" 
d="M1348 602q0 -131 -38.5 -244.5t-112 -200.5t-190 -136.5t-262.5 -49.5t-262.5 49.5t-189.5 136.5t-111.5 200.5t-38.5 244.5v229q0 131 38.5 244.5t111.5 200.5t189.5 136.5t262.5 49.5t262.5 -49.5t190 -136.5t112 -200.5t38.5 -244.5v-229zM745 104q89 0 163 25
t125 69.5t86.5 106.5t52 134.5t16.5 154.5v246q0 103 -27 190.5t-79.5 155t-139 105.5t-197.5 38t-197 -38t-139 -105.5t-79.5 -155t-26.5 -190.5v-246q0 -103 26.5 -190.5t79.5 -155.5t139 -106t197 -38z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="1308" 
d="M186 0v1434h562q221 0 354 -128.5t133 -326.5t-133 -328.5t-354 -130.5h-402v-520h-160zM346 653h387q168 0 255 93t87 233t-87 230.5t-255 90.5h-387v-647z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="1490" 
d="M1348 602q0 -170 -64 -309.5t-186 -222.5l102 -189l-123 -63l-102 190q-109 -37 -230 -37q-146 0 -262.5 49.5t-189.5 136.5t-111.5 200.5t-38.5 244.5v229q0 131 38.5 244.5t111.5 200.5t189.5 136.5t262.5 49.5t262.5 -49.5t190 -136.5t112 -200.5t38.5 -244.5v-229z
M811 313l121 66l98 -182q78 65 118 168t40 229v246q0 103 -27 190.5t-79.5 155t-139 105.5t-197.5 38t-197 -38t-139 -105.5t-79.5 -155t-26.5 -190.5v-246q0 -103 26.5 -190.5t79.5 -155.5t139 -106t197 -38q103 0 164 25z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="1384" 
d="M186 0v1434h592q153 0 264 -62t164.5 -164t53.5 -231q0 -151 -82.5 -267t-227.5 -161l312 -549h-183l-291 524h-442v-524h-160zM346 651h412q166 0 254 90t88 236q0 145 -90 236.5t-252 91.5h-412v-654z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="1333" 
d="M676 -29q-233 0 -384 116.5t-151 309.5v37h152v-33q0 -137 103 -220t280 -83q178 0 278.5 74.5t100.5 210.5q0 88 -52.5 147.5t-152.5 85.5l-406 107q-150 38 -217 123.5t-67 214.5q0 188 135 294.5t360 106.5q222 0 362 -113.5t140 -295.5v-35h-149v31
q0 129 -94.5 207.5t-258.5 78.5q-155 0 -248.5 -68.5t-93.5 -193.5q0 -89 43 -141.5t136 -77.5l415 -111q138 -37 219.5 -126t81.5 -222q0 -193 -149.5 -308.5t-382.5 -115.5z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="1150" 
d="M496 0v1300h-439v134h1037v-134h-439v-1300h-159z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="1449" 
d="M725 -29q-251 0 -401 143t-150 369v951h160v-965q0 -168 105 -266.5t286 -98.5t286 98.5t105 266.5v965h160v-951q0 -226 -150 -369t-401 -143z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="1277" 
d="M551 0l-498 1434h156l430 -1280l430 1280h156l-498 -1434h-176z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="1888" 
d="M1817 1434l-340 -1434h-154l-379 1260l-379 -1260h-153l-340 1434h153l271 -1221l368 1221h160l369 -1221l270 1221h154z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="1286" 
d="M725 729l496 -729h-168l-410 606l-410 -606h-167l495 729l-477 705h164l395 -582l395 582h164z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="1236" 
d="M539 0v543l-510 891h168l421 -746l422 746h168l-510 -891v-543h-159z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="1275" 
d="M135 0v133l817 1167h-780v134h950v-134l-817 -1167h840v-133h-1010z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="743" 
d="M176 -274v1876h486v-127h-330v-1622h330v-127h-486z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="866" 
d="M217 1536l578 -1751h-146l-577 1751h145z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="743" 
d="M565 1602v-1876h-485v127h330v1622h-330v127h485z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="1019" 
d="M578 1434l329 -570h-153l-244 439l-244 -439h-153l329 570h136z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="1017" 
d="M-2 -242v74h1022v-74h-1022z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="727" 
d="M387 1192l-201 291h191l164 -291h-154z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="1179" 
d="M1026 0h-149v131q-37 -61 -137.5 -108.5t-223.5 -47.5q-183 0 -290 92.5t-107 237.5q0 151 113.5 239.5t302.5 88.5q113 0 197.5 -38t137.5 -93v194q0 111 -70 182.5t-192 71.5q-192 0 -262 -157h-143q40 132 146.5 206t260.5 74q190 0 303 -108.5t113 -268.5v-696z
M549 76q127 0 224 66.5t97 164.5q0 95 -95 159t-218 64q-133 0 -212 -60t-79 -167q0 -106 79 -166.5t204 -60.5z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="1220" 
d="M166 1495h156v-602q49 82 140 131t210 49q196 0 315 -131.5t119 -337.5v-160q0 -132 -51 -237.5t-152 -168.5t-235 -63q-124 0 -221.5 56t-133.5 133v-164h-147v1495zM950 451v147q0 151 -81.5 249.5t-225.5 98.5q-135 0 -228 -95.5t-93 -252.5v-135q0 -158 84.5 -259.5
t234.5 -101.5q105 0 176 48t102 124.5t31 176.5z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="1146" 
d="M580 -25q-214 0 -339.5 129.5t-125.5 331.5v176q0 202 125.5 331.5t339.5 129.5q194 0 316.5 -111.5t131.5 -281.5h-139q-17 117 -92.5 193.5t-214.5 76.5q-156 0 -236 -91t-80 -245v-180q0 -154 80 -245t236 -91q139 0 214.5 76.5t92.5 194.5h139q-9 -170 -131.5 -282
t-316.5 -112z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="1220" 
d="M899 1495h156v-1495h-148v164q-36 -77 -133 -133t-221 -56q-134 0 -235 63t-152 168.5t-51 237.5v160q0 206 119 337.5t315 131.5q119 0 210 -49t140 -131v602zM270 598v-147q0 -74 18.5 -136t55 -110.5t97 -75.5t139.5 -27q150 0 234.5 101.5t84.5 259.5v135
q0 157 -93 252.5t-228 95.5q-144 0 -226 -98.5t-82 -249.5z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="1134" 
d="M578 -25q-207 0 -335 123.5t-128 333.5v168q0 218 119 345.5t337 127.5q145 0 247 -61t150 -162.5t48 -233.5v-155h-752v-37q0 -146 79 -238t235 -92q114 0 187.5 55t100.5 148h140q-26 -142 -145 -232t-283 -90zM264 612v-32h606v49q0 147 -75.5 236t-223.5 89
q-82 0 -142.5 -26t-96 -73t-52 -107.5t-16.5 -135.5z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="720" 
d="M240 0v922h-154v127h154v139q0 155 73 231t230 76h147v-127h-135q-83 0 -121.5 -40t-38.5 -128v-151h258v-127h-258v-922h-155z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="1216" 
d="M588 -381q-162 0 -287.5 71t-167.5 197h154q83 -145 301 -145q137 0 222 71.5t85 190.5v238q-26 -77 -126 -138t-226 -61q-197 0 -312.5 123t-115.5 305v174q0 185 114.5 306.5t315.5 121.5q93 0 169.5 -31.5t119.5 -74.5t67 -93v175h150v-1049q0 -163 -132 -272
t-331 -109zM573 170q138 0 230 86t92 231v136q0 135 -90.5 229t-224.5 94q-153 0 -232.5 -80.5t-79.5 -232.5v-150q0 -149 78 -231t227 -82z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="1204" 
d="M166 0v1495h156v-608q101 186 342 186q177 0 281 -111t104 -300v-662h-156v639q0 140 -66 219.5t-202 79.5q-135 0 -219 -82.5t-84 -239.5v-616h-156z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="507" 
d="M176 0v1049h156v-1049h-156zM156 1325q0 42 27 68t71 26t71 -26t27 -68t-27 -68t-71 -26t-71 26t-27 68z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="507" 
d="M332 1049v-1164q0 -116 -64.5 -178.5t-185.5 -62.5h-100v125h55q73 0 106 29t33 98v1153h156zM156 1325q0 42 27 68t71 26t71 -26t27 -68t-27 -68t-71 -26t-71 26t-27 68z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="1050" 
d="M166 0v1495h156v-940l452 494h195l-480 -506l500 -543h-194l-473 528v-528h-156z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="507" 
d="M176 0v1495h156v-1495h-156z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="1906" 
d="M166 0v1049h147v-179q38 93 126 148t220 55q116 0 202.5 -54t131.5 -151q56 96 152.5 150.5t228.5 54.5q171 0 274 -112.5t103 -298.5v-662h-156v639q0 137 -67 218t-193 81q-135 0 -217 -82.5t-82 -239.5v-616h-155v639q0 137 -67 218t-193 81q-135 0 -217 -82.5
t-82 -239.5v-616h-156z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="1204" 
d="M166 0v1049h147v-179q38 93 128.5 148t222.5 55q177 0 281 -111t104 -300v-662h-156v639q0 140 -66 219.5t-202 79.5q-135 0 -219 -82.5t-84 -239.5v-616h-156z" />
    <glyph glyph-name="o" unicode="o" 
d="M584 -25q-145 0 -253 62.5t-162 166.5t-54 234v172q0 130 54.5 234t162.5 166.5t252 62.5q216 0 341.5 -131.5t125.5 -331.5v-172q0 -200 -125.5 -331.5t-341.5 -131.5zM901 608q0 154 -83 247t-234 93q-152 0 -235 -93.5t-83 -246.5v-168q0 -153 83 -246.5t235 -93.5
q151 0 234 93t83 247v168z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="1220" 
d="M322 -356h-156v1405h147v-164q36 77 133.5 132.5t221.5 55.5q205 0 321.5 -131.5t116.5 -337.5v-160q0 -206 -119 -337.5t-315 -131.5q-119 0 -210 49t-140 132v-512zM950 451v147q0 100 -31 176.5t-102 124t-176 47.5q-151 0 -235 -101t-84 -259v-135q0 -157 93 -253
t228 -96q144 0 225.5 99t81.5 250z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="1220" 
d="M1055 -356h-156v512q-49 -83 -140 -132t-210 -49q-196 0 -315 131.5t-119 337.5v160q0 204 118.5 336.5t319.5 132.5q124 0 221 -55.5t133 -132.5v164h148v-1405zM270 598v-147q0 -151 82 -250t226 -99q135 0 228 96t93 253v135q0 158 -84 259t-235 101
q-155 0 -232.5 -97t-77.5 -251z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="722" 
d="M166 0v1049h145v-242q60 248 342 248h25v-146h-31q-153 0 -239 -100.5t-86 -294.5v-514h-156z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="1077" 
d="M551 -25q-193 0 -313.5 89.5t-120.5 240.5v10h141v-6q0 -97 82.5 -157t212.5 -60q127 0 202.5 50.5t75.5 148.5q0 56 -34 94t-97 51l-311 72q-118 26 -182 95t-64 171q0 144 109.5 221.5t288.5 77.5q166 0 277.5 -92t111.5 -236v-10h-142v6q0 98 -66.5 156.5t-182.5 58.5
q-113 0 -182.5 -46.5t-69.5 -131.5q0 -109 110 -135l322 -76q127 -31 192.5 -95.5t65.5 -174.5q0 -139 -120.5 -230.5t-305.5 -91.5z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="763" 
d="M668 0h-131q-285 0 -285 279v643h-178v127h178v301h156v-301h268v-127h-268v-643q0 -81 34.5 -116.5t120.5 -35.5h105v-127z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="1193" 
d="M1028 1049v-1049h-147v178q-38 -93 -128.5 -148t-222.5 -55q-172 0 -273 111.5t-101 300.5v662h155v-639q0 -140 64 -222t194 -82q135 0 219 84.5t84 241.5v617h156z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="1040" 
d="M987 1049l-385 -1049h-164l-385 1049h154l313 -893l314 893h153z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="1556" 
d="M1491 1049l-287 -1049h-147l-279 885l-278 -885h-148l-286 1049h147l221 -863l271 863h147l270 -863l221 863h148z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="1040" 
d="M600 532l369 -532h-164l-285 418l-284 -418h-164l368 532l-354 517h162l272 -400l273 400h161z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="1040" 
d="M463 -356h-152l133 356l-391 1049h154l319 -891l308 891h153l-387 -1049z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="1021" 
d="M117 0v117l618 809h-594v123h766v-117l-618 -809h626v-123h-798z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="870" 
d="M66 727h57q98 0 149.5 54t51.5 153v360q0 144 81.5 226t239.5 82h139v-127h-114q-102 0 -146.5 -47t-44.5 -140v-366q0 -224 -198 -258q198 -34 198 -258v-367q0 -93 44.5 -139.5t146.5 -46.5h114v-127h-139q-158 0 -239.5 81.5t-81.5 225.5v360q0 99 -51.5 153
t-149.5 54h-57v127z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="622" 
d="M246 -307v1945h131v-1945h-131z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="870" 
d="M748 727h57v-127h-57q-98 0 -149.5 -54t-51.5 -153v-360q0 -144 -82 -225.5t-240 -81.5h-139v127h115q102 0 146 46.5t44 139.5v367q0 224 199 258q-199 34 -199 258v366q0 93 -44 140t-146 47h-115v127h139q158 0 240 -82t82 -226v-360q0 -99 51.5 -153t149.5 -54z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="1220" 
d="M133 600q10 307 254 307q55 0 127 -31.5t129 -68.5t116.5 -68.5t92.5 -31.5q22 0 39 6.5t28.5 23t18.5 28.5t12.5 40t7 39t5.5 43l135 -19q-2 -60 -16.5 -111.5t-42 -93.5t-74 -66t-107.5 -24q-49 0 -117.5 31.5t-125 69t-119 69t-99.5 31.5q-29 0 -50.5 -9.5t-34.5 -24
t-22 -40.5t-13.5 -49.5t-8.5 -60.5z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="491" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="536" 
d="M309 1057l41 -514v-543h-164v543l41 514h82zM373 1346q0 -44 -29.5 -72.5t-75.5 -28.5t-75 28.5t-29 72.5t29.5 72t74.5 28t75 -28t30 -72z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="1169" 
d="M514 0v209q-169 24 -269 138t-100 294v152q0 178 101.5 292t269.5 138v211h154v-211q155 -23 246.5 -115t107.5 -225h-143q-16 97 -90.5 158t-196.5 61q-141 0 -219 -81.5t-78 -229.5v-148q0 -148 78 -229.5t219 -81.5q122 0 196.5 61t90.5 158h143q-15 -132 -111 -226.5
t-245 -115.5v-209h-154z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="1353" 
d="M150 0v127h247v496h-219v127h219v292q0 205 109.5 312.5t308.5 107.5q194 0 304 -106.5t110 -280.5v-33h-146v31q0 119 -69.5 190.5t-196.5 71.5q-266 0 -266 -291v-294h426v-127h-426v-496h604v-127h-1005z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="1228" 
d="M614 303q-111 0 -196 45l-113 -172h-155l167 256q-73 92 -73 221v123q0 126 73 221l-167 256h155l113 -174q83 47 196 47q115 0 195 -47l115 174h155l-170 -256q76 -95 76 -221v-123q0 -129 -76 -221l170 -256h-155l-115 172q-82 -45 -195 -45zM614 416q115 0 176.5 64.5
t61.5 166.5v135q0 102 -61.5 167t-176.5 65t-176 -65t-61 -167v-135q0 -102 61 -166.5t176 -64.5z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="1216" 
d="M164 350v119h368v100l-67 144h-301v118h244l-285 603h166l319 -713l320 713h166l-287 -603h246v-118h-301l-68 -144v-100h369v-119h-369v-350h-152v350h-368z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="622" 
d="M246 -307v784h131v-784h-131zM377 1638v-782h-131v782h131z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="1245" 
d="M618 -262q-186 0 -300.5 92.5t-114.5 245.5v24h139v-20q0 -103 71 -166.5t205 -63.5q119 0 192 53t73 152q0 127 -150 183l-346 137q-114 42 -172.5 110t-58.5 177q0 168 161 276q-84 74 -84 201q0 150 108.5 236.5t289.5 86.5q193 0 302 -90.5t109 -247.5v-24h-133v24
q0 104 -71.5 165t-204.5 61q-122 0 -190 -53.5t-68 -151.5q0 -71 38 -114t111 -73l367 -153q106 -45 157.5 -111t51.5 -172q0 -73 -41.5 -149t-100.5 -127q66 -72 66 -185q0 -140 -114.5 -231.5t-291.5 -91.5zM758 360q81 -33 112 -49q93 104 93 203q0 62 -30 105.5
t-102 74.5l-331 142q-58 25 -92 41q-115 -89 -115 -205q0 -68 34.5 -111.5t116.5 -73.5z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="944" 
d="M168 1333q0 42 27 68t71 26t71.5 -26.5t27.5 -67.5t-27.5 -67.5t-71.5 -26.5t-71 26t-27 68zM578 1333q0 42 27 68t71 26t71 -26t27 -68t-27 -68t-71 -26t-71 26t-27 68z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="1765" 
d="M883 -25q-164 0 -304 59.5t-235 161t-148 236.5t-53 285t53 285t148 236t235 160.5t304 59.5t304 -59.5t234.5 -160.5t147.5 -236t53 -285t-53 -285t-147.5 -236.5t-234.5 -161t-304 -59.5zM883 29q154 0 283.5 55.5t215.5 150t133.5 219t47.5 263.5t-47.5 263.5
t-133.5 219t-215.5 150t-283.5 55.5t-283.5 -55.5t-215.5 -150t-133.5 -219t-47.5 -263.5t47.5 -263.5t133.5 -219t215.5 -150t283.5 -55.5zM678 657q0 -105 51.5 -168t153.5 -63q86 0 134 46t58 120h92q-9 -104 -86.5 -176t-197.5 -72q-143 0 -221 89.5t-78 223.5v119
q0 134 78 224t221 90q120 0 197.5 -72t86.5 -176h-92q-10 74 -58 120t-134 46q-102 0 -153.5 -63.5t-51.5 -168.5v-119z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="868" 
d="M133 537v92h596v-92h-596zM709 776h-101v82q-21 -37 -86 -67.5t-137 -30.5q-114 0 -184 59t-70 150q0 100 74.5 156.5t193.5 56.5q70 0 122 -23.5t81 -54.5v102q0 72 -41 117t-117 45q-64 0 -105.5 -27t-57.5 -73h-95q18 88 87.5 136t172.5 48q116 0 189.5 -70.5
t73.5 -177.5v-428zM408 834q83 0 139.5 39t56.5 98q0 57 -58 98t-136 41q-85 0 -131 -36.5t-46 -104.5q0 -63 48.5 -99t126.5 -36z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="1257" 
d="M426 123l-352 434l352 434h178l-352 -434l352 -434h-178zM965 123l-353 434l353 434h178l-352 -434l352 -434h-178z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="1200" 
d="M123 676v123h913v-541h-139v418h-774z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="1093" 
d="M102 1024q0 86 31 164.5t87.5 138.5t141 95.5t185.5 35.5q133 0 236 -61t155.5 -159t52.5 -214t-52.5 -214t-155.5 -159t-236 -61q-101 0 -185.5 35.5t-141 95.5t-87.5 138.5t-31 164.5zM150 1024q0 -167 109 -280t288 -113t288 113t109 280t-109 280t-288 113t-288 -113
t-109 -280zM647 844l-72 133h-102v-133h-63v391h180q62 0 97.5 -37t35.5 -92q0 -40 -23 -73t-61 -48l82 -141h-74zM473 1030h96q90 0 90 76q0 36 -22.5 57t-67.5 21h-96v-154z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="993" 
d="M186 1264v118h621v-118h-621z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="811" 
d="M102 1151q0 126 80 214.5t224 88.5q143 0 223 -88t80 -215t-80 -215t-223 -88q-144 0 -224 88.5t-80 214.5zM406 1376q-103 0 -158 -66t-55 -159t55 -159t158 -66t157.5 65.5t54.5 159.5t-54.5 159.5t-157.5 65.5z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="1261" 
d="M561 303v395h-387v123h387v396h139v-396h387v-123h-387v-395h-139zM174 0v123h913v-123h-913z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="849" 
d="M154 952v88l346 318q80 73 80 145q0 58 -40.5 93.5t-111.5 35.5q-76 0 -116 -37t-40 -106v-14h-108v18q0 111 72.5 174.5t193.5 63.5q120 0 193 -63.5t73 -162.5q0 -66 -32.5 -118t-96.5 -107l-264 -231h383v-97h-532z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="856" 
d="M207 1624v90h465v-90l-230 -233h7q109 0 181 -56.5t72 -156.5q0 -109 -83 -174.5t-201 -65.5q-108 0 -188 62.5t-87 166.5h105q9 -66 55.5 -102.5t118.5 -36.5q77 0 122.5 41t45.5 105t-47 99.5t-131 35.5h-103v84l223 231h-325z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="727" 
d="M340 1192h-154l166 291h189z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="1204" 
d="M1038 1049v-1049h-147v178q-44 -94 -131.5 -148.5t-218.5 -54.5q-63 0 -121.5 23t-97.5 61v-415h-156v1405h156v-654q0 -133 65 -211t193 -78q135 0 219 84.5t84 241.5v617h155z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="1179" 
d="M870 -356v1693h-215v-944q-251 0 -395.5 138.5t-144.5 379.5q0 245 141.5 384t398.5 139h328v-1790h-113z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="487" 
d="M139 594q0 44 29.5 72t75.5 28t75 -28t29 -72t-29.5 -72t-74.5 -28t-75 28t-30 72z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="714" 
d="M326 -381q-72 0 -136 12v90q56 -12 113 -12q103 0 103 55q0 50 -89 56l-94 6l39 174h98l-16 -94l27 -2q157 -10 157 -133q0 -69 -55 -110.5t-147 -41.5z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="622" 
d="M301 952v647l-178 -69v102l196 82h99v-762h-117z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="901" 
d="M168 537v92h565v-92h-565zM756 1059q0 -133 -83 -218t-222 -85t-222.5 85t-83.5 218v90q0 133 83.5 218t222.5 85t222 -85t83 -218v-90zM451 842q99 0 150.5 60t51.5 157v90q0 97 -51.5 157t-150.5 60t-151 -60t-52 -157v-90q0 -97 52 -157t151 -60z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="1257" 
d="M1184 557l-353 -434h-178l353 434l-353 434h178zM645 557l-352 -434h-178l352 434l-352 434h178z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="1632" 
d="M322 672v647l-179 -70v103l197 82h98v-762h-116zM895 172v90l258 500h111l-250 -496h272v162h109v-162h104v-94h-104v-172h-109v172h-391zM342 0l743 1434h119l-743 -1434h-119z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="1667" 
d="M991 0v88l346 318q80 73 80 145q0 58 -40 93.5t-111 35.5q-76 0 -116 -37t-40 -106v-15h-109v19q0 111 72.5 174t194.5 63q120 0 193 -63t73 -162q0 -65 -32.5 -115.5t-96.5 -109.5l-264 -232h383v-96h-533zM342 0l743 1434h119l-743 -1434h-119zM322 672v647l-179 -70
v103l197 82h98v-762h-116z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="1767" 
d="M217 1343v91h465v-91l-229 -233h6q109 0 181.5 -56.5t72.5 -156.5q0 -108 -83.5 -174t-201.5 -66q-108 0 -187.5 63t-86.5 167h104q9 -66 55.5 -102.5t118.5 -36.5q77 0 122.5 40.5t45.5 104.5t-47 99.5t-131 35.5h-103v84l224 231h-326zM477 0l744 1434h118l-743 -1434
h-119zM1030 172v90l258 500h111l-250 -496h272v162h109v-162h104v-94h-104v-172h-109v172h-391z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="1038" 
d="M639 1075v-96q0 -134 -30.5 -210.5t-108.5 -137.5l-103 -80q-71 -56 -104 -106.5t-33 -122.5q0 -104 73.5 -164t198.5 -60q126 0 197.5 64t71.5 184v39h143v-41q0 -181 -110.5 -277t-303.5 -96q-191 0 -308.5 97.5t-117.5 253.5q0 179 162 303l109 84q76 59 101.5 112
t25.5 168v86h137zM674 1346q0 -44 -29.5 -72.5t-75.5 -28.5t-75 28.5t-29 72.5t29.5 72t74.5 28t75 -28t30 -72z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="1392" 
d="M776 1434l539 -1434h-158l-131 365h-661l-129 -365h-158l538 1434h160zM696 1288l-286 -796h571zM600 1556l-180 259h188l148 -259h-156z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="1392" 
d="M776 1434l539 -1434h-158l-131 365h-661l-129 -365h-158l538 1434h160zM696 1288l-286 -796h571zM799 1556h-156l148 259h188z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="1392" 
d="M776 1434l539 -1434h-158l-131 365h-661l-129 -365h-158l538 1434h160zM696 1288l-286 -796h571zM383 1556l238 259h151l238 -259h-152l-162 175l-161 -175h-152z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="1392" 
d="M776 1434l539 -1434h-158l-131 365h-661l-129 -365h-158l538 1434h160zM696 1288l-286 -796h571zM420 1556l-107 19q33 229 209 229q54 0 129.5 -32t140.5 -63.5t93 -31.5q11 0 20 2t16.5 7.5t13.5 10t11.5 14.5t9 16t8 20t6.5 20.5t6.5 22.5t6.5 22l104 -30
q-12 -48 -26.5 -83t-37 -67t-56.5 -48.5t-78 -16.5q-46 0 -115.5 31.5t-138 63.5t-109.5 32q-49 0 -71.5 -35t-34.5 -103z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="1392" 
d="M776 1434l539 -1434h-158l-131 365h-661l-129 -365h-158l538 1434h160zM696 1288l-286 -796h571zM371 1696q0 42 27 68t71 26t71 -26t27 -68t-27 -68t-71 -26t-71 26t-27 68zM825 1696q0 41 27.5 67.5t71.5 26.5t71 -26t27 -68t-27 -68t-71 -26t-71.5 26.5t-27.5 67.5z
" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="1392" 
d="M236 0h-158l518 1382q-41 22 -64.5 62t-23.5 88q0 73 51.5 122.5t136.5 49.5t137 -49.5t52 -122.5q0 -48 -24 -88t-66 -62l520 -1382h-158l-131 365h-661zM696 1288l-286 -796h571zM696 1434q48 0 75.5 27t27.5 71t-27.5 71t-75.5 27t-75 -27t-27 -71t27 -71t75 -27z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="2131" 
d="M1071 0v375h-625l-249 -375h-172l976 1434h1016v-134h-786v-507h727v-134h-727v-526h788v-133h-948zM532 504h539v807z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="1435" 
d="M752 -381q-72 0 -136 12v90q56 -12 113 -12q102 0 102 55q0 50 -88 56l-94 6l33 147q-175 12 -298 97t-182 219t-59 307v242q0 181 65.5 320t202 221.5t328.5 82.5q121 0 224.5 -40t175 -109t114.5 -159.5t52 -192.5h-154q-10 76 -39.5 141t-79.5 116.5t-125.5 81
t-169.5 29.5q-212 0 -323 -132.5t-111 -352.5v-254q0 -220 111 -353t323 -133q94 0 169.5 29.5t125.5 81.5t79.5 117t39.5 141h154q-18 -202 -160 -344.5t-365 -155.5l-10 -67l27 -2q157 -10 157 -133q0 -69 -55 -110.5t-147 -41.5z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="1247" 
d="M186 0v1434h947v-134h-787v-507h727v-134h-727v-526h789v-133h-949zM594 1556l-180 259h188l148 -259h-156z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="1247" 
d="M186 0v1434h947v-134h-787v-507h727v-134h-727v-526h789v-133h-949zM723 1556h-156l148 259h188z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="1247" 
d="M186 0v1434h947v-134h-787v-507h727v-134h-727v-526h789v-133h-949zM346 1556l238 259h151l238 -259h-152l-162 175l-161 -175h-152z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="1247" 
d="M186 0v1434h947v-134h-787v-507h727v-134h-727v-526h789v-133h-949zM334 1696q0 42 27 68t71 26t71 -26t27 -68t-27 -68t-71 -26t-71 26t-27 68zM788 1696q0 41 27.5 67.5t71.5 26.5t71 -26t27 -68t-27 -68t-71 -26t-71.5 26.5t-27.5 67.5z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="552" 
d="M197 0v1434h159v-1434h-159zM197 1556l-181 259h189l147 -259h-155z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="552" 
d="M197 0v1434h159v-1434h-159zM369 1556h-156l147 259h189z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="552" 
d="M197 0v1434h159v-1434h-159zM-37 1556l238 259h151l238 -259h-152l-162 175l-161 -175h-152z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="552" 
d="M197 0v1434h159v-1434h-159zM-49 1696q0 42 27 68t71 26t71 -26t27 -68t-27 -68t-71 -26t-71 26t-27 68zM406 1696q0 42 27 68t71 26t71 -26t27 -68t-27 -68t-71 -26t-71 26t-27 68z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="1497" 
d="M80 651v131h172v652h539q267 0 416 -152.5t149 -398.5v-332q0 -246 -149 -398.5t-416 -152.5h-539v651h-172zM768 131q207 0 318.5 114t111.5 306v332q0 192 -111.5 306t-318.5 114h-358v-521h376v-131h-376v-520h358z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="1466" 
d="M463 1556l-107 19q33 229 209 229q54 0 129.5 -32t140.5 -63.5t93 -31.5q11 0 20 2t16.5 7.5t13.5 10t11.5 14.5t9 16t8 20t6.5 20.5t6.5 22.5t6.5 22l104 -30q-12 -48 -26.5 -83t-37 -67t-56.5 -48.5t-78 -16.5q-46 0 -115.5 31.5t-138 63.5t-109.5 32q-49 0 -71.5 -35
t-34.5 -103zM186 0v1434h150l788 -1178v1178h156v-1434h-150l-788 1178v-1178h-156z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="1490" 
d="M676 1556l-180 259h188l147 -259h-155zM1348 602q0 -131 -38.5 -244.5t-112 -200.5t-190 -136.5t-262.5 -49.5t-262.5 49.5t-189.5 136.5t-111.5 200.5t-38.5 244.5v229q0 131 38.5 244.5t111.5 200.5t189.5 136.5t262.5 49.5t262.5 -49.5t190 -136.5t112 -200.5
t38.5 -244.5v-229zM745 104q89 0 163 25t125 69.5t86.5 106.5t52 134.5t16.5 154.5v246q0 103 -27 190.5t-79.5 155t-139 105.5t-197.5 38t-197 -38t-139 -105.5t-79.5 -155t-26.5 -190.5v-246q0 -103 26.5 -190.5t79.5 -155.5t139 -106t197 -38z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="1490" 
d="M1348 602q0 -131 -38.5 -244.5t-112 -200.5t-190 -136.5t-262.5 -49.5t-262.5 49.5t-189.5 136.5t-111.5 200.5t-38.5 244.5v229q0 131 38.5 244.5t111.5 200.5t189.5 136.5t262.5 49.5t262.5 -49.5t190 -136.5t112 -200.5t38.5 -244.5v-229zM745 104q89 0 163 25
t125 69.5t86.5 106.5t52 134.5t16.5 154.5v246q0 103 -27 190.5t-79.5 155t-139 105.5t-197.5 38t-197 -38t-139 -105.5t-79.5 -155t-26.5 -190.5v-246q0 -103 26.5 -190.5t79.5 -155.5t139 -106t197 -38zM829 1556h-155l147 259h189z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="1490" 
d="M1348 602q0 -131 -38.5 -244.5t-112 -200.5t-190 -136.5t-262.5 -49.5t-262.5 49.5t-189.5 136.5t-111.5 200.5t-38.5 244.5v229q0 131 38.5 244.5t111.5 200.5t189.5 136.5t262.5 49.5t262.5 -49.5t190 -136.5t112 -200.5t38.5 -244.5v-229zM745 104q89 0 163 25
t125 69.5t86.5 106.5t52 134.5t16.5 154.5v246q0 103 -27 190.5t-79.5 155t-139 105.5t-197.5 38t-197 -38t-139 -105.5t-79.5 -155t-26.5 -190.5v-246q0 -103 26.5 -190.5t79.5 -155.5t139 -106t197 -38zM432 1556l238 259h151l238 -259h-152l-162 175l-161 -175h-152z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="1490" 
d="M1348 602q0 -131 -38.5 -244.5t-112 -200.5t-190 -136.5t-262.5 -49.5t-262.5 49.5t-189.5 136.5t-111.5 200.5t-38.5 244.5v229q0 131 38.5 244.5t111.5 200.5t189.5 136.5t262.5 49.5t262.5 -49.5t190 -136.5t112 -200.5t38.5 -244.5v-229zM745 104q89 0 163 25
t125 69.5t86.5 106.5t52 134.5t16.5 154.5v246q0 103 -27 190.5t-79.5 155t-139 105.5t-197.5 38t-197 -38t-139 -105.5t-79.5 -155t-26.5 -190.5v-246q0 -103 26.5 -190.5t79.5 -155.5t139 -106t197 -38zM469 1556l-107 19q33 229 209 229q54 0 129.5 -32t140.5 -63.5
t93 -31.5q11 0 20 2t16.5 7.5t13.5 10t11.5 14.5t9 16t8 20t6.5 20.5t6.5 22.5t6.5 22l105 -30q-25 -101 -70 -158t-129 -57q-46 0 -115.5 31.5t-138 63.5t-109.5 32q-49 0 -71.5 -35t-34.5 -103z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="1490" 
d="M1348 602q0 -131 -38.5 -244.5t-112 -200.5t-190 -136.5t-262.5 -49.5t-262.5 49.5t-189.5 136.5t-111.5 200.5t-38.5 244.5v229q0 131 38.5 244.5t111.5 200.5t189.5 136.5t262.5 49.5t262.5 -49.5t190 -136.5t112 -200.5t38.5 -244.5v-229zM745 104q89 0 163 25
t125 69.5t86.5 106.5t52 134.5t16.5 154.5v246q0 103 -27 190.5t-79.5 155t-139 105.5t-197.5 38t-197 -38t-139 -105.5t-79.5 -155t-26.5 -190.5v-246q0 -103 26.5 -190.5t79.5 -155.5t139 -106t197 -38zM420 1696q0 42 27 68t71 26t71 -26t27 -68t-27 -68t-71 -26t-71 26
t-27 68zM874 1696q0 41 27.5 67.5t71.5 26.5t71 -26t27 -68t-27 -68t-71 -26t-71.5 26.5t-27.5 67.5z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="1175" 
d="M164 406l332 329l-332 332l94 92l330 -332l332 332l92 -92l-332 -332l332 -329l-92 -93l-332 330l-330 -330z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="1490" 
d="M1348 602q0 -131 -38.5 -244.5t-112 -200.5t-190 -136.5t-262.5 -49.5q-211 0 -358 103l-115 -176h-129l162 245q-162 174 -162 459v229q0 131 38.5 244.5t111.5 200.5t189.5 136.5t262.5 49.5q208 0 357 -102l117 176h129l-162 -248q79 -86 120.5 -204t41.5 -253v-229z
M745 104q89 0 163 25t125 69.5t86.5 106.5t52 134.5t16.5 154.5v246q0 191 -88 319l-635 -966q113 -89 280 -89zM303 594q0 -195 86 -320l635 967q-112 88 -279 88q-111 0 -197 -38t-139 -105.5t-79.5 -155t-26.5 -190.5v-246z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="1449" 
d="M666 1556l-181 259h189l147 -259h-155zM725 -29q-251 0 -401 143t-150 369v951h160v-965q0 -168 105 -266.5t286 -98.5t286 98.5t105 266.5v965h160v-951q0 -226 -150 -369t-401 -143z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="1449" 
d="M725 -29q-251 0 -401 143t-150 369v951h160v-965q0 -168 105 -266.5t286 -98.5t286 98.5t105 266.5v965h160v-951q0 -226 -150 -369t-401 -143zM793 1556h-156l147 259h189z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="1449" 
d="M412 1556l237 259h152l237 -259h-151l-162 175l-162 -175h-151zM725 -29q-251 0 -401 143t-150 369v951h160v-965q0 -168 105 -266.5t286 -98.5t286 98.5t105 266.5v965h160v-951q0 -226 -150 -369t-401 -143z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="1449" 
d="M725 -29q-251 0 -401 143t-150 369v951h160v-965q0 -168 105 -266.5t286 -98.5t286 98.5t105 266.5v965h160v-951q0 -226 -150 -369t-401 -143zM397 1696q0 41 27.5 67.5t71.5 26.5t71 -26t27 -68t-27 -68t-71 -26t-71.5 26.5t-27.5 67.5zM852 1696q0 42 27 68t71 26
t71.5 -26.5t27.5 -67.5t-27.5 -67.5t-71.5 -26.5t-71 26t-27 68z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="1236" 
d="M539 0v543l-510 891h168l421 -746l422 746h168l-510 -891v-543h-159zM688 1556h-156l148 259h188z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="1308" 
d="M186 0v1434h160v-236h402q221 0 354 -128.5t133 -326.5t-133 -328t-354 -130h-402v-285h-160zM346 418h387q168 0 255 92.5t87 232.5t-87 231t-255 91h-387v-647z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="1232" 
d="M166 0v1094q0 200 119 304.5t319 104.5q198 0 321.5 -109.5t123.5 -279.5q0 -119 -63 -213t-165 -139q142 -29 225.5 -122.5t83.5 -242.5q0 -177 -123.5 -287t-324.5 -110h-78v127h70q138 0 220.5 74.5t82.5 204.5q0 128 -89 202t-231 74h-53v127q138 4 214.5 89.5
t76.5 205.5q0 121 -79 197.5t-212 76.5q-129 0 -205.5 -68.5t-76.5 -209.5v-1100h-156z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="1179" 
d="M1026 0h-149v131q-37 -61 -137.5 -108.5t-223.5 -47.5q-183 0 -290 92.5t-107 237.5q0 151 113.5 239.5t302.5 88.5q113 0 197.5 -38t137.5 -93v194q0 111 -70 182.5t-192 71.5q-192 0 -262 -157h-143q40 132 146.5 206t260.5 74q190 0 303 -108.5t113 -268.5v-696z
M549 76q127 0 224 66.5t97 164.5q0 95 -95 159t-218 64q-133 0 -212 -60t-79 -167q0 -106 79 -166.5t204 -60.5zM547 1192l-201 291h191l163 -291h-153z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="1179" 
d="M1026 0h-149v131q-37 -61 -137.5 -108.5t-223.5 -47.5q-183 0 -290 92.5t-107 237.5q0 151 113.5 239.5t302.5 88.5q113 0 197.5 -38t137.5 -93v194q0 111 -70 182.5t-192 71.5q-192 0 -262 -157h-143q40 132 146.5 206t260.5 74q190 0 303 -108.5t113 -268.5v-696z
M549 76q127 0 224 66.5t97 164.5q0 95 -95 159t-218 64q-133 0 -212 -60t-79 -167q0 -106 79 -166.5t204 -60.5zM686 1192h-154l166 291h189z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="1179" 
d="M1026 0h-149v131q-37 -61 -137.5 -108.5t-223.5 -47.5q-183 0 -290 92.5t-107 237.5q0 151 113.5 239.5t302.5 88.5q113 0 197.5 -38t137.5 -93v194q0 111 -70 182.5t-192 71.5q-192 0 -262 -157h-143q40 132 146.5 206t260.5 74q190 0 303 -108.5t113 -268.5v-696z
M549 76q127 0 224 66.5t97 164.5q0 95 -95 159t-218 64q-133 0 -212 -60t-79 -167q0 -106 79 -166.5t204 -60.5zM330 1192l225 291h152l225 -291h-146l-155 203l-156 -203h-145z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="1179" 
d="M1026 0h-149v131q-37 -61 -137.5 -108.5t-223.5 -47.5q-183 0 -290 92.5t-107 237.5q0 151 113.5 239.5t302.5 88.5q113 0 197.5 -38t137.5 -93v194q0 111 -70 182.5t-192 71.5q-192 0 -262 -157h-143q40 132 146.5 206t260.5 74q190 0 303 -108.5t113 -268.5v-696z
M549 76q127 0 224 66.5t97 164.5q0 95 -95 159t-218 64q-133 0 -212 -60t-79 -167q0 -106 79 -166.5t204 -60.5zM371 1194l-103 20q19 109 66 168.5t137 59.5q50 0 117.5 -32t125 -63.5t83.5 -31.5q11 0 20 2t16.5 8t13.5 10t11.5 15t8.5 16t8 20t6.5 20t6 22.5t5.5 21.5
l102 -23q-13 -50 -26 -84.5t-35 -69t-55.5 -52t-77.5 -17.5q-42 0 -103.5 32t-122 63.5t-100.5 31.5q-17 0 -31 -4t-24.5 -17t-16.5 -20t-13.5 -30.5t-9.5 -30t-9 -35.5z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="1179" 
d="M1026 0h-149v131q-37 -61 -137.5 -108.5t-223.5 -47.5q-183 0 -290 92.5t-107 237.5q0 151 113.5 239.5t302.5 88.5q113 0 197.5 -38t137.5 -93v194q0 111 -70 182.5t-192 71.5q-192 0 -262 -157h-143q40 132 146.5 206t260.5 74q190 0 303 -108.5t113 -268.5v-696z
M549 76q127 0 224 66.5t97 164.5q0 95 -95 159t-218 64q-133 0 -212 -60t-79 -167q0 -106 79 -166.5t204 -60.5zM322 1333q0 42 27 68t71 26t71 -26t27 -68t-27 -68t-71 -26t-71 26t-27 68zM731 1333q0 42 27 68t71 26t71.5 -26.5t27.5 -67.5t-27.5 -67.5t-71.5 -26.5
t-71 26t-27 68z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="1179" 
d="M1026 0h-149v131q-37 -61 -137.5 -108.5t-223.5 -47.5q-183 0 -290 92.5t-107 237.5q0 151 113.5 239.5t302.5 88.5q113 0 197.5 -38t137.5 -93v194q0 111 -70 182.5t-192 71.5q-192 0 -262 -157h-143q40 132 146.5 206t260.5 74q190 0 303 -108.5t113 -268.5v-696z
M549 76q127 0 224 66.5t97 164.5q0 95 -95 159t-218 64q-133 0 -212 -60t-79 -167q0 -106 79 -166.5t204 -60.5zM639 1192q-85 0 -136.5 49.5t-51.5 122.5t51.5 122.5t136.5 49.5t136.5 -49.5t51.5 -122.5t-51.5 -122.5t-136.5 -49.5zM639 1266q48 0 75 27t27 71t-27 71
t-75 27t-75 -27t-27 -71t27 -71t75 -27z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="1896" 
d="M1339 -25q-123 0 -228 57.5t-161 147.5q-44 -78 -154.5 -141.5t-254.5 -63.5q-191 0 -306.5 91.5t-115.5 240.5q0 147 115.5 237.5t300.5 90.5q113 0 201.5 -40t133.5 -87v176q0 118 -71 192t-193 74q-200 0 -266 -162h-143q37 141 145.5 213t265.5 72q235 0 350 -178
q59 84 154.5 131t220.5 47q145 0 247 -61t150 -162.5t48 -233.5v-155h-752v-37q0 -146 78.5 -238t234.5 -92q114 0 188 55t101 148h139q-26 -142 -145 -232t-283 -90zM553 78q126 0 221.5 66.5t95.5 162.5q0 92 -93 158.5t-218 66.5q-134 0 -213.5 -61.5t-79.5 -165.5
q0 -101 76 -164t211 -63zM1026 627v-47h606v49q0 147 -75.5 236t-223.5 89q-80 0 -140 -25t-95.5 -70t-52.5 -102.5t-19 -129.5z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="1146" 
d="M584 -381q-70 0 -135 12v90q56 -12 112 -12q103 0 103 55q0 50 -89 56l-94 6l33 154q-186 18 -292.5 144t-106.5 312v176q0 202 125.5 331.5t339.5 129.5q194 0 316.5 -111.5t131.5 -281.5h-139q-17 117 -92.5 193.5t-214.5 76.5q-156 0 -236 -91t-80 -245v-180
q0 -154 80 -245t236 -91q139 0 214.5 76.5t92.5 194.5h139q-9 -162 -121.5 -272t-292.5 -120l-12 -71l27 -2q157 -10 157 -133q0 -69 -55 -110.5t-147 -41.5z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="1134" 
d="M578 -25q-207 0 -335 123.5t-128 333.5v168q0 218 119 345.5t337 127.5q145 0 247 -61t150 -162.5t48 -233.5v-155h-752v-37q0 -146 79 -238t235 -92q114 0 187.5 55t100.5 148h140q-26 -142 -145 -232t-283 -90zM264 612v-32h606v49q0 147 -75.5 236t-223.5 89
q-82 0 -142.5 -26t-96 -73t-52 -107.5t-16.5 -135.5zM496 1192l-201 291h190l164 -291h-153z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="1134" 
d="M639 1192h-154l166 291h189zM578 -25q-207 0 -335 123.5t-128 333.5v168q0 218 119 345.5t337 127.5q145 0 247 -61t150 -162.5t48 -233.5v-155h-752v-37q0 -146 79 -238t235 -92q114 0 187.5 55t100.5 148h140q-26 -142 -145 -232t-283 -90zM264 612v-32h606v49
q0 147 -75.5 236t-223.5 89q-82 0 -142.5 -26t-96 -73t-52 -107.5t-16.5 -135.5z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="1134" 
d="M276 1192l226 291h151l226 -291h-146l-155 203l-156 -203h-146zM578 -25q-207 0 -335 123.5t-128 333.5v168q0 218 119 345.5t337 127.5q145 0 247 -61t150 -162.5t48 -233.5v-155h-752v-37q0 -146 79 -238t235 -92q114 0 187.5 55t100.5 148h140q-26 -142 -145 -232
t-283 -90zM264 612v-32h606v49q0 147 -75.5 236t-223.5 89q-82 0 -142.5 -26t-96 -73t-52 -107.5t-16.5 -135.5z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="1134" 
d="M279 1333q0 42 27 68t71 26t71 -26t27 -68t-27 -68t-71 -26t-71 26t-27 68zM688 1333q0 42 27 68t71 26t71.5 -26.5t27.5 -67.5t-27.5 -67.5t-71.5 -26.5t-71 26t-27 68zM578 -25q-207 0 -335 123.5t-128 333.5v168q0 218 119 345.5t337 127.5q145 0 247 -61t150 -162.5
t48 -233.5v-155h-752v-37q0 -146 79 -238t235 -92q114 0 187.5 55t100.5 148h140q-26 -142 -145 -232t-283 -90zM264 612v-32h606v49q0 147 -75.5 236t-223.5 89q-82 0 -142.5 -26t-96 -73t-52 -107.5t-16.5 -135.5z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="507" 
d="M176 0v1049h156v-1049h-156zM170 1192l-201 291h191l164 -291h-154z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="507" 
d="M346 1192h-153l165 291h189zM176 0v1049h156v-1049h-156z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="507" 
d="M-47 1192l225 291h152l225 -291h-145l-156 203l-156 -203h-145zM176 0v1049h156v-1049h-156z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="507" 
d="M-49 1333q0 42 27 68t71 26t71 -26t27 -68t-27 -68t-71 -26t-71 26t-27 68zM360 1333q0 41 27.5 67.5t71.5 26.5t71 -26t27 -68t-27 -68t-71 -26t-71.5 26.5t-27.5 67.5zM176 0v1049h156v-1049h-156z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="1167" 
d="M309 1130l-37 107l246 78q-100 80 -256 180h197q104 -54 203 -133l229 74l37 -105l-172 -55q135 -140 216 -312.5t81 -357.5v-160q0 -201 -131.5 -338t-343.5 -137q-201 0 -332 129.5t-131 327.5v131q0 217 115.5 343t316.5 126q115 0 197 -42.5t128 -113.5
q-53 117 -112 202.5t-142 156.5zM580 96q151 0 236 92t85 240v137q0 151 -92.5 248.5t-224.5 97.5q-150 0 -234 -93t-84 -257v-145q0 -144 89 -232t225 -88z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="1204" 
d="M166 0v1049h147v-179q38 93 128.5 148t222.5 55q177 0 281 -111t104 -300v-662h-156v639q0 140 -66 219.5t-202 79.5q-135 0 -219 -82.5t-84 -239.5v-616h-156zM354 1194l-102 20q19 109 66 168.5t137 59.5q50 0 117 -32t124.5 -63.5t83.5 -31.5q22 0 37.5 9t26 29.5
t16 36t13.5 45.5q1 3 1.5 5t1 5t1.5 5l102 -23q-12 -49 -26 -84t-36 -69.5t-55.5 -52t-77.5 -17.5q-42 0 -103 32t-121.5 63.5t-100.5 31.5q-21 0 -37 -8.5t-25.5 -18t-19 -33t-12.5 -35t-11 -42.5z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" 
d="M584 -25q-145 0 -253 62.5t-162 166.5t-54 234v172q0 130 54.5 234t162.5 166.5t252 62.5q216 0 341.5 -131.5t125.5 -331.5v-172q0 -200 -125.5 -331.5t-341.5 -131.5zM901 608q0 154 -83 247t-234 93q-152 0 -235 -93.5t-83 -246.5v-168q0 -153 83 -246.5t235 -93.5
q151 0 234 93t83 247v168zM506 1192l-201 291h191l163 -291h-153z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" 
d="M674 1192h-154l166 291h188zM584 -25q-145 0 -253 62.5t-162 166.5t-54 234v172q0 130 54.5 234t162.5 166.5t252 62.5q216 0 341.5 -131.5t125.5 -331.5v-172q0 -200 -125.5 -331.5t-341.5 -131.5zM901 608q0 154 -83 247t-234 93q-152 0 -235 -93.5t-83 -246.5v-168
q0 -153 83 -246.5t235 -93.5q151 0 234 93t83 247v168z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" 
d="M281 1192l225 291h151l226 -291h-146l-155 203l-156 -203h-145zM584 -25q-145 0 -253 62.5t-162 166.5t-54 234v172q0 130 54.5 234t162.5 166.5t252 62.5q216 0 341.5 -131.5t125.5 -331.5v-172q0 -200 -125.5 -331.5t-341.5 -131.5zM901 608q0 154 -83 247t-234 93
q-152 0 -235 -93.5t-83 -246.5v-168q0 -153 83 -246.5t235 -93.5q151 0 234 93t83 247v168z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" 
d="M584 -25q-145 0 -253 62.5t-162 166.5t-54 234v172q0 130 54.5 234t162.5 166.5t252 62.5q216 0 341.5 -131.5t125.5 -331.5v-172q0 -200 -125.5 -331.5t-341.5 -131.5zM901 608q0 154 -83 247t-234 93q-152 0 -235 -93.5t-83 -246.5v-168q0 -153 83 -246.5t235 -93.5
q151 0 234 93t83 247v168zM340 1194l-102 20q19 109 65.5 168.5t136.5 59.5q50 0 117.5 -32t125 -63.5t83.5 -31.5q11 0 20 2t16.5 8t13.5 10t11.5 15t8.5 16t8 20t6.5 20t6 22.5t5.5 21.5l103 -23q-12 -49 -26 -84t-36 -69.5t-55.5 -52t-77.5 -17.5q-42 0 -103.5 32
t-122 63.5t-100.5 31.5q-17 0 -31 -4t-24.5 -17t-16.5 -20t-13.5 -30.5t-9.5 -30t-9 -35.5z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" 
d="M279 1333q0 42 27 68t71 26t71 -26t27 -68t-27 -68t-71 -26t-71 26t-27 68zM688 1333q0 42 27 68t71 26t71.5 -26.5t27.5 -67.5t-27.5 -67.5t-71.5 -26.5t-71 26t-27 68zM584 -25q-145 0 -253 62.5t-162 166.5t-54 234v172q0 130 54.5 234t162.5 166.5t252 62.5
q216 0 341.5 -131.5t125.5 -331.5v-172q0 -200 -125.5 -331.5t-341.5 -131.5zM901 608q0 154 -83 247t-234 93q-152 0 -235 -93.5t-83 -246.5v-168q0 -153 83 -246.5t235 -93.5q151 0 234 93t83 247v168z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="1216" 
d="M164 672v131h889v-131h-889zM506 1133q0 43 29 70.5t73 27.5t73.5 -27.5t29.5 -70.5q0 -42 -30 -70.5t-73 -28.5t-72.5 28.5t-29.5 70.5zM506 342q0 43 29 70.5t73 27.5t73.5 -27.5t29.5 -70.5q0 -42 -30 -70t-73 -28t-72.5 28t-29.5 70z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" 
d="M584 -25q-143 0 -258 64l-93 -141h-118l131 202q-131 128 -131 338v172q0 130 54.5 234t162.5 166.5t252 62.5q146 0 254 -63l94 143h119l-133 -205q133 -130 133 -338v-172q0 -200 -125.5 -331.5t-341.5 -131.5zM901 608q0 134 -61 219l-443 -675q77 -52 187 -52
q151 0 234 93t83 247v168zM266 440q0 -136 60 -219l440 678q-77 49 -182 49q-152 0 -235 -93.5t-83 -246.5v-168z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="1196" 
d="M1030 1049v-1049h-147v178q-38 -93 -128.5 -148t-222.5 -55q-172 0 -273 111.5t-101 300.5v662h155v-639q0 -140 64 -222t194 -82q135 0 219 84.5t84 241.5v617h156zM565 1192l-200 291h190l164 -291h-154z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="1196" 
d="M653 1192h-153l166 291h188zM1030 1049v-1049h-147v178q-38 -93 -128.5 -148t-222.5 -55q-172 0 -273 111.5t-101 300.5v662h155v-639q0 -140 64 -222t194 -82q135 0 219 84.5t84 241.5v617h156z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="1196" 
d="M315 1192l226 291h151l226 -291h-146l-156 203l-155 -203h-146zM1030 1049v-1049h-147v178q-38 -93 -128.5 -148t-222.5 -55q-172 0 -273 111.5t-101 300.5v662h155v-639q0 -140 64 -222t194 -82q135 0 219 84.5t84 241.5v617h156z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="1196" 
d="M311 1333q0 41 27.5 67.5t71.5 26.5t71 -26t27 -68t-27 -68t-71 -26t-71.5 26.5t-27.5 67.5zM721 1333q0 42 27 68t71 26t71.5 -26.5t27.5 -67.5t-27.5 -67.5t-71.5 -26.5t-71 26t-27 68zM1030 1049v-1049h-147v178q-38 -93 -128.5 -148t-222.5 -55q-172 0 -273 111.5
t-101 300.5v662h155v-639q0 -140 64 -222t194 -82q135 0 219 84.5t84 241.5v617h156z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="1040" 
d="M463 -356h-152l133 356l-391 1049h154l319 -891l308 891h153l-387 -1049zM584 1192h-154l166 291h188z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="1220" 
d="M322 -356h-156v1851h156v-594q41 72 135 122t211 50q205 0 321.5 -131.5t116.5 -337.5v-160q0 -206 -119 -337.5t-315 -131.5q-119 0 -210 49t-140 132v-512zM950 451v147q0 100 -31 176.5t-102 124t-176 47.5q-151 0 -235 -101t-84 -259v-135q0 -157 93 -253t228 -96
q144 0 225.5 99t81.5 250z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="1040" 
d="M463 -356h-152l133 356l-391 1049h154l319 -891l308 891h153l-387 -1049zM223 1333q0 41 27.5 67.5t71.5 26.5t71 -26t27 -68t-27 -68t-71 -26t-71.5 26.5t-27.5 67.5zM633 1333q0 42 27 68t71 26t71 -26t27 -68t-27 -68t-71 -26t-71 26t-27 68z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="1392" 
d="M776 1434l539 -1434h-158l-131 365h-661l-129 -365h-158l538 1434h160zM696 1288l-286 -796h571zM367 1630v121h659v-121h-659z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="1179" 
d="M1026 0h-149v131q-37 -61 -137.5 -108.5t-223.5 -47.5q-183 0 -290 92.5t-107 237.5q0 151 113.5 239.5t302.5 88.5q113 0 197.5 -38t137.5 -93v194q0 111 -70 182.5t-192 71.5q-192 0 -262 -157h-143q40 132 146.5 206t260.5 74q190 0 303 -108.5t113 -268.5v-696z
M549 76q127 0 224 66.5t97 164.5q0 95 -95 159t-218 64q-133 0 -212 -60t-79 -167q0 -106 79 -166.5t204 -60.5zM311 1264v118h621v-118h-621z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="1392" 
d="M776 1434l539 -1434h-158l-131 365h-661l-129 -365h-158l538 1434h160zM696 1288l-286 -796h571zM696 1556q-152 0 -240.5 71t-88.5 188h120q0 -73 54.5 -116.5t154.5 -43.5t154.5 43.5t54.5 116.5h121q0 -117 -89 -188t-241 -71z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="1179" 
d="M1026 0h-149v131q-37 -61 -137.5 -108.5t-223.5 -47.5q-183 0 -290 92.5t-107 237.5q0 151 113.5 239.5t302.5 88.5q113 0 197.5 -38t137.5 -93v194q0 111 -70 182.5t-192 71.5q-192 0 -262 -157h-143q40 132 146.5 206t260.5 74q190 0 303 -108.5t113 -268.5v-696z
M549 76q127 0 224 66.5t97 164.5q0 95 -95 159t-218 64q-133 0 -212 -60t-79 -167q0 -106 79 -166.5t204 -60.5zM633 1192q-149 0 -234.5 80t-85.5 211h115q0 -91 52 -143t153 -52t153 52t52 143h114q0 -131 -85 -211t-234 -80z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="1392" 
d="M1188 -381q-83 0 -133.5 44.5t-50.5 121.5q0 137 153 215l-131 365h-661l-129 -365h-158l538 1434h160l539 -1434q-40 -24 -63 -40t-53.5 -42.5t-45 -55.5t-14.5 -61q0 -38 24.5 -55.5t77.5 -17.5q39 0 66 6v-101q-63 -14 -119 -14zM696 1288l-286 -796h571z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="1179" 
d="M877 131q-37 -61 -137.5 -108.5t-223.5 -47.5q-183 0 -290 92.5t-107 237.5q0 151 113.5 239.5t302.5 88.5q113 0 197.5 -38t137.5 -93v194q0 111 -70 182.5t-192 71.5q-192 0 -262 -157h-143q40 132 146.5 206t260.5 74q190 0 303 -108.5t113 -268.5v-696
q-33 -19 -56 -35.5t-52.5 -42.5t-45.5 -57t-16 -64q0 -38 24.5 -55.5t77.5 -17.5q39 0 66 6v-101q-63 -14 -119 -14q-86 0 -135 43t-49 119q0 133 156 219v131zM549 76q127 0 224 66.5t97 164.5q0 95 -95 159t-218 64q-133 0 -212 -60t-79 -167q0 -106 79 -166.5t204 -60.5z
" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="1435" 
d="M739 -29q-145 0 -259.5 47.5t-187.5 132t-111 197.5t-38 248v242q0 181 65.5 320t202 221.5t328.5 82.5q121 0 224.5 -40t175 -109t114.5 -159.5t52 -192.5h-154q-10 76 -39.5 141t-79.5 116.5t-125.5 81t-169.5 29.5q-212 0 -323 -132.5t-111 -352.5v-254
q0 -220 111 -353t323 -133q94 0 169.5 29.5t125.5 81.5t79.5 117t39.5 141h154q-9 -102 -52 -192.5t-114.5 -159.5t-175 -109.5t-224.5 -40.5zM813 1556h-156l148 259h188z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="1146" 
d="M651 1192h-153l166 291h188zM580 -25q-214 0 -339.5 129.5t-125.5 331.5v176q0 202 125.5 331.5t339.5 129.5q194 0 316.5 -111.5t131.5 -281.5h-139q-17 117 -92.5 193.5t-214.5 76.5q-156 0 -236 -91t-80 -245v-180q0 -154 80 -245t236 -91q139 0 214.5 76.5
t92.5 194.5h139q-9 -170 -131.5 -282t-316.5 -112z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="1435" 
d="M739 -29q-145 0 -259.5 47.5t-187.5 132t-111 197.5t-38 248v242q0 181 65.5 320t202 221.5t328.5 82.5q121 0 224.5 -40t175 -109t114.5 -159.5t52 -192.5h-154q-10 76 -39.5 141t-79.5 116.5t-125.5 81t-169.5 29.5q-212 0 -323 -132.5t-111 -352.5v-254
q0 -220 111 -353t323 -133q94 0 169.5 29.5t125.5 81.5t79.5 117t39.5 141h154q-9 -102 -52 -192.5t-114.5 -159.5t-175 -109.5t-224.5 -40.5zM432 1556l238 259h151l238 -259h-152l-162 175l-161 -175h-152z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="1146" 
d="M285 1192l225 291h152l225 -291h-146l-155 203l-156 -203h-145zM580 -25q-214 0 -339.5 129.5t-125.5 331.5v176q0 202 125.5 331.5t339.5 129.5q194 0 316.5 -111.5t131.5 -281.5h-139q-17 117 -92.5 193.5t-214.5 76.5q-156 0 -236 -91t-80 -245v-180q0 -154 80 -245
t236 -91q139 0 214.5 76.5t92.5 194.5h139q-9 -170 -131.5 -282t-316.5 -112z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="1435" 
d="M645 1683q0 42 27 68.5t71 26.5t71.5 -26.5t27.5 -68.5q0 -41 -27.5 -67.5t-71.5 -26.5t-71 26t-27 68zM739 -29q-145 0 -259.5 47.5t-187.5 132t-111 197.5t-38 248v242q0 181 65.5 320t202 221.5t328.5 82.5q121 0 224.5 -40t175 -109t114.5 -159.5t52 -192.5h-154
q-10 76 -39.5 141t-79.5 116.5t-125.5 81t-169.5 29.5q-212 0 -323 -132.5t-111 -352.5v-254q0 -220 111 -353t323 -133q94 0 169.5 29.5t125.5 81.5t79.5 117t39.5 141h154q-9 -102 -52 -192.5t-114.5 -159.5t-175 -109.5t-224.5 -40.5z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="1146" 
d="M580 -25q-214 0 -339.5 129.5t-125.5 331.5v176q0 202 125.5 331.5t339.5 129.5q194 0 316.5 -111.5t131.5 -281.5h-139q-17 117 -92.5 193.5t-214.5 76.5q-156 0 -236 -91t-80 -245v-180q0 -154 80 -245t236 -91q139 0 214.5 76.5t92.5 194.5h139q-9 -170 -131.5 -282
t-316.5 -112zM492 1325q0 42 27 68t71 26t71 -26t27 -68t-27 -68t-71 -26t-71 26t-27 68z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="1435" 
d="M739 -29q-145 0 -259.5 47.5t-187.5 132t-111 197.5t-38 248v242q0 181 65.5 320t202 221.5t328.5 82.5q121 0 224.5 -40t175 -109t114.5 -159.5t52 -192.5h-154q-10 76 -39.5 141t-79.5 116.5t-125.5 81t-169.5 29.5q-212 0 -323 -132.5t-111 -352.5v-254
q0 -220 111 -353t323 -133q94 0 169.5 29.5t125.5 81.5t79.5 117t39.5 141h154q-9 -102 -52 -192.5t-114.5 -159.5t-175 -109.5t-224.5 -40.5zM666 1556l-238 259h152l161 -175l162 175h152l-238 -259h-151z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="1146" 
d="M512 1192l-225 291h145l156 -203l155 203h146l-225 -291h-152zM580 -25q-214 0 -339.5 129.5t-125.5 331.5v176q0 202 125.5 331.5t339.5 129.5q194 0 316.5 -111.5t131.5 -281.5h-139q-17 117 -92.5 193.5t-214.5 76.5q-156 0 -236 -91t-80 -245v-180q0 -154 80 -245
t236 -91q139 0 214.5 76.5t92.5 194.5h139q-9 -170 -131.5 -282t-316.5 -112z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="1431" 
d="M186 1434h539q267 0 416 -152.5t149 -398.5v-332q0 -246 -149 -398.5t-416 -152.5h-539v1434zM702 131q208 0 319.5 114t111.5 306v332q0 192 -111.5 306t-319.5 114h-358v-1172h358zM614 1556l-237 259h151l162 -175l162 175h152l-238 -259h-152z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="1521" 
d="M1270 1094l82 401h168l-123 -401h-127zM899 1495h156v-1495h-148v164q-36 -77 -133 -133t-221 -56q-134 0 -235 63t-152 168.5t-51 237.5v160q0 206 119 337.5t315 131.5q119 0 210 -49t140 -131v602zM270 598v-147q0 -74 18.5 -136t55 -110.5t97 -75.5t139.5 -27
q150 0 234.5 101.5t84.5 259.5v135q0 157 -93 252.5t-228 95.5q-144 0 -226 -98.5t-82 -249.5z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="1497" 
d="M80 651v131h172v652h539q267 0 416 -152.5t149 -398.5v-332q0 -246 -149 -398.5t-416 -152.5h-539v651h-172zM768 131q207 0 318.5 114t111.5 306v332q0 192 -111.5 306t-318.5 114h-358v-521h376v-131h-376v-520h358z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="1265" 
d="M629 1200v115h270v180h156v-180h157v-115h-157v-1200h-148v164q-36 -77 -133 -133t-221 -56q-134 0 -235 63t-152 168.5t-51 237.5v160q0 206 119 337.5t315 131.5q119 0 210 -49t140 -131v307h-270zM270 598v-147q0 -74 18.5 -136t55 -110.5t97 -75.5t139.5 -27
q150 0 234.5 101.5t84.5 259.5v135q0 157 -93 252.5t-228 95.5q-144 0 -226 -98.5t-82 -249.5z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="1247" 
d="M186 0v1434h947v-134h-787v-507h727v-134h-727v-526h789v-133h-949zM334 1630v121h659v-121h-659z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="1134" 
d="M578 -25q-207 0 -335 123.5t-128 333.5v168q0 218 119 345.5t337 127.5q145 0 247 -61t150 -162.5t48 -233.5v-155h-752v-37q0 -146 79 -238t235 -92q114 0 187.5 55t100.5 148h140q-26 -142 -145 -232t-283 -90zM264 612v-32h606v49q0 147 -75.5 236t-223.5 89
q-82 0 -142.5 -26t-96 -73t-52 -107.5t-16.5 -135.5zM270 1264v118h621v-118h-621z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="1247" 
d="M186 0v1434h947v-134h-787v-507h727v-134h-727v-526h789v-133h-949zM676 1556q-152 0 -241 71t-89 188h121q0 -73 54.5 -116.5t154.5 -43.5t154.5 43.5t54.5 116.5h121q0 -117 -89 -188t-241 -71z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="1134" 
d="M578 -25q-207 0 -335 123.5t-128 333.5v168q0 218 119 345.5t337 127.5q145 0 247 -61t150 -162.5t48 -233.5v-155h-752v-37q0 -146 79 -238t235 -92q114 0 187.5 55t100.5 148h140q-26 -142 -145 -232t-283 -90zM264 612v-32h606v49q0 147 -75.5 236t-223.5 89
q-82 0 -142.5 -26t-96 -73t-52 -107.5t-16.5 -135.5zM582 1192q-149 0 -234.5 80t-85.5 211h115q0 -91 52 -143t153 -52t152.5 52t51.5 143h115q0 -131 -85 -211t-234 -80z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="1247" 
d="M569 1683q0 42 27.5 68.5t71.5 26.5t71 -26.5t27 -68.5t-27 -68t-71 -26t-71.5 26.5t-27.5 67.5zM186 0v1434h947v-134h-787v-507h727v-134h-727v-526h789v-133h-949z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="1134" 
d="M578 -25q-207 0 -335 123.5t-128 333.5v168q0 218 119 345.5t337 127.5q145 0 247 -61t150 -162.5t48 -233.5v-155h-752v-37q0 -146 79 -238t235 -92q114 0 187.5 55t100.5 148h140q-26 -142 -145 -232t-283 -90zM264 612v-32h606v49q0 147 -75.5 236t-223.5 89
q-82 0 -142.5 -26t-96 -73t-52 -107.5t-16.5 -135.5zM475 1325q0 42 27 68t71 26t71.5 -26.5t27.5 -67.5t-27.5 -67.5t-71.5 -26.5t-71 26t-27 68z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="1247" 
d="M186 0v1434h947v-134h-787v-507h727v-134h-727v-526h789v-133q-170 -100 -170 -199q0 -38 24.5 -55.5t77.5 -17.5q39 0 66 6v-101q-63 -14 -119 -14q-87 0 -136 43t-49 119q0 74 45 130t113 89h-801z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="1134" 
d="M578 -25q-207 0 -335 123.5t-128 333.5v168q0 218 119 345.5t337 127.5q145 0 247 -61t150 -162.5t48 -233.5v-155h-752v-37q0 -146 79 -238t235 -92q114 0 187.5 55t100.5 148h140q-23 -126 -132 -219q-45 -39 -58.5 -51t-48.5 -47.5t-48 -57.5t-26 -55t-13 -66
q0 -38 24.5 -55.5t77.5 -17.5q39 0 66 6v-101q-63 -14 -119 -14q-84 0 -134 44t-50 122q0 64 37.5 118t85.5 81q-65 -9 -90 -9zM264 612v-32h606v49q0 147 -75.5 236t-223.5 89q-82 0 -142.5 -26t-96 -73t-52 -107.5t-16.5 -135.5z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="1247" 
d="M186 0v1434h947v-134h-787v-507h727v-134h-727v-526h789v-133h-949zM602 1556l-237 259h151l162 -175l162 175h151l-237 -259h-152z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="1134" 
d="M506 1192l-225 291h145l156 -203l155 203h146l-226 -291h-151zM578 -25q-207 0 -335 123.5t-128 333.5v168q0 218 119 345.5t337 127.5q145 0 247 -61t150 -162.5t48 -233.5v-155h-752v-37q0 -146 79 -238t235 -92q114 0 187.5 55t100.5 148h140q-26 -142 -145 -232
t-283 -90zM264 612v-32h606v49q0 147 -75.5 236t-223.5 89q-82 0 -142.5 -26t-96 -73t-52 -107.5t-16.5 -135.5z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="1452" 
d="M420 1556l237 259h152l238 -259h-152l-162 175l-162 -175h-151zM1294 963h-151q-5 157 -115 261.5t-297 104.5q-144 0 -241.5 -63.5t-142 -171t-44.5 -254.5v-238q0 -87 15.5 -161t49.5 -137.5t84.5 -108.5t124 -70t164.5 -25q195 0 304.5 109t109.5 313v37h-401v133h555
v-151q0 -166 -65 -294t-195.5 -202t-309.5 -74q-146 0 -261 49.5t-187.5 137t-110 201.5t-37.5 247v228q0 185 66.5 325.5t200.5 221.5t319 81q256 0 408 -140.5t157 -358.5z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="1216" 
d="M311 1192l226 291h151l225 -291h-145l-156 203l-155 -203h-146zM588 -381q-162 0 -287.5 71t-167.5 197h154q83 -145 301 -145q137 0 222 71.5t85 190.5v238q-26 -77 -126 -138t-226 -61q-197 0 -312.5 123t-115.5 305v174q0 185 114.5 306.5t315.5 121.5
q93 0 169.5 -31.5t119.5 -74.5t67 -93v175h150v-1049q0 -163 -132 -272t-331 -109zM573 170q138 0 230 86t92 231v136q0 135 -90.5 229t-224.5 94q-153 0 -232.5 -80.5t-79.5 -232.5v-150q0 -149 78 -231t227 -82z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="1452" 
d="M731 1556q-152 0 -241 71t-89 188h121q0 -73 54.5 -116.5t154.5 -43.5t154.5 43.5t54.5 116.5h121q0 -117 -89 -188t-241 -71zM1294 963h-151q-5 157 -115 261.5t-297 104.5q-144 0 -241.5 -63.5t-142 -171t-44.5 -254.5v-238q0 -87 15.5 -161t49.5 -137.5t84.5 -108.5
t124 -70t164.5 -25q195 0 304.5 109t109.5 313v37h-401v133h555v-151q0 -166 -65 -294t-195.5 -202t-309.5 -74q-146 0 -261 49.5t-187.5 137t-110 201.5t-37.5 247v228q0 185 66.5 325.5t200.5 221.5t319 81q256 0 408 -140.5t157 -358.5z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="1216" 
d="M588 -381q-162 0 -287.5 71t-167.5 197h154q83 -145 301 -145q137 0 222 71.5t85 190.5v238q-26 -77 -126 -138t-226 -61q-197 0 -312.5 123t-115.5 305v174q0 185 114.5 306.5t315.5 121.5q93 0 169.5 -31.5t119.5 -74.5t67 -93v175h150v-1049q0 -163 -132 -272
t-331 -109zM573 170q138 0 230 86t92 231v136q0 135 -90.5 229t-224.5 94q-153 0 -232.5 -80.5t-79.5 -232.5v-150q0 -149 78 -231t227 -82zM616 1192q-149 0 -234 80t-85 211h115q0 -91 51.5 -143t152.5 -52t153 52t52 143h115q0 -131 -85.5 -211t-234.5 -80z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="1452" 
d="M627 1683q0 42 27 68.5t71 26.5t71 -26.5t27 -68.5t-27 -68t-71 -26t-71 26t-27 68zM1294 963h-151q-5 157 -115 261.5t-297 104.5q-144 0 -241.5 -63.5t-142 -171t-44.5 -254.5v-238q0 -87 15.5 -161t49.5 -137.5t84.5 -108.5t124 -70t164.5 -25q195 0 304.5 109
t109.5 313v37h-401v133h555v-151q0 -166 -65 -294t-195.5 -202t-309.5 -74q-146 0 -261 49.5t-187.5 137t-110 201.5t-37.5 247v228q0 185 66.5 325.5t200.5 221.5t319 81q256 0 408 -140.5t157 -358.5z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="1216" 
d="M588 -381q-162 0 -287.5 71t-167.5 197h154q83 -145 301 -145q137 0 222 71.5t85 190.5v238q-26 -77 -126 -138t-226 -61q-197 0 -312.5 123t-115.5 305v174q0 185 114.5 306.5t315.5 121.5q93 0 169.5 -31.5t119.5 -74.5t67 -93v175h150v-1049q0 -163 -132 -272
t-331 -109zM573 170q138 0 230 86t92 231v136q0 135 -90.5 229t-224.5 94q-153 0 -232.5 -80.5t-79.5 -232.5v-150q0 -149 78 -231t227 -82zM506 1325q0 42 27 68t71 26t71 -26t27 -68t-27 -68t-71 -26t-71 26t-27 68z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="1452" 
d="M594 -512l78 348h174l-123 -348h-129zM1294 963h-151q-5 157 -115 261.5t-297 104.5q-144 0 -241.5 -63.5t-142 -171t-44.5 -254.5v-238q0 -87 15.5 -161t49.5 -137.5t84.5 -108.5t124 -70t164.5 -25q195 0 304.5 109t109.5 313v37h-401v133h555v-151q0 -166 -65 -294
t-195.5 -202t-309.5 -74q-146 0 -261 49.5t-187.5 137t-110 201.5t-37.5 247v228q0 185 66.5 325.5t200.5 221.5t319 81q256 0 408 -140.5t157 -358.5z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="1216" 
d="M510 1192l108 291h152l-72 -291h-188zM588 -381q-162 0 -287.5 71t-167.5 197h154q83 -145 301 -145q137 0 222 71.5t85 190.5v238q-26 -77 -126 -138t-226 -61q-197 0 -312.5 123t-115.5 305v174q0 185 114.5 306.5t315.5 121.5q93 0 169.5 -31.5t119.5 -74.5t67 -93
v175h150v-1049q0 -163 -132 -272t-331 -109zM573 170q138 0 230 86t92 231v136q0 135 -90.5 229t-224.5 94q-153 0 -232.5 -80.5t-79.5 -232.5v-150q0 -149 78 -231t227 -82z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="1449" 
d="M186 0v1434h160v-643h758v643h160v-1434h-160v647h-758v-647h-160zM412 1556l237 259h152l237 -259h-151l-162 175l-162 -175h-151z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="1204" 
d="M166 0v1495h156v-608q101 186 342 186q177 0 281 -111t104 -300v-662h-156v639q0 140 -66 219.5t-202 79.5q-135 0 -219 -82.5t-84 -239.5v-616h-156zM-68 1597l238 258h152l237 -258h-151l-162 175l-162 -175h-152z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="1579" 
d="M80 1061v96h170v277h160v-277h757v277h160v-277h172v-96h-172v-1061h-160v647h-757v-647h-160v1061h-170zM410 791h757v270h-757v-270z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="1249" 
d="M53 1200v115h158v180h156v-180h270v-115h-270v-313q101 186 342 186q177 0 281 -111t104 -300v-662h-156v639q0 140 -66 219.5t-202 79.5q-135 0 -219 -82.5t-84 -239.5v-616h-156v1200h-158z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="552" 
d="M197 0v1434h159v-1434h-159zM0 1556l-106 19q33 229 208 229q54 0 129.5 -32t140.5 -63.5t93 -31.5q11 0 20 2t16.5 7.5t13.5 10t11.5 14.5t9 16t8 20t6.5 20.5t6.5 22.5t6.5 22l105 -30q-25 -101 -70 -158t-129 -57q-46 0 -115.5 31.5t-138 63.5t-109.5 32
q-49 0 -71.5 -35t-34.5 -103z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="507" 
d="M176 0v1049h156v-1049h-156zM-4 1194l-102 20q19 109 65.5 168.5t136.5 59.5q50 0 117.5 -32t125 -63.5t83.5 -31.5q11 0 20 2t16.5 8t13.5 10t11.5 15t8.5 16t8 20t6.5 20t6 22.5t5.5 21.5l103 -23q-12 -49 -26 -84t-36 -69.5t-55.5 -52t-77.5 -17.5q-42 0 -103.5 32
t-122 63.5t-100.5 31.5q-17 0 -31 -4t-24.5 -17t-16.5 -20t-13.5 -30.5t-9.5 -30t-9 -35.5z" />
    <glyph glyph-name="uni012A" unicode="&#x12a;" horiz-adv-x="552" 
d="M197 0v1434h159v-1434h-159zM-53 1630v121h659v-121h-659z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="507" 
d="M176 0v1049h156v-1049h-156zM-55 1264v118h620v-118h-620z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="552" 
d="M197 0v1434h159v-1434h-159zM276 1556q-152 0 -240.5 71t-88.5 188h121q0 -73 54 -116.5t154 -43.5t154.5 43.5t54.5 116.5h121q0 -117 -89 -188t-241 -71z" />
    <glyph glyph-name="uni012D" unicode="&#x12d;" horiz-adv-x="507" 
d="M176 0v1049h156v-1049h-156zM254 1192q-149 0 -234.5 80t-85.5 211h115q0 -91 52 -143t153 -52t153 52t52 143h114q0 -131 -85 -211t-234 -80z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="552" 
d="M274 -381q-86 0 -133 47t-47 127q0 64 27.5 112.5t75.5 94.5v1434h159v-1434q-35 -33 -47.5 -46.5t-36 -42.5t-32.5 -55t-9 -55q0 -73 86 -73q23 0 37 2v-103q-40 -8 -80 -8z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="507" 
d="M250 -381q-86 0 -133 47t-47 127q0 113 106 207v1049h156v-1049q-30 -29 -38.5 -37.5t-31.5 -34t-30.5 -41t-16 -39t-8.5 -47.5q0 -73 86 -73q23 0 37 2v-103q-40 -8 -80 -8zM156 1325q0 42 27 68t71 26t71 -26t27 -68t-27 -68t-71 -26t-71 26t-27 68z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="552" 
d="M197 0v1434h159v-1434h-159zM178 1683q0 42 27 68.5t71 26.5t71.5 -26.5t27.5 -68.5q0 -41 -27.5 -67.5t-71.5 -26.5t-71 26t-27 68z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="507" 
d="M176 0v1049h156v-1049h-156z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="1759" 
d="M197 0v1434h159v-1434h-159zM1112 -29q-211 0 -339 115t-128 301v12h152v-12q0 -128 81 -205.5t232 -77.5q148 0 229.5 75.5t81.5 209.5v1045h160v-1045q0 -184 -129 -301t-340 -117z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="1015" 
d="M176 0v1049h156v-1049h-156zM156 1325q0 42 27 68t71 26t71 -26t27 -68t-27 -68t-71 -26t-71 26t-27 68zM840 1049v-1164q0 -116 -64.5 -178.5t-185.5 -62.5h-101v125h56q73 0 106 29t33 98v1153h156zM664 1325q0 42 27 68t71 26t71 -26t27 -68t-27 -68t-71 -26t-71 26
t-27 68z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="1206" 
d="M559 -29q-211 0 -339 115t-128 301v12h152v-12q0 -128 81 -205.5t232 -77.5q148 0 229.5 75.5t81.5 209.5v1045h160v-1045q0 -184 -129 -301t-340 -117zM633 1556l237 259h152l238 -259h-152l-162 175l-162 -175h-151z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="507" 
d="M332 1049v-1164q0 -116 -64.5 -178.5t-185.5 -62.5h-100v125h55q73 0 106 29t33 98v1153h156zM-47 1192l225 291h152l225 -291h-145l-156 203l-156 -203h-145z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="1253" 
d="M186 0v1434h160v-699l647 699h193l-666 -709l688 -725h-192l-670 713v-713h-160zM524 -512l78 348h174l-123 -348h-129z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="1050" 
d="M166 0v1495h156v-940l452 494h195l-480 -506l500 -543h-194l-473 528v-528h-156zM399 -512l78 348h174l-123 -348h-129z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="1050" 
d="M166 0v1049h156v-494l452 494h195l-482 -506l502 -543h-194l-473 528v-528h-156z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" 
d="M354 1556h-155l147 259h189zM186 0v1434h160v-1301h735v-133h-895z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="507" 
d="M336 1620h-146l123 229h183zM176 0v1495h156v-1495h-156z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" 
d="M483 -512l78 348h174l-123 -348h-129zM186 0v1434h160v-1301h735v-133h-895z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" horiz-adv-x="507" 
d="M84 -512l78 348h174l-123 -348h-129zM176 0v1495h156v-1495h-156z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" 
d="M676 1032l82 402h168l-123 -402h-127zM186 0v1434h160v-1301h735v-133h-895z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="780" 
d="M547 1094l82 401h168l-123 -401h-127zM176 0v1495h156v-1495h-156z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" 
d="M186 0v1434h160v-1301h735v-133h-895zM635 711q0 44 29 72t75 28t75.5 -28t29.5 -72t-30 -72.5t-75 -28.5t-74.5 28.5t-29.5 72.5z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="759" 
d="M176 0v1495h156v-1495h-156zM514 614q0 44 29 72.5t75 28.5t75.5 -28.5t29.5 -72.5t-30 -72t-75 -28t-74.5 28t-29.5 72z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="1196" 
d="M41 514v143l176 107v670h160v-574l342 209v-143l-342 -209v-584h735v-133h-895v621z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="532" 
d="M66 559v148l118 102v686h156v-553l145 127v-147l-145 -127v-795h-156v662z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="1466" 
d="M776 1556h-155l147 259h188zM186 0v1434h150l788 -1178v1178h156v-1434h-150l-788 1178v-1178h-156z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="1204" 
d="M674 1192h-154l166 291h188zM166 0v1049h147v-179q38 93 128.5 148t222.5 55q177 0 281 -111t104 -300v-662h-156v639q0 140 -66 219.5t-202 79.5q-135 0 -219 -82.5t-84 -239.5v-616h-156z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="1466" 
d="M586 -512l78 348h174l-123 -348h-129zM186 0v1434h150l788 -1178v1178h156v-1434h-150l-788 1178v-1178h-156z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" horiz-adv-x="1204" 
d="M469 -512l78 348h174l-123 -348h-129zM166 0v1049h147v-179q38 93 128.5 148t222.5 55q177 0 281 -111t104 -300v-662h-156v639q0 140 -66 219.5t-202 79.5q-135 0 -219 -82.5t-84 -239.5v-616h-156z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="1466" 
d="M186 0v1434h150l788 -1178v1178h156v-1434h-150l-788 1178v-1178h-156zM657 1556l-237 259h151l162 -175l162 175h152l-238 -259h-152z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="1204" 
d="M528 1192l-225 291h146l155 -203l156 203h145l-225 -291h-152zM166 0v1049h147v-179q38 93 128.5 148t222.5 55q177 0 281 -111t104 -300v-662h-156v639q0 140 -66 219.5t-202 79.5q-135 0 -219 -82.5t-84 -239.5v-616h-156z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" horiz-adv-x="1445" 
d="M37 1094l82 401h168l-123 -401h-127zM406 0v1049h147v-179q38 93 128 148t222 55q177 0 281 -111t104 -300v-662h-155v639q0 139 -66.5 219t-202.5 80q-135 0 -219 -82.5t-84 -239.5v-616h-155z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="1466" 
d="M186 0v1434h150l788 -1178v1178h156v-1448q0 -161 -90 -251.5t-268 -90.5h-86v129h53q122 0 178.5 53t56.5 166v16l-782 1170v-1178h-156z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="1204" 
d="M166 0v1049h147v-179q38 93 128.5 148t222.5 55q177 0 281 -111t104 -300v-689q0 -156 -96.5 -242.5t-270.5 -86.5h-41v125h43q209 0 209 206v664q0 140 -66 219.5t-202 79.5q-135 0 -219 -82.5t-84 -239.5v-616h-156z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="1490" 
d="M1348 602q0 -131 -38.5 -244.5t-112 -200.5t-190 -136.5t-262.5 -49.5t-262.5 49.5t-189.5 136.5t-111.5 200.5t-38.5 244.5v229q0 131 38.5 244.5t111.5 200.5t189.5 136.5t262.5 49.5t262.5 -49.5t190 -136.5t112 -200.5t38.5 -244.5v-229zM745 104q89 0 163 25
t125 69.5t86.5 106.5t52 134.5t16.5 154.5v246q0 103 -27 190.5t-79.5 155t-139 105.5t-197.5 38t-197 -38t-139 -105.5t-79.5 -155t-26.5 -190.5v-246q0 -103 26.5 -190.5t79.5 -155.5t139 -106t197 -38zM416 1630v121h659v-121h-659z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" 
d="M272 1264v118h621v-118h-621zM584 -25q-145 0 -253 62.5t-162 166.5t-54 234v172q0 130 54.5 234t162.5 166.5t252 62.5q216 0 341.5 -131.5t125.5 -331.5v-172q0 -200 -125.5 -331.5t-341.5 -131.5zM901 608q0 154 -83 247t-234 93q-152 0 -235 -93.5t-83 -246.5v-168
q0 -153 83 -246.5t235 -93.5q151 0 234 93t83 247v168z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="1490" 
d="M1348 602q0 -131 -38.5 -244.5t-112 -200.5t-190 -136.5t-262.5 -49.5t-262.5 49.5t-189.5 136.5t-111.5 200.5t-38.5 244.5v229q0 131 38.5 244.5t111.5 200.5t189.5 136.5t262.5 49.5t262.5 -49.5t190 -136.5t112 -200.5t38.5 -244.5v-229zM745 104q89 0 163 25
t125 69.5t86.5 106.5t52 134.5t16.5 154.5v246q0 103 -27 190.5t-79.5 155t-139 105.5t-197.5 38t-197 -38t-139 -105.5t-79.5 -155t-26.5 -190.5v-246q0 -103 26.5 -190.5t79.5 -155.5t139 -106t197 -38zM745 1556q-152 0 -240.5 71t-88.5 188h121q0 -73 54 -116.5
t154 -43.5t154.5 43.5t54.5 116.5h121q0 -117 -89 -188t-241 -71z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" 
d="M584 -25q-145 0 -253 62.5t-162 166.5t-54 234v172q0 130 54.5 234t162.5 166.5t252 62.5q216 0 341.5 -131.5t125.5 -331.5v-172q0 -200 -125.5 -331.5t-341.5 -131.5zM901 608q0 154 -83 247t-234 93q-152 0 -235 -93.5t-83 -246.5v-168q0 -153 83 -246.5t235 -93.5
q151 0 234 93t83 247v168zM582 1192q-149 0 -234.5 80t-85.5 211h115q0 -91 52 -143t153 -52t152.5 52t51.5 143h115q0 -131 -85 -211t-234 -80z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="1490" 
d="M610 1556h-139l109 259h165zM1040 1556h-139l109 259h166zM1348 602q0 -131 -38.5 -244.5t-112 -200.5t-190 -136.5t-262.5 -49.5t-262.5 49.5t-189.5 136.5t-111.5 200.5t-38.5 244.5v229q0 131 38.5 244.5t111.5 200.5t189.5 136.5t262.5 49.5t262.5 -49.5t190 -136.5
t112 -200.5t38.5 -244.5v-229zM745 104q89 0 163 25t125 69.5t86.5 106.5t52 134.5t16.5 154.5v246q0 103 -27 190.5t-79.5 155t-139 105.5t-197.5 38t-197 -38t-139 -105.5t-79.5 -155t-26.5 -190.5v-246q0 -103 26.5 -190.5t79.5 -155.5t139 -106t197 -38z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" 
d="M481 1192h-141l127 291h168zM858 1192h-141l127 291h168zM584 -25q-145 0 -253 62.5t-162 166.5t-54 234v172q0 130 54.5 234t162.5 166.5t252 62.5q216 0 341.5 -131.5t125.5 -331.5v-172q0 -200 -125.5 -331.5t-341.5 -131.5zM901 608q0 154 -83 247t-234 93
q-152 0 -235 -93.5t-83 -246.5v-168q0 -153 83 -246.5t235 -93.5q151 0 234 93t83 247v168z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="2187" 
d="M776 0q-157 0 -279 48t-198.5 133.5t-116 199t-39.5 248.5v176q0 135 39.5 248.5t116 199t198.5 133.5t279 48h1297v-134h-787v-507h727v-134h-727v-526h789v-133h-1299zM303 623q0 -104 28 -191t84 -155t148.5 -106t212.5 -38h350v1167h-350q-120 0 -212.5 -38
t-148.5 -105.5t-84 -154.5t-28 -191v-188z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="1921" 
d="M584 -25q-145 0 -253 62.5t-162 166.5t-54 234v172q0 130 54.5 234t162.5 166.5t252 62.5q132 0 232.5 -50.5t156.5 -141.5q117 192 385 192q109 0 195 -35t139.5 -97t81.5 -144.5t28 -180.5v-155h-751v-43q2 -144 80 -234t233 -90q114 0 188 55t101 148h139
q-26 -142 -145 -232t-283 -90q-130 0 -239.5 54.5t-151.5 142.5q-44 -84 -151 -140.5t-238 -56.5zM901 426v195q-5 149 -87.5 238t-229.5 89q-152 0 -235 -93.5t-83 -246.5v-168q0 -153 83 -246.5t235 -93.5q147 0 229.5 89.5t87.5 236.5zM1051 612v-32h606v49
q0 147 -75.5 236t-223.5 89q-82 0 -142.5 -26t-96 -73t-52 -107.5t-16.5 -135.5z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="1384" 
d="M186 0v1434h592q153 0 264 -62t164.5 -164t53.5 -231q0 -151 -82.5 -267t-227.5 -161l312 -549h-183l-291 524h-442v-524h-160zM346 651h412q166 0 254 90t88 236q0 145 -90 236.5t-252 91.5h-412v-654zM721 1556h-156l148 259h188z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="722" 
d="M467 1192h-154l166 291h189zM166 0v1049h145v-242q60 248 342 248h25v-146h-31q-153 0 -239 -100.5t-86 -294.5v-514h-156z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="1384" 
d="M545 -512l78 348h174l-123 -348h-129zM186 0v1434h592q153 0 264 -62t164.5 -164t53.5 -231q0 -151 -82.5 -267t-227.5 -161l312 -549h-183l-291 524h-442v-524h-160zM346 651h412q166 0 254 90t88 236q0 145 -90 236.5t-252 91.5h-412v-654z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="722" 
d="M72 -512l78 348h174l-123 -348h-129zM166 0v1049h145v-242q60 248 342 248h25v-146h-31q-153 0 -239 -100.5t-86 -294.5v-514h-156z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="1384" 
d="M186 0v1434h592q153 0 264 -62t164.5 -164t53.5 -231q0 -151 -82.5 -267t-227.5 -161l312 -549h-183l-291 524h-442v-524h-160zM346 651h412q166 0 254 90t88 236q0 145 -90 236.5t-252 91.5h-412v-654zM614 1556l-237 259h151l162 -175l162 175h152l-238 -259h-152z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="722" 
d="M362 1192l-225 291h146l155 -203l156 203h145l-225 -291h-152zM166 0v1049h145v-242q60 248 342 248h25v-146h-31q-153 0 -239 -100.5t-86 -294.5v-514h-156z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="1333" 
d="M676 -29q-233 0 -384 116.5t-151 309.5v37h152v-33q0 -137 103 -220t280 -83q178 0 278.5 74.5t100.5 210.5q0 88 -52.5 147.5t-152.5 85.5l-406 107q-150 38 -217 123.5t-67 214.5q0 188 135 294.5t360 106.5q222 0 362 -113.5t140 -295.5v-35h-149v31
q0 129 -94.5 207.5t-258.5 78.5q-155 0 -248.5 -68.5t-93.5 -193.5q0 -89 43 -141.5t136 -77.5l415 -111q138 -37 219.5 -126t81.5 -222q0 -193 -149.5 -308.5t-382.5 -115.5zM729 1556h-156l148 259h188z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="1077" 
d="M610 1192h-153l166 291h188zM551 -25q-193 0 -313.5 89.5t-120.5 240.5v10h141v-6q0 -97 82.5 -157t212.5 -60q127 0 202.5 50.5t75.5 148.5q0 56 -34 94t-97 51l-311 72q-118 26 -182 95t-64 171q0 144 109.5 221.5t288.5 77.5q166 0 277.5 -92t111.5 -236v-10h-142v6
q0 98 -66.5 156.5t-182.5 58.5q-113 0 -182.5 -46.5t-69.5 -131.5q0 -109 110 -135l322 -76q127 -31 192.5 -95.5t65.5 -174.5q0 -139 -120.5 -230.5t-305.5 -91.5z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="1333" 
d="M676 -29q-233 0 -384 116.5t-151 309.5v37h152v-33q0 -137 103 -220t280 -83q178 0 278.5 74.5t100.5 210.5q0 88 -52.5 147.5t-152.5 85.5l-406 107q-150 38 -217 123.5t-67 214.5q0 188 135 294.5t360 106.5q222 0 362 -113.5t140 -295.5v-35h-149v31
q0 129 -94.5 207.5t-258.5 78.5q-155 0 -248.5 -68.5t-93.5 -193.5q0 -89 43 -141.5t136 -77.5l415 -111q138 -37 219.5 -126t81.5 -222q0 -193 -149.5 -308.5t-382.5 -115.5zM369 1556l237 259h152l237 -259h-151l-162 175l-162 -175h-151z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="1077" 
d="M248 1192l225 291h152l225 -291h-145l-156 203l-156 -203h-145zM551 -25q-193 0 -313.5 89.5t-120.5 240.5v10h141v-6q0 -97 82.5 -157t212.5 -60q127 0 202.5 50.5t75.5 148.5q0 56 -34 94t-97 51l-311 72q-118 26 -182 95t-64 171q0 144 109.5 221.5t288.5 77.5
q166 0 277.5 -92t111.5 -236v-10h-142v6q0 98 -66.5 156.5t-182.5 58.5q-113 0 -182.5 -46.5t-69.5 -131.5q0 -109 110 -135l322 -76q127 -31 192.5 -95.5t65.5 -174.5q0 -139 -120.5 -230.5t-305.5 -91.5z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="1333" 
d="M696 -381q-70 0 -135 12v90q55 -12 113 -12q102 0 102 55q0 50 -88 56l-94 6l33 147q-215 12 -350.5 126.5t-135.5 297.5v37h152v-33q0 -137 103 -220t280 -83q178 0 278.5 74.5t100.5 210.5q0 88 -52.5 147.5t-152.5 85.5l-406 107q-150 38 -217 123.5t-67 214.5
q0 188 135 294.5t360 106.5q222 0 362 -113.5t140 -295.5v-35h-149v31q0 129 -94.5 207.5t-258.5 78.5q-155 0 -248.5 -68.5t-93.5 -193.5q0 -89 43 -141.5t136 -77.5l415 -111q138 -37 219.5 -126t81.5 -222q0 -183 -134 -296.5t-349 -125.5l-10 -67l26 -2
q158 -11 158 -133q0 -69 -55.5 -110.5t-147.5 -41.5z" />
    <glyph glyph-name="Scedilla" unicode="&#xf6c1;" horiz-adv-x="1333" 
d="M696 -381q-70 0 -135 12v90q55 -12 113 -12q102 0 102 55q0 50 -88 56l-94 6l33 147q-215 12 -350.5 126.5t-135.5 297.5v37h152v-33q0 -137 103 -220t280 -83q178 0 278.5 74.5t100.5 210.5q0 88 -52.5 147.5t-152.5 85.5l-406 107q-150 38 -217 123.5t-67 214.5
q0 188 135 294.5t360 106.5q222 0 362 -113.5t140 -295.5v-35h-149v31q0 129 -94.5 207.5t-258.5 78.5q-155 0 -248.5 -68.5t-93.5 -193.5q0 -89 43 -141.5t136 -77.5l415 -111q138 -37 219.5 -126t81.5 -222q0 -183 -134 -296.5t-349 -125.5l-10 -67l26 -2
q158 -11 158 -133q0 -69 -55.5 -110.5t-147.5 -41.5z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="1077" 
d="M563 -381q-71 0 -135 12v90q56 -12 113 -12q102 0 102 55q0 50 -88 56l-94 6l33 151q-170 14 -273.5 101.5t-103.5 226.5v10h141v-6q0 -97 82.5 -157t212.5 -60q127 0 202.5 50.5t75.5 148.5q0 56 -34 94t-97 51l-311 72q-118 26 -182 95t-64 171q0 144 109.5 221.5
t288.5 77.5q166 0 277.5 -92t111.5 -236v-10h-142v6q0 98 -66.5 156.5t-182.5 58.5q-113 0 -182.5 -46.5t-69.5 -131.5q0 -109 110 -135l322 -76q127 -31 192.5 -95.5t65.5 -174.5q0 -131 -107.5 -220t-275.5 -100l-12 -71l26 -2q158 -11 158 -133q0 -69 -55.5 -110.5
t-147.5 -41.5z" />
    <glyph glyph-name="scedilla" unicode="&#xf6c2;" horiz-adv-x="1077" 
d="M563 -381q-71 0 -135 12v90q56 -12 113 -12q102 0 102 55q0 50 -88 56l-94 6l33 151q-170 14 -273.5 101.5t-103.5 226.5v10h141v-6q0 -97 82.5 -157t212.5 -60q127 0 202.5 50.5t75.5 148.5q0 56 -34 94t-97 51l-311 72q-118 26 -182 95t-64 171q0 144 109.5 221.5
t288.5 77.5q166 0 277.5 -92t111.5 -236v-10h-142v6q0 98 -66.5 156.5t-182.5 58.5q-113 0 -182.5 -46.5t-69.5 -131.5q0 -109 110 -135l322 -76q127 -31 192.5 -95.5t65.5 -174.5q0 -131 -107.5 -220t-275.5 -100l-12 -71l26 -2q158 -11 158 -133q0 -69 -55.5 -110.5
t-147.5 -41.5z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="1333" 
d="M676 -29q-233 0 -384 116.5t-151 309.5v37h152v-33q0 -137 103 -220t280 -83q178 0 278.5 74.5t100.5 210.5q0 88 -52.5 147.5t-152.5 85.5l-406 107q-150 38 -217 123.5t-67 214.5q0 188 135 294.5t360 106.5q222 0 362 -113.5t140 -295.5v-35h-149v31
q0 129 -94.5 207.5t-258.5 78.5q-155 0 -248.5 -68.5t-93.5 -193.5q0 -89 43 -141.5t136 -77.5l415 -111q138 -37 219.5 -126t81.5 -222q0 -193 -149.5 -308.5t-382.5 -115.5zM600 1556l-238 259h152l162 -175l162 175h151l-237 -259h-152z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="1077" 
d="M467 1192l-225 291h145l156 -203l155 203h146l-226 -291h-151zM551 -25q-193 0 -313.5 89.5t-120.5 240.5v10h141v-6q0 -97 82.5 -157t212.5 -60q127 0 202.5 50.5t75.5 148.5q0 56 -34 94t-97 51l-311 72q-118 26 -182 95t-64 171q0 144 109.5 221.5t288.5 77.5
q166 0 277.5 -92t111.5 -236v-10h-142v6q0 98 -66.5 156.5t-182.5 58.5q-113 0 -182.5 -46.5t-69.5 -131.5q0 -109 110 -135l322 -76q127 -31 192.5 -95.5t65.5 -174.5q0 -139 -120.5 -230.5t-305.5 -91.5z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x162;" horiz-adv-x="1153" 
d="M401 -512l78 348h174l-123 -348h-129zM496 0v1300h-439v134h1037v-134h-439v-1300h-159z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x21a;" horiz-adv-x="1153" 
d="M401 -512l78 348h174l-123 -348h-129zM496 0v1300h-439v134h1037v-134h-439v-1300h-159z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x163;" horiz-adv-x="763" 
d="M295 -512l78 348h174l-123 -348h-129zM668 0h-131q-285 0 -285 279v643h-178v127h178v301h156v-301h268v-127h-268v-643q0 -81 34.5 -116.5t120.5 -35.5h105v-127z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x21b;" horiz-adv-x="763" 
d="M295 -512l78 348h174l-123 -348h-129zM668 0h-131q-285 0 -285 279v643h-178v127h178v301h156v-301h268v-127h-268v-643q0 -81 34.5 -116.5t120.5 -35.5h105v-127z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="1153" 
d="M496 0v1300h-439v134h1037v-134h-439v-1300h-159zM500 1556l-238 259h152l161 -175l162 175h152l-238 -259h-151z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="763" 
d="M557 1208l53 287h156l-84 -287h-125zM668 0h-131q-285 0 -285 279v643h-178v127h178v301h156v-301h268v-127h-268v-643q0 -81 34.5 -116.5t120.5 -35.5h105v-127z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="1150" 
d="M193 623v127h303v550h-439v134h1037v-134h-439v-550h303v-127h-303v-623h-159v623h-303z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="763" 
d="M74 489v115h178v318h-178v127h178v301h156v-301h268v-127h-268v-318h229v-115h-229v-210q0 -81 34.5 -116.5t120.5 -35.5h105v-127h-131q-285 0 -285 279v210h-178z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="1449" 
d="M725 -29q-251 0 -401 143t-150 369v951h160v-965q0 -168 105 -266.5t286 -98.5t286 98.5t105 266.5v965h160v-951q0 -226 -150 -369t-401 -143zM451 1556l-107 19q33 229 209 229q54 0 129.5 -32t140 -63.5t92.5 -31.5q11 0 20 2t17 7.5t13.5 9.5t11.5 15t9 16t8 20t7 20
t7 23t6 22l104 -30q-12 -48 -26.5 -83t-37 -67t-56.5 -48.5t-78 -16.5q-46 0 -115.5 31.5t-138 63.5t-109.5 32q-49 0 -71.5 -35t-34.5 -103z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="1196" 
d="M1030 1049v-1049h-147v178q-38 -93 -128.5 -148t-222.5 -55q-172 0 -273 111.5t-101 300.5v662h155v-639q0 -140 64 -222t194 -82q135 0 219 84.5t84 241.5v617h156zM365 1194l-103 20q19 109 66 168.5t137 59.5q50 0 117.5 -32t125 -63.5t83.5 -31.5q11 0 20 2t16.5 8
t13.5 10t11.5 15t8.5 16t8 20t6.5 20t6 22.5t5.5 21.5l102 -23q-13 -50 -26 -84.5t-35 -69t-55.5 -52t-77.5 -17.5q-42 0 -103.5 32t-122 63.5t-100.5 31.5q-17 0 -31 -4t-24.5 -17t-16.5 -20t-13.5 -30.5t-9.5 -30t-9 -35.5z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="1449" 
d="M725 -29q-251 0 -401 143t-150 369v951h160v-965q0 -168 105 -266.5t286 -98.5t286 98.5t105 266.5v965h160v-951q0 -226 -150 -369t-401 -143zM395 1630v121h660v-121h-660z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="1196" 
d="M1030 1049v-1049h-147v178q-38 -93 -128.5 -148t-222.5 -55q-172 0 -273 111.5t-101 300.5v662h155v-639q0 -140 64 -222t194 -82q135 0 219 84.5t84 241.5v617h156zM301 1264v118h621v-118h-621z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="1449" 
d="M725 -29q-251 0 -401 143t-150 369v951h160v-965q0 -168 105 -266.5t286 -98.5t286 98.5t105 266.5v965h160v-951q0 -226 -150 -369t-401 -143zM725 1556q-152 0 -241 71t-89 188h121q0 -73 54.5 -116.5t154.5 -43.5t154.5 43.5t54.5 116.5h121q0 -117 -89 -188t-241 -71
z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="1196" 
d="M1030 1049v-1049h-147v178q-38 -93 -128.5 -148t-222.5 -55q-172 0 -273 111.5t-101 300.5v662h155v-639q0 -140 64 -222t194 -82q135 0 219 84.5t84 241.5v617h156zM610 1192q-149 0 -234 80t-85 211h115q0 -91 51.5 -143t152.5 -52t153 52t52 143h115
q0 -131 -85.5 -211t-234.5 -80z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="1449" 
d="M725 1556q-85 0 -136.5 50t-51.5 123t51.5 122.5t136.5 49.5t136.5 -49.5t51.5 -122.5t-51.5 -123t-136.5 -50zM725 1630q48 0 75 27.5t27 71.5t-27 71t-75 27t-75 -27t-27 -71t27 -71.5t75 -27.5zM725 -29q-251 0 -401 143t-150 369v951h160v-965q0 -168 105 -266.5
t286 -98.5t286 98.5t105 266.5v965h160v-951q0 -226 -150 -369t-401 -143z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="1196" 
d="M621 1192q-85 0 -137 49.5t-52 122.5t52 122.5t137 49.5t136.5 -49.5t51.5 -122.5t-51.5 -122.5t-136.5 -49.5zM621 1266q48 0 75 27t27 71t-27 71t-75 27t-75.5 -27t-27.5 -71t27.5 -71t75.5 -27zM1030 1049v-1049h-147v178q-38 -93 -128.5 -148t-222.5 -55
q-172 0 -273 111.5t-101 300.5v662h155v-639q0 -140 64 -222t194 -82q135 0 219 84.5t84 241.5v617h156z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="1449" 
d="M567 1556h-139l109 259h165zM997 1556h-139l109 259h166zM725 -29q-251 0 -401 143t-150 369v951h160v-965q0 -168 105 -266.5t286 -98.5t286 98.5t105 266.5v965h160v-951q0 -226 -150 -369t-401 -143z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="1196" 
d="M1030 1049v-1049h-147v178q-38 -93 -128.5 -148t-222.5 -55q-172 0 -273 111.5t-101 300.5v662h155v-639q0 -140 64 -222t194 -82q135 0 219 84.5t84 241.5v617h156zM496 1192h-142l127 291h168zM872 1192h-141l127 291h168z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="1449" 
d="M725 -29q-251 0 -401 143t-150 369v951h160v-965q0 -168 105 -266.5t286 -98.5t286 98.5t105 266.5v965h160v-951q0 -141 -62.5 -253.5t-175.5 -180.5q-38 -23 -63 -39t-60.5 -43.5t-56.5 -51t-37 -54t-16 -60.5q0 -38 24.5 -55.5t77.5 -17.5q39 0 66 6v-101
q-63 -14 -119 -14q-86 0 -135 43t-49 119q0 56 34 106t82 86q-20 -2 -61 -2z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="1196" 
d="M1030 1049v-1049q-170 -100 -170 -199q0 -73 103 -73q38 0 65 6v-101q-63 -14 -119 -14q-86 0 -135 43t-49 119q0 73 41.5 124t116.5 95v178q-38 -93 -128.5 -148t-222.5 -55q-172 0 -273 111.5t-101 300.5v662h155v-639q0 -140 64 -222t194 -82q135 0 219 84.5t84 241.5
v617h156z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="1888" 
d="M629 1556l237 259h152l237 -259h-151l-162 175l-162 -175h-151zM1817 1434l-340 -1434h-154l-379 1260l-379 -1260h-153l-340 1434h153l271 -1221l368 1221h160l369 -1221l270 1221h154z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="1556" 
d="M477 1192l225 291h152l225 -291h-145l-156 203l-155 -203h-146zM1491 1049l-287 -1049h-147l-279 885l-278 -885h-148l-286 1049h147l221 -863l271 863h147l270 -863l221 863h148z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="1236" 
d="M539 0v543l-510 891h168l421 -746l422 746h168l-510 -891v-543h-159zM305 1556l238 259h151l238 -259h-152l-162 175l-161 -175h-152z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="1040" 
d="M463 -356h-152l133 356l-391 1049h154l319 -891l308 891h153l-387 -1049zM231 1192l226 291h151l226 -291h-146l-156 203l-155 -203h-146z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="1236" 
d="M539 0v543l-510 891h168l421 -746l422 746h168l-510 -891v-543h-159zM291 1696q0 42 27 68t71 26t71 -26t27 -68t-27 -68t-71 -26t-71 26t-27 68zM745 1696q0 41 27.5 67.5t71.5 26.5t71 -26t27 -68t-27 -68t-71 -26t-71.5 26.5t-27.5 67.5z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="1275" 
d="M135 0v133l817 1167h-780v134h950v-134l-817 -1167h840v-133h-1010zM702 1556h-155l147 259h189z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="1021" 
d="M575 1192h-153l166 291h188zM117 0v117l618 809h-594v123h766v-117l-618 -809h626v-123h-798z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="1275" 
d="M553 1683q0 42 27 68.5t71 26.5t71.5 -26.5t27.5 -68.5q0 -41 -27.5 -67.5t-71.5 -26.5t-71 26t-27 68zM135 0v133l817 1167h-780v134h950v-134l-817 -1167h840v-133h-1010z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="1021" 
d="M117 0v117l618 809h-594v123h766v-117l-618 -809h626v-123h-798zM438 1325q0 41 27.5 67.5t71.5 26.5t71 -26t27 -68t-27 -68t-71 -26t-71.5 26.5t-27.5 67.5z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="1275" 
d="M135 0v133l817 1167h-780v134h950v-134l-817 -1167h840v-133h-1010zM586 1556l-238 259h152l162 -175l161 175h152l-238 -259h-151z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="1021" 
d="M463 1192l-225 291h145l156 -203l155 203h146l-226 -291h-151zM117 0v117l618 809h-594v123h766v-117l-618 -809h626v-123h-798z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="952" 
d="M61 -356v127h105q80 0 116 36t44 119l69 793h-196v127h207l28 323q23 265 268 265h168v-127h-135q-75 0 -107 -36.5t-40 -119.5l-27 -305h238v-127h-248l-72 -807q-11 -124 -80 -196t-204 -72h-134z" />
    <glyph glyph-name="Aringacute" unicode="&#x1fa;" horiz-adv-x="1392" 
d="M885 1530q0 -48 -24 -87t-66 -61l520 -1382h-158l-131 365h-661l-129 -365h-158l518 1382q-41 22 -64.5 61.5t-23.5 86.5q0 73 51.5 121.5t136.5 48.5q86 0 137.5 -48.5t51.5 -121.5zM696 1288l-286 -796h571zM801 1763h-158l115 209h188zM696 1434q49 0 76 26t27 70
t-27 70t-76 26t-75.5 -26t-26.5 -70t26.5 -70t75.5 -26z" />
    <glyph glyph-name="aringacute" unicode="&#x1fb;" horiz-adv-x="1179" 
d="M733 1571h-158l115 209h189zM637 1161q-85 0 -136.5 48.5t-51.5 121.5t51.5 121.5t136.5 48.5t136.5 -48.5t51.5 -121.5t-51.5 -121.5t-136.5 -48.5zM637 1235q49 0 75.5 26t26.5 70t-26.5 70t-75.5 26t-75.5 -26t-26.5 -70t26.5 -70t75.5 -26zM1026 0h-149v131
q-37 -61 -137.5 -108.5t-223.5 -47.5q-183 0 -290 92.5t-107 237.5q0 151 113.5 239.5t302.5 88.5q113 0 197.5 -38t137.5 -93v194q0 111 -70 182.5t-192 71.5q-192 0 -262 -157h-143q40 132 146.5 206t260.5 74q190 0 303 -108.5t113 -268.5v-696zM549 76q127 0 224 66.5
t97 164.5q0 95 -95 159t-218 64q-133 0 -212 -60t-79 -167q0 -106 79 -166.5t204 -60.5z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="2131" 
d="M1231 1556h-156l148 259h188zM1071 0v375h-625l-249 -375h-172l976 1434h1016v-134h-786v-507h727v-134h-727v-526h788v-133h-948zM532 504h539v807z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="1904" 
d="M1047 1192h-154l166 291h188zM1339 -25q-123 0 -228 57.5t-161 147.5q-44 -78 -154.5 -141.5t-254.5 -63.5q-191 0 -306.5 91.5t-115.5 240.5q0 147 115.5 237.5t300.5 90.5q113 0 201.5 -40t133.5 -87v176q0 118 -71 192t-193 74q-200 0 -266 -162h-143
q37 141 145.5 213t265.5 72q235 0 350 -178q59 84 154.5 131t220.5 47q145 0 247 -61t150 -162.5t48 -233.5v-155h-752v-37q0 -146 78.5 -238t234.5 -92q114 0 188 55t101 148h139q-26 -142 -145 -232t-283 -90zM553 78q126 0 221.5 66.5t95.5 162.5q0 92 -93 158.5
t-218 66.5q-134 0 -213.5 -61.5t-79.5 -165.5q0 -101 76 -164t211 -63zM1026 627v-47h606v49q0 147 -75.5 236t-223.5 89q-80 0 -140 -25t-95.5 -70t-52.5 -102.5t-19 -129.5z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="1490" 
d="M809 1556h-156l148 259h188zM1348 602q0 -131 -38.5 -244.5t-112 -200.5t-190 -136.5t-262.5 -49.5q-211 0 -358 103l-115 -176h-129l162 245q-162 174 -162 459v229q0 131 38.5 244.5t111.5 200.5t189.5 136.5t262.5 49.5q208 0 357 -102l117 176h129l-162 -248
q79 -86 120.5 -204t41.5 -253v-229zM745 104q89 0 163 25t125 69.5t86.5 106.5t52 134.5t16.5 154.5v246q0 191 -88 319l-635 -966q113 -89 280 -89zM303 594q0 -195 86 -320l635 967q-112 88 -279 88q-111 0 -197 -38t-139 -105.5t-79.5 -155t-26.5 -190.5v-246z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" 
d="M649 1192h-153l166 291h188zM584 -25q-143 0 -258 64l-93 -141h-118l131 202q-131 128 -131 338v172q0 130 54.5 234t162.5 166.5t252 62.5q146 0 254 -63l94 143h119l-133 -205q133 -130 133 -338v-172q0 -200 -125.5 -331.5t-341.5 -131.5zM901 608q0 134 -61 219
l-443 -675q77 -52 187 -52q151 0 234 93t83 247v168zM266 440q0 -136 60 -219l440 678q-77 49 -182 49q-152 0 -235 -93.5t-83 -246.5v-168z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="1333" 
d="M676 -29q-233 0 -384 116.5t-151 309.5v37h152v-33q0 -137 103 -220t280 -83q178 0 278.5 74.5t100.5 210.5q0 88 -52.5 147.5t-152.5 85.5l-406 107q-150 38 -217 123.5t-67 214.5q0 188 135 294.5t360 106.5q222 0 362 -113.5t140 -295.5v-35h-149v31
q0 129 -94.5 207.5t-258.5 78.5q-155 0 -248.5 -68.5t-93.5 -193.5q0 -89 43 -141.5t136 -77.5l415 -111q138 -37 219.5 -126t81.5 -222q0 -193 -149.5 -308.5t-382.5 -115.5zM520 -512l78 348h174l-123 -348h-129z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="1077" 
d="M551 -25q-193 0 -313.5 89.5t-120.5 240.5v10h141v-6q0 -97 82.5 -157t212.5 -60q127 0 202.5 50.5t75.5 148.5q0 56 -34 94t-97 51l-311 72q-118 26 -182 95t-64 171q0 144 109.5 221.5t288.5 77.5q166 0 277.5 -92t111.5 -236v-10h-142v6q0 98 -66.5 156.5t-182.5 58.5
q-113 0 -182.5 -46.5t-69.5 -131.5q0 -109 110 -135l322 -76q127 -31 192.5 -95.5t65.5 -174.5q0 -139 -120.5 -230.5t-305.5 -91.5zM389 -512l78 348h174l-123 -348h-129z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="974" 
d="M186 1192l226 291h151l225 -291h-145l-156 203l-155 -203h-146z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="974" 
d="M412 1192l-226 291h146l155 -203l156 203h145l-225 -291h-151z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="1011" 
d="M506 1192q-149 0 -234.5 80t-85.5 211h115q0 -91 52 -143t153 -52t153 52t52 143h114q0 -131 -85 -211t-234 -80z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="528" 
d="M166 1325q0 42 27 68t71 26t71 -26t27 -68t-27 -68t-71 -26t-71 26t-27 68z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="749" 
d="M375 1192q-85 0 -137 49.5t-52 122.5t52 122.5t137 49.5t136.5 -49.5t51.5 -122.5t-51.5 -122.5t-136.5 -49.5zM375 1266q48 0 75 27t27 71t-27 71t-75 27t-75.5 -27t-27.5 -71t27.5 -71t75.5 -27z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="677" 
d="M371 -381q-87 0 -136 43t-49 119q0 73 41.5 124t116.5 95h148q-170 -100 -170 -199q0 -38 24.5 -55.5t77.5 -17.5q38 0 65 6v-101q-63 -14 -118 -14z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="1099" 
d="M289 1194l-103 20q19 109 66 168.5t137 59.5q50 0 117.5 -32t125 -63.5t83.5 -31.5q11 0 20 2t16.5 8t13.5 10t11.5 15t8.5 16t8 20t6.5 20t6 22.5t5.5 21.5l102 -23q-13 -50 -26 -84.5t-35 -69t-55.5 -52t-77.5 -17.5q-42 0 -103.5 32t-122 63.5t-100.5 31.5
q-17 0 -31 -4t-24.5 -17t-16.5 -20t-13.5 -30.5t-9.5 -30t-9 -35.5z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="1044" 
d="M328 1192h-142l127 291h168zM705 1192h-142l127 291h168z" />
    <glyph glyph-name="uni0312" unicode="&#x312;" horiz-adv-x="632" 
d="M186 1192l109 291h151l-71 -291h-189z" />
    <glyph glyph-name="uni0315" unicode="&#x315;" horiz-adv-x="622" 
d="M186 1094l82 401h168l-123 -401h-127z" />
    <glyph glyph-name="uni0326" unicode="&#x326;" horiz-adv-x="624" 
d="M186 -512l78 348h174l-123 -348h-129z" />
    <glyph glyph-name="Omega" unicode="&#x3a9;" horiz-adv-x="1503" 
d="M1333 129v-129h-381v129q107 108 173.5 262t66.5 320v100q0 245 -110.5 382.5t-329.5 137.5t-330 -138t-111 -382v-100q0 -166 66.5 -320t173.5 -262v-129h-381v129h219q-99 103 -166 263t-67 325v100q0 142 38 259.5t111 203.5t187 134t260 48t260 -48t187 -134
t111 -203.5t38 -259.5v-100q0 -165 -67.5 -324.5t-166.5 -263.5h219z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="1497" 
d="M348 0v922h-223v127h1247v-127h-270v-633q0 -86 33.5 -124t117.5 -38h113v-127h-147q-137 0 -205 73.5t-68 211.5v637h-442v-922h-156z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="1888" 
d="M852 1556l-180 259h188l148 -259h-156zM1817 1434l-340 -1434h-154l-379 1260l-379 -1260h-153l-340 1434h153l271 -1221l368 1221h160l369 -1221l270 1221h154z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="1556" 
d="M1491 1049l-287 -1049h-147l-279 885l-278 -885h-148l-286 1049h147l221 -863l271 863h147l270 -863l221 863h148zM692 1192l-200 291h190l164 -291h-154z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="1888" 
d="M1040 1556h-155l147 259h189zM1817 1434l-340 -1434h-154l-379 1260l-379 -1260h-153l-340 1434h153l271 -1221l368 1221h160l369 -1221l270 1221h154z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="1556" 
d="M881 1192h-154l166 291h188zM1491 1049l-287 -1049h-147l-279 885l-278 -885h-148l-286 1049h147l221 -863l271 863h147l270 -863l221 863h148z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="1888" 
d="M1817 1434l-340 -1434h-154l-379 1260l-379 -1260h-153l-340 1434h153l271 -1221l368 1221h160l369 -1221l270 1221h154zM616 1696q0 41 27.5 67.5t71.5 26.5t71 -26t27 -68t-27 -68t-71 -26t-71.5 26.5t-27.5 67.5zM1071 1696q0 42 27 68t71 26t71.5 -26.5t27.5 -67.5
t-27.5 -67.5t-71.5 -26.5t-71 26t-27 68z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="1556" 
d="M475 1333q0 42 27 68t71 26t71.5 -26.5t27.5 -67.5t-27.5 -67.5t-71.5 -26.5t-71 26t-27 68zM885 1333q0 42 27 68t71 26t71 -26t27 -68t-27 -68t-71 -26t-71 26t-27 68zM1491 1049l-287 -1049h-147l-279 885l-278 -885h-148l-286 1049h147l221 -863l271 863h147
l270 -863l221 863h148z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="1236" 
d="M539 0v543l-510 891h168l421 -746l422 746h168l-510 -891v-543h-159zM537 1556l-181 259h189l147 -259h-155z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="1040" 
d="M463 -356h-152l133 356l-391 1049h154l319 -891l308 891h153l-387 -1049zM459 1192l-201 291h191l163 -291h-153z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="1253" 
d="M137 571v138h979v-138h-979z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="1865" 
d="M137 571v138h1592v-138h-1592z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="466" 
d="M92 975l141 459h132l-99 -459h-174z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="466" 
d="M102 975l99 459h174l-142 -459h-131z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="487" 
d="M72 -233l98 458h174l-141 -458h-131z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="890" 
d="M516 975l141 459h131l-98 -459h-174zM92 975l141 459h132l-99 -459h-174z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="890" 
d="M102 975l99 459h174l-142 -459h-131zM526 975l99 459h174l-142 -459h-131z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="911" 
d="M72 -233l98 458h174l-141 -458h-131zM496 -233l98 458h174l-141 -458h-131z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="1007" 
d="M440 -205v1166h-295v106h295v367h127v-367h295v-106h-295v-1166h-127z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="1028" 
d="M451 268v693h-295v106h295v367h127v-367h294v-106h-294v-693h294v-106h-294v-367h-127v367h-295v106h295z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="856" 
d="M428 967q111 0 176.5 -67t65.5 -171t-65 -170.5t-177 -66.5t-177 66.5t-65 170.5t65.5 171t176.5 67z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="1462" 
d="M139 88q0 44 29.5 72t75.5 28t75 -28t29 -72t-29.5 -72t-74.5 -28t-75 28t-30 72zM627 88q0 44 29 72t75 28t75.5 -28t29.5 -72t-30 -72t-75 -28t-74.5 28t-29.5 72zM1114 88q0 44 29.5 72t75.5 28t75 -28t29 -72t-29.5 -72t-74.5 -28t-75 28t-30 72z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="2521" 
d="M434 657q-141 0 -225 85.5t-84 222.5v178q0 137 84 222t225 85t225 -85t84 -222v-178q0 -137 -84 -222.5t-225 -85.5zM434 756q92 0 142.5 54.5t50.5 145.5v195q0 91 -50.5 146t-142.5 55t-142 -55t-50 -146v-195q0 -91 50 -145.5t142 -54.5zM1315 -16q-141 0 -225 85
t-84 222v178q0 137 84 222t225 85t225 -85t84 -222v-178q0 -137 -84 -222t-225 -85zM1315 82q92 0 142 55t50 146v194q0 91 -50 146t-142 55t-142.5 -55t-50.5 -146v-194q0 -91 50.5 -146t142.5 -55zM444 0l744 1434h119l-744 -1434h-119zM2097 -16q-141 0 -225 85t-84 222
v178q0 137 84 222t225 85t225 -85t84 -222v-178q0 -137 -84 -222t-225 -85zM2097 82q92 0 142.5 55t50.5 146v194q0 91 -50.5 146t-142.5 55t-142 -55t-50 -146v-194q0 -91 50 -146t142 -55z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="718" 
d="M426 123l-352 434l352 434h178l-352 -434l352 -434h-178z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="718" 
d="M645 557l-352 -434h-178l352 434l-352 434h178z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="759" 
d="M-51 0l743 1434h119l-743 -1434h-119z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="1372" 
d="M94 842v114h174v31q0 136 55.5 242t168 169.5t266.5 63.5q212 0 341.5 -126t137.5 -318h-139q-9 142 -100 231.5t-238 89.5q-166 0 -253 -92.5t-87 -253.5v-37h409v-114h-409v-250h409v-115h-409v-37q0 -161 87 -253.5t253 -92.5q147 0 238 90t100 232h139
q-8 -192 -137.5 -318.5t-341.5 -126.5q-154 0 -266.5 63.5t-168 169.5t-55.5 242v31h-174v115h174v250h-174z" />
    <glyph glyph-name="published" unicode="&#x2117;" horiz-adv-x="1765" 
d="M948 604h-192v-254h-94v725h286q112 0 181 -66.5t69 -168.5q0 -104 -68.5 -170t-181.5 -66zM756 682h176q84 0 128 44.5t44 111.5q0 68 -44.5 113.5t-127.5 45.5h-176v-315zM883 -25q-164 0 -304 59.5t-235 161t-148 236.5t-53 285t53 285t148 236t235 160.5t304 59.5
t304 -59.5t234.5 -160.5t147.5 -236t53 -285t-53 -285t-147.5 -236.5t-234.5 -161t-304 -59.5zM883 29q154 0 283.5 55.5t215.5 150t133.5 219t47.5 263.5t-47.5 263.5t-133.5 219t-215.5 150t-283.5 55.5t-283.5 -55.5t-215.5 -150t-133.5 -219t-47.5 -263.5t47.5 -263.5
t133.5 -219t215.5 -150t283.5 -55.5z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="1339" 
d="M291 844v522h-182v68h446v-68h-182v-522h-82zM668 844v590h75l189 -371l186 371h76v-590h-78v440l-145 -287h-80l-146 287v-440h-77z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" horiz-adv-x="1204" 
d="M592 -29q-212 0 -341.5 133t-129.5 328v64q0 200 122 327t324 127q131 0 228 -53t133 -123q-17 121 -62.5 216t-122 167t-193.5 110t-269 38h-33v129h43q170 0 305 -44.5t224.5 -122.5t148.5 -189.5t84.5 -239t25.5 -277.5v-94q0 -132 -58.5 -244.5t-171 -182
t-257.5 -69.5zM596 92q148 0 240 95t92 243v72q0 137 -95 232t-233 95q-153 0 -240.5 -89.5t-87.5 -241.5v-74q0 -154 89 -243t235 -89z" />
    <glyph glyph-name="Delta" unicode="&#x2206;" horiz-adv-x="1372" 
d="M760 1434l489 -1317v-117h-1126v117l489 1317h148zM268 127h836l-418 1165z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="1640" 
d="M387 -356v1663h-272v127h1411v-127h-273v-1663h-155v1663h-555v-1663h-156z" />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="1249" 
d="M156 -356v127l487 768l-487 768v127h958v-127h-790l481 -768l-481 -768h790v-127h-958z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="1224" 
d="M164 674v127h897v-127h-897z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="1445" 
d="M670 -205l-269 752h-268v135h367l258 -745l401 1599h144l-447 -1741h-186z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="1810" 
d="M1667 737q0 -160 -98.5 -257t-255.5 -97q-140 0 -248.5 70.5t-159.5 167.5q-51 -97 -159 -167.5t-248 -70.5q-157 0 -256 97t-99 257t99 257.5t256 97.5q140 0 248 -70.5t159 -167.5q51 97 159.5 167.5t248.5 70.5q157 0 255.5 -97.5t98.5 -257.5zM1292 971
q-207 0 -317 -234q110 -233 317 -233q108 0 174 64t66 169t-66 169.5t-174 64.5zM518 971q-107 0 -173 -64t-66 -170q0 -105 65.5 -169t173.5 -64q208 0 318 233q-20 43 -45.5 79.5t-64 74t-92 59t-116.5 21.5z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="897" 
d="M514 -55q0 -150 -71.5 -225.5t-223.5 -75.5h-104v127h71q96 0 136 36t40 127v1199q0 150 71.5 225.5t223.5 75.5h105v-127h-72q-96 0 -136 -36.5t-40 -127.5v-1198z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="1253" 
d="M862 401q-41 0 -87 13t-69 23.5t-79 37.5q-5 3 -22 11t-22 10.5t-19.5 9.5t-21 9.5t-19 7.5t-20 7t-19 5t-20 4.5t-19.5 2.5t-21 1q-39 0 -75.5 -17t-57.5 -36t-53 -54l-84 78q62 74 124.5 112t151.5 38q60 0 112.5 -18.5t123.5 -55.5q4 -2 23.5 -12.5t23 -12t20 -10
t22 -10t19.5 -7.5t21.5 -7.5l20 -4t23.5 -3.5t23 -1q54 0 94.5 19.5t79.5 60.5l74 -78q-44 -53 -109 -88t-139 -35zM862 813q-41 0 -87 13t-69 23.5t-79 37.5q-103 50 -145 60q-30 7 -58 7q-30 0 -56.5 -8t-50 -25t-39.5 -32t-40 -41l-84 78q61 73 124 111t152 38
q60 0 112.5 -18.5t123.5 -55.5q52 -28 70.5 -37t54.5 -19.5t71 -10.5q54 0 94.5 19.5t79.5 60.5l74 -78q-44 -53 -109 -88t-139 -35z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="1277" 
d="M195 920v122h528l125 250h137l-125 -250h223v-122h-284l-183 -365h467v-123h-528l-125 -250h-135l125 250h-225v123h286l181 365h-467z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="1251" 
d="M1067 334l-913 381v123l913 379v-138l-735 -303l735 -305v-137zM154 0v129h913v-129h-913z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="1251" 
d="M1098 715l-914 -381v137l736 305l-736 303v138l914 -379v-123zM1098 129v-129h-914v129h914z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" horiz-adv-x="1261" 
d="M1169 717l-471 -717h-135l-471 717l471 717h135zM631 127l379 590l-379 590l-379 -590z" />
    <glyph glyph-name="apple" unicode="&#xf8ff;" horiz-adv-x="1746" 
d="M227 334v741q0 152 103.5 255.5t255.5 103.5h717q152 0 255 -102.5t103 -254.5t-103 -254t-255 -102h-359v-387q0 -152 -103 -255.5t-255 -103.5t-255.5 103.5t-103.5 255.5z" />
    <glyph glyph-name="fi" unicode="&#xfb01;" horiz-adv-x="1228" 
d="M240 0v922h-154v127h154v139q0 155 73 231t230 76h147v-127h-135q-83 0 -121.5 -40t-38.5 -128v-151h258v-127h-258v-922h-155zM897 0v1049h156v-1049h-156zM877 1325q0 42 27 68t71 26t71 -26t27 -68t-27 -68t-71 -26t-71 26t-27 68z" />
    <glyph glyph-name="fl" unicode="&#xfb02;" horiz-adv-x="1228" 
d="M240 0v922h-154v127h154v139q0 155 73 231t230 76h147v-127h-135q-83 0 -121.5 -40t-38.5 -128v-151h258v-127h-258v-922h-155zM897 0v1495h156v-1495h-156z" />
    <glyph glyph-name="acute.case" horiz-adv-x="708" 
d="M342 1556h-156l148 259h188z" />
    <glyph glyph-name="breve.case" horiz-adv-x="1032" 
d="M516 1556q-152 0 -241 71t-89 188h121q0 -73 54.5 -116.5t154.5 -43.5t154.5 43.5t54.5 116.5h121q0 -117 -89 -188t-241 -71z" />
    <glyph glyph-name="caron.case" horiz-adv-x="999" 
d="M424 1556l-238 259h152l162 -175l162 175h151l-238 -259h-151z" />
    <glyph glyph-name="circumflex.case" horiz-adv-x="999" 
d="M186 1556l238 259h151l238 -259h-151l-162 175l-162 -175h-152z" />
    <glyph glyph-name="dieresis.case" horiz-adv-x="978" 
d="M164 1696q0 42 27 68t71 26t71 -26t27 -68t-27 -68t-71 -26t-71 26t-27 68zM618 1696q0 41 27.5 67.5t71.5 26.5t71 -26t27 -68t-27 -68t-71 -26t-71.5 26.5t-27.5 67.5z" />
    <glyph glyph-name="dotaccent.case" horiz-adv-x="528" 
d="M166 1683q0 42 27 68.5t71 26.5t71 -26.5t27 -68.5t-27 -68t-71 -26t-71 26t-27 68z" />
    <glyph glyph-name="grave.case" horiz-adv-x="708" 
d="M367 1556l-181 259h189l147 -259h-155z" />
    <glyph glyph-name="hungarumlaut.case" horiz-adv-x="1077" 
d="M326 1556h-140l109 259h166zM756 1556h-140l109 259h166z" />
    <glyph glyph-name="macron.case" horiz-adv-x="1032" 
d="M186 1630v121h660v-121h-660z" />
    <glyph glyph-name="ring.case" horiz-adv-x="749" 
d="M375 1556q-85 0 -137 50t-52 123t52 122.5t137 49.5t136.5 -49.5t51.5 -122.5t-51.5 -123t-136.5 -50zM375 1630q48 0 75 27.5t27 71.5t-27 71t-75 27t-75.5 -27t-27.5 -71t27.5 -71.5t75.5 -27.5z" />
    <glyph glyph-name="tilde.case" horiz-adv-x="1146" 
d="M293 1556l-107 19q33 229 209 229q54 0 129.5 -32t140.5 -63.5t93 -31.5q11 0 20 2t16.5 7.5t13.5 10t11.5 14.5t9 16t8 20t6.5 20.5t6.5 22.5t6.5 22l105 -30q-25 -101 -70 -158t-129 -57q-46 0 -115.5 31.5t-138 63.5t-109.5 32q-49 0 -71.5 -35t-34.5 -103z" />
    <glyph glyph-name="J.salt" horiz-adv-x="1241" 
d="M594 -29q-213 0 -342 113t-129 299v14h151v-16q0 -129 87 -203t231 -74q149 0 230 75t81 210v914h-674v131h834v-1049q0 -181 -130 -297.5t-339 -116.5z" />
    <glyph glyph-name="IJ.salt" horiz-adv-x="1794" 
d="M197 0v1434h159v-1434h-159zM1147 -29q-213 0 -342 113t-129 299v14h151v-16q0 -129 87 -203t231 -74q149 0 230 75t81 210v914h-674v131h834v-1049q0 -181 -130 -297.5t-339 -116.5z" />
    <glyph glyph-name="Jcircumflex.salt" horiz-adv-x="1239" 
d="M362 1556l238 259h152l237 -259h-151l-162 175l-162 -175h-152zM592 -29q-213 0 -342 113t-129 299v14h151v-16q0 -129 87 -203t231 -74q149 0 230 75t81 210v914h-674v131h834v-1049q0 -181 -130 -297.5t-339 -116.5z" />
    <glyph glyph-name="ornm01.ornm" horiz-adv-x="2138" 
d="M207 653v127h1136v-127h-1136zM1096 1434h180l717 -717l-717 -717h-180l716 717z" />
    <glyph glyph-name="ornm02.ornm" horiz-adv-x="2138" 
d="M145 717l717 717h180l-716 -717l716 -717h-180zM795 780h1136v-127h-1136v127z" />
    <glyph glyph-name="ornm03.ornm" horiz-adv-x="1767" 
d="M1362 1307h-1012l127 127h1022v-1012l-137 -129v1014zM1077 926l-776 -776l-94 96l774 776z" />
    <glyph glyph-name="ornm04.ornm" horiz-adv-x="1767" 
d="M1561 246l-97 -96l-776 776l98 96zM1288 1434l129 -127h-1014v-1014l-135 129v1012h1020z" />
    <glyph glyph-name="ornm05.ornm" horiz-adv-x="1767" 
d="M207 1188l94 96l776 -776l-96 -96zM477 0l-127 127h1012v1014l137 -129v-1012h-1022z" />
    <glyph glyph-name="ornm06.ornm" horiz-adv-x="1767" 
d="M403 127h1014l-129 -127h-1020v1012l135 129v-1014zM688 508l776 776l97 -96l-775 -776z" />
    <glyph glyph-name="ornm07.ornm" horiz-adv-x="1847" 
d="M993 -289h-139v1098h139v-1098zM1640 745v-180l-716 717l-717 -717v180l717 717z" />
    <glyph glyph-name="ornm08.ornm" horiz-adv-x="1847" 
d="M924 -135l716 717v-181l-716 -716l-717 716v181zM854 338v1096h139v-1096h-139z" />
    <glyph glyph-name="ornm09.ornm" horiz-adv-x="1871" 
d="M186 547v127h914v-127h-914zM999 1178h181l567 -568l-567 -567h-181l568 567z" />
    <glyph glyph-name="ornm10.ornm" horiz-adv-x="1871" 
d="M125 610l567 568h180l-567 -568l567 -567h-180zM772 674h914v-127h-914v127z" />
    <glyph glyph-name="ornm11.ornm" horiz-adv-x="1585" 
d="M274 -96l-88 88l645 645l89 -88zM403 926l127 123h807v-799l-131 -127v803h-803z" />
    <glyph glyph-name="ornm12.ornm" horiz-adv-x="1585" 
d="M248 1049h807l127 -123h-803v-803l-131 127v799zM754 637l645 -645l-88 -88l-645 645z" />
    <glyph glyph-name="ornm13.ornm" horiz-adv-x="1585" 
d="M1337 0h-807l-127 121h803v805l131 -129v-797zM831 412l-645 645l88 88l646 -645z" />
    <glyph glyph-name="ornm14.ornm" horiz-adv-x="1585" 
d="M1311 1145l88 -88l-645 -645l-88 88zM1182 121l-127 -121h-807v797l131 129v-805h803z" />
    <glyph glyph-name="ornm15.ornm" horiz-adv-x="1548" 
d="M774 1225l567 -570v-180l-567 569l-567 -569v180zM842 575v-837h-135v837h135z" />
    <glyph glyph-name="ornm16.ornm" horiz-adv-x="1548" 
d="M707 1204h135v-839h-135v839zM1341 465v-180l-567 -568l-567 568v180l567 -567z" />
    <glyph glyph-name="ornm17.ornm" horiz-adv-x="1609" 
d="M526 1520h43q17 -15 25.5 -43.5t14 -68t7.5 -48.5q18 -10 81.5 -25t84.5 -35q23 20 58.5 26t67.5 0.5t61 -17.5q-10 -17 -7 -21t27 -4q0 -27 12 -47q5 1 15.5 19t32.5 10q14 -13 49.5 -36t50.5 -40q31 0 65.5 23.5t49.5 27.5q6 -21 28.5 -27.5t47.5 -3t51.5 0t38.5 -20.5
q5 0 23 2t32.5 2t21.5 -4q-30 -35 -101.5 -62t-92.5 -41q-36 -22 -79 -78t-52 -65q-19 -18 -53.5 -43.5t-59.5 -48t-39 -47.5q0 -19 6 -64t4 -73q-11 -11 -40.5 -29.5t-44.5 -37t-17 -48.5q4 -4 15.5 -15t17 -19.5t8.5 -18.5q-16 -21 -19.5 -52.5t-1 -59t-4.5 -62.5
q13 -10 68.5 -46.5t86.5 -64t52 -61.5q26 26 56.5 9t37.5 -44q-11 -50 -4 -119h-985q0 312 13 477q-57 29 -72 84q3 4 27 21t26 36q11 39 -10 105.5t-23 81.5q-3 32 0.5 121.5t-4.5 130.5q-2 28 -23 80.5t-24 72.5q-1 30 5 80t4 85t-21 59q60 2 181 -2.5t177 -1.5
q5 9 6 22.5t-0.5 36t-1.5 31.5z" />
    <glyph glyph-name="ornm18.ornm" horiz-adv-x="1894" 
d="M141 227q-13 -1 -16.5 10t6.5 17q83 24 135 70l33 30q34 49 57 107q4 12 17 48t16 54l-4 119q-6 75 -6 84q0 49 17 90t38.5 66.5t57 48.5t55 32t49.5 21q7 3 10 4q139 47 283 47q47 0 141 -8q31 -4 62 -6t46 -2h15q18 0 43 4q12 3 23.5 14.5t19.5 24t17 33t13 31.5t11 31
l6 20q19 65 84 139q2 4 5 37t9 41q9 2 31.5 0.5t26.5 -4.5q2 0 57 -11q25 0 29 -6q4 -3 9.5 -10.5t10.5 -11.5q15 -3 25 -13q2 -6 26 -30q4 -4 10.5 -16t6.5 -17q0 -11 51 -43q11 -8 74 -47q34 -17 28 -37q-5 -13 -41 -25t-55 -12t-49 6q-27 7 -63 8t-50 -6q-6 -1 -17 -26
t-13 -35q-5 -58 -15 -103q-11 -43 -29.5 -105t-21.5 -75q-2 -10 -5 -34t-7 -45.5t-10 -39.5q-7 -13 -17 -29t-15 -25t-7 -15q-11 -13 -27 -112.5t-20 -149.5q-10 -101 -7 -224q0 -17 20 -41.5t36 -21.5q11 0 19.5 -3t10.5 -6l3 -3q3 -4 2 -18t-7 -17l-8 -4l-41 2
q2 -1 5 -3.5t7 -8t1 -9.5q-7 -7 -9 -8h-86q-10 3 -24 31q-1 2 -4.5 18.5t-8 37t-6.5 26.5q-3 9 -11 14t-9 10q0 4 3 26.5t3 49.5q0 4 -6 10.5t-10 14.5q-2 6 -2 47v90t-2 69q0 7 -5 51.5t-6 61.5q-4 17 1 32t5 17q-1 5 -5 7.5t-11 3t-13.5 0t-16 -1.5t-15.5 -1
q-35 0 -91.5 18t-103.5 52q-24 18 -54 52t-52.5 54.5t-44.5 20.5t-46.5 -10t-37.5 -25q-16 -14 -33 -71q-2 -8 -2 -43q-3 -30 -20 -99q-3 -13 -29 -88q-4 -12 -17 -31.5t-23 -33.5l-11 -13q-8 -11 -21 -24.5t-21 -22.5t-9 -14q-6 -9 -11.5 -24t-7.5 -26l-2 -10
q-2 -110 0 -126q0 -12 3 -22t3 -11q6 -3 14 -10t15 -11q2 0 7.5 -1t8.5 -2t6.5 -3.5t4.5 -5.5q2 -8 -0.5 -18t-8.5 -11h-53q-13 4 -29 15l-20 18q-2 2 -5 33t-5.5 64t-3.5 36l-1 8q-1 8 -1.5 19.5t-1.5 25.5t-0.5 29.5t2.5 26.5q0 18 -7 18q-3 0 -10 -10t-19 -24.5
t-16 -20.5q-4 -9 -24 -72.5t-21 -79.5v-84l17 -16q16 -7 16 -21q0 -15 -14 -24h-39q-16 0 -39 29q-6 11 0 104q0 9 2 30t2 38q0 45 4 77q-56 -41 -119 -47z" />
    <hkern u1="&#x20;" u2="&#x1ef3;" k="14" />
    <hkern u1="&#x20;" u2="&#x1ef2;" k="25" />
    <hkern u1="&#x20;" u2="&#x1e84;" k="25" />
    <hkern u1="&#x20;" u2="&#x1e82;" k="25" />
    <hkern u1="&#x20;" u2="&#x1e80;" k="25" />
    <hkern u1="&#x20;" u2="&#x178;" k="25" />
    <hkern u1="&#x20;" u2="&#x177;" k="14" />
    <hkern u1="&#x20;" u2="&#x176;" k="25" />
    <hkern u1="&#x20;" u2="&#x174;" k="25" />
    <hkern u1="&#x20;" u2="&#x164;" k="41" />
    <hkern u1="&#x20;" u2="&#x162;" k="41" />
    <hkern u1="&#x20;" u2="&#xff;" k="14" />
    <hkern u1="&#x20;" u2="&#xfd;" k="14" />
    <hkern u1="&#x20;" u2="&#xdd;" k="25" />
    <hkern u1="&#x20;" u2="y" k="14" />
    <hkern u1="&#x20;" u2="Y" k="25" />
    <hkern u1="&#x20;" u2="W" k="25" />
    <hkern u1="&#x20;" u2="T" k="41" />
    <hkern u1="&#x20;" u2="v" k="6" />
    <hkern u1="&#x20;" u2="V" k="35" />
    <hkern u1="&#x21;" g2="Jcircumflex.salt" k="10" />
    <hkern u1="&#x21;" g2="J.salt" k="10" />
    <hkern u1="&#x21;" u2="&#x2039;" k="6" />
    <hkern u1="&#x21;" u2="&#x1ef2;" k="10" />
    <hkern u1="&#x21;" u2="&#x178;" k="10" />
    <hkern u1="&#x21;" u2="&#x176;" k="10" />
    <hkern u1="&#x21;" u2="&#x164;" k="-10" />
    <hkern u1="&#x21;" u2="&#x162;" k="-10" />
    <hkern u1="&#x21;" u2="&#x134;" k="10" />
    <hkern u1="&#x21;" u2="&#xdd;" k="10" />
    <hkern u1="&#x21;" u2="&#xab;" k="6" />
    <hkern u1="&#x21;" u2="&#x7d;" k="10" />
    <hkern u1="&#x21;" u2="]" k="10" />
    <hkern u1="&#x21;" u2="Y" k="10" />
    <hkern u1="&#x21;" u2="T" k="-10" />
    <hkern u1="&#x21;" u2="J" k="10" />
    <hkern u1="&#x21;" u2="&#x29;" k="10" />
    <hkern u1="&#x21;" u2="\" k="6" />
    <hkern u1="&#x21;" u2="X" k="-10" />
    <hkern u1="&#x21;" u2="&#x37;" k="4" />
    <hkern u1="&#x21;" u2="&#x36;" k="6" />
    <hkern u1="&#x21;" u2="&#x35;" k="10" />
    <hkern u1="&#x21;" u2="&#x2f;" k="12" />
    <hkern u1="&#x23;" u2="&#x203a;" k="14" />
    <hkern u1="&#x23;" u2="&#x2026;" k="51" />
    <hkern u1="&#x23;" u2="&#x201e;" k="57" />
    <hkern u1="&#x23;" u2="&#x201c;" k="-10" />
    <hkern u1="&#x23;" u2="&#x201a;" k="57" />
    <hkern u1="&#x23;" u2="&#x2018;" k="-10" />
    <hkern u1="&#x23;" u2="&#xbb;" k="14" />
    <hkern u1="&#x23;" u2="&#x7d;" k="35" />
    <hkern u1="&#x23;" u2="]" k="35" />
    <hkern u1="&#x23;" u2="&#x2e;" k="51" />
    <hkern u1="&#x23;" u2="&#x2c;" k="57" />
    <hkern u1="&#x23;" u2="&#x29;" k="35" />
    <hkern u1="&#x23;" u2="&#x2122;" k="20" />
    <hkern u1="&#x23;" u2="\" k="47" />
    <hkern u1="&#x23;" u2="&#x40;" k="16" />
    <hkern u1="&#x23;" u2="&#x34;" k="-6" />
    <hkern u1="&#x23;" u2="&#x31;" k="-6" />
    <hkern u1="&#x23;" u2="&#x2f;" k="96" />
    <hkern u1="&#x24;" u2="&#x201e;" k="27" />
    <hkern u1="&#x24;" u2="&#x201d;" k="35" />
    <hkern u1="&#x24;" u2="&#x201c;" k="27" />
    <hkern u1="&#x24;" u2="&#x201a;" k="27" />
    <hkern u1="&#x24;" u2="&#x2019;" k="35" />
    <hkern u1="&#x24;" u2="&#x2018;" k="27" />
    <hkern u1="&#x24;" u2="&#x149;" k="35" />
    <hkern u1="&#x24;" u2="&#x7d;" k="16" />
    <hkern u1="&#x24;" u2="]" k="16" />
    <hkern u1="&#x24;" u2="&#x2c;" k="27" />
    <hkern u1="&#x24;" u2="&#x29;" k="16" />
    <hkern u1="&#x24;" u2="&#x2122;" k="20" />
    <hkern u1="&#x24;" u2="\" k="72" />
    <hkern u1="&#x24;" u2="&#x37;" k="10" />
    <hkern u1="&#x24;" u2="&#x2f;" k="55" />
    <hkern u1="&#x25;" u2="&#x2122;" k="125" />
    <hkern u1="&#x25;" u2="&#xbf;" k="-27" />
    <hkern u1="&#x25;" u2="&#xae;" k="47" />
    <hkern u1="&#x25;" u2="\" k="129" />
    <hkern u1="&#x25;" u2="&#x3f;" k="27" />
    <hkern u1="&#x25;" u2="&#x39;" k="14" />
    <hkern u1="&#x25;" u2="&#x38;" k="-6" />
    <hkern u1="&#x25;" u2="&#x37;" k="-6" />
    <hkern u1="&#x25;" u2="&#x36;" k="-12" />
    <hkern u1="&#x25;" u2="&#x35;" k="-12" />
    <hkern u1="&#x25;" u2="&#x34;" k="-12" />
    <hkern u1="&#x25;" u2="&#x33;" k="-12" />
    <hkern u1="&#x25;" u2="&#x32;" k="-12" />
    <hkern u1="&#x25;" u2="&#x31;" k="6" />
    <hkern u1="&#x25;" u2="&#x30;" k="-16" />
    <hkern u1="&#x25;" u2="&#x2f;" k="14" />
    <hkern u1="&#x25;" u2="&#x2a;" k="86" />
    <hkern u1="&#x26;" u2="&#x2030;" k="27" />
    <hkern u1="&#x26;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x26;" u2="&#x201e;" k="-20" />
    <hkern u1="&#x26;" u2="&#x201d;" k="113" />
    <hkern u1="&#x26;" u2="&#x201c;" k="102" />
    <hkern u1="&#x26;" u2="&#x201a;" k="-20" />
    <hkern u1="&#x26;" u2="&#x2019;" k="113" />
    <hkern u1="&#x26;" u2="&#x2018;" k="102" />
    <hkern u1="&#x26;" u2="&#x2014;" k="25" />
    <hkern u1="&#x26;" u2="&#x2013;" k="25" />
    <hkern u1="&#x26;" u2="&#x1ef3;" k="35" />
    <hkern u1="&#x26;" u2="&#x1ef2;" k="150" />
    <hkern u1="&#x26;" u2="&#x1e85;" k="14" />
    <hkern u1="&#x26;" u2="&#x1e84;" k="72" />
    <hkern u1="&#x26;" u2="&#x1e83;" k="14" />
    <hkern u1="&#x26;" u2="&#x1e82;" k="72" />
    <hkern u1="&#x26;" u2="&#x1e81;" k="14" />
    <hkern u1="&#x26;" u2="&#x1e80;" k="72" />
    <hkern u1="&#x26;" u2="&#x1fa;" k="-20" />
    <hkern u1="&#x26;" u2="&#x178;" k="150" />
    <hkern u1="&#x26;" u2="&#x177;" k="35" />
    <hkern u1="&#x26;" u2="&#x176;" k="150" />
    <hkern u1="&#x26;" u2="&#x175;" k="14" />
    <hkern u1="&#x26;" u2="&#x174;" k="72" />
    <hkern u1="&#x26;" u2="&#x164;" k="104" />
    <hkern u1="&#x26;" u2="&#x162;" k="104" />
    <hkern u1="&#x26;" u2="&#x149;" k="113" />
    <hkern u1="&#x26;" u2="&#x104;" k="-20" />
    <hkern u1="&#x26;" u2="&#x102;" k="-20" />
    <hkern u1="&#x26;" u2="&#x100;" k="-20" />
    <hkern u1="&#x26;" u2="&#xff;" k="35" />
    <hkern u1="&#x26;" u2="&#xfd;" k="35" />
    <hkern u1="&#x26;" u2="&#xdd;" k="150" />
    <hkern u1="&#x26;" u2="&#xc5;" k="-20" />
    <hkern u1="&#x26;" u2="&#xc4;" k="-20" />
    <hkern u1="&#x26;" u2="&#xc3;" k="-20" />
    <hkern u1="&#x26;" u2="&#xc2;" k="-20" />
    <hkern u1="&#x26;" u2="&#xc1;" k="-20" />
    <hkern u1="&#x26;" u2="&#xc0;" k="-20" />
    <hkern u1="&#x26;" u2="&#x7d;" k="35" />
    <hkern u1="&#x26;" u2="y" k="35" />
    <hkern u1="&#x26;" u2="w" k="14" />
    <hkern u1="&#x26;" u2="]" k="35" />
    <hkern u1="&#x26;" u2="Y" k="150" />
    <hkern u1="&#x26;" u2="W" k="72" />
    <hkern u1="&#x26;" u2="T" k="104" />
    <hkern u1="&#x26;" u2="A" k="-20" />
    <hkern u1="&#x26;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x26;" u2="&#x2d;" k="25" />
    <hkern u1="&#x26;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x26;" u2="&#x29;" k="35" />
    <hkern u1="&#x26;" u2="&#x25;" k="27" />
    <hkern u1="&#x26;" u2="&#xa5;" k="14" />
    <hkern u1="&#x26;" u2="&#xa2;" k="20" />
    <hkern u1="&#x26;" u2="v" k="35" />
    <hkern u1="&#x26;" u2="\" k="100" />
    <hkern u1="&#x26;" u2="X" k="-66" />
    <hkern u1="&#x26;" u2="V" k="104" />
    <hkern u1="&#x26;" u2="&#x40;" k="10" />
    <hkern u1="&#x26;" u2="&#x3f;" k="63" />
    <hkern u1="&#x26;" u2="&#x39;" k="27" />
    <hkern u1="&#x26;" u2="&#x37;" k="20" />
    <hkern u1="&#x26;" u2="&#x35;" k="10" />
    <hkern u1="&#x26;" u2="&#x31;" k="20" />
    <hkern u1="&#x26;" u2="&#x2a;" k="76" />
    <hkern u1="&#x28;" u2="&#x2122;" k="20" />
    <hkern u1="&#x28;" u2="&#x20ac;" k="74" />
    <hkern u1="&#x28;" u2="&#xbf;" k="35" />
    <hkern u1="&#x28;" u2="&#xae;" k="41" />
    <hkern u1="&#x28;" u2="&#xa5;" k="20" />
    <hkern u1="&#x28;" u2="&#xa2;" k="41" />
    <hkern u1="&#x28;" u2="x" k="4" />
    <hkern u1="&#x28;" u2="v" k="25" />
    <hkern u1="&#x28;" u2="j" k="-45" />
    <hkern u1="&#x28;" u2="i" k="10" />
    <hkern u1="&#x28;" u2="\" k="20" />
    <hkern u1="&#x28;" u2="&#x40;" k="41" />
    <hkern u1="&#x28;" u2="&#x3f;" k="16" />
    <hkern u1="&#x28;" u2="&#x39;" k="45" />
    <hkern u1="&#x28;" u2="&#x38;" k="45" />
    <hkern u1="&#x28;" u2="&#x37;" k="10" />
    <hkern u1="&#x28;" u2="&#x36;" k="47" />
    <hkern u1="&#x28;" u2="&#x35;" k="27" />
    <hkern u1="&#x28;" u2="&#x34;" k="57" />
    <hkern u1="&#x28;" u2="&#x33;" k="16" />
    <hkern u1="&#x28;" u2="&#x32;" k="8" />
    <hkern u1="&#x28;" u2="&#x31;" k="16" />
    <hkern u1="&#x28;" u2="&#x30;" k="33" />
    <hkern u1="&#x28;" u2="&#x2a;" k="66" />
    <hkern u1="&#x28;" u2="&#x26;" k="45" />
    <hkern u1="&#x28;" u2="&#x24;" k="16" />
    <hkern u1="&#x28;" u2="&#x23;" k="45" />
    <hkern u1="&#x28;" u2="&#x21;" k="10" />
    <hkern u1="&#x29;" u2="&#x2122;" k="37" />
    <hkern u1="&#x29;" u2="\" k="61" />
    <hkern u1="&#x29;" u2="&#x2f;" k="45" />
    <hkern u1="&#x29;" u2="&#x2a;" k="14" />
    <hkern u1="&#x2a;" g2="Jcircumflex.salt" k="102" />
    <hkern u1="&#x2a;" g2="J.salt" k="102" />
    <hkern u1="&#x2a;" u2="&#x203a;" k="20" />
    <hkern u1="&#x2a;" u2="&#x2039;" k="47" />
    <hkern u1="&#x2a;" u2="&#x2026;" k="205" />
    <hkern u1="&#x2a;" u2="&#x201e;" k="217" />
    <hkern u1="&#x2a;" u2="&#x201d;" k="6" />
    <hkern u1="&#x2a;" u2="&#x201c;" k="4" />
    <hkern u1="&#x2a;" u2="&#x201a;" k="217" />
    <hkern u1="&#x2a;" u2="&#x2019;" k="6" />
    <hkern u1="&#x2a;" u2="&#x2018;" k="4" />
    <hkern u1="&#x2a;" u2="&#x2014;" k="25" />
    <hkern u1="&#x2a;" u2="&#x2013;" k="25" />
    <hkern u1="&#x2a;" u2="&#x1ef2;" k="25" />
    <hkern u1="&#x2a;" u2="&#x1e84;" k="14" />
    <hkern u1="&#x2a;" u2="&#x1e82;" k="14" />
    <hkern u1="&#x2a;" u2="&#x1e80;" k="14" />
    <hkern u1="&#x2a;" u2="&#x219;" k="41" />
    <hkern u1="&#x2a;" u2="&#x1ff;" k="31" />
    <hkern u1="&#x2a;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x2a;" u2="&#x1fd;" k="72" />
    <hkern u1="&#x2a;" u2="&#x1fb;" k="72" />
    <hkern u1="&#x2a;" u2="&#x1fa;" k="236" />
    <hkern u1="&#x2a;" u2="&#x17d;" k="35" />
    <hkern u1="&#x2a;" u2="&#x17b;" k="35" />
    <hkern u1="&#x2a;" u2="&#x179;" k="35" />
    <hkern u1="&#x2a;" u2="&#x178;" k="25" />
    <hkern u1="&#x2a;" u2="&#x176;" k="25" />
    <hkern u1="&#x2a;" u2="&#x174;" k="14" />
    <hkern u1="&#x2a;" u2="&#x161;" k="41" />
    <hkern u1="&#x2a;" u2="&#x160;" k="31" />
    <hkern u1="&#x2a;" u2="&#x15f;" k="41" />
    <hkern u1="&#x2a;" u2="&#x15e;" k="31" />
    <hkern u1="&#x2a;" u2="&#x15d;" k="41" />
    <hkern u1="&#x2a;" u2="&#x15c;" k="31" />
    <hkern u1="&#x2a;" u2="&#x15b;" k="41" />
    <hkern u1="&#x2a;" u2="&#x15a;" k="31" />
    <hkern u1="&#x2a;" u2="&#x152;" k="10" />
    <hkern u1="&#x2a;" u2="&#x151;" k="31" />
    <hkern u1="&#x2a;" u2="&#x150;" k="10" />
    <hkern u1="&#x2a;" u2="&#x14f;" k="31" />
    <hkern u1="&#x2a;" u2="&#x14e;" k="10" />
    <hkern u1="&#x2a;" u2="&#x14d;" k="31" />
    <hkern u1="&#x2a;" u2="&#x14c;" k="10" />
    <hkern u1="&#x2a;" u2="&#x149;" k="6" />
    <hkern u1="&#x2a;" u2="&#x134;" k="399" />
    <hkern u1="&#x2a;" u2="&#x123;" k="31" />
    <hkern u1="&#x2a;" u2="&#x122;" k="10" />
    <hkern u1="&#x2a;" u2="&#x121;" k="31" />
    <hkern u1="&#x2a;" u2="&#x120;" k="10" />
    <hkern u1="&#x2a;" u2="&#x11f;" k="31" />
    <hkern u1="&#x2a;" u2="&#x11e;" k="10" />
    <hkern u1="&#x2a;" u2="&#x11d;" k="31" />
    <hkern u1="&#x2a;" u2="&#x11c;" k="10" />
    <hkern u1="&#x2a;" u2="&#x11b;" k="31" />
    <hkern u1="&#x2a;" u2="&#x119;" k="31" />
    <hkern u1="&#x2a;" u2="&#x117;" k="31" />
    <hkern u1="&#x2a;" u2="&#x115;" k="31" />
    <hkern u1="&#x2a;" u2="&#x113;" k="31" />
    <hkern u1="&#x2a;" u2="&#x111;" k="31" />
    <hkern u1="&#x2a;" u2="&#x10f;" k="31" />
    <hkern u1="&#x2a;" u2="&#x10d;" k="31" />
    <hkern u1="&#x2a;" u2="&#x10c;" k="10" />
    <hkern u1="&#x2a;" u2="&#x10b;" k="31" />
    <hkern u1="&#x2a;" u2="&#x10a;" k="10" />
    <hkern u1="&#x2a;" u2="&#x109;" k="31" />
    <hkern u1="&#x2a;" u2="&#x108;" k="10" />
    <hkern u1="&#x2a;" u2="&#x107;" k="31" />
    <hkern u1="&#x2a;" u2="&#x106;" k="10" />
    <hkern u1="&#x2a;" u2="&#x105;" k="72" />
    <hkern u1="&#x2a;" u2="&#x104;" k="236" />
    <hkern u1="&#x2a;" u2="&#x103;" k="72" />
    <hkern u1="&#x2a;" u2="&#x102;" k="236" />
    <hkern u1="&#x2a;" u2="&#x101;" k="72" />
    <hkern u1="&#x2a;" u2="&#x100;" k="236" />
    <hkern u1="&#x2a;" u2="&#xf8;" k="31" />
    <hkern u1="&#x2a;" u2="&#xf6;" k="31" />
    <hkern u1="&#x2a;" u2="&#xf5;" k="31" />
    <hkern u1="&#x2a;" u2="&#xf4;" k="31" />
    <hkern u1="&#x2a;" u2="&#xf3;" k="31" />
    <hkern u1="&#x2a;" u2="&#xf2;" k="31" />
    <hkern u1="&#x2a;" u2="&#xeb;" k="31" />
    <hkern u1="&#x2a;" u2="&#xea;" k="31" />
    <hkern u1="&#x2a;" u2="&#xe9;" k="31" />
    <hkern u1="&#x2a;" u2="&#xe8;" k="31" />
    <hkern u1="&#x2a;" u2="&#xe7;" k="31" />
    <hkern u1="&#x2a;" u2="&#xe6;" k="72" />
    <hkern u1="&#x2a;" u2="&#xe5;" k="72" />
    <hkern u1="&#x2a;" u2="&#xe4;" k="72" />
    <hkern u1="&#x2a;" u2="&#xe3;" k="72" />
    <hkern u1="&#x2a;" u2="&#xe2;" k="72" />
    <hkern u1="&#x2a;" u2="&#xe1;" k="72" />
    <hkern u1="&#x2a;" u2="&#xe0;" k="72" />
    <hkern u1="&#x2a;" u2="&#xdd;" k="25" />
    <hkern u1="&#x2a;" u2="&#xd8;" k="10" />
    <hkern u1="&#x2a;" u2="&#xd6;" k="10" />
    <hkern u1="&#x2a;" u2="&#xd5;" k="10" />
    <hkern u1="&#x2a;" u2="&#xd4;" k="10" />
    <hkern u1="&#x2a;" u2="&#xd3;" k="10" />
    <hkern u1="&#x2a;" u2="&#xd2;" k="10" />
    <hkern u1="&#x2a;" u2="&#xc7;" k="10" />
    <hkern u1="&#x2a;" u2="&#xc5;" k="236" />
    <hkern u1="&#x2a;" u2="&#xc4;" k="236" />
    <hkern u1="&#x2a;" u2="&#xc3;" k="236" />
    <hkern u1="&#x2a;" u2="&#xc2;" k="236" />
    <hkern u1="&#x2a;" u2="&#xc1;" k="236" />
    <hkern u1="&#x2a;" u2="&#xc0;" k="236" />
    <hkern u1="&#x2a;" u2="&#xbb;" k="20" />
    <hkern u1="&#x2a;" u2="&#xab;" k="47" />
    <hkern u1="&#x2a;" u2="&#x7d;" k="57" />
    <hkern u1="&#x2a;" u2="&#x7b;" k="20" />
    <hkern u1="&#x2a;" u2="s" k="41" />
    <hkern u1="&#x2a;" u2="q" k="31" />
    <hkern u1="&#x2a;" u2="o" k="31" />
    <hkern u1="&#x2a;" u2="g" k="31" />
    <hkern u1="&#x2a;" u2="e" k="31" />
    <hkern u1="&#x2a;" u2="d" k="31" />
    <hkern u1="&#x2a;" u2="c" k="31" />
    <hkern u1="&#x2a;" u2="a" k="72" />
    <hkern u1="&#x2a;" u2="]" k="57" />
    <hkern u1="&#x2a;" u2="[" k="20" />
    <hkern u1="&#x2a;" u2="Z" k="35" />
    <hkern u1="&#x2a;" u2="Y" k="25" />
    <hkern u1="&#x2a;" u2="W" k="14" />
    <hkern u1="&#x2a;" u2="S" k="31" />
    <hkern u1="&#x2a;" u2="Q" k="10" />
    <hkern u1="&#x2a;" u2="O" k="10" />
    <hkern u1="&#x2a;" u2="J" k="399" />
    <hkern u1="&#x2a;" u2="G" k="10" />
    <hkern u1="&#x2a;" u2="C" k="10" />
    <hkern u1="&#x2a;" u2="A" k="236" />
    <hkern u1="&#x2a;" u2="&#x3b;" k="35" />
    <hkern u1="&#x2a;" u2="&#x3a;" k="35" />
    <hkern u1="&#x2a;" u2="&#x2e;" k="205" />
    <hkern u1="&#x2a;" u2="&#x2d;" k="25" />
    <hkern u1="&#x2a;" u2="&#x2c;" k="217" />
    <hkern u1="&#x2a;" u2="&#x29;" k="57" />
    <hkern u1="&#x2a;" u2="&#x28;" k="20" />
    <hkern u1="&#x2a;" u2="&#xbf;" k="162" />
    <hkern u1="&#x2a;" u2="&#xa5;" k="20" />
    <hkern u1="&#x2a;" u2="&#xa3;" k="61" />
    <hkern u1="&#x2a;" u2="&#xa1;" k="35" />
    <hkern u1="&#x2a;" u2="x" k="10" />
    <hkern u1="&#x2a;" u2="\" k="20" />
    <hkern u1="&#x2a;" u2="X" k="72" />
    <hkern u1="&#x2a;" u2="V" k="10" />
    <hkern u1="&#x2a;" u2="&#x40;" k="61" />
    <hkern u1="&#x2a;" u2="&#x38;" k="55" />
    <hkern u1="&#x2a;" u2="&#x37;" k="20" />
    <hkern u1="&#x2a;" u2="&#x36;" k="92" />
    <hkern u1="&#x2a;" u2="&#x35;" k="47" />
    <hkern u1="&#x2a;" u2="&#x34;" k="127" />
    <hkern u1="&#x2a;" u2="&#x33;" k="20" />
    <hkern u1="&#x2a;" u2="&#x32;" k="20" />
    <hkern u1="&#x2a;" u2="&#x2f;" k="223" />
    <hkern u1="&#x2a;" u2="&#x26;" k="57" />
    <hkern u1="&#x2a;" u2="&#x23;" k="25" />
    <hkern u1="&#x2c;" u2="&#x20ac;" k="20" />
    <hkern u1="&#x2c;" u2="&#xbf;" k="14" />
    <hkern u1="&#x2c;" u2="&#xa5;" k="66" />
    <hkern u1="&#x2c;" u2="&#xa2;" k="53" />
    <hkern u1="&#x2c;" u2="x" k="37" />
    <hkern u1="&#x2c;" u2="v" k="117" />
    <hkern u1="&#x2c;" u2="\" k="211" />
    <hkern u1="&#x2c;" u2="X" k="27" />
    <hkern u1="&#x2c;" u2="V" k="211" />
    <hkern u1="&#x2c;" u2="&#x40;" k="14" />
    <hkern u1="&#x2c;" u2="&#x3f;" k="104" />
    <hkern u1="&#x2c;" u2="&#x39;" k="139" />
    <hkern u1="&#x2c;" u2="&#x38;" k="29" />
    <hkern u1="&#x2c;" u2="&#x37;" k="57" />
    <hkern u1="&#x2c;" u2="&#x36;" k="14" />
    <hkern u1="&#x2c;" u2="&#x35;" k="14" />
    <hkern u1="&#x2c;" u2="&#x34;" k="29" />
    <hkern u1="&#x2c;" u2="&#x31;" k="72" />
    <hkern u1="&#x2c;" u2="&#x30;" k="14" />
    <hkern u1="&#x2c;" u2="&#x2f;" k="6" />
    <hkern u1="&#x2c;" u2="&#x2a;" k="246" />
    <hkern u1="&#x2c;" u2="&#x23;" k="14" />
    <hkern u1="&#x2c;" u2="&#x21;" k="10" />
    <hkern u1="&#x2d;" u2="&#x2122;" k="35" />
    <hkern u1="&#x2d;" u2="&#xa5;" k="20" />
    <hkern u1="&#x2d;" u2="x" k="102" />
    <hkern u1="&#x2d;" u2="v" k="31" />
    <hkern u1="&#x2d;" u2="\" k="119" />
    <hkern u1="&#x2d;" u2="X" k="133" />
    <hkern u1="&#x2d;" u2="V" k="123" />
    <hkern u1="&#x2d;" u2="&#x3f;" k="45" />
    <hkern u1="&#x2d;" u2="&#x39;" k="25" />
    <hkern u1="&#x2d;" u2="&#x37;" k="76" />
    <hkern u1="&#x2d;" u2="&#x36;" k="6" />
    <hkern u1="&#x2d;" u2="&#x35;" k="16" />
    <hkern u1="&#x2d;" u2="&#x34;" k="10" />
    <hkern u1="&#x2d;" u2="&#x33;" k="16" />
    <hkern u1="&#x2d;" u2="&#x32;" k="39" />
    <hkern u1="&#x2d;" u2="&#x31;" k="41" />
    <hkern u1="&#x2d;" u2="&#x2f;" k="127" />
    <hkern u1="&#x2d;" u2="&#x2a;" k="20" />
    <hkern u1="&#x2e;" u2="&#x2122;" k="188" />
    <hkern u1="&#x2e;" u2="&#x20ac;" k="47" />
    <hkern u1="&#x2e;" u2="&#xae;" k="129" />
    <hkern u1="&#x2e;" u2="&#xa5;" k="78" />
    <hkern u1="&#x2e;" u2="&#xa2;" k="47" />
    <hkern u1="&#x2e;" u2="x" k="14" />
    <hkern u1="&#x2e;" u2="v" k="158" />
    <hkern u1="&#x2e;" u2="\" k="178" />
    <hkern u1="&#x2e;" u2="X" k="10" />
    <hkern u1="&#x2e;" u2="V" k="276" />
    <hkern u1="&#x2e;" u2="&#x40;" k="16" />
    <hkern u1="&#x2e;" u2="&#x3f;" k="68" />
    <hkern u1="&#x2e;" u2="&#x39;" k="129" />
    <hkern u1="&#x2e;" u2="&#x37;" k="20" />
    <hkern u1="&#x2e;" u2="&#x36;" k="10" />
    <hkern u1="&#x2e;" u2="&#x35;" k="20" />
    <hkern u1="&#x2e;" u2="&#x34;" k="53" />
    <hkern u1="&#x2e;" u2="&#x31;" k="139" />
    <hkern u1="&#x2e;" u2="&#x30;" k="10" />
    <hkern u1="&#x2e;" u2="&#x2a;" k="188" />
    <hkern u1="&#x2e;" u2="&#x26;" k="31" />
    <hkern u1="&#x2f;" g2="Jcircumflex.salt" k="68" />
    <hkern u1="&#x2f;" g2="J.salt" k="68" />
    <hkern u1="&#x2f;" u2="&#xfb02;" k="47" />
    <hkern u1="&#x2f;" u2="&#xfb01;" k="47" />
    <hkern u1="&#x2f;" u2="&#x2117;" k="76" />
    <hkern u1="&#x2f;" u2="&#x203a;" k="92" />
    <hkern u1="&#x2f;" u2="&#x2039;" k="123" />
    <hkern u1="&#x2f;" u2="&#x2030;" k="27" />
    <hkern u1="&#x2f;" u2="&#x2026;" k="258" />
    <hkern u1="&#x2f;" u2="&#x201e;" k="236" />
    <hkern u1="&#x2f;" u2="&#x201a;" k="236" />
    <hkern u1="&#x2f;" u2="&#x2014;" k="160" />
    <hkern u1="&#x2f;" u2="&#x2013;" k="160" />
    <hkern u1="&#x2f;" u2="&#x1ef3;" k="35" />
    <hkern u1="&#x2f;" u2="&#x1e85;" k="31" />
    <hkern u1="&#x2f;" u2="&#x1e83;" k="31" />
    <hkern u1="&#x2f;" u2="&#x1e81;" k="31" />
    <hkern u1="&#x2f;" u2="&#x219;" k="113" />
    <hkern u1="&#x2f;" u2="&#x1ff;" k="129" />
    <hkern u1="&#x2f;" u2="&#x1fe;" k="76" />
    <hkern u1="&#x2f;" u2="&#x1fd;" k="174" />
    <hkern u1="&#x2f;" u2="&#x1fb;" k="174" />
    <hkern u1="&#x2f;" u2="&#x1fa;" k="174" />
    <hkern u1="&#x2f;" u2="&#x17e;" k="66" />
    <hkern u1="&#x2f;" u2="&#x17c;" k="66" />
    <hkern u1="&#x2f;" u2="&#x17a;" k="66" />
    <hkern u1="&#x2f;" u2="&#x177;" k="35" />
    <hkern u1="&#x2f;" u2="&#x175;" k="31" />
    <hkern u1="&#x2f;" u2="&#x173;" k="88" />
    <hkern u1="&#x2f;" u2="&#x171;" k="88" />
    <hkern u1="&#x2f;" u2="&#x16f;" k="88" />
    <hkern u1="&#x2f;" u2="&#x16d;" k="88" />
    <hkern u1="&#x2f;" u2="&#x16b;" k="88" />
    <hkern u1="&#x2f;" u2="&#x169;" k="88" />
    <hkern u1="&#x2f;" u2="&#x163;" k="25" />
    <hkern u1="&#x2f;" u2="&#x161;" k="113" />
    <hkern u1="&#x2f;" u2="&#x160;" k="57" />
    <hkern u1="&#x2f;" u2="&#x15f;" k="113" />
    <hkern u1="&#x2f;" u2="&#x15e;" k="57" />
    <hkern u1="&#x2f;" u2="&#x15d;" k="113" />
    <hkern u1="&#x2f;" u2="&#x15c;" k="57" />
    <hkern u1="&#x2f;" u2="&#x15b;" k="113" />
    <hkern u1="&#x2f;" u2="&#x15a;" k="57" />
    <hkern u1="&#x2f;" u2="&#x159;" k="92" />
    <hkern u1="&#x2f;" u2="&#x157;" k="92" />
    <hkern u1="&#x2f;" u2="&#x155;" k="92" />
    <hkern u1="&#x2f;" u2="&#x152;" k="76" />
    <hkern u1="&#x2f;" u2="&#x151;" k="129" />
    <hkern u1="&#x2f;" u2="&#x150;" k="76" />
    <hkern u1="&#x2f;" u2="&#x14f;" k="129" />
    <hkern u1="&#x2f;" u2="&#x14e;" k="76" />
    <hkern u1="&#x2f;" u2="&#x14d;" k="129" />
    <hkern u1="&#x2f;" u2="&#x14c;" k="76" />
    <hkern u1="&#x2f;" u2="&#x14b;" k="92" />
    <hkern u1="&#x2f;" u2="&#x148;" k="92" />
    <hkern u1="&#x2f;" u2="&#x146;" k="92" />
    <hkern u1="&#x2f;" u2="&#x144;" k="92" />
    <hkern u1="&#x2f;" u2="&#x134;" k="150" />
    <hkern u1="&#x2f;" u2="&#x123;" k="129" />
    <hkern u1="&#x2f;" u2="&#x122;" k="76" />
    <hkern u1="&#x2f;" u2="&#x121;" k="129" />
    <hkern u1="&#x2f;" u2="&#x120;" k="76" />
    <hkern u1="&#x2f;" u2="&#x11f;" k="129" />
    <hkern u1="&#x2f;" u2="&#x11e;" k="76" />
    <hkern u1="&#x2f;" u2="&#x11d;" k="129" />
    <hkern u1="&#x2f;" u2="&#x11c;" k="76" />
    <hkern u1="&#x2f;" u2="&#x11b;" k="129" />
    <hkern u1="&#x2f;" u2="&#x119;" k="129" />
    <hkern u1="&#x2f;" u2="&#x117;" k="129" />
    <hkern u1="&#x2f;" u2="&#x115;" k="129" />
    <hkern u1="&#x2f;" u2="&#x113;" k="129" />
    <hkern u1="&#x2f;" u2="&#x111;" k="129" />
    <hkern u1="&#x2f;" u2="&#x10f;" k="129" />
    <hkern u1="&#x2f;" u2="&#x10d;" k="129" />
    <hkern u1="&#x2f;" u2="&#x10c;" k="76" />
    <hkern u1="&#x2f;" u2="&#x10b;" k="129" />
    <hkern u1="&#x2f;" u2="&#x10a;" k="76" />
    <hkern u1="&#x2f;" u2="&#x109;" k="129" />
    <hkern u1="&#x2f;" u2="&#x108;" k="76" />
    <hkern u1="&#x2f;" u2="&#x107;" k="129" />
    <hkern u1="&#x2f;" u2="&#x106;" k="76" />
    <hkern u1="&#x2f;" u2="&#x105;" k="174" />
    <hkern u1="&#x2f;" u2="&#x104;" k="174" />
    <hkern u1="&#x2f;" u2="&#x103;" k="174" />
    <hkern u1="&#x2f;" u2="&#x102;" k="174" />
    <hkern u1="&#x2f;" u2="&#x101;" k="174" />
    <hkern u1="&#x2f;" u2="&#x100;" k="174" />
    <hkern u1="&#x2f;" u2="&#xff;" k="35" />
    <hkern u1="&#x2f;" u2="&#xfd;" k="35" />
    <hkern u1="&#x2f;" u2="&#xfc;" k="88" />
    <hkern u1="&#x2f;" u2="&#xfb;" k="88" />
    <hkern u1="&#x2f;" u2="&#xfa;" k="88" />
    <hkern u1="&#x2f;" u2="&#xf9;" k="88" />
    <hkern u1="&#x2f;" u2="&#xf8;" k="129" />
    <hkern u1="&#x2f;" u2="&#xf6;" k="129" />
    <hkern u1="&#x2f;" u2="&#xf5;" k="129" />
    <hkern u1="&#x2f;" u2="&#xf4;" k="129" />
    <hkern u1="&#x2f;" u2="&#xf3;" k="129" />
    <hkern u1="&#x2f;" u2="&#xf2;" k="129" />
    <hkern u1="&#x2f;" u2="&#xeb;" k="129" />
    <hkern u1="&#x2f;" u2="&#xea;" k="129" />
    <hkern u1="&#x2f;" u2="&#xe9;" k="129" />
    <hkern u1="&#x2f;" u2="&#xe8;" k="129" />
    <hkern u1="&#x2f;" u2="&#xe7;" k="129" />
    <hkern u1="&#x2f;" u2="&#xe6;" k="174" />
    <hkern u1="&#x2f;" u2="&#xe5;" k="174" />
    <hkern u1="&#x2f;" u2="&#xe4;" k="174" />
    <hkern u1="&#x2f;" u2="&#xe3;" k="174" />
    <hkern u1="&#x2f;" u2="&#xe2;" k="174" />
    <hkern u1="&#x2f;" u2="&#xe1;" k="174" />
    <hkern u1="&#x2f;" u2="&#xe0;" k="174" />
    <hkern u1="&#x2f;" u2="&#xd8;" k="76" />
    <hkern u1="&#x2f;" u2="&#xd6;" k="76" />
    <hkern u1="&#x2f;" u2="&#xd5;" k="76" />
    <hkern u1="&#x2f;" u2="&#xd4;" k="76" />
    <hkern u1="&#x2f;" u2="&#xd3;" k="76" />
    <hkern u1="&#x2f;" u2="&#xd2;" k="76" />
    <hkern u1="&#x2f;" u2="&#xc7;" k="76" />
    <hkern u1="&#x2f;" u2="&#xc5;" k="174" />
    <hkern u1="&#x2f;" u2="&#xc4;" k="174" />
    <hkern u1="&#x2f;" u2="&#xc3;" k="174" />
    <hkern u1="&#x2f;" u2="&#xc2;" k="174" />
    <hkern u1="&#x2f;" u2="&#xc1;" k="174" />
    <hkern u1="&#x2f;" u2="&#xc0;" k="174" />
    <hkern u1="&#x2f;" u2="&#xbb;" k="92" />
    <hkern u1="&#x2f;" u2="&#xba;" k="35" />
    <hkern u1="&#x2f;" u2="&#xab;" k="123" />
    <hkern u1="&#x2f;" u2="&#xaa;" k="35" />
    <hkern u1="&#x2f;" u2="&#xa9;" k="76" />
    <hkern u1="&#x2f;" u2="&#x7b;" k="61" />
    <hkern u1="&#x2f;" u2="z" k="66" />
    <hkern u1="&#x2f;" u2="y" k="35" />
    <hkern u1="&#x2f;" u2="w" k="31" />
    <hkern u1="&#x2f;" u2="u" k="88" />
    <hkern u1="&#x2f;" u2="t" k="25" />
    <hkern u1="&#x2f;" u2="s" k="113" />
    <hkern u1="&#x2f;" u2="r" k="92" />
    <hkern u1="&#x2f;" u2="q" k="129" />
    <hkern u1="&#x2f;" u2="p" k="92" />
    <hkern u1="&#x2f;" u2="o" k="129" />
    <hkern u1="&#x2f;" u2="n" k="92" />
    <hkern u1="&#x2f;" u2="m" k="92" />
    <hkern u1="&#x2f;" u2="g" k="129" />
    <hkern u1="&#x2f;" u2="f" k="47" />
    <hkern u1="&#x2f;" u2="e" k="129" />
    <hkern u1="&#x2f;" u2="d" k="129" />
    <hkern u1="&#x2f;" u2="c" k="129" />
    <hkern u1="&#x2f;" u2="a" k="174" />
    <hkern u1="&#x2f;" u2="[" k="61" />
    <hkern u1="&#x2f;" u2="S" k="57" />
    <hkern u1="&#x2f;" u2="Q" k="76" />
    <hkern u1="&#x2f;" u2="O" k="76" />
    <hkern u1="&#x2f;" u2="J" k="150" />
    <hkern u1="&#x2f;" u2="G" k="76" />
    <hkern u1="&#x2f;" u2="C" k="76" />
    <hkern u1="&#x2f;" u2="A" k="174" />
    <hkern u1="&#x2f;" u2="&#x3b;" k="168" />
    <hkern u1="&#x2f;" u2="&#x3a;" k="168" />
    <hkern u1="&#x2f;" u2="&#x2e;" k="258" />
    <hkern u1="&#x2f;" u2="&#x2d;" k="160" />
    <hkern u1="&#x2f;" u2="&#x2c;" k="236" />
    <hkern u1="&#x2f;" u2="&#x28;" k="61" />
    <hkern u1="&#x2f;" u2="&#x25;" k="27" />
    <hkern u1="&#x2f;" u2="&#x2122;" k="-20" />
    <hkern u1="&#x2f;" u2="&#x20ac;" k="78" />
    <hkern u1="&#x2f;" u2="&#xbf;" k="143" />
    <hkern u1="&#x2f;" u2="&#xae;" k="47" />
    <hkern u1="&#x2f;" u2="&#xa5;" k="16" />
    <hkern u1="&#x2f;" u2="&#xa3;" k="119" />
    <hkern u1="&#x2f;" u2="&#xa2;" k="78" />
    <hkern u1="&#x2f;" u2="&#xa1;" k="20" />
    <hkern u1="&#x2f;" u2="x" k="51" />
    <hkern u1="&#x2f;" u2="v" k="31" />
    <hkern u1="&#x2f;" u2="&#x40;" k="147" />
    <hkern u1="&#x2f;" u2="&#x3f;" k="37" />
    <hkern u1="&#x2f;" u2="&#x39;" k="47" />
    <hkern u1="&#x2f;" u2="&#x38;" k="72" />
    <hkern u1="&#x2f;" u2="&#x36;" k="129" />
    <hkern u1="&#x2f;" u2="&#x35;" k="72" />
    <hkern u1="&#x2f;" u2="&#x34;" k="123" />
    <hkern u1="&#x2f;" u2="&#x33;" k="57" />
    <hkern u1="&#x2f;" u2="&#x32;" k="55" />
    <hkern u1="&#x2f;" u2="&#x31;" k="10" />
    <hkern u1="&#x2f;" u2="&#x30;" k="66" />
    <hkern u1="&#x2f;" u2="&#x2a;" k="20" />
    <hkern u1="&#x2f;" u2="&#x26;" k="133" />
    <hkern u1="&#x2f;" u2="&#x24;" k="55" />
    <hkern u1="&#x2f;" u2="&#x23;" k="88" />
    <hkern u1="&#x30;" u2="&#x2026;" k="10" />
    <hkern u1="&#x30;" u2="&#x201e;" k="20" />
    <hkern u1="&#x30;" u2="&#x201a;" k="20" />
    <hkern u1="&#x30;" u2="&#x7d;" k="33" />
    <hkern u1="&#x30;" u2="]" k="33" />
    <hkern u1="&#x30;" u2="&#x2e;" k="10" />
    <hkern u1="&#x30;" u2="&#x2c;" k="20" />
    <hkern u1="&#x30;" u2="&#x29;" k="33" />
    <hkern u1="&#x30;" u2="&#x2122;" k="25" />
    <hkern u1="&#x30;" u2="\" k="25" />
    <hkern u1="&#x30;" u2="&#x37;" k="16" />
    <hkern u1="&#x30;" u2="&#x2f;" k="45" />
    <hkern u1="&#x32;" u2="&#x203a;" k="10" />
    <hkern u1="&#x32;" u2="&#x2039;" k="20" />
    <hkern u1="&#x32;" u2="&#x201e;" k="10" />
    <hkern u1="&#x32;" u2="&#x201d;" k="20" />
    <hkern u1="&#x32;" u2="&#x201c;" k="20" />
    <hkern u1="&#x32;" u2="&#x201a;" k="10" />
    <hkern u1="&#x32;" u2="&#x2019;" k="20" />
    <hkern u1="&#x32;" u2="&#x2018;" k="20" />
    <hkern u1="&#x32;" u2="&#x2014;" k="41" />
    <hkern u1="&#x32;" u2="&#x2013;" k="41" />
    <hkern u1="&#x32;" u2="&#x149;" k="20" />
    <hkern u1="&#x32;" u2="&#xbb;" k="10" />
    <hkern u1="&#x32;" u2="&#xab;" k="20" />
    <hkern u1="&#x32;" u2="&#x7d;" k="16" />
    <hkern u1="&#x32;" u2="]" k="16" />
    <hkern u1="&#x32;" u2="&#x2d;" k="41" />
    <hkern u1="&#x32;" u2="&#x2c;" k="10" />
    <hkern u1="&#x32;" u2="&#x29;" k="16" />
    <hkern u1="&#x32;" u2="&#x2122;" k="31" />
    <hkern u1="&#x32;" u2="&#xa3;" k="6" />
    <hkern u1="&#x32;" u2="\" k="31" />
    <hkern u1="&#x32;" u2="&#x39;" k="8" />
    <hkern u1="&#x32;" u2="&#x37;" k="25" />
    <hkern u1="&#x32;" u2="&#x36;" k="25" />
    <hkern u1="&#x32;" u2="&#x35;" k="20" />
    <hkern u1="&#x32;" u2="&#x34;" k="41" />
    <hkern u1="&#x32;" u2="&#x33;" k="20" />
    <hkern u1="&#x32;" u2="&#x32;" k="16" />
    <hkern u1="&#x32;" u2="&#x30;" k="12" />
    <hkern u1="&#x32;" u2="&#x2f;" k="45" />
    <hkern u1="&#x32;" u2="&#x2a;" k="10" />
    <hkern u1="&#x32;" u2="&#x23;" k="14" />
    <hkern u1="&#x33;" u2="&#x203a;" k="27" />
    <hkern u1="&#x33;" u2="&#x2030;" k="27" />
    <hkern u1="&#x33;" u2="&#x201e;" k="31" />
    <hkern u1="&#x33;" u2="&#x201d;" k="51" />
    <hkern u1="&#x33;" u2="&#x201c;" k="55" />
    <hkern u1="&#x33;" u2="&#x201a;" k="31" />
    <hkern u1="&#x33;" u2="&#x2019;" k="51" />
    <hkern u1="&#x33;" u2="&#x2018;" k="55" />
    <hkern u1="&#x33;" u2="&#x2014;" k="20" />
    <hkern u1="&#x33;" u2="&#x2013;" k="20" />
    <hkern u1="&#x33;" u2="&#x149;" k="51" />
    <hkern u1="&#x33;" u2="&#xbb;" k="27" />
    <hkern u1="&#x33;" u2="&#x7d;" k="33" />
    <hkern u1="&#x33;" u2="]" k="33" />
    <hkern u1="&#x33;" u2="&#x3b;" k="20" />
    <hkern u1="&#x33;" u2="&#x3a;" k="20" />
    <hkern u1="&#x33;" u2="&#x2d;" k="20" />
    <hkern u1="&#x33;" u2="&#x2c;" k="31" />
    <hkern u1="&#x33;" u2="&#x29;" k="33" />
    <hkern u1="&#x33;" u2="&#x25;" k="27" />
    <hkern u1="&#x33;" u2="&#x2122;" k="25" />
    <hkern u1="&#x33;" u2="&#xae;" k="27" />
    <hkern u1="&#x33;" u2="\" k="31" />
    <hkern u1="&#x33;" u2="&#x3f;" k="20" />
    <hkern u1="&#x33;" u2="&#x39;" k="37" />
    <hkern u1="&#x33;" u2="&#x37;" k="29" />
    <hkern u1="&#x33;" u2="&#x32;" k="14" />
    <hkern u1="&#x33;" u2="&#x31;" k="10" />
    <hkern u1="&#x33;" u2="&#x2f;" k="72" />
    <hkern u1="&#x33;" u2="&#x2a;" k="39" />
    <hkern u1="&#x34;" u2="&#x2117;" k="20" />
    <hkern u1="&#x34;" u2="&#x203a;" k="14" />
    <hkern u1="&#x34;" u2="&#x2039;" k="-27" />
    <hkern u1="&#x34;" u2="&#x2030;" k="47" />
    <hkern u1="&#x34;" u2="&#x2026;" k="29" />
    <hkern u1="&#x34;" u2="&#x201e;" k="6" />
    <hkern u1="&#x34;" u2="&#x201d;" k="164" />
    <hkern u1="&#x34;" u2="&#x201c;" k="152" />
    <hkern u1="&#x34;" u2="&#x201a;" k="6" />
    <hkern u1="&#x34;" u2="&#x2019;" k="164" />
    <hkern u1="&#x34;" u2="&#x2018;" k="152" />
    <hkern u1="&#x34;" u2="&#x2014;" k="20" />
    <hkern u1="&#x34;" u2="&#x2013;" k="20" />
    <hkern u1="&#x34;" u2="&#x149;" k="164" />
    <hkern u1="&#x34;" u2="&#xbb;" k="14" />
    <hkern u1="&#x34;" u2="&#xba;" k="29" />
    <hkern u1="&#x34;" u2="&#xab;" k="-27" />
    <hkern u1="&#x34;" u2="&#xaa;" k="29" />
    <hkern u1="&#x34;" u2="&#xa9;" k="20" />
    <hkern u1="&#x34;" u2="&#x7d;" k="33" />
    <hkern u1="&#x34;" u2="]" k="33" />
    <hkern u1="&#x34;" u2="&#x2e;" k="29" />
    <hkern u1="&#x34;" u2="&#x2d;" k="20" />
    <hkern u1="&#x34;" u2="&#x2c;" k="6" />
    <hkern u1="&#x34;" u2="&#x29;" k="33" />
    <hkern u1="&#x34;" u2="&#x25;" k="47" />
    <hkern u1="&#x34;" u2="&#x2122;" k="178" />
    <hkern u1="&#x34;" u2="&#xae;" k="127" />
    <hkern u1="&#x34;" u2="\" k="96" />
    <hkern u1="&#x34;" u2="&#x3f;" k="47" />
    <hkern u1="&#x34;" u2="&#x39;" k="72" />
    <hkern u1="&#x34;" u2="&#x37;" k="47" />
    <hkern u1="&#x34;" u2="&#x36;" k="6" />
    <hkern u1="&#x34;" u2="&#x35;" k="20" />
    <hkern u1="&#x34;" u2="&#x32;" k="6" />
    <hkern u1="&#x34;" u2="&#x31;" k="66" />
    <hkern u1="&#x34;" u2="&#x30;" k="6" />
    <hkern u1="&#x34;" u2="&#x2f;" k="41" />
    <hkern u1="&#x34;" u2="&#x2a;" k="117" />
    <hkern u1="&#x34;" u2="&#x23;" k="6" />
    <hkern u1="&#x35;" u2="&#x203a;" k="20" />
    <hkern u1="&#x35;" u2="&#x2039;" k="-14" />
    <hkern u1="&#x35;" u2="&#x2030;" k="27" />
    <hkern u1="&#x35;" u2="&#x2026;" k="16" />
    <hkern u1="&#x35;" u2="&#x201e;" k="25" />
    <hkern u1="&#x35;" u2="&#x201d;" k="76" />
    <hkern u1="&#x35;" u2="&#x201c;" k="66" />
    <hkern u1="&#x35;" u2="&#x201a;" k="25" />
    <hkern u1="&#x35;" u2="&#x2019;" k="76" />
    <hkern u1="&#x35;" u2="&#x2018;" k="66" />
    <hkern u1="&#x35;" u2="&#x2014;" k="6" />
    <hkern u1="&#x35;" u2="&#x2013;" k="6" />
    <hkern u1="&#x35;" u2="&#x149;" k="76" />
    <hkern u1="&#x35;" u2="&#xbb;" k="20" />
    <hkern u1="&#x35;" u2="&#xab;" k="-14" />
    <hkern u1="&#x35;" u2="&#x7d;" k="37" />
    <hkern u1="&#x35;" u2="]" k="37" />
    <hkern u1="&#x35;" u2="&#x3b;" k="16" />
    <hkern u1="&#x35;" u2="&#x3a;" k="16" />
    <hkern u1="&#x35;" u2="&#x2e;" k="16" />
    <hkern u1="&#x35;" u2="&#x2d;" k="6" />
    <hkern u1="&#x35;" u2="&#x2c;" k="25" />
    <hkern u1="&#x35;" u2="&#x29;" k="37" />
    <hkern u1="&#x35;" u2="&#x25;" k="27" />
    <hkern u1="&#x35;" u2="&#x2122;" k="47" />
    <hkern u1="&#x35;" u2="&#xae;" k="61" />
    <hkern u1="&#x35;" u2="\" k="45" />
    <hkern u1="&#x35;" u2="&#x3f;" k="20" />
    <hkern u1="&#x35;" u2="&#x39;" k="45" />
    <hkern u1="&#x35;" u2="&#x37;" k="76" />
    <hkern u1="&#x35;" u2="&#x36;" k="10" />
    <hkern u1="&#x35;" u2="&#x35;" k="10" />
    <hkern u1="&#x35;" u2="&#x33;" k="10" />
    <hkern u1="&#x35;" u2="&#x32;" k="18" />
    <hkern u1="&#x35;" u2="&#x31;" k="47" />
    <hkern u1="&#x35;" u2="&#x2f;" k="68" />
    <hkern u1="&#x35;" u2="&#x2a;" k="66" />
    <hkern u1="&#x35;" u2="&#x21;" k="10" />
    <hkern u1="&#x36;" u2="&#x203a;" k="16" />
    <hkern u1="&#x36;" u2="&#x2039;" k="-20" />
    <hkern u1="&#x36;" u2="&#x2030;" k="14" />
    <hkern u1="&#x36;" u2="&#x2026;" k="10" />
    <hkern u1="&#x36;" u2="&#x201e;" k="37" />
    <hkern u1="&#x36;" u2="&#x201d;" k="129" />
    <hkern u1="&#x36;" u2="&#x201c;" k="123" />
    <hkern u1="&#x36;" u2="&#x201a;" k="37" />
    <hkern u1="&#x36;" u2="&#x2019;" k="129" />
    <hkern u1="&#x36;" u2="&#x2018;" k="123" />
    <hkern u1="&#x36;" u2="&#x2014;" k="6" />
    <hkern u1="&#x36;" u2="&#x2013;" k="6" />
    <hkern u1="&#x36;" u2="&#x149;" k="129" />
    <hkern u1="&#x36;" u2="&#xbb;" k="16" />
    <hkern u1="&#x36;" u2="&#xab;" k="-20" />
    <hkern u1="&#x36;" u2="&#x7d;" k="47" />
    <hkern u1="&#x36;" u2="]" k="47" />
    <hkern u1="&#x36;" u2="&#x2e;" k="10" />
    <hkern u1="&#x36;" u2="&#x2d;" k="6" />
    <hkern u1="&#x36;" u2="&#x2c;" k="37" />
    <hkern u1="&#x36;" u2="&#x29;" k="47" />
    <hkern u1="&#x36;" u2="&#x25;" k="14" />
    <hkern u1="&#x36;" u2="&#x2122;" k="117" />
    <hkern u1="&#x36;" u2="&#xae;" k="55" />
    <hkern u1="&#x36;" u2="\" k="86" />
    <hkern u1="&#x36;" u2="&#x3f;" k="37" />
    <hkern u1="&#x36;" u2="&#x39;" k="41" />
    <hkern u1="&#x36;" u2="&#x37;" k="31" />
    <hkern u1="&#x36;" u2="&#x32;" k="10" />
    <hkern u1="&#x36;" u2="&#x31;" k="47" />
    <hkern u1="&#x36;" u2="&#x2f;" k="57" />
    <hkern u1="&#x36;" u2="&#x2a;" k="57" />
    <hkern u1="&#x36;" u2="&#x21;" k="4" />
    <hkern u1="&#x37;" u2="&#x2117;" k="59" />
    <hkern u1="&#x37;" u2="&#x203a;" k="76" />
    <hkern u1="&#x37;" u2="&#x2039;" k="143" />
    <hkern u1="&#x37;" u2="&#x2026;" k="291" />
    <hkern u1="&#x37;" u2="&#x201e;" k="272" />
    <hkern u1="&#x37;" u2="&#x201d;" k="6" />
    <hkern u1="&#x37;" u2="&#x201a;" k="272" />
    <hkern u1="&#x37;" u2="&#x2019;" k="6" />
    <hkern u1="&#x37;" u2="&#x2014;" k="174" />
    <hkern u1="&#x37;" u2="&#x2013;" k="174" />
    <hkern u1="&#x37;" u2="&#x149;" k="6" />
    <hkern u1="&#x37;" u2="&#xbb;" k="76" />
    <hkern u1="&#x37;" u2="&#xba;" k="35" />
    <hkern u1="&#x37;" u2="&#xab;" k="143" />
    <hkern u1="&#x37;" u2="&#xaa;" k="35" />
    <hkern u1="&#x37;" u2="&#xa9;" k="59" />
    <hkern u1="&#x37;" u2="&#x7d;" k="10" />
    <hkern u1="&#x37;" u2="]" k="10" />
    <hkern u1="&#x37;" u2="&#x3b;" k="166" />
    <hkern u1="&#x37;" u2="&#x3a;" k="166" />
    <hkern u1="&#x37;" u2="&#x2e;" k="291" />
    <hkern u1="&#x37;" u2="&#x2d;" k="174" />
    <hkern u1="&#x37;" u2="&#x2c;" k="272" />
    <hkern u1="&#x37;" u2="&#x29;" k="10" />
    <hkern u1="&#x37;" u2="&#x2122;" k="-27" />
    <hkern u1="&#x37;" u2="&#x20ac;" k="53" />
    <hkern u1="&#x37;" u2="&#xbf;" k="158" />
    <hkern u1="&#x37;" u2="&#xae;" k="29" />
    <hkern u1="&#x37;" u2="&#xa3;" k="102" />
    <hkern u1="&#x37;" u2="&#xa2;" k="82" />
    <hkern u1="&#x37;" u2="&#x40;" k="109" />
    <hkern u1="&#x37;" u2="&#x39;" k="31" />
    <hkern u1="&#x37;" u2="&#x38;" k="82" />
    <hkern u1="&#x37;" u2="&#x37;" k="20" />
    <hkern u1="&#x37;" u2="&#x36;" k="129" />
    <hkern u1="&#x37;" u2="&#x35;" k="72" />
    <hkern u1="&#x37;" u2="&#x34;" k="143" />
    <hkern u1="&#x37;" u2="&#x33;" k="57" />
    <hkern u1="&#x37;" u2="&#x32;" k="59" />
    <hkern u1="&#x37;" u2="&#x30;" k="61" />
    <hkern u1="&#x37;" u2="&#x2f;" k="225" />
    <hkern u1="&#x37;" u2="&#x26;" k="86" />
    <hkern u1="&#x37;" u2="&#x23;" k="53" />
    <hkern u1="&#x38;" u2="&#x203a;" k="20" />
    <hkern u1="&#x38;" u2="&#x201e;" k="27" />
    <hkern u1="&#x38;" u2="&#x201d;" k="51" />
    <hkern u1="&#x38;" u2="&#x201c;" k="47" />
    <hkern u1="&#x38;" u2="&#x201a;" k="27" />
    <hkern u1="&#x38;" u2="&#x2019;" k="51" />
    <hkern u1="&#x38;" u2="&#x2018;" k="47" />
    <hkern u1="&#x38;" u2="&#x149;" k="51" />
    <hkern u1="&#x38;" u2="&#xbb;" k="20" />
    <hkern u1="&#x38;" u2="&#x7d;" k="45" />
    <hkern u1="&#x38;" u2="]" k="45" />
    <hkern u1="&#x38;" u2="&#x2c;" k="27" />
    <hkern u1="&#x38;" u2="&#x29;" k="45" />
    <hkern u1="&#x38;" u2="&#x2122;" k="76" />
    <hkern u1="&#x38;" u2="&#xae;" k="27" />
    <hkern u1="&#x38;" u2="\" k="76" />
    <hkern u1="&#x38;" u2="&#x3f;" k="20" />
    <hkern u1="&#x38;" u2="&#x39;" k="20" />
    <hkern u1="&#x38;" u2="&#x37;" k="41" />
    <hkern u1="&#x38;" u2="&#x34;" k="10" />
    <hkern u1="&#x38;" u2="&#x32;" k="10" />
    <hkern u1="&#x38;" u2="&#x31;" k="41" />
    <hkern u1="&#x38;" u2="&#x2f;" k="66" />
    <hkern u1="&#x38;" u2="&#x2a;" k="55" />
    <hkern u1="&#x39;" u2="&#x203a;" k="20" />
    <hkern u1="&#x39;" u2="&#x2039;" k="31" />
    <hkern u1="&#x39;" u2="&#x2026;" k="168" />
    <hkern u1="&#x39;" u2="&#x201e;" k="164" />
    <hkern u1="&#x39;" u2="&#x201d;" k="4" />
    <hkern u1="&#x39;" u2="&#x201a;" k="164" />
    <hkern u1="&#x39;" u2="&#x2019;" k="4" />
    <hkern u1="&#x39;" u2="&#x2014;" k="41" />
    <hkern u1="&#x39;" u2="&#x2013;" k="41" />
    <hkern u1="&#x39;" u2="&#x149;" k="4" />
    <hkern u1="&#x39;" u2="&#xbb;" k="20" />
    <hkern u1="&#x39;" u2="&#xab;" k="31" />
    <hkern u1="&#x39;" u2="&#x7d;" k="45" />
    <hkern u1="&#x39;" u2="]" k="45" />
    <hkern u1="&#x39;" u2="&#x3b;" k="20" />
    <hkern u1="&#x39;" u2="&#x3a;" k="20" />
    <hkern u1="&#x39;" u2="&#x2e;" k="168" />
    <hkern u1="&#x39;" u2="&#x2d;" k="41" />
    <hkern u1="&#x39;" u2="&#x2c;" k="164" />
    <hkern u1="&#x39;" u2="&#x29;" k="45" />
    <hkern u1="&#x39;" u2="&#x2122;" k="6" />
    <hkern u1="&#x39;" u2="&#xbf;" k="47" />
    <hkern u1="&#x39;" u2="&#xa3;" k="29" />
    <hkern u1="&#x39;" u2="&#xa1;" k="4" />
    <hkern u1="&#x39;" u2="\" k="35" />
    <hkern u1="&#x39;" u2="&#x38;" k="20" />
    <hkern u1="&#x39;" u2="&#x37;" k="51" />
    <hkern u1="&#x39;" u2="&#x36;" k="41" />
    <hkern u1="&#x39;" u2="&#x35;" k="31" />
    <hkern u1="&#x39;" u2="&#x34;" k="51" />
    <hkern u1="&#x39;" u2="&#x33;" k="27" />
    <hkern u1="&#x39;" u2="&#x32;" k="20" />
    <hkern u1="&#x39;" u2="&#x2f;" k="143" />
    <hkern u1="&#x39;" u2="&#x26;" k="41" />
    <hkern u1="&#x3a;" u2="&#xa5;" k="41" />
    <hkern u1="&#x3a;" u2="&#xa2;" k="8" />
    <hkern u1="&#x3a;" u2="x" k="14" />
    <hkern u1="&#x3a;" u2="v" k="45" />
    <hkern u1="&#x3a;" u2="\" k="119" />
    <hkern u1="&#x3a;" u2="X" k="10" />
    <hkern u1="&#x3a;" u2="V" k="127" />
    <hkern u1="&#x3a;" u2="&#x3f;" k="16" />
    <hkern u1="&#x3a;" u2="&#x39;" k="10" />
    <hkern u1="&#x3a;" u2="&#x37;" k="39" />
    <hkern u1="&#x3a;" u2="&#x36;" k="16" />
    <hkern u1="&#x3a;" u2="&#x35;" k="16" />
    <hkern u1="&#x3a;" u2="&#x31;" k="25" />
    <hkern u1="&#x3a;" u2="&#x2f;" k="23" />
    <hkern u1="&#x3a;" u2="&#x2a;" k="25" />
    <hkern u1="&#x3b;" u2="&#xa5;" k="41" />
    <hkern u1="&#x3b;" u2="&#xa2;" k="8" />
    <hkern u1="&#x3b;" u2="x" k="14" />
    <hkern u1="&#x3b;" u2="v" k="45" />
    <hkern u1="&#x3b;" u2="\" k="119" />
    <hkern u1="&#x3b;" u2="X" k="10" />
    <hkern u1="&#x3b;" u2="V" k="127" />
    <hkern u1="&#x3b;" u2="&#x3f;" k="16" />
    <hkern u1="&#x3b;" u2="&#x39;" k="10" />
    <hkern u1="&#x3b;" u2="&#x37;" k="39" />
    <hkern u1="&#x3b;" u2="&#x36;" k="16" />
    <hkern u1="&#x3b;" u2="&#x35;" k="16" />
    <hkern u1="&#x3b;" u2="&#x31;" k="25" />
    <hkern u1="&#x3b;" u2="&#x2f;" k="23" />
    <hkern u1="&#x3b;" u2="&#x2a;" k="25" />
    <hkern u1="&#x3f;" g2="Jcircumflex.salt" k="55" />
    <hkern u1="&#x3f;" g2="J.salt" k="55" />
    <hkern u1="&#x3f;" u2="&#xfb02;" k="-35" />
    <hkern u1="&#x3f;" u2="&#xfb01;" k="-35" />
    <hkern u1="&#x3f;" u2="&#x2117;" k="10" />
    <hkern u1="&#x3f;" u2="&#x2039;" k="35" />
    <hkern u1="&#x3f;" u2="&#x2026;" k="129" />
    <hkern u1="&#x3f;" u2="&#x201e;" k="152" />
    <hkern u1="&#x3f;" u2="&#x201a;" k="152" />
    <hkern u1="&#x3f;" u2="&#x2014;" k="100" />
    <hkern u1="&#x3f;" u2="&#x2013;" k="100" />
    <hkern u1="&#x3f;" u2="&#x1ef3;" k="-20" />
    <hkern u1="&#x3f;" u2="&#x1ef2;" k="6" />
    <hkern u1="&#x3f;" u2="&#x1e85;" k="-20" />
    <hkern u1="&#x3f;" u2="&#x1e83;" k="-20" />
    <hkern u1="&#x3f;" u2="&#x1e81;" k="-20" />
    <hkern u1="&#x3f;" u2="&#x219;" k="20" />
    <hkern u1="&#x3f;" u2="&#x1ff;" k="20" />
    <hkern u1="&#x3f;" u2="&#x1fd;" k="92" />
    <hkern u1="&#x3f;" u2="&#x1fb;" k="92" />
    <hkern u1="&#x3f;" u2="&#x1fa;" k="137" />
    <hkern u1="&#x3f;" u2="&#x17d;" k="16" />
    <hkern u1="&#x3f;" u2="&#x17b;" k="16" />
    <hkern u1="&#x3f;" u2="&#x179;" k="16" />
    <hkern u1="&#x3f;" u2="&#x178;" k="6" />
    <hkern u1="&#x3f;" u2="&#x177;" k="-20" />
    <hkern u1="&#x3f;" u2="&#x176;" k="6" />
    <hkern u1="&#x3f;" u2="&#x175;" k="-20" />
    <hkern u1="&#x3f;" u2="&#x164;" k="-6" />
    <hkern u1="&#x3f;" u2="&#x162;" k="-6" />
    <hkern u1="&#x3f;" u2="&#x161;" k="20" />
    <hkern u1="&#x3f;" u2="&#x15f;" k="20" />
    <hkern u1="&#x3f;" u2="&#x15d;" k="20" />
    <hkern u1="&#x3f;" u2="&#x15b;" k="20" />
    <hkern u1="&#x3f;" u2="&#x151;" k="20" />
    <hkern u1="&#x3f;" u2="&#x14f;" k="20" />
    <hkern u1="&#x3f;" u2="&#x14d;" k="20" />
    <hkern u1="&#x3f;" u2="&#x134;" k="139" />
    <hkern u1="&#x3f;" u2="&#x123;" k="20" />
    <hkern u1="&#x3f;" u2="&#x121;" k="20" />
    <hkern u1="&#x3f;" u2="&#x11f;" k="20" />
    <hkern u1="&#x3f;" u2="&#x11d;" k="20" />
    <hkern u1="&#x3f;" u2="&#x11b;" k="20" />
    <hkern u1="&#x3f;" u2="&#x119;" k="20" />
    <hkern u1="&#x3f;" u2="&#x117;" k="20" />
    <hkern u1="&#x3f;" u2="&#x115;" k="20" />
    <hkern u1="&#x3f;" u2="&#x113;" k="20" />
    <hkern u1="&#x3f;" u2="&#x111;" k="20" />
    <hkern u1="&#x3f;" u2="&#x10f;" k="20" />
    <hkern u1="&#x3f;" u2="&#x10d;" k="20" />
    <hkern u1="&#x3f;" u2="&#x10b;" k="20" />
    <hkern u1="&#x3f;" u2="&#x109;" k="20" />
    <hkern u1="&#x3f;" u2="&#x107;" k="20" />
    <hkern u1="&#x3f;" u2="&#x105;" k="92" />
    <hkern u1="&#x3f;" u2="&#x104;" k="137" />
    <hkern u1="&#x3f;" u2="&#x103;" k="92" />
    <hkern u1="&#x3f;" u2="&#x102;" k="137" />
    <hkern u1="&#x3f;" u2="&#x101;" k="92" />
    <hkern u1="&#x3f;" u2="&#x100;" k="137" />
    <hkern u1="&#x3f;" u2="&#xff;" k="-20" />
    <hkern u1="&#x3f;" u2="&#xfd;" k="-20" />
    <hkern u1="&#x3f;" u2="&#xf8;" k="20" />
    <hkern u1="&#x3f;" u2="&#xf6;" k="20" />
    <hkern u1="&#x3f;" u2="&#xf5;" k="20" />
    <hkern u1="&#x3f;" u2="&#xf4;" k="20" />
    <hkern u1="&#x3f;" u2="&#xf3;" k="20" />
    <hkern u1="&#x3f;" u2="&#xf2;" k="20" />
    <hkern u1="&#x3f;" u2="&#xeb;" k="20" />
    <hkern u1="&#x3f;" u2="&#xea;" k="20" />
    <hkern u1="&#x3f;" u2="&#xe9;" k="20" />
    <hkern u1="&#x3f;" u2="&#xe8;" k="20" />
    <hkern u1="&#x3f;" u2="&#xe7;" k="20" />
    <hkern u1="&#x3f;" u2="&#xe6;" k="92" />
    <hkern u1="&#x3f;" u2="&#xe5;" k="92" />
    <hkern u1="&#x3f;" u2="&#xe4;" k="92" />
    <hkern u1="&#x3f;" u2="&#xe3;" k="92" />
    <hkern u1="&#x3f;" u2="&#xe2;" k="92" />
    <hkern u1="&#x3f;" u2="&#xe1;" k="92" />
    <hkern u1="&#x3f;" u2="&#xe0;" k="92" />
    <hkern u1="&#x3f;" u2="&#xdd;" k="6" />
    <hkern u1="&#x3f;" u2="&#xc5;" k="137" />
    <hkern u1="&#x3f;" u2="&#xc4;" k="137" />
    <hkern u1="&#x3f;" u2="&#xc3;" k="137" />
    <hkern u1="&#x3f;" u2="&#xc2;" k="137" />
    <hkern u1="&#x3f;" u2="&#xc1;" k="137" />
    <hkern u1="&#x3f;" u2="&#xc0;" k="137" />
    <hkern u1="&#x3f;" u2="&#xab;" k="35" />
    <hkern u1="&#x3f;" u2="&#xa9;" k="10" />
    <hkern u1="&#x3f;" u2="&#x7d;" k="16" />
    <hkern u1="&#x3f;" u2="y" k="-20" />
    <hkern u1="&#x3f;" u2="w" k="-20" />
    <hkern u1="&#x3f;" u2="s" k="20" />
    <hkern u1="&#x3f;" u2="q" k="20" />
    <hkern u1="&#x3f;" u2="o" k="20" />
    <hkern u1="&#x3f;" u2="g" k="20" />
    <hkern u1="&#x3f;" u2="f" k="-35" />
    <hkern u1="&#x3f;" u2="e" k="20" />
    <hkern u1="&#x3f;" u2="d" k="20" />
    <hkern u1="&#x3f;" u2="c" k="20" />
    <hkern u1="&#x3f;" u2="a" k="92" />
    <hkern u1="&#x3f;" u2="]" k="16" />
    <hkern u1="&#x3f;" u2="Z" k="16" />
    <hkern u1="&#x3f;" u2="Y" k="6" />
    <hkern u1="&#x3f;" u2="T" k="-6" />
    <hkern u1="&#x3f;" u2="J" k="139" />
    <hkern u1="&#x3f;" u2="A" k="137" />
    <hkern u1="&#x3f;" u2="&#x3b;" k="20" />
    <hkern u1="&#x3f;" u2="&#x3a;" k="20" />
    <hkern u1="&#x3f;" u2="&#x2e;" k="129" />
    <hkern u1="&#x3f;" u2="&#x2d;" k="100" />
    <hkern u1="&#x3f;" u2="&#x2c;" k="152" />
    <hkern u1="&#x3f;" u2="&#x29;" k="16" />
    <hkern u1="&#x3f;" u2="&#x2122;" k="20" />
    <hkern u1="&#x3f;" u2="&#xa3;" k="27" />
    <hkern u1="&#x3f;" u2="v" k="-27" />
    <hkern u1="&#x3f;" u2="\" k="10" />
    <hkern u1="&#x3f;" u2="X" k="20" />
    <hkern u1="&#x3f;" u2="V" k="10" />
    <hkern u1="&#x3f;" u2="&#x40;" k="61" />
    <hkern u1="&#x3f;" u2="&#x38;" k="20" />
    <hkern u1="&#x3f;" u2="&#x36;" k="31" />
    <hkern u1="&#x3f;" u2="&#x35;" k="31" />
    <hkern u1="&#x3f;" u2="&#x34;" k="72" />
    <hkern u1="&#x3f;" u2="&#x33;" k="20" />
    <hkern u1="&#x3f;" u2="&#x2f;" k="172" />
    <hkern u1="&#x3f;" u2="&#x26;" k="45" />
    <hkern u1="&#x40;" g2="Jcircumflex.salt" k="10" />
    <hkern u1="&#x40;" g2="J.salt" k="10" />
    <hkern u1="&#x40;" u2="&#x203a;" k="10" />
    <hkern u1="&#x40;" u2="&#x2026;" k="41" />
    <hkern u1="&#x40;" u2="&#x201e;" k="61" />
    <hkern u1="&#x40;" u2="&#x201d;" k="6" />
    <hkern u1="&#x40;" u2="&#x201c;" k="6" />
    <hkern u1="&#x40;" u2="&#x201a;" k="61" />
    <hkern u1="&#x40;" u2="&#x2019;" k="6" />
    <hkern u1="&#x40;" u2="&#x2018;" k="6" />
    <hkern u1="&#x40;" u2="&#x1ef2;" k="94" />
    <hkern u1="&#x40;" u2="&#x1e84;" k="35" />
    <hkern u1="&#x40;" u2="&#x1e82;" k="35" />
    <hkern u1="&#x40;" u2="&#x1e80;" k="35" />
    <hkern u1="&#x40;" u2="&#x1fd;" k="31" />
    <hkern u1="&#x40;" u2="&#x1fb;" k="31" />
    <hkern u1="&#x40;" u2="&#x1fa;" k="82" />
    <hkern u1="&#x40;" u2="&#x178;" k="94" />
    <hkern u1="&#x40;" u2="&#x176;" k="94" />
    <hkern u1="&#x40;" u2="&#x174;" k="35" />
    <hkern u1="&#x40;" u2="&#x164;" k="37" />
    <hkern u1="&#x40;" u2="&#x162;" k="37" />
    <hkern u1="&#x40;" u2="&#x149;" k="6" />
    <hkern u1="&#x40;" u2="&#x134;" k="35" />
    <hkern u1="&#x40;" u2="&#x105;" k="31" />
    <hkern u1="&#x40;" u2="&#x104;" k="82" />
    <hkern u1="&#x40;" u2="&#x103;" k="31" />
    <hkern u1="&#x40;" u2="&#x102;" k="82" />
    <hkern u1="&#x40;" u2="&#x101;" k="31" />
    <hkern u1="&#x40;" u2="&#x100;" k="82" />
    <hkern u1="&#x40;" u2="&#xe6;" k="31" />
    <hkern u1="&#x40;" u2="&#xe5;" k="31" />
    <hkern u1="&#x40;" u2="&#xe4;" k="31" />
    <hkern u1="&#x40;" u2="&#xe3;" k="31" />
    <hkern u1="&#x40;" u2="&#xe2;" k="31" />
    <hkern u1="&#x40;" u2="&#xe1;" k="31" />
    <hkern u1="&#x40;" u2="&#xe0;" k="31" />
    <hkern u1="&#x40;" u2="&#xdd;" k="94" />
    <hkern u1="&#x40;" u2="&#xc5;" k="82" />
    <hkern u1="&#x40;" u2="&#xc4;" k="82" />
    <hkern u1="&#x40;" u2="&#xc3;" k="82" />
    <hkern u1="&#x40;" u2="&#xc2;" k="82" />
    <hkern u1="&#x40;" u2="&#xc1;" k="82" />
    <hkern u1="&#x40;" u2="&#xc0;" k="82" />
    <hkern u1="&#x40;" u2="&#xbb;" k="10" />
    <hkern u1="&#x40;" u2="&#x7d;" k="49" />
    <hkern u1="&#x40;" u2="a" k="31" />
    <hkern u1="&#x40;" u2="]" k="49" />
    <hkern u1="&#x40;" u2="Y" k="94" />
    <hkern u1="&#x40;" u2="W" k="35" />
    <hkern u1="&#x40;" u2="T" k="37" />
    <hkern u1="&#x40;" u2="J" k="35" />
    <hkern u1="&#x40;" u2="A" k="82" />
    <hkern u1="&#x40;" u2="&#x3b;" k="20" />
    <hkern u1="&#x40;" u2="&#x3a;" k="20" />
    <hkern u1="&#x40;" u2="&#x2e;" k="41" />
    <hkern u1="&#x40;" u2="&#x2c;" k="61" />
    <hkern u1="&#x40;" u2="&#x29;" k="49" />
    <hkern u1="&#x40;" u2="&#x2122;" k="70" />
    <hkern u1="&#x40;" u2="&#xbf;" k="20" />
    <hkern u1="&#x40;" u2="&#xa5;" k="20" />
    <hkern u1="&#x40;" u2="x" k="10" />
    <hkern u1="&#x40;" u2="\" k="98" />
    <hkern u1="&#x40;" u2="X" k="63" />
    <hkern u1="&#x40;" u2="V" k="57" />
    <hkern u1="&#x40;" u2="&#x3f;" k="10" />
    <hkern u1="&#x40;" u2="&#x37;" k="45" />
    <hkern u1="&#x40;" u2="&#x34;" k="14" />
    <hkern u1="&#x40;" u2="&#x33;" k="10" />
    <hkern u1="&#x40;" u2="&#x32;" k="10" />
    <hkern u1="&#x40;" u2="&#x2f;" k="129" />
    <hkern u1="&#x40;" u2="&#x2a;" k="20" />
    <hkern u1="&#x40;" u2="&#x26;" k="16" />
    <hkern u1="A" u2="&#x2122;" k="252" />
    <hkern u1="A" u2="&#xbf;" k="14" />
    <hkern u1="A" u2="&#xae;" k="184" />
    <hkern u1="A" u2="x" k="-20" />
    <hkern u1="A" u2="v" k="82" />
    <hkern u1="A" u2="\" k="154" />
    <hkern u1="A" u2="X" k="-41" />
    <hkern u1="A" u2="V" k="188" />
    <hkern u1="A" u2="&#x40;" k="20" />
    <hkern u1="A" u2="&#x3f;" k="106" />
    <hkern u1="A" u2="&#x2a;" k="236" />
    <hkern u1="A" u2="&#x26;" k="20" />
    <hkern u1="B" u2="&#xfb02;" k="16" />
    <hkern u1="B" u2="&#xfb01;" k="16" />
    <hkern u1="B" u2="&#x203a;" k="27" />
    <hkern u1="B" u2="&#x2026;" k="31" />
    <hkern u1="B" u2="&#x201e;" k="20" />
    <hkern u1="B" u2="&#x201d;" k="72" />
    <hkern u1="B" u2="&#x201c;" k="55" />
    <hkern u1="B" u2="&#x201a;" k="20" />
    <hkern u1="B" u2="&#x2019;" k="72" />
    <hkern u1="B" u2="&#x2018;" k="55" />
    <hkern u1="B" u2="&#x2014;" k="25" />
    <hkern u1="B" u2="&#x2013;" k="25" />
    <hkern u1="B" u2="&#x1ef3;" k="25" />
    <hkern u1="B" u2="&#x1ef2;" k="96" />
    <hkern u1="B" u2="&#x1e85;" k="23" />
    <hkern u1="B" u2="&#x1e84;" k="35" />
    <hkern u1="B" u2="&#x1e83;" k="23" />
    <hkern u1="B" u2="&#x1e82;" k="35" />
    <hkern u1="B" u2="&#x1e81;" k="23" />
    <hkern u1="B" u2="&#x1e80;" k="35" />
    <hkern u1="B" u2="&#x1fd;" k="12" />
    <hkern u1="B" u2="&#x1fb;" k="12" />
    <hkern u1="B" u2="&#x1fa;" k="25" />
    <hkern u1="B" u2="&#x17d;" k="10" />
    <hkern u1="B" u2="&#x17b;" k="10" />
    <hkern u1="B" u2="&#x179;" k="10" />
    <hkern u1="B" u2="&#x178;" k="96" />
    <hkern u1="B" u2="&#x177;" k="25" />
    <hkern u1="B" u2="&#x176;" k="96" />
    <hkern u1="B" u2="&#x175;" k="23" />
    <hkern u1="B" u2="&#x174;" k="35" />
    <hkern u1="B" u2="&#x173;" k="6" />
    <hkern u1="B" u2="&#x172;" k="10" />
    <hkern u1="B" u2="&#x171;" k="6" />
    <hkern u1="B" u2="&#x170;" k="10" />
    <hkern u1="B" u2="&#x16f;" k="6" />
    <hkern u1="B" u2="&#x16e;" k="10" />
    <hkern u1="B" u2="&#x16d;" k="6" />
    <hkern u1="B" u2="&#x16c;" k="10" />
    <hkern u1="B" u2="&#x16b;" k="6" />
    <hkern u1="B" u2="&#x16a;" k="10" />
    <hkern u1="B" u2="&#x169;" k="6" />
    <hkern u1="B" u2="&#x168;" k="10" />
    <hkern u1="B" u2="&#x164;" k="76" />
    <hkern u1="B" u2="&#x163;" k="27" />
    <hkern u1="B" u2="&#x162;" k="76" />
    <hkern u1="B" u2="&#x149;" k="72" />
    <hkern u1="B" u2="&#x105;" k="12" />
    <hkern u1="B" u2="&#x104;" k="25" />
    <hkern u1="B" u2="&#x103;" k="12" />
    <hkern u1="B" u2="&#x102;" k="25" />
    <hkern u1="B" u2="&#x101;" k="12" />
    <hkern u1="B" u2="&#x100;" k="25" />
    <hkern u1="B" u2="&#xff;" k="25" />
    <hkern u1="B" u2="&#xfd;" k="25" />
    <hkern u1="B" u2="&#xfc;" k="6" />
    <hkern u1="B" u2="&#xfb;" k="6" />
    <hkern u1="B" u2="&#xfa;" k="6" />
    <hkern u1="B" u2="&#xf9;" k="6" />
    <hkern u1="B" u2="&#xe6;" k="12" />
    <hkern u1="B" u2="&#xe5;" k="12" />
    <hkern u1="B" u2="&#xe4;" k="12" />
    <hkern u1="B" u2="&#xe3;" k="12" />
    <hkern u1="B" u2="&#xe2;" k="12" />
    <hkern u1="B" u2="&#xe1;" k="12" />
    <hkern u1="B" u2="&#xe0;" k="12" />
    <hkern u1="B" u2="&#xdd;" k="96" />
    <hkern u1="B" u2="&#xdc;" k="10" />
    <hkern u1="B" u2="&#xdb;" k="10" />
    <hkern u1="B" u2="&#xda;" k="10" />
    <hkern u1="B" u2="&#xd9;" k="10" />
    <hkern u1="B" u2="&#xc5;" k="25" />
    <hkern u1="B" u2="&#xc4;" k="25" />
    <hkern u1="B" u2="&#xc3;" k="25" />
    <hkern u1="B" u2="&#xc2;" k="25" />
    <hkern u1="B" u2="&#xc1;" k="25" />
    <hkern u1="B" u2="&#xc0;" k="25" />
    <hkern u1="B" u2="&#xbb;" k="27" />
    <hkern u1="B" u2="&#x7d;" k="37" />
    <hkern u1="B" u2="y" k="25" />
    <hkern u1="B" u2="w" k="23" />
    <hkern u1="B" u2="u" k="6" />
    <hkern u1="B" u2="t" k="27" />
    <hkern u1="B" u2="f" k="16" />
    <hkern u1="B" u2="a" k="12" />
    <hkern u1="B" u2="]" k="37" />
    <hkern u1="B" u2="Z" k="10" />
    <hkern u1="B" u2="Y" k="96" />
    <hkern u1="B" u2="W" k="35" />
    <hkern u1="B" u2="U" k="10" />
    <hkern u1="B" u2="T" k="76" />
    <hkern u1="B" u2="A" k="25" />
    <hkern u1="B" u2="&#x3b;" k="16" />
    <hkern u1="B" u2="&#x3a;" k="16" />
    <hkern u1="B" u2="&#x2e;" k="31" />
    <hkern u1="B" u2="&#x2d;" k="25" />
    <hkern u1="B" u2="&#x2c;" k="20" />
    <hkern u1="B" u2="&#x29;" k="37" />
    <hkern u1="B" u2="&#x2122;" k="82" />
    <hkern u1="B" u2="&#xae;" k="41" />
    <hkern u1="B" u2="x" k="31" />
    <hkern u1="B" u2="v" k="20" />
    <hkern u1="B" u2="\" k="76" />
    <hkern u1="B" u2="X" k="16" />
    <hkern u1="B" u2="V" k="72" />
    <hkern u1="B" u2="&#x3f;" k="31" />
    <hkern u1="B" u2="&#x2f;" k="41" />
    <hkern u1="B" u2="&#x2a;" k="61" />
    <hkern u1="C" u2="x" k="6" />
    <hkern u1="C" u2="\" k="31" />
    <hkern u1="C" u2="X" k="31" />
    <hkern u1="C" u2="V" k="41" />
    <hkern u1="C" u2="&#x2f;" k="51" />
    <hkern u1="D" u2="&#x2122;" k="51" />
    <hkern u1="D" u2="x" k="31" />
    <hkern u1="D" u2="v" k="6" />
    <hkern u1="D" u2="\" k="94" />
    <hkern u1="D" u2="X" k="47" />
    <hkern u1="D" u2="V" k="51" />
    <hkern u1="D" u2="&#x2f;" k="55" />
    <hkern u1="D" u2="&#x2a;" k="10" />
    <hkern u1="E" u2="x" k="-10" />
    <hkern u1="E" u2="X" k="-14" />
    <hkern u1="F" g2="Jcircumflex.salt" k="51" />
    <hkern u1="F" g2="J.salt" k="51" />
    <hkern u1="F" u2="&#xfb02;" k="10" />
    <hkern u1="F" u2="&#xfb01;" k="10" />
    <hkern u1="F" u2="&#x2039;" k="4" />
    <hkern u1="F" u2="&#x2026;" k="186" />
    <hkern u1="F" u2="&#x201e;" k="133" />
    <hkern u1="F" u2="&#x201a;" k="133" />
    <hkern u1="F" u2="&#x2014;" k="10" />
    <hkern u1="F" u2="&#x2013;" k="10" />
    <hkern u1="F" u2="&#x1ef3;" k="6" />
    <hkern u1="F" u2="&#x1ef2;" k="-10" />
    <hkern u1="F" u2="&#x1e84;" k="-25" />
    <hkern u1="F" u2="&#x1e82;" k="-25" />
    <hkern u1="F" u2="&#x1e80;" k="-25" />
    <hkern u1="F" u2="&#x219;" k="10" />
    <hkern u1="F" u2="&#x1ff;" k="12" />
    <hkern u1="F" u2="&#x1fe;" k="31" />
    <hkern u1="F" u2="&#x1fd;" k="53" />
    <hkern u1="F" u2="&#x1fb;" k="53" />
    <hkern u1="F" u2="&#x1fa;" k="143" />
    <hkern u1="F" u2="&#x17e;" k="10" />
    <hkern u1="F" u2="&#x17d;" k="10" />
    <hkern u1="F" u2="&#x17c;" k="10" />
    <hkern u1="F" u2="&#x17b;" k="10" />
    <hkern u1="F" u2="&#x17a;" k="10" />
    <hkern u1="F" u2="&#x179;" k="10" />
    <hkern u1="F" u2="&#x178;" k="-10" />
    <hkern u1="F" u2="&#x177;" k="6" />
    <hkern u1="F" u2="&#x176;" k="-10" />
    <hkern u1="F" u2="&#x174;" k="-25" />
    <hkern u1="F" u2="&#x173;" k="10" />
    <hkern u1="F" u2="&#x171;" k="10" />
    <hkern u1="F" u2="&#x16f;" k="10" />
    <hkern u1="F" u2="&#x16d;" k="10" />
    <hkern u1="F" u2="&#x16b;" k="10" />
    <hkern u1="F" u2="&#x169;" k="10" />
    <hkern u1="F" u2="&#x161;" k="10" />
    <hkern u1="F" u2="&#x160;" k="20" />
    <hkern u1="F" u2="&#x15f;" k="10" />
    <hkern u1="F" u2="&#x15e;" k="20" />
    <hkern u1="F" u2="&#x15d;" k="10" />
    <hkern u1="F" u2="&#x15c;" k="20" />
    <hkern u1="F" u2="&#x15b;" k="10" />
    <hkern u1="F" u2="&#x15a;" k="20" />
    <hkern u1="F" u2="&#x159;" k="8" />
    <hkern u1="F" u2="&#x157;" k="8" />
    <hkern u1="F" u2="&#x155;" k="8" />
    <hkern u1="F" u2="&#x152;" k="31" />
    <hkern u1="F" u2="&#x151;" k="12" />
    <hkern u1="F" u2="&#x150;" k="31" />
    <hkern u1="F" u2="&#x14f;" k="12" />
    <hkern u1="F" u2="&#x14e;" k="31" />
    <hkern u1="F" u2="&#x14d;" k="12" />
    <hkern u1="F" u2="&#x14c;" k="31" />
    <hkern u1="F" u2="&#x14b;" k="8" />
    <hkern u1="F" u2="&#x148;" k="8" />
    <hkern u1="F" u2="&#x146;" k="8" />
    <hkern u1="F" u2="&#x144;" k="8" />
    <hkern u1="F" u2="&#x134;" k="133" />
    <hkern u1="F" u2="&#x123;" k="12" />
    <hkern u1="F" u2="&#x122;" k="31" />
    <hkern u1="F" u2="&#x121;" k="12" />
    <hkern u1="F" u2="&#x120;" k="31" />
    <hkern u1="F" u2="&#x11f;" k="12" />
    <hkern u1="F" u2="&#x11e;" k="31" />
    <hkern u1="F" u2="&#x11d;" k="12" />
    <hkern u1="F" u2="&#x11c;" k="31" />
    <hkern u1="F" u2="&#x11b;" k="12" />
    <hkern u1="F" u2="&#x119;" k="12" />
    <hkern u1="F" u2="&#x117;" k="12" />
    <hkern u1="F" u2="&#x115;" k="12" />
    <hkern u1="F" u2="&#x113;" k="12" />
    <hkern u1="F" u2="&#x111;" k="12" />
    <hkern u1="F" u2="&#x10f;" k="12" />
    <hkern u1="F" u2="&#x10d;" k="12" />
    <hkern u1="F" u2="&#x10c;" k="31" />
    <hkern u1="F" u2="&#x10b;" k="12" />
    <hkern u1="F" u2="&#x10a;" k="31" />
    <hkern u1="F" u2="&#x109;" k="12" />
    <hkern u1="F" u2="&#x108;" k="31" />
    <hkern u1="F" u2="&#x107;" k="12" />
    <hkern u1="F" u2="&#x106;" k="31" />
    <hkern u1="F" u2="&#x105;" k="53" />
    <hkern u1="F" u2="&#x104;" k="143" />
    <hkern u1="F" u2="&#x103;" k="53" />
    <hkern u1="F" u2="&#x102;" k="143" />
    <hkern u1="F" u2="&#x101;" k="53" />
    <hkern u1="F" u2="&#x100;" k="143" />
    <hkern u1="F" u2="&#xff;" k="6" />
    <hkern u1="F" u2="&#xfd;" k="6" />
    <hkern u1="F" u2="&#xfc;" k="10" />
    <hkern u1="F" u2="&#xfb;" k="10" />
    <hkern u1="F" u2="&#xfa;" k="10" />
    <hkern u1="F" u2="&#xf9;" k="10" />
    <hkern u1="F" u2="&#xf8;" k="12" />
    <hkern u1="F" u2="&#xf6;" k="12" />
    <hkern u1="F" u2="&#xf5;" k="12" />
    <hkern u1="F" u2="&#xf4;" k="12" />
    <hkern u1="F" u2="&#xf3;" k="12" />
    <hkern u1="F" u2="&#xf2;" k="12" />
    <hkern u1="F" u2="&#xeb;" k="12" />
    <hkern u1="F" u2="&#xea;" k="12" />
    <hkern u1="F" u2="&#xe9;" k="12" />
    <hkern u1="F" u2="&#xe8;" k="12" />
    <hkern u1="F" u2="&#xe7;" k="12" />
    <hkern u1="F" u2="&#xe6;" k="53" />
    <hkern u1="F" u2="&#xe5;" k="53" />
    <hkern u1="F" u2="&#xe4;" k="53" />
    <hkern u1="F" u2="&#xe3;" k="53" />
    <hkern u1="F" u2="&#xe2;" k="53" />
    <hkern u1="F" u2="&#xe1;" k="53" />
    <hkern u1="F" u2="&#xe0;" k="53" />
    <hkern u1="F" u2="&#xdd;" k="-10" />
    <hkern u1="F" u2="&#xd8;" k="31" />
    <hkern u1="F" u2="&#xd6;" k="31" />
    <hkern u1="F" u2="&#xd5;" k="31" />
    <hkern u1="F" u2="&#xd4;" k="31" />
    <hkern u1="F" u2="&#xd3;" k="31" />
    <hkern u1="F" u2="&#xd2;" k="31" />
    <hkern u1="F" u2="&#xc7;" k="31" />
    <hkern u1="F" u2="&#xc5;" k="143" />
    <hkern u1="F" u2="&#xc4;" k="143" />
    <hkern u1="F" u2="&#xc3;" k="143" />
    <hkern u1="F" u2="&#xc2;" k="143" />
    <hkern u1="F" u2="&#xc1;" k="143" />
    <hkern u1="F" u2="&#xc0;" k="143" />
    <hkern u1="F" u2="&#xab;" k="4" />
    <hkern u1="F" u2="&#x7d;" k="-10" />
    <hkern u1="F" u2="z" k="10" />
    <hkern u1="F" u2="y" k="6" />
    <hkern u1="F" u2="u" k="10" />
    <hkern u1="F" u2="s" k="10" />
    <hkern u1="F" u2="r" k="8" />
    <hkern u1="F" u2="q" k="12" />
    <hkern u1="F" u2="p" k="8" />
    <hkern u1="F" u2="o" k="12" />
    <hkern u1="F" u2="n" k="8" />
    <hkern u1="F" u2="m" k="8" />
    <hkern u1="F" u2="g" k="12" />
    <hkern u1="F" u2="f" k="10" />
    <hkern u1="F" u2="e" k="12" />
    <hkern u1="F" u2="d" k="12" />
    <hkern u1="F" u2="c" k="12" />
    <hkern u1="F" u2="a" k="53" />
    <hkern u1="F" u2="]" k="-10" />
    <hkern u1="F" u2="Z" k="10" />
    <hkern u1="F" u2="Y" k="-10" />
    <hkern u1="F" u2="W" k="-25" />
    <hkern u1="F" u2="S" k="20" />
    <hkern u1="F" u2="Q" k="31" />
    <hkern u1="F" u2="O" k="31" />
    <hkern u1="F" u2="J" k="133" />
    <hkern u1="F" u2="G" k="31" />
    <hkern u1="F" u2="C" k="31" />
    <hkern u1="F" u2="A" k="143" />
    <hkern u1="F" u2="&#x3b;" k="25" />
    <hkern u1="F" u2="&#x3a;" k="25" />
    <hkern u1="F" u2="&#x2e;" k="186" />
    <hkern u1="F" u2="&#x2d;" k="10" />
    <hkern u1="F" u2="&#x2c;" k="133" />
    <hkern u1="F" u2="&#x29;" k="-10" />
    <hkern u1="F" u2="&#x2122;" k="-20" />
    <hkern u1="F" u2="&#xbf;" k="31" />
    <hkern u1="F" u2="&#xae;" k="-14" />
    <hkern u1="F" u2="x" k="16" />
    <hkern u1="F" u2="v" k="6" />
    <hkern u1="F" u2="\" k="-20" />
    <hkern u1="F" u2="V" k="-25" />
    <hkern u1="F" u2="&#x40;" k="10" />
    <hkern u1="F" u2="&#x2f;" k="86" />
    <hkern u1="G" u2="&#x2122;" k="20" />
    <hkern u1="G" u2="\" k="76" />
    <hkern u1="G" u2="X" k="20" />
    <hkern u1="G" u2="V" k="35" />
    <hkern u1="G" u2="&#x2f;" k="57" />
    <hkern u1="J" u2="&#x2f;" k="35" />
    <hkern u1="K" u2="&#xbf;" k="14" />
    <hkern u1="K" u2="&#xae;" k="66" />
    <hkern u1="K" u2="x" k="-35" />
    <hkern u1="K" u2="v" k="70" />
    <hkern u1="K" u2="X" k="-27" />
    <hkern u1="K" u2="&#x40;" k="25" />
    <hkern u1="K" u2="&#x3f;" k="12" />
    <hkern u1="K" u2="&#x2f;" k="-20" />
    <hkern u1="K" u2="&#x2a;" k="41" />
    <hkern u1="K" u2="&#x26;" k="6" />
    <hkern u1="L" u2="&#x2122;" k="471" />
    <hkern u1="L" u2="&#xae;" k="350" />
    <hkern u1="L" u2="x" k="-10" />
    <hkern u1="L" u2="v" k="129" />
    <hkern u1="L" u2="\" k="223" />
    <hkern u1="L" u2="X" k="-14" />
    <hkern u1="L" u2="V" k="205" />
    <hkern u1="L" u2="&#x40;" k="29" />
    <hkern u1="L" u2="&#x3f;" k="119" />
    <hkern u1="L" u2="&#x2f;" k="-14" />
    <hkern u1="L" u2="&#x2a;" k="440" />
    <hkern u1="L" u2="&#x26;" k="14" />
    <hkern u1="O" u2="&#x2122;" k="51" />
    <hkern u1="O" u2="x" k="31" />
    <hkern u1="O" u2="v" k="6" />
    <hkern u1="O" u2="\" k="94" />
    <hkern u1="O" u2="X" k="47" />
    <hkern u1="O" u2="V" k="51" />
    <hkern u1="O" u2="&#x2f;" k="55" />
    <hkern u1="O" u2="&#x2a;" k="10" />
    <hkern u1="P" g2="Jcircumflex.salt" k="55" />
    <hkern u1="P" g2="J.salt" k="55" />
    <hkern u1="P" u2="&#xfb02;" k="-6" />
    <hkern u1="P" u2="&#xfb01;" k="-6" />
    <hkern u1="P" u2="&#x2026;" k="266" />
    <hkern u1="P" u2="&#x201e;" k="225" />
    <hkern u1="P" u2="&#x201d;" k="-16" />
    <hkern u1="P" u2="&#x201c;" k="-31" />
    <hkern u1="P" u2="&#x201a;" k="225" />
    <hkern u1="P" u2="&#x2019;" k="-16" />
    <hkern u1="P" u2="&#x2018;" k="-31" />
    <hkern u1="P" u2="&#x2014;" k="10" />
    <hkern u1="P" u2="&#x2013;" k="10" />
    <hkern u1="P" u2="&#x1ef3;" k="-20" />
    <hkern u1="P" u2="&#x1ef2;" k="20" />
    <hkern u1="P" u2="&#x1e85;" k="-25" />
    <hkern u1="P" u2="&#x1e83;" k="-25" />
    <hkern u1="P" u2="&#x1e81;" k="-25" />
    <hkern u1="P" u2="&#x1ff;" k="10" />
    <hkern u1="P" u2="&#x1fd;" k="41" />
    <hkern u1="P" u2="&#x1fb;" k="41" />
    <hkern u1="P" u2="&#x1fa;" k="150" />
    <hkern u1="P" u2="&#x17d;" k="29" />
    <hkern u1="P" u2="&#x17b;" k="29" />
    <hkern u1="P" u2="&#x179;" k="29" />
    <hkern u1="P" u2="&#x178;" k="20" />
    <hkern u1="P" u2="&#x177;" k="-20" />
    <hkern u1="P" u2="&#x176;" k="20" />
    <hkern u1="P" u2="&#x175;" k="-25" />
    <hkern u1="P" u2="&#x164;" k="10" />
    <hkern u1="P" u2="&#x163;" k="-4" />
    <hkern u1="P" u2="&#x162;" k="10" />
    <hkern u1="P" u2="&#x151;" k="10" />
    <hkern u1="P" u2="&#x14f;" k="10" />
    <hkern u1="P" u2="&#x14d;" k="10" />
    <hkern u1="P" u2="&#x149;" k="-16" />
    <hkern u1="P" u2="&#x134;" k="143" />
    <hkern u1="P" u2="&#x123;" k="10" />
    <hkern u1="P" u2="&#x121;" k="10" />
    <hkern u1="P" u2="&#x11f;" k="10" />
    <hkern u1="P" u2="&#x11d;" k="10" />
    <hkern u1="P" u2="&#x11b;" k="10" />
    <hkern u1="P" u2="&#x119;" k="10" />
    <hkern u1="P" u2="&#x117;" k="10" />
    <hkern u1="P" u2="&#x115;" k="10" />
    <hkern u1="P" u2="&#x113;" k="10" />
    <hkern u1="P" u2="&#x111;" k="10" />
    <hkern u1="P" u2="&#x10f;" k="10" />
    <hkern u1="P" u2="&#x10d;" k="10" />
    <hkern u1="P" u2="&#x10b;" k="10" />
    <hkern u1="P" u2="&#x109;" k="10" />
    <hkern u1="P" u2="&#x107;" k="10" />
    <hkern u1="P" u2="&#x105;" k="41" />
    <hkern u1="P" u2="&#x104;" k="150" />
    <hkern u1="P" u2="&#x103;" k="41" />
    <hkern u1="P" u2="&#x102;" k="150" />
    <hkern u1="P" u2="&#x101;" k="41" />
    <hkern u1="P" u2="&#x100;" k="150" />
    <hkern u1="P" u2="&#xff;" k="-20" />
    <hkern u1="P" u2="&#xfd;" k="-20" />
    <hkern u1="P" u2="&#xf8;" k="10" />
    <hkern u1="P" u2="&#xf6;" k="10" />
    <hkern u1="P" u2="&#xf5;" k="10" />
    <hkern u1="P" u2="&#xf4;" k="10" />
    <hkern u1="P" u2="&#xf3;" k="10" />
    <hkern u1="P" u2="&#xf2;" k="10" />
    <hkern u1="P" u2="&#xeb;" k="10" />
    <hkern u1="P" u2="&#xea;" k="10" />
    <hkern u1="P" u2="&#xe9;" k="10" />
    <hkern u1="P" u2="&#xe8;" k="10" />
    <hkern u1="P" u2="&#xe7;" k="10" />
    <hkern u1="P" u2="&#xe6;" k="41" />
    <hkern u1="P" u2="&#xe5;" k="41" />
    <hkern u1="P" u2="&#xe4;" k="41" />
    <hkern u1="P" u2="&#xe3;" k="41" />
    <hkern u1="P" u2="&#xe2;" k="41" />
    <hkern u1="P" u2="&#xe1;" k="41" />
    <hkern u1="P" u2="&#xe0;" k="41" />
    <hkern u1="P" u2="&#xdd;" k="20" />
    <hkern u1="P" u2="&#xc5;" k="150" />
    <hkern u1="P" u2="&#xc4;" k="150" />
    <hkern u1="P" u2="&#xc3;" k="150" />
    <hkern u1="P" u2="&#xc2;" k="150" />
    <hkern u1="P" u2="&#xc1;" k="150" />
    <hkern u1="P" u2="&#xc0;" k="150" />
    <hkern u1="P" u2="&#xba;" k="-10" />
    <hkern u1="P" u2="&#xaa;" k="-10" />
    <hkern u1="P" u2="&#x7d;" k="35" />
    <hkern u1="P" u2="y" k="-20" />
    <hkern u1="P" u2="w" k="-25" />
    <hkern u1="P" u2="t" k="-4" />
    <hkern u1="P" u2="q" k="10" />
    <hkern u1="P" u2="o" k="10" />
    <hkern u1="P" u2="g" k="10" />
    <hkern u1="P" u2="f" k="-6" />
    <hkern u1="P" u2="e" k="10" />
    <hkern u1="P" u2="d" k="10" />
    <hkern u1="P" u2="c" k="10" />
    <hkern u1="P" u2="a" k="41" />
    <hkern u1="P" u2="]" k="35" />
    <hkern u1="P" u2="Z" k="29" />
    <hkern u1="P" u2="Y" k="20" />
    <hkern u1="P" u2="T" k="10" />
    <hkern u1="P" u2="J" k="143" />
    <hkern u1="P" u2="A" k="150" />
    <hkern u1="P" u2="&#x3b;" k="16" />
    <hkern u1="P" u2="&#x3a;" k="16" />
    <hkern u1="P" u2="&#x2e;" k="266" />
    <hkern u1="P" u2="&#x2d;" k="10" />
    <hkern u1="P" u2="&#x2c;" k="225" />
    <hkern u1="P" u2="&#x29;" k="35" />
    <hkern u1="P" u2="&#xbf;" k="57" />
    <hkern u1="P" u2="&#xae;" k="-25" />
    <hkern u1="P" u2="x" k="-4" />
    <hkern u1="P" u2="v" k="-20" />
    <hkern u1="P" u2="\" k="27" />
    <hkern u1="P" u2="X" k="41" />
    <hkern u1="P" u2="V" k="10" />
    <hkern u1="P" u2="&#x40;" k="10" />
    <hkern u1="P" u2="&#x3f;" k="-16" />
    <hkern u1="P" u2="&#x2f;" k="143" />
    <hkern u1="P" u2="&#x21;" k="-10" />
    <hkern u1="Q" u2="&#x2122;" k="51" />
    <hkern u1="Q" u2="x" k="31" />
    <hkern u1="Q" u2="v" k="6" />
    <hkern u1="Q" u2="\" k="94" />
    <hkern u1="Q" u2="X" k="47" />
    <hkern u1="Q" u2="V" k="51" />
    <hkern u1="Q" u2="&#x2f;" k="55" />
    <hkern u1="Q" u2="&#x2a;" k="10" />
    <hkern u1="R" u2="&#x2122;" k="41" />
    <hkern u1="R" u2="&#xbf;" k="10" />
    <hkern u1="R" u2="&#xae;" k="10" />
    <hkern u1="R" u2="x" k="-27" />
    <hkern u1="R" u2="\" k="51" />
    <hkern u1="R" u2="X" k="-20" />
    <hkern u1="R" u2="V" k="45" />
    <hkern u1="R" u2="&#x40;" k="10" />
    <hkern u1="R" u2="&#x3f;" k="10" />
    <hkern u1="R" u2="&#x2a;" k="20" />
    <hkern u1="R" u2="&#x26;" k="10" />
    <hkern u1="S" u2="&#x2122;" k="61" />
    <hkern u1="S" u2="x" k="14" />
    <hkern u1="S" u2="v" k="20" />
    <hkern u1="S" u2="\" k="78" />
    <hkern u1="S" u2="X" k="10" />
    <hkern u1="S" u2="V" k="68" />
    <hkern u1="S" u2="&#x3f;" k="10" />
    <hkern u1="S" u2="&#x2f;" k="57" />
    <hkern u1="S" u2="&#x2a;" k="31" />
    <hkern u1="T" u2="&#x2122;" k="-14" />
    <hkern u1="T" u2="&#xbf;" k="121" />
    <hkern u1="T" u2="x" k="143" />
    <hkern u1="T" u2="v" k="127" />
    <hkern u1="T" u2="i" k="14" />
    <hkern u1="T" u2="X" k="-10" />
    <hkern u1="T" u2="V" k="-20" />
    <hkern u1="T" u2="&#x40;" k="66" />
    <hkern u1="T" u2="&#x3f;" k="-6" />
    <hkern u1="T" u2="&#x2f;" k="180" />
    <hkern u1="T" u2="&#x26;" k="35" />
    <hkern u1="T" u2="&#x21;" k="-10" />
    <hkern u1="U" u2="&#x2f;" k="41" />
    <hkern u1="V" g2="Jcircumflex.salt" k="55" />
    <hkern u1="V" g2="J.salt" k="55" />
    <hkern u1="V" u2="&#xfb02;" k="41" />
    <hkern u1="V" u2="&#xfb01;" k="41" />
    <hkern u1="V" u2="&#x2117;" k="41" />
    <hkern u1="V" u2="&#x203a;" k="82" />
    <hkern u1="V" u2="&#x2039;" k="109" />
    <hkern u1="V" u2="&#x2026;" k="276" />
    <hkern u1="V" u2="&#x201e;" k="211" />
    <hkern u1="V" u2="&#x201a;" k="211" />
    <hkern u1="V" u2="&#x2014;" k="123" />
    <hkern u1="V" u2="&#x2013;" k="123" />
    <hkern u1="V" u2="&#x1ef3;" k="23" />
    <hkern u1="V" u2="&#x1e85;" k="35" />
    <hkern u1="V" u2="&#x1e83;" k="35" />
    <hkern u1="V" u2="&#x1e81;" k="35" />
    <hkern u1="V" u2="&#x219;" k="96" />
    <hkern u1="V" u2="&#x1ff;" k="104" />
    <hkern u1="V" u2="&#x1fe;" k="51" />
    <hkern u1="V" u2="&#x1fd;" k="145" />
    <hkern u1="V" u2="&#x1fb;" k="145" />
    <hkern u1="V" u2="&#x1fa;" k="188" />
    <hkern u1="V" u2="&#x17e;" k="57" />
    <hkern u1="V" u2="&#x17c;" k="57" />
    <hkern u1="V" u2="&#x17a;" k="57" />
    <hkern u1="V" u2="&#x177;" k="23" />
    <hkern u1="V" u2="&#x175;" k="35" />
    <hkern u1="V" u2="&#x173;" k="57" />
    <hkern u1="V" u2="&#x171;" k="57" />
    <hkern u1="V" u2="&#x16f;" k="57" />
    <hkern u1="V" u2="&#x16d;" k="57" />
    <hkern u1="V" u2="&#x16b;" k="57" />
    <hkern u1="V" u2="&#x169;" k="57" />
    <hkern u1="V" u2="&#x164;" k="-20" />
    <hkern u1="V" u2="&#x163;" k="31" />
    <hkern u1="V" u2="&#x162;" k="-20" />
    <hkern u1="V" u2="&#x161;" k="96" />
    <hkern u1="V" u2="&#x160;" k="41" />
    <hkern u1="V" u2="&#x15f;" k="96" />
    <hkern u1="V" u2="&#x15e;" k="41" />
    <hkern u1="V" u2="&#x15d;" k="96" />
    <hkern u1="V" u2="&#x15c;" k="41" />
    <hkern u1="V" u2="&#x15b;" k="96" />
    <hkern u1="V" u2="&#x15a;" k="41" />
    <hkern u1="V" u2="&#x159;" k="61" />
    <hkern u1="V" u2="&#x157;" k="61" />
    <hkern u1="V" u2="&#x155;" k="61" />
    <hkern u1="V" u2="&#x152;" k="51" />
    <hkern u1="V" u2="&#x151;" k="104" />
    <hkern u1="V" u2="&#x150;" k="51" />
    <hkern u1="V" u2="&#x14f;" k="104" />
    <hkern u1="V" u2="&#x14e;" k="51" />
    <hkern u1="V" u2="&#x14d;" k="104" />
    <hkern u1="V" u2="&#x14c;" k="51" />
    <hkern u1="V" u2="&#x14b;" k="61" />
    <hkern u1="V" u2="&#x148;" k="61" />
    <hkern u1="V" u2="&#x146;" k="61" />
    <hkern u1="V" u2="&#x144;" k="61" />
    <hkern u1="V" u2="&#x134;" k="158" />
    <hkern u1="V" u2="&#x123;" k="104" />
    <hkern u1="V" u2="&#x122;" k="51" />
    <hkern u1="V" u2="&#x121;" k="104" />
    <hkern u1="V" u2="&#x120;" k="51" />
    <hkern u1="V" u2="&#x11f;" k="104" />
    <hkern u1="V" u2="&#x11e;" k="51" />
    <hkern u1="V" u2="&#x11d;" k="104" />
    <hkern u1="V" u2="&#x11c;" k="51" />
    <hkern u1="V" u2="&#x11b;" k="104" />
    <hkern u1="V" u2="&#x119;" k="104" />
    <hkern u1="V" u2="&#x117;" k="104" />
    <hkern u1="V" u2="&#x115;" k="104" />
    <hkern u1="V" u2="&#x113;" k="104" />
    <hkern u1="V" u2="&#x111;" k="104" />
    <hkern u1="V" u2="&#x10f;" k="104" />
    <hkern u1="V" u2="&#x10d;" k="104" />
    <hkern u1="V" u2="&#x10c;" k="51" />
    <hkern u1="V" u2="&#x10b;" k="104" />
    <hkern u1="V" u2="&#x10a;" k="51" />
    <hkern u1="V" u2="&#x109;" k="104" />
    <hkern u1="V" u2="&#x108;" k="51" />
    <hkern u1="V" u2="&#x107;" k="104" />
    <hkern u1="V" u2="&#x106;" k="51" />
    <hkern u1="V" u2="&#x105;" k="145" />
    <hkern u1="V" u2="&#x104;" k="188" />
    <hkern u1="V" u2="&#x103;" k="145" />
    <hkern u1="V" u2="&#x102;" k="188" />
    <hkern u1="V" u2="&#x101;" k="145" />
    <hkern u1="V" u2="&#x100;" k="188" />
    <hkern u1="V" u2="&#xff;" k="23" />
    <hkern u1="V" u2="&#xfd;" k="23" />
    <hkern u1="V" u2="&#xfc;" k="57" />
    <hkern u1="V" u2="&#xfb;" k="57" />
    <hkern u1="V" u2="&#xfa;" k="57" />
    <hkern u1="V" u2="&#xf9;" k="57" />
    <hkern u1="V" u2="&#xf8;" k="104" />
    <hkern u1="V" u2="&#xf6;" k="104" />
    <hkern u1="V" u2="&#xf5;" k="104" />
    <hkern u1="V" u2="&#xf4;" k="104" />
    <hkern u1="V" u2="&#xf3;" k="104" />
    <hkern u1="V" u2="&#xf2;" k="104" />
    <hkern u1="V" u2="&#xeb;" k="104" />
    <hkern u1="V" u2="&#xea;" k="104" />
    <hkern u1="V" u2="&#xe9;" k="104" />
    <hkern u1="V" u2="&#xe8;" k="104" />
    <hkern u1="V" u2="&#xe7;" k="104" />
    <hkern u1="V" u2="&#xe6;" k="145" />
    <hkern u1="V" u2="&#xe5;" k="145" />
    <hkern u1="V" u2="&#xe4;" k="145" />
    <hkern u1="V" u2="&#xe3;" k="145" />
    <hkern u1="V" u2="&#xe2;" k="145" />
    <hkern u1="V" u2="&#xe1;" k="145" />
    <hkern u1="V" u2="&#xe0;" k="145" />
    <hkern u1="V" u2="&#xd8;" k="51" />
    <hkern u1="V" u2="&#xd6;" k="51" />
    <hkern u1="V" u2="&#xd5;" k="51" />
    <hkern u1="V" u2="&#xd4;" k="51" />
    <hkern u1="V" u2="&#xd3;" k="51" />
    <hkern u1="V" u2="&#xd2;" k="51" />
    <hkern u1="V" u2="&#xc7;" k="51" />
    <hkern u1="V" u2="&#xc5;" k="188" />
    <hkern u1="V" u2="&#xc4;" k="188" />
    <hkern u1="V" u2="&#xc3;" k="188" />
    <hkern u1="V" u2="&#xc2;" k="188" />
    <hkern u1="V" u2="&#xc1;" k="188" />
    <hkern u1="V" u2="&#xc0;" k="188" />
    <hkern u1="V" u2="&#xbb;" k="82" />
    <hkern u1="V" u2="&#xab;" k="109" />
    <hkern u1="V" u2="&#xa9;" k="41" />
    <hkern u1="V" u2="z" k="57" />
    <hkern u1="V" u2="y" k="23" />
    <hkern u1="V" u2="w" k="35" />
    <hkern u1="V" u2="u" k="57" />
    <hkern u1="V" u2="t" k="31" />
    <hkern u1="V" u2="s" k="96" />
    <hkern u1="V" u2="r" k="61" />
    <hkern u1="V" u2="q" k="104" />
    <hkern u1="V" u2="p" k="61" />
    <hkern u1="V" u2="o" k="104" />
    <hkern u1="V" u2="n" k="61" />
    <hkern u1="V" u2="m" k="61" />
    <hkern u1="V" u2="g" k="104" />
    <hkern u1="V" u2="f" k="41" />
    <hkern u1="V" u2="e" k="104" />
    <hkern u1="V" u2="d" k="104" />
    <hkern u1="V" u2="c" k="104" />
    <hkern u1="V" u2="a" k="145" />
    <hkern u1="V" u2="T" k="-20" />
    <hkern u1="V" u2="S" k="41" />
    <hkern u1="V" u2="Q" k="51" />
    <hkern u1="V" u2="O" k="51" />
    <hkern u1="V" u2="J" k="158" />
    <hkern u1="V" u2="G" k="51" />
    <hkern u1="V" u2="C" k="51" />
    <hkern u1="V" u2="A" k="188" />
    <hkern u1="V" u2="&#x3b;" k="127" />
    <hkern u1="V" u2="&#x3a;" k="127" />
    <hkern u1="V" u2="&#x2e;" k="276" />
    <hkern u1="V" u2="&#x2d;" k="123" />
    <hkern u1="V" u2="&#x2c;" k="211" />
    <hkern u1="V" u2="&#x2122;" k="-27" />
    <hkern u1="V" u2="&#xbf;" k="96" />
    <hkern u1="V" u2="&#xae;" k="20" />
    <hkern u1="V" u2="&#xa1;" k="27" />
    <hkern u1="V" u2="x" k="47" />
    <hkern u1="V" u2="v" k="23" />
    <hkern u1="V" u2="i" k="16" />
    <hkern u1="V" u2="h" k="6" />
    <hkern u1="V" u2="&#x40;" k="78" />
    <hkern u1="V" u2="&#x3f;" k="10" />
    <hkern u1="V" u2="&#x2f;" k="195" />
    <hkern u1="V" u2="&#x2a;" k="10" />
    <hkern u1="V" u2="&#x26;" k="47" />
    <hkern u1="W" u2="&#x2122;" k="-27" />
    <hkern u1="W" u2="&#xbf;" k="51" />
    <hkern u1="W" u2="&#xa1;" k="20" />
    <hkern u1="W" u2="x" k="20" />
    <hkern u1="W" u2="v" k="6" />
    <hkern u1="W" u2="\" k="-4" />
    <hkern u1="W" u2="&#x40;" k="35" />
    <hkern u1="W" u2="&#x2f;" k="104" />
    <hkern u1="W" u2="&#x2a;" k="14" />
    <hkern u1="W" u2="&#x26;" k="16" />
    <hkern u1="X" g2="Jcircumflex.salt" k="25" />
    <hkern u1="X" g2="J.salt" k="25" />
    <hkern u1="X" u2="&#xfb02;" k="16" />
    <hkern u1="X" u2="&#xfb01;" k="16" />
    <hkern u1="X" u2="&#x2117;" k="33" />
    <hkern u1="X" u2="&#x203a;" k="6" />
    <hkern u1="X" u2="&#x2039;" k="53" />
    <hkern u1="X" u2="&#x2026;" k="10" />
    <hkern u1="X" u2="&#x201e;" k="-14" />
    <hkern u1="X" u2="&#x201d;" k="57" />
    <hkern u1="X" u2="&#x201c;" k="70" />
    <hkern u1="X" u2="&#x201a;" k="-14" />
    <hkern u1="X" u2="&#x2019;" k="57" />
    <hkern u1="X" u2="&#x2018;" k="70" />
    <hkern u1="X" u2="&#x2014;" k="133" />
    <hkern u1="X" u2="&#x2013;" k="133" />
    <hkern u1="X" u2="&#x1ef3;" k="59" />
    <hkern u1="X" u2="&#x1e85;" k="18" />
    <hkern u1="X" u2="&#x1e83;" k="18" />
    <hkern u1="X" u2="&#x1e81;" k="18" />
    <hkern u1="X" u2="&#x219;" k="20" />
    <hkern u1="X" u2="&#x1ff;" k="35" />
    <hkern u1="X" u2="&#x1fe;" k="47" />
    <hkern u1="X" u2="&#x1fd;" k="20" />
    <hkern u1="X" u2="&#x1fb;" k="20" />
    <hkern u1="X" u2="&#x1fa;" k="-41" />
    <hkern u1="X" u2="&#x17e;" k="-14" />
    <hkern u1="X" u2="&#x17c;" k="-14" />
    <hkern u1="X" u2="&#x17a;" k="-14" />
    <hkern u1="X" u2="&#x177;" k="59" />
    <hkern u1="X" u2="&#x175;" k="18" />
    <hkern u1="X" u2="&#x173;" k="27" />
    <hkern u1="X" u2="&#x172;" k="4" />
    <hkern u1="X" u2="&#x171;" k="27" />
    <hkern u1="X" u2="&#x170;" k="4" />
    <hkern u1="X" u2="&#x16f;" k="27" />
    <hkern u1="X" u2="&#x16e;" k="4" />
    <hkern u1="X" u2="&#x16d;" k="27" />
    <hkern u1="X" u2="&#x16c;" k="4" />
    <hkern u1="X" u2="&#x16b;" k="27" />
    <hkern u1="X" u2="&#x16a;" k="4" />
    <hkern u1="X" u2="&#x169;" k="27" />
    <hkern u1="X" u2="&#x168;" k="4" />
    <hkern u1="X" u2="&#x164;" k="-10" />
    <hkern u1="X" u2="&#x163;" k="33" />
    <hkern u1="X" u2="&#x162;" k="-10" />
    <hkern u1="X" u2="&#x161;" k="20" />
    <hkern u1="X" u2="&#x160;" k="23" />
    <hkern u1="X" u2="&#x15f;" k="20" />
    <hkern u1="X" u2="&#x15e;" k="23" />
    <hkern u1="X" u2="&#x15d;" k="20" />
    <hkern u1="X" u2="&#x15c;" k="23" />
    <hkern u1="X" u2="&#x15b;" k="20" />
    <hkern u1="X" u2="&#x15a;" k="23" />
    <hkern u1="X" u2="&#x152;" k="47" />
    <hkern u1="X" u2="&#x151;" k="35" />
    <hkern u1="X" u2="&#x150;" k="47" />
    <hkern u1="X" u2="&#x14f;" k="35" />
    <hkern u1="X" u2="&#x14e;" k="47" />
    <hkern u1="X" u2="&#x14d;" k="35" />
    <hkern u1="X" u2="&#x14c;" k="47" />
    <hkern u1="X" u2="&#x149;" k="57" />
    <hkern u1="X" u2="&#x134;" k="23" />
    <hkern u1="X" u2="&#x123;" k="35" />
    <hkern u1="X" u2="&#x122;" k="47" />
    <hkern u1="X" u2="&#x121;" k="35" />
    <hkern u1="X" u2="&#x120;" k="47" />
    <hkern u1="X" u2="&#x11f;" k="35" />
    <hkern u1="X" u2="&#x11e;" k="47" />
    <hkern u1="X" u2="&#x11d;" k="35" />
    <hkern u1="X" u2="&#x11c;" k="47" />
    <hkern u1="X" u2="&#x11b;" k="35" />
    <hkern u1="X" u2="&#x119;" k="35" />
    <hkern u1="X" u2="&#x117;" k="35" />
    <hkern u1="X" u2="&#x115;" k="35" />
    <hkern u1="X" u2="&#x113;" k="35" />
    <hkern u1="X" u2="&#x111;" k="35" />
    <hkern u1="X" u2="&#x10f;" k="35" />
    <hkern u1="X" u2="&#x10d;" k="35" />
    <hkern u1="X" u2="&#x10c;" k="47" />
    <hkern u1="X" u2="&#x10b;" k="35" />
    <hkern u1="X" u2="&#x10a;" k="47" />
    <hkern u1="X" u2="&#x109;" k="35" />
    <hkern u1="X" u2="&#x108;" k="47" />
    <hkern u1="X" u2="&#x107;" k="35" />
    <hkern u1="X" u2="&#x106;" k="47" />
    <hkern u1="X" u2="&#x105;" k="20" />
    <hkern u1="X" u2="&#x104;" k="-41" />
    <hkern u1="X" u2="&#x103;" k="20" />
    <hkern u1="X" u2="&#x102;" k="-41" />
    <hkern u1="X" u2="&#x101;" k="20" />
    <hkern u1="X" u2="&#x100;" k="-41" />
    <hkern u1="X" u2="&#xff;" k="59" />
    <hkern u1="X" u2="&#xfd;" k="59" />
    <hkern u1="X" u2="&#xfc;" k="27" />
    <hkern u1="X" u2="&#xfb;" k="27" />
    <hkern u1="X" u2="&#xfa;" k="27" />
    <hkern u1="X" u2="&#xf9;" k="27" />
    <hkern u1="X" u2="&#xf8;" k="35" />
    <hkern u1="X" u2="&#xf6;" k="35" />
    <hkern u1="X" u2="&#xf5;" k="35" />
    <hkern u1="X" u2="&#xf4;" k="35" />
    <hkern u1="X" u2="&#xf3;" k="35" />
    <hkern u1="X" u2="&#xf2;" k="35" />
    <hkern u1="X" u2="&#xeb;" k="35" />
    <hkern u1="X" u2="&#xea;" k="35" />
    <hkern u1="X" u2="&#xe9;" k="35" />
    <hkern u1="X" u2="&#xe8;" k="35" />
    <hkern u1="X" u2="&#xe7;" k="35" />
    <hkern u1="X" u2="&#xe6;" k="20" />
    <hkern u1="X" u2="&#xe5;" k="20" />
    <hkern u1="X" u2="&#xe4;" k="20" />
    <hkern u1="X" u2="&#xe3;" k="20" />
    <hkern u1="X" u2="&#xe2;" k="20" />
    <hkern u1="X" u2="&#xe1;" k="20" />
    <hkern u1="X" u2="&#xe0;" k="20" />
    <hkern u1="X" u2="&#xdc;" k="4" />
    <hkern u1="X" u2="&#xdb;" k="4" />
    <hkern u1="X" u2="&#xda;" k="4" />
    <hkern u1="X" u2="&#xd9;" k="4" />
    <hkern u1="X" u2="&#xd8;" k="47" />
    <hkern u1="X" u2="&#xd6;" k="47" />
    <hkern u1="X" u2="&#xd5;" k="47" />
    <hkern u1="X" u2="&#xd4;" k="47" />
    <hkern u1="X" u2="&#xd3;" k="47" />
    <hkern u1="X" u2="&#xd2;" k="47" />
    <hkern u1="X" u2="&#xc7;" k="47" />
    <hkern u1="X" u2="&#xc5;" k="-41" />
    <hkern u1="X" u2="&#xc4;" k="-41" />
    <hkern u1="X" u2="&#xc3;" k="-41" />
    <hkern u1="X" u2="&#xc2;" k="-41" />
    <hkern u1="X" u2="&#xc1;" k="-41" />
    <hkern u1="X" u2="&#xc0;" k="-41" />
    <hkern u1="X" u2="&#xbb;" k="6" />
    <hkern u1="X" u2="&#xba;" k="12" />
    <hkern u1="X" u2="&#xab;" k="53" />
    <hkern u1="X" u2="&#xaa;" k="12" />
    <hkern u1="X" u2="&#xa9;" k="33" />
    <hkern u1="X" u2="z" k="-14" />
    <hkern u1="X" u2="y" k="59" />
    <hkern u1="X" u2="w" k="18" />
    <hkern u1="X" u2="u" k="27" />
    <hkern u1="X" u2="t" k="33" />
    <hkern u1="X" u2="s" k="20" />
    <hkern u1="X" u2="q" k="35" />
    <hkern u1="X" u2="o" k="35" />
    <hkern u1="X" u2="g" k="35" />
    <hkern u1="X" u2="f" k="16" />
    <hkern u1="X" u2="e" k="35" />
    <hkern u1="X" u2="d" k="35" />
    <hkern u1="X" u2="c" k="35" />
    <hkern u1="X" u2="a" k="20" />
    <hkern u1="X" u2="U" k="4" />
    <hkern u1="X" u2="T" k="-10" />
    <hkern u1="X" u2="S" k="23" />
    <hkern u1="X" u2="Q" k="47" />
    <hkern u1="X" u2="O" k="47" />
    <hkern u1="X" u2="J" k="23" />
    <hkern u1="X" u2="G" k="47" />
    <hkern u1="X" u2="C" k="47" />
    <hkern u1="X" u2="A" k="-41" />
    <hkern u1="X" u2="&#x3b;" k="10" />
    <hkern u1="X" u2="&#x3a;" k="10" />
    <hkern u1="X" u2="&#x2e;" k="10" />
    <hkern u1="X" u2="&#x2d;" k="133" />
    <hkern u1="X" u2="&#x2c;" k="-14" />
    <hkern u1="X" u2="&#xbf;" k="10" />
    <hkern u1="X" u2="&#xae;" k="53" />
    <hkern u1="X" u2="x" k="-31" />
    <hkern u1="X" u2="v" k="53" />
    <hkern u1="X" u2="X" k="-10" />
    <hkern u1="X" u2="&#x40;" k="45" />
    <hkern u1="X" u2="&#x3f;" k="14" />
    <hkern u1="X" u2="&#x2a;" k="72" />
    <hkern u1="X" u2="&#x26;" k="12" />
    <hkern u1="X" u2="&#x21;" k="-10" />
    <hkern u1="Y" u2="&#x2122;" k="-37" />
    <hkern u1="Y" u2="&#xbf;" k="139" />
    <hkern u1="Y" u2="&#xae;" k="35" />
    <hkern u1="Y" u2="&#xa1;" k="41" />
    <hkern u1="Y" u2="x" k="92" />
    <hkern u1="Y" u2="v" k="72" />
    <hkern u1="Y" u2="i" k="12" />
    <hkern u1="Y" u2="&#x40;" k="119" />
    <hkern u1="Y" u2="&#x3f;" k="6" />
    <hkern u1="Y" u2="&#x2f;" k="225" />
    <hkern u1="Y" u2="&#x2a;" k="31" />
    <hkern u1="Y" u2="&#x26;" k="92" />
    <hkern u1="Y" u2="&#x21;" k="10" />
    <hkern u1="Z" u2="&#x2122;" k="6" />
    <hkern u1="Z" u2="&#xbf;" k="14" />
    <hkern u1="Z" u2="&#xae;" k="27" />
    <hkern u1="Z" u2="v" k="35" />
    <hkern u1="Z" u2="&#x40;" k="20" />
    <hkern u1="Z" u2="&#x3f;" k="16" />
    <hkern u1="Z" u2="&#x2a;" k="20" />
    <hkern u1="Z" u2="&#x26;" k="20" />
    <hkern u1="[" u2="&#x2122;" k="20" />
    <hkern u1="[" u2="&#x20ac;" k="74" />
    <hkern u1="[" u2="&#xbf;" k="35" />
    <hkern u1="[" u2="&#xae;" k="41" />
    <hkern u1="[" u2="&#xa5;" k="20" />
    <hkern u1="[" u2="&#xa2;" k="41" />
    <hkern u1="[" u2="x" k="4" />
    <hkern u1="[" u2="v" k="25" />
    <hkern u1="[" u2="j" k="-45" />
    <hkern u1="[" u2="i" k="10" />
    <hkern u1="[" u2="\" k="20" />
    <hkern u1="[" u2="&#x40;" k="41" />
    <hkern u1="[" u2="&#x3f;" k="16" />
    <hkern u1="[" u2="&#x39;" k="45" />
    <hkern u1="[" u2="&#x38;" k="45" />
    <hkern u1="[" u2="&#x37;" k="10" />
    <hkern u1="[" u2="&#x36;" k="47" />
    <hkern u1="[" u2="&#x35;" k="27" />
    <hkern u1="[" u2="&#x34;" k="57" />
    <hkern u1="[" u2="&#x33;" k="16" />
    <hkern u1="[" u2="&#x32;" k="8" />
    <hkern u1="[" u2="&#x31;" k="16" />
    <hkern u1="[" u2="&#x30;" k="33" />
    <hkern u1="[" u2="&#x2a;" k="66" />
    <hkern u1="[" u2="&#x26;" k="45" />
    <hkern u1="[" u2="&#x24;" k="16" />
    <hkern u1="[" u2="&#x23;" k="45" />
    <hkern u1="[" u2="&#x21;" k="10" />
    <hkern u1="\" g2="Jcircumflex.salt" k="47" />
    <hkern u1="\" g2="J.salt" k="47" />
    <hkern u1="\" u2="&#xfb02;" k="20" />
    <hkern u1="\" u2="&#xfb01;" k="20" />
    <hkern u1="\" u2="&#x2117;" k="68" />
    <hkern u1="\" u2="&#x2039;" k="45" />
    <hkern u1="\" u2="&#x2030;" k="164" />
    <hkern u1="\" u2="&#x2026;" k="20" />
    <hkern u1="\" u2="&#x201e;" k="-35" />
    <hkern u1="\" u2="&#x201d;" k="238" />
    <hkern u1="\" u2="&#x201c;" k="231" />
    <hkern u1="\" u2="&#x201a;" k="-35" />
    <hkern u1="\" u2="&#x2019;" k="238" />
    <hkern u1="\" u2="&#x2018;" k="231" />
    <hkern u1="\" u2="&#x2014;" k="129" />
    <hkern u1="\" u2="&#x2013;" k="129" />
    <hkern u1="\" u2="&#x1ef3;" k="57" />
    <hkern u1="\" u2="&#x1ef2;" k="211" />
    <hkern u1="\" u2="&#x1e85;" k="51" />
    <hkern u1="\" u2="&#x1e84;" k="109" />
    <hkern u1="\" u2="&#x1e83;" k="51" />
    <hkern u1="\" u2="&#x1e82;" k="109" />
    <hkern u1="\" u2="&#x1e81;" k="51" />
    <hkern u1="\" u2="&#x1e80;" k="109" />
    <hkern u1="\" u2="&#x219;" k="27" />
    <hkern u1="\" u2="&#x1ff;" k="45" />
    <hkern u1="\" u2="&#x1fe;" k="94" />
    <hkern u1="\" u2="&#x1fd;" k="25" />
    <hkern u1="\" u2="&#x1fb;" k="25" />
    <hkern u1="\" u2="&#x1fa;" k="-4" />
    <hkern u1="\" u2="&#x178;" k="211" />
    <hkern u1="\" u2="&#x177;" k="57" />
    <hkern u1="\" u2="&#x176;" k="211" />
    <hkern u1="\" u2="&#x175;" k="51" />
    <hkern u1="\" u2="&#x174;" k="109" />
    <hkern u1="\" u2="&#x173;" k="37" />
    <hkern u1="\" u2="&#x172;" k="51" />
    <hkern u1="\" u2="&#x171;" k="37" />
    <hkern u1="\" u2="&#x170;" k="51" />
    <hkern u1="\" u2="&#x16f;" k="37" />
    <hkern u1="\" u2="&#x16e;" k="51" />
    <hkern u1="\" u2="&#x16d;" k="37" />
    <hkern u1="\" u2="&#x16c;" k="51" />
    <hkern u1="\" u2="&#x16b;" k="37" />
    <hkern u1="\" u2="&#x16a;" k="51" />
    <hkern u1="\" u2="&#x169;" k="37" />
    <hkern u1="\" u2="&#x168;" k="51" />
    <hkern u1="\" u2="&#x164;" k="150" />
    <hkern u1="\" u2="&#x163;" k="88" />
    <hkern u1="\" u2="&#x162;" k="150" />
    <hkern u1="\" u2="&#x161;" k="27" />
    <hkern u1="\" u2="&#x160;" k="51" />
    <hkern u1="\" u2="&#x15f;" k="27" />
    <hkern u1="\" u2="&#x15e;" k="51" />
    <hkern u1="\" u2="&#x15d;" k="27" />
    <hkern u1="\" u2="&#x15c;" k="51" />
    <hkern u1="\" u2="&#x15b;" k="27" />
    <hkern u1="\" u2="&#x15a;" k="51" />
    <hkern u1="\" u2="&#x152;" k="94" />
    <hkern u1="\" u2="&#x151;" k="45" />
    <hkern u1="\" u2="&#x150;" k="94" />
    <hkern u1="\" u2="&#x14f;" k="45" />
    <hkern u1="\" u2="&#x14e;" k="94" />
    <hkern u1="\" u2="&#x14d;" k="45" />
    <hkern u1="\" u2="&#x14c;" k="94" />
    <hkern u1="\" u2="&#x149;" k="238" />
    <hkern u1="\" u2="&#x134;" k="41" />
    <hkern u1="\" u2="&#x123;" k="45" />
    <hkern u1="\" u2="&#x122;" k="94" />
    <hkern u1="\" u2="&#x121;" k="45" />
    <hkern u1="\" u2="&#x120;" k="94" />
    <hkern u1="\" u2="&#x11f;" k="45" />
    <hkern u1="\" u2="&#x11e;" k="94" />
    <hkern u1="\" u2="&#x11d;" k="45" />
    <hkern u1="\" u2="&#x11c;" k="94" />
    <hkern u1="\" u2="&#x11b;" k="45" />
    <hkern u1="\" u2="&#x119;" k="45" />
    <hkern u1="\" u2="&#x117;" k="45" />
    <hkern u1="\" u2="&#x115;" k="45" />
    <hkern u1="\" u2="&#x113;" k="45" />
    <hkern u1="\" u2="&#x111;" k="45" />
    <hkern u1="\" u2="&#x10f;" k="45" />
    <hkern u1="\" u2="&#x10d;" k="45" />
    <hkern u1="\" u2="&#x10c;" k="94" />
    <hkern u1="\" u2="&#x10b;" k="45" />
    <hkern u1="\" u2="&#x10a;" k="94" />
    <hkern u1="\" u2="&#x109;" k="45" />
    <hkern u1="\" u2="&#x108;" k="94" />
    <hkern u1="\" u2="&#x107;" k="45" />
    <hkern u1="\" u2="&#x106;" k="94" />
    <hkern u1="\" u2="&#x105;" k="25" />
    <hkern u1="\" u2="&#x104;" k="-4" />
    <hkern u1="\" u2="&#x103;" k="25" />
    <hkern u1="\" u2="&#x102;" k="-4" />
    <hkern u1="\" u2="&#x101;" k="25" />
    <hkern u1="\" u2="&#x100;" k="-4" />
    <hkern u1="\" u2="&#xff;" k="57" />
    <hkern u1="\" u2="&#xfd;" k="57" />
    <hkern u1="\" u2="&#xfc;" k="37" />
    <hkern u1="\" u2="&#xfb;" k="37" />
    <hkern u1="\" u2="&#xfa;" k="37" />
    <hkern u1="\" u2="&#xf9;" k="37" />
    <hkern u1="\" u2="&#xf8;" k="45" />
    <hkern u1="\" u2="&#xf6;" k="45" />
    <hkern u1="\" u2="&#xf5;" k="45" />
    <hkern u1="\" u2="&#xf4;" k="45" />
    <hkern u1="\" u2="&#xf3;" k="45" />
    <hkern u1="\" u2="&#xf2;" k="45" />
    <hkern u1="\" u2="&#xeb;" k="45" />
    <hkern u1="\" u2="&#xea;" k="45" />
    <hkern u1="\" u2="&#xe9;" k="45" />
    <hkern u1="\" u2="&#xe8;" k="45" />
    <hkern u1="\" u2="&#xe7;" k="45" />
    <hkern u1="\" u2="&#xe6;" k="25" />
    <hkern u1="\" u2="&#xe5;" k="25" />
    <hkern u1="\" u2="&#xe4;" k="25" />
    <hkern u1="\" u2="&#xe3;" k="25" />
    <hkern u1="\" u2="&#xe2;" k="25" />
    <hkern u1="\" u2="&#xe1;" k="25" />
    <hkern u1="\" u2="&#xe0;" k="25" />
    <hkern u1="\" u2="&#xdd;" k="211" />
    <hkern u1="\" u2="&#xdc;" k="51" />
    <hkern u1="\" u2="&#xdb;" k="51" />
    <hkern u1="\" u2="&#xda;" k="51" />
    <hkern u1="\" u2="&#xd9;" k="51" />
    <hkern u1="\" u2="&#xd8;" k="94" />
    <hkern u1="\" u2="&#xd6;" k="94" />
    <hkern u1="\" u2="&#xd5;" k="94" />
    <hkern u1="\" u2="&#xd4;" k="94" />
    <hkern u1="\" u2="&#xd3;" k="94" />
    <hkern u1="\" u2="&#xd2;" k="94" />
    <hkern u1="\" u2="&#xc7;" k="94" />
    <hkern u1="\" u2="&#xc5;" k="-4" />
    <hkern u1="\" u2="&#xc4;" k="-4" />
    <hkern u1="\" u2="&#xc3;" k="-4" />
    <hkern u1="\" u2="&#xc2;" k="-4" />
    <hkern u1="\" u2="&#xc1;" k="-4" />
    <hkern u1="\" u2="&#xc0;" k="-4" />
    <hkern u1="\" u2="&#xba;" k="123" />
    <hkern u1="\" u2="&#xab;" k="45" />
    <hkern u1="\" u2="&#xaa;" k="123" />
    <hkern u1="\" u2="&#xa9;" k="68" />
    <hkern u1="\" u2="&#x7d;" k="-14" />
    <hkern u1="\" u2="&#x7b;" k="20" />
    <hkern u1="\" u2="y" k="57" />
    <hkern u1="\" u2="w" k="51" />
    <hkern u1="\" u2="u" k="37" />
    <hkern u1="\" u2="t" k="88" />
    <hkern u1="\" u2="s" k="27" />
    <hkern u1="\" u2="q" k="45" />
    <hkern u1="\" u2="o" k="45" />
    <hkern u1="\" u2="g" k="45" />
    <hkern u1="\" u2="f" k="20" />
    <hkern u1="\" u2="e" k="45" />
    <hkern u1="\" u2="d" k="45" />
    <hkern u1="\" u2="c" k="45" />
    <hkern u1="\" u2="a" k="25" />
    <hkern u1="\" u2="]" k="-14" />
    <hkern u1="\" u2="[" k="20" />
    <hkern u1="\" u2="Y" k="211" />
    <hkern u1="\" u2="W" k="109" />
    <hkern u1="\" u2="U" k="51" />
    <hkern u1="\" u2="T" k="150" />
    <hkern u1="\" u2="S" k="51" />
    <hkern u1="\" u2="Q" k="94" />
    <hkern u1="\" u2="O" k="94" />
    <hkern u1="\" u2="J" k="41" />
    <hkern u1="\" u2="G" k="94" />
    <hkern u1="\" u2="C" k="94" />
    <hkern u1="\" u2="A" k="-4" />
    <hkern u1="\" u2="&#x3b;" k="27" />
    <hkern u1="\" u2="&#x3a;" k="27" />
    <hkern u1="\" u2="&#x2e;" k="20" />
    <hkern u1="\" u2="&#x2d;" k="129" />
    <hkern u1="\" u2="&#x2c;" k="-35" />
    <hkern u1="\" u2="&#x29;" k="-14" />
    <hkern u1="\" u2="&#x28;" k="20" />
    <hkern u1="\" u2="&#x25;" k="164" />
    <hkern u1="\" u2="&#x2122;" k="279" />
    <hkern u1="\" u2="&#x20ac;" k="78" />
    <hkern u1="\" u2="&#xbf;" k="14" />
    <hkern u1="\" u2="&#xae;" k="236" />
    <hkern u1="\" u2="&#xa5;" k="72" />
    <hkern u1="\" u2="&#xa3;" k="10" />
    <hkern u1="\" u2="&#xa2;" k="98" />
    <hkern u1="\" u2="v" k="100" />
    <hkern u1="\" u2="j" k="-47" />
    <hkern u1="\" u2="V" k="197" />
    <hkern u1="\" u2="&#x40;" k="41" />
    <hkern u1="\" u2="&#x3f;" k="145" />
    <hkern u1="\" u2="&#x39;" k="129" />
    <hkern u1="\" u2="&#x38;" k="37" />
    <hkern u1="\" u2="&#x37;" k="25" />
    <hkern u1="\" u2="&#x36;" k="31" />
    <hkern u1="\" u2="&#x35;" k="41" />
    <hkern u1="\" u2="&#x34;" k="41" />
    <hkern u1="\" u2="&#x33;" k="25" />
    <hkern u1="\" u2="&#x31;" k="72" />
    <hkern u1="\" u2="&#x30;" k="47" />
    <hkern u1="\" u2="&#x2a;" k="231" />
    <hkern u1="\" u2="&#x26;" k="66" />
    <hkern u1="\" u2="&#x24;" k="35" />
    <hkern u1="\" u2="&#x23;" k="16" />
    <hkern u1="\" u2="&#x21;" k="6" />
    <hkern u1="]" u2="&#x2122;" k="37" />
    <hkern u1="]" u2="\" k="61" />
    <hkern u1="]" u2="&#x2f;" k="45" />
    <hkern u1="]" u2="&#x2a;" k="14" />
    <hkern u1="a" u2="&#x2122;" k="129" />
    <hkern u1="a" u2="&#xae;" k="20" />
    <hkern u1="a" u2="v" k="16" />
    <hkern u1="a" u2="\" k="104" />
    <hkern u1="a" u2="&#x3f;" k="37" />
    <hkern u1="a" u2="&#x2a;" k="31" />
    <hkern u1="b" u2="&#x2122;" k="82" />
    <hkern u1="b" u2="&#xae;" k="14" />
    <hkern u1="b" u2="x" k="27" />
    <hkern u1="b" u2="v" k="25" />
    <hkern u1="b" u2="\" k="117" />
    <hkern u1="b" u2="&#x3f;" k="25" />
    <hkern u1="b" u2="&#x2f;" k="47" />
    <hkern u1="b" u2="&#x2a;" k="31" />
    <hkern u1="c" u2="&#x2122;" k="82" />
    <hkern u1="c" u2="&#xae;" k="10" />
    <hkern u1="c" u2="v" k="16" />
    <hkern u1="c" u2="\" k="102" />
    <hkern u1="c" u2="&#x3f;" k="25" />
    <hkern u1="c" u2="&#x2f;" k="20" />
    <hkern u1="c" u2="&#x2a;" k="20" />
    <hkern u1="e" u2="&#x2122;" k="88" />
    <hkern u1="e" u2="&#xae;" k="20" />
    <hkern u1="e" u2="x" k="20" />
    <hkern u1="e" u2="v" k="20" />
    <hkern u1="e" u2="\" k="102" />
    <hkern u1="e" u2="&#x3f;" k="25" />
    <hkern u1="e" u2="&#x2f;" k="25" />
    <hkern u1="e" u2="&#x2a;" k="20" />
    <hkern u1="f" u2="&#xfb02;" k="8" />
    <hkern u1="f" u2="&#xfb01;" k="8" />
    <hkern u1="f" u2="&#x203a;" k="10" />
    <hkern u1="f" u2="&#x2039;" k="63" />
    <hkern u1="f" u2="&#x2026;" k="135" />
    <hkern u1="f" u2="&#x201e;" k="113" />
    <hkern u1="f" u2="&#x201d;" k="-41" />
    <hkern u1="f" u2="&#x201c;" k="-41" />
    <hkern u1="f" u2="&#x201a;" k="113" />
    <hkern u1="f" u2="&#x2019;" k="-41" />
    <hkern u1="f" u2="&#x2018;" k="-41" />
    <hkern u1="f" u2="&#x2014;" k="82" />
    <hkern u1="f" u2="&#x2013;" k="82" />
    <hkern u1="f" u2="&#x219;" k="41" />
    <hkern u1="f" u2="&#x1ff;" k="57" />
    <hkern u1="f" u2="&#x1fd;" k="98" />
    <hkern u1="f" u2="&#x1fb;" k="98" />
    <hkern u1="f" u2="&#x17e;" k="10" />
    <hkern u1="f" u2="&#x17c;" k="10" />
    <hkern u1="f" u2="&#x17a;" k="10" />
    <hkern u1="f" u2="&#x173;" k="20" />
    <hkern u1="f" u2="&#x171;" k="20" />
    <hkern u1="f" u2="&#x16f;" k="20" />
    <hkern u1="f" u2="&#x16d;" k="20" />
    <hkern u1="f" u2="&#x16b;" k="20" />
    <hkern u1="f" u2="&#x169;" k="20" />
    <hkern u1="f" u2="&#x163;" k="10" />
    <hkern u1="f" u2="&#x161;" k="41" />
    <hkern u1="f" u2="&#x15f;" k="41" />
    <hkern u1="f" u2="&#x15d;" k="41" />
    <hkern u1="f" u2="&#x15b;" k="41" />
    <hkern u1="f" u2="&#x159;" k="31" />
    <hkern u1="f" u2="&#x157;" k="31" />
    <hkern u1="f" u2="&#x155;" k="31" />
    <hkern u1="f" u2="&#x151;" k="57" />
    <hkern u1="f" u2="&#x14f;" k="57" />
    <hkern u1="f" u2="&#x14d;" k="57" />
    <hkern u1="f" u2="&#x14b;" k="31" />
    <hkern u1="f" u2="&#x149;" k="-41" />
    <hkern u1="f" u2="&#x148;" k="31" />
    <hkern u1="f" u2="&#x146;" k="31" />
    <hkern u1="f" u2="&#x144;" k="31" />
    <hkern u1="f" u2="&#x123;" k="57" />
    <hkern u1="f" u2="&#x121;" k="57" />
    <hkern u1="f" u2="&#x11f;" k="57" />
    <hkern u1="f" u2="&#x11d;" k="57" />
    <hkern u1="f" u2="&#x11b;" k="57" />
    <hkern u1="f" u2="&#x119;" k="57" />
    <hkern u1="f" u2="&#x117;" k="57" />
    <hkern u1="f" u2="&#x115;" k="57" />
    <hkern u1="f" u2="&#x113;" k="57" />
    <hkern u1="f" u2="&#x111;" k="57" />
    <hkern u1="f" u2="&#x10f;" k="57" />
    <hkern u1="f" u2="&#x10d;" k="57" />
    <hkern u1="f" u2="&#x10b;" k="57" />
    <hkern u1="f" u2="&#x109;" k="57" />
    <hkern u1="f" u2="&#x107;" k="57" />
    <hkern u1="f" u2="&#x105;" k="98" />
    <hkern u1="f" u2="&#x103;" k="98" />
    <hkern u1="f" u2="&#x101;" k="98" />
    <hkern u1="f" u2="&#xfc;" k="20" />
    <hkern u1="f" u2="&#xfb;" k="20" />
    <hkern u1="f" u2="&#xfa;" k="20" />
    <hkern u1="f" u2="&#xf9;" k="20" />
    <hkern u1="f" u2="&#xf8;" k="57" />
    <hkern u1="f" u2="&#xf6;" k="57" />
    <hkern u1="f" u2="&#xf5;" k="57" />
    <hkern u1="f" u2="&#xf4;" k="57" />
    <hkern u1="f" u2="&#xf3;" k="57" />
    <hkern u1="f" u2="&#xf2;" k="57" />
    <hkern u1="f" u2="&#xeb;" k="57" />
    <hkern u1="f" u2="&#xea;" k="57" />
    <hkern u1="f" u2="&#xe9;" k="57" />
    <hkern u1="f" u2="&#xe8;" k="57" />
    <hkern u1="f" u2="&#xe7;" k="57" />
    <hkern u1="f" u2="&#xe6;" k="98" />
    <hkern u1="f" u2="&#xe5;" k="98" />
    <hkern u1="f" u2="&#xe4;" k="98" />
    <hkern u1="f" u2="&#xe3;" k="98" />
    <hkern u1="f" u2="&#xe2;" k="98" />
    <hkern u1="f" u2="&#xe1;" k="98" />
    <hkern u1="f" u2="&#xe0;" k="98" />
    <hkern u1="f" u2="&#xbb;" k="10" />
    <hkern u1="f" u2="&#xba;" k="-14" />
    <hkern u1="f" u2="&#xab;" k="63" />
    <hkern u1="f" u2="&#xaa;" k="-14" />
    <hkern u1="f" u2="z" k="10" />
    <hkern u1="f" u2="u" k="20" />
    <hkern u1="f" u2="t" k="10" />
    <hkern u1="f" u2="s" k="41" />
    <hkern u1="f" u2="r" k="31" />
    <hkern u1="f" u2="q" k="57" />
    <hkern u1="f" u2="p" k="31" />
    <hkern u1="f" u2="o" k="57" />
    <hkern u1="f" u2="n" k="31" />
    <hkern u1="f" u2="m" k="31" />
    <hkern u1="f" u2="g" k="57" />
    <hkern u1="f" u2="f" k="8" />
    <hkern u1="f" u2="e" k="57" />
    <hkern u1="f" u2="d" k="57" />
    <hkern u1="f" u2="c" k="57" />
    <hkern u1="f" u2="a" k="98" />
    <hkern u1="f" u2="&#x3b;" k="41" />
    <hkern u1="f" u2="&#x3a;" k="41" />
    <hkern u1="f" u2="&#x2e;" k="135" />
    <hkern u1="f" u2="&#x2d;" k="82" />
    <hkern u1="f" u2="&#x2c;" k="113" />
    <hkern u1="f" u2="&#x2122;" k="-47" />
    <hkern u1="f" u2="&#xbf;" k="96" />
    <hkern u1="f" u2="&#xae;" k="-25" />
    <hkern u1="f" u2="&#x40;" k="10" />
    <hkern u1="f" u2="&#x3f;" k="-27" />
    <hkern u1="f" u2="&#x2f;" k="106" />
    <hkern u1="f" u2="&#x2a;" k="-10" />
    <hkern u1="f" u2="&#x26;" k="35" />
    <hkern u1="g" u2="&#x2122;" k="14" />
    <hkern u1="g" u2="\" k="18" />
    <hkern u1="h" u2="&#x2122;" k="117" />
    <hkern u1="h" u2="&#xae;" k="25" />
    <hkern u1="h" u2="v" k="10" />
    <hkern u1="h" u2="\" k="106" />
    <hkern u1="h" u2="&#x2a;" k="31" />
    <hkern u1="i" u2="&#x7d;" k="10" />
    <hkern u1="i" u2="]" k="10" />
    <hkern u1="i" u2="&#x29;" k="10" />
    <hkern u1="j" u2="&#x201e;" k="10" />
    <hkern u1="j" u2="&#x201a;" k="10" />
    <hkern u1="j" u2="&#x7d;" k="10" />
    <hkern u1="j" u2="]" k="10" />
    <hkern u1="j" u2="&#x2c;" k="10" />
    <hkern u1="j" u2="&#x29;" k="10" />
    <hkern u1="j" u2="\" k="20" />
    <hkern u1="j" u2="&#x2f;" k="8" />
    <hkern u1="k" u2="&#x2122;" k="41" />
    <hkern u1="k" u2="&#xbf;" k="31" />
    <hkern u1="k" u2="v" k="10" />
    <hkern u1="k" u2="\" k="102" />
    <hkern u1="k" u2="&#x40;" k="27" />
    <hkern u1="k" u2="&#x3f;" k="-10" />
    <hkern u1="k" u2="&#x26;" k="6" />
    <hkern u1="m" u2="&#x2122;" k="117" />
    <hkern u1="m" u2="&#xae;" k="25" />
    <hkern u1="m" u2="v" k="10" />
    <hkern u1="m" u2="\" k="106" />
    <hkern u1="m" u2="&#x2a;" k="31" />
    <hkern u1="n" u2="&#x2122;" k="117" />
    <hkern u1="n" u2="&#xae;" k="25" />
    <hkern u1="n" u2="v" k="10" />
    <hkern u1="n" u2="\" k="106" />
    <hkern u1="n" u2="&#x2a;" k="31" />
    <hkern u1="o" u2="&#x2122;" k="82" />
    <hkern u1="o" u2="&#xae;" k="14" />
    <hkern u1="o" u2="x" k="27" />
    <hkern u1="o" u2="v" k="25" />
    <hkern u1="o" u2="\" k="117" />
    <hkern u1="o" u2="&#x3f;" k="25" />
    <hkern u1="o" u2="&#x2f;" k="47" />
    <hkern u1="o" u2="&#x2a;" k="31" />
    <hkern u1="p" u2="&#x2122;" k="82" />
    <hkern u1="p" u2="&#xae;" k="14" />
    <hkern u1="p" u2="x" k="27" />
    <hkern u1="p" u2="v" k="25" />
    <hkern u1="p" u2="\" k="117" />
    <hkern u1="p" u2="&#x3f;" k="25" />
    <hkern u1="p" u2="&#x2f;" k="47" />
    <hkern u1="p" u2="&#x2a;" k="31" />
    <hkern u1="q" u2="&#x2122;" k="14" />
    <hkern u1="q" u2="\" k="18" />
    <hkern u1="r" u2="&#xbf;" k="16" />
    <hkern u1="r" u2="&#xae;" k="-20" />
    <hkern u1="r" u2="x" k="-18" />
    <hkern u1="r" u2="v" k="-45" />
    <hkern u1="r" u2="\" k="27" />
    <hkern u1="r" u2="&#x3f;" k="-20" />
    <hkern u1="r" u2="&#x2f;" k="102" />
    <hkern u1="r" u2="&#x2a;" k="-41" />
    <hkern u1="r" u2="&#x26;" k="16" />
    <hkern u1="s" u2="&#x2122;" k="88" />
    <hkern u1="s" u2="&#xae;" k="10" />
    <hkern u1="s" u2="x" k="10" />
    <hkern u1="s" u2="v" k="25" />
    <hkern u1="s" u2="\" k="113" />
    <hkern u1="s" u2="&#x3f;" k="20" />
    <hkern u1="s" u2="&#x2f;" k="45" />
    <hkern u1="s" u2="&#x2a;" k="41" />
    <hkern u1="t" u2="&#x2122;" k="27" />
    <hkern u1="t" u2="&#xae;" k="-10" />
    <hkern u1="t" u2="x" k="-25" />
    <hkern u1="t" u2="v" k="-10" />
    <hkern u1="t" u2="\" k="72" />
    <hkern u1="t" u2="&#x2f;" k="14" />
    <hkern u1="u" u2="&#x2122;" k="14" />
    <hkern u1="u" u2="\" k="18" />
    <hkern u1="v" u2="&#x2039;" k="10" />
    <hkern u1="v" u2="&#x2026;" k="158" />
    <hkern u1="v" u2="&#x201e;" k="123" />
    <hkern u1="v" u2="&#x201d;" k="-51" />
    <hkern u1="v" u2="&#x201c;" k="-51" />
    <hkern u1="v" u2="&#x201a;" k="123" />
    <hkern u1="v" u2="&#x2019;" k="-51" />
    <hkern u1="v" u2="&#x2018;" k="-51" />
    <hkern u1="v" u2="&#x2014;" k="31" />
    <hkern u1="v" u2="&#x2013;" k="31" />
    <hkern u1="v" u2="&#x1ef3;" k="-16" />
    <hkern u1="v" u2="&#x1e85;" k="-6" />
    <hkern u1="v" u2="&#x1e83;" k="-6" />
    <hkern u1="v" u2="&#x1e81;" k="-6" />
    <hkern u1="v" u2="&#x219;" k="20" />
    <hkern u1="v" u2="&#x1ff;" k="25" />
    <hkern u1="v" u2="&#x1fd;" k="66" />
    <hkern u1="v" u2="&#x1fb;" k="66" />
    <hkern u1="v" u2="&#x177;" k="-16" />
    <hkern u1="v" u2="&#x175;" k="-6" />
    <hkern u1="v" u2="&#x163;" k="-8" />
    <hkern u1="v" u2="&#x161;" k="20" />
    <hkern u1="v" u2="&#x15f;" k="20" />
    <hkern u1="v" u2="&#x15d;" k="20" />
    <hkern u1="v" u2="&#x15b;" k="20" />
    <hkern u1="v" u2="&#x151;" k="25" />
    <hkern u1="v" u2="&#x14f;" k="25" />
    <hkern u1="v" u2="&#x14d;" k="25" />
    <hkern u1="v" u2="&#x149;" k="-51" />
    <hkern u1="v" u2="&#x123;" k="25" />
    <hkern u1="v" u2="&#x121;" k="25" />
    <hkern u1="v" u2="&#x11f;" k="25" />
    <hkern u1="v" u2="&#x11d;" k="25" />
    <hkern u1="v" u2="&#x11b;" k="25" />
    <hkern u1="v" u2="&#x119;" k="25" />
    <hkern u1="v" u2="&#x117;" k="25" />
    <hkern u1="v" u2="&#x115;" k="25" />
    <hkern u1="v" u2="&#x113;" k="25" />
    <hkern u1="v" u2="&#x111;" k="25" />
    <hkern u1="v" u2="&#x10f;" k="25" />
    <hkern u1="v" u2="&#x10d;" k="25" />
    <hkern u1="v" u2="&#x10b;" k="25" />
    <hkern u1="v" u2="&#x109;" k="25" />
    <hkern u1="v" u2="&#x107;" k="25" />
    <hkern u1="v" u2="&#x105;" k="66" />
    <hkern u1="v" u2="&#x103;" k="66" />
    <hkern u1="v" u2="&#x101;" k="66" />
    <hkern u1="v" u2="&#xff;" k="-16" />
    <hkern u1="v" u2="&#xfd;" k="-16" />
    <hkern u1="v" u2="&#xf8;" k="25" />
    <hkern u1="v" u2="&#xf6;" k="25" />
    <hkern u1="v" u2="&#xf5;" k="25" />
    <hkern u1="v" u2="&#xf4;" k="25" />
    <hkern u1="v" u2="&#xf3;" k="25" />
    <hkern u1="v" u2="&#xf2;" k="25" />
    <hkern u1="v" u2="&#xeb;" k="25" />
    <hkern u1="v" u2="&#xea;" k="25" />
    <hkern u1="v" u2="&#xe9;" k="25" />
    <hkern u1="v" u2="&#xe8;" k="25" />
    <hkern u1="v" u2="&#xe7;" k="25" />
    <hkern u1="v" u2="&#xe6;" k="66" />
    <hkern u1="v" u2="&#xe5;" k="66" />
    <hkern u1="v" u2="&#xe4;" k="66" />
    <hkern u1="v" u2="&#xe3;" k="66" />
    <hkern u1="v" u2="&#xe2;" k="66" />
    <hkern u1="v" u2="&#xe1;" k="66" />
    <hkern u1="v" u2="&#xe0;" k="66" />
    <hkern u1="v" u2="&#xba;" k="-14" />
    <hkern u1="v" u2="&#xab;" k="10" />
    <hkern u1="v" u2="&#xaa;" k="-14" />
    <hkern u1="v" u2="&#x7d;" k="25" />
    <hkern u1="v" u2="y" k="-16" />
    <hkern u1="v" u2="w" k="-6" />
    <hkern u1="v" u2="t" k="-8" />
    <hkern u1="v" u2="s" k="20" />
    <hkern u1="v" u2="q" k="25" />
    <hkern u1="v" u2="o" k="25" />
    <hkern u1="v" u2="g" k="25" />
    <hkern u1="v" u2="e" k="25" />
    <hkern u1="v" u2="d" k="25" />
    <hkern u1="v" u2="c" k="25" />
    <hkern u1="v" u2="a" k="66" />
    <hkern u1="v" u2="]" k="25" />
    <hkern u1="v" u2="&#x3b;" k="45" />
    <hkern u1="v" u2="&#x3a;" k="45" />
    <hkern u1="v" u2="&#x2e;" k="158" />
    <hkern u1="v" u2="&#x2d;" k="31" />
    <hkern u1="v" u2="&#x2c;" k="123" />
    <hkern u1="v" u2="&#x29;" k="25" />
    <hkern u1="v" u2="&#x2122;" k="-20" />
    <hkern u1="v" u2="&#xbf;" k="43" />
    <hkern u1="v" u2="&#xae;" k="-35" />
    <hkern u1="v" u2="v" k="-6" />
    <hkern u1="v" u2="\" k="25" />
    <hkern u1="v" u2="&#x3f;" k="-31" />
    <hkern u1="v" u2="&#x2f;" k="102" />
    <hkern u1="v" u2="&#x26;" k="20" />
    <hkern u1="w" u2="&#xbf;" k="45" />
    <hkern u1="w" u2="&#xae;" k="-35" />
    <hkern u1="w" u2="v" k="-6" />
    <hkern u1="w" u2="\" k="16" />
    <hkern u1="w" u2="&#x3f;" k="-27" />
    <hkern u1="w" u2="&#x2f;" k="72" />
    <hkern u1="w" u2="&#x26;" k="14" />
    <hkern u1="x" u2="&#x2117;" k="20" />
    <hkern u1="x" u2="&#x2039;" k="47" />
    <hkern u1="x" u2="&#x2026;" k="14" />
    <hkern u1="x" u2="&#x201d;" k="6" />
    <hkern u1="x" u2="&#x201c;" k="6" />
    <hkern u1="x" u2="&#x2019;" k="6" />
    <hkern u1="x" u2="&#x2018;" k="6" />
    <hkern u1="x" u2="&#x2014;" k="102" />
    <hkern u1="x" u2="&#x2013;" k="102" />
    <hkern u1="x" u2="&#x1ef3;" k="-6" />
    <hkern u1="x" u2="&#x219;" k="10" />
    <hkern u1="x" u2="&#x1ff;" k="27" />
    <hkern u1="x" u2="&#x1fd;" k="37" />
    <hkern u1="x" u2="&#x1fb;" k="37" />
    <hkern u1="x" u2="&#x177;" k="-6" />
    <hkern u1="x" u2="&#x173;" k="6" />
    <hkern u1="x" u2="&#x171;" k="6" />
    <hkern u1="x" u2="&#x16f;" k="6" />
    <hkern u1="x" u2="&#x16d;" k="6" />
    <hkern u1="x" u2="&#x16b;" k="6" />
    <hkern u1="x" u2="&#x169;" k="6" />
    <hkern u1="x" u2="&#x163;" k="10" />
    <hkern u1="x" u2="&#x161;" k="10" />
    <hkern u1="x" u2="&#x15f;" k="10" />
    <hkern u1="x" u2="&#x15d;" k="10" />
    <hkern u1="x" u2="&#x15b;" k="10" />
    <hkern u1="x" u2="&#x151;" k="27" />
    <hkern u1="x" u2="&#x14f;" k="27" />
    <hkern u1="x" u2="&#x14d;" k="27" />
    <hkern u1="x" u2="&#x149;" k="6" />
    <hkern u1="x" u2="&#x123;" k="27" />
    <hkern u1="x" u2="&#x121;" k="27" />
    <hkern u1="x" u2="&#x11f;" k="27" />
    <hkern u1="x" u2="&#x11d;" k="27" />
    <hkern u1="x" u2="&#x11b;" k="27" />
    <hkern u1="x" u2="&#x119;" k="27" />
    <hkern u1="x" u2="&#x117;" k="27" />
    <hkern u1="x" u2="&#x115;" k="27" />
    <hkern u1="x" u2="&#x113;" k="27" />
    <hkern u1="x" u2="&#x111;" k="27" />
    <hkern u1="x" u2="&#x10f;" k="27" />
    <hkern u1="x" u2="&#x10d;" k="27" />
    <hkern u1="x" u2="&#x10b;" k="27" />
    <hkern u1="x" u2="&#x109;" k="27" />
    <hkern u1="x" u2="&#x107;" k="27" />
    <hkern u1="x" u2="&#x105;" k="37" />
    <hkern u1="x" u2="&#x103;" k="37" />
    <hkern u1="x" u2="&#x101;" k="37" />
    <hkern u1="x" u2="&#xff;" k="-6" />
    <hkern u1="x" u2="&#xfd;" k="-6" />
    <hkern u1="x" u2="&#xfc;" k="6" />
    <hkern u1="x" u2="&#xfb;" k="6" />
    <hkern u1="x" u2="&#xfa;" k="6" />
    <hkern u1="x" u2="&#xf9;" k="6" />
    <hkern u1="x" u2="&#xf8;" k="27" />
    <hkern u1="x" u2="&#xf6;" k="27" />
    <hkern u1="x" u2="&#xf5;" k="27" />
    <hkern u1="x" u2="&#xf4;" k="27" />
    <hkern u1="x" u2="&#xf3;" k="27" />
    <hkern u1="x" u2="&#xf2;" k="27" />
    <hkern u1="x" u2="&#xeb;" k="27" />
    <hkern u1="x" u2="&#xea;" k="27" />
    <hkern u1="x" u2="&#xe9;" k="27" />
    <hkern u1="x" u2="&#xe8;" k="27" />
    <hkern u1="x" u2="&#xe7;" k="27" />
    <hkern u1="x" u2="&#xe6;" k="37" />
    <hkern u1="x" u2="&#xe5;" k="37" />
    <hkern u1="x" u2="&#xe4;" k="37" />
    <hkern u1="x" u2="&#xe3;" k="37" />
    <hkern u1="x" u2="&#xe2;" k="37" />
    <hkern u1="x" u2="&#xe1;" k="37" />
    <hkern u1="x" u2="&#xe0;" k="37" />
    <hkern u1="x" u2="&#xab;" k="47" />
    <hkern u1="x" u2="&#xa9;" k="20" />
    <hkern u1="x" u2="&#x7d;" k="4" />
    <hkern u1="x" u2="y" k="-6" />
    <hkern u1="x" u2="u" k="6" />
    <hkern u1="x" u2="t" k="10" />
    <hkern u1="x" u2="s" k="10" />
    <hkern u1="x" u2="q" k="27" />
    <hkern u1="x" u2="o" k="27" />
    <hkern u1="x" u2="g" k="27" />
    <hkern u1="x" u2="e" k="27" />
    <hkern u1="x" u2="d" k="27" />
    <hkern u1="x" u2="c" k="27" />
    <hkern u1="x" u2="a" k="37" />
    <hkern u1="x" u2="]" k="4" />
    <hkern u1="x" u2="&#x3b;" k="14" />
    <hkern u1="x" u2="&#x3a;" k="14" />
    <hkern u1="x" u2="&#x2e;" k="14" />
    <hkern u1="x" u2="&#x2d;" k="102" />
    <hkern u1="x" u2="&#x29;" k="4" />
    <hkern u1="x" u2="&#x2122;" k="27" />
    <hkern u1="x" u2="&#xbf;" k="25" />
    <hkern u1="x" u2="x" k="-10" />
    <hkern u1="x" u2="\" k="47" />
    <hkern u1="x" u2="&#x40;" k="10" />
    <hkern u1="x" u2="&#x2a;" k="10" />
    <hkern u1="x" u2="&#x26;" k="35" />
    <hkern u1="y" u2="&#xbf;" k="41" />
    <hkern u1="y" u2="&#xae;" k="-35" />
    <hkern u1="y" u2="&#xa1;" k="10" />
    <hkern u1="y" u2="\" k="16" />
    <hkern u1="y" u2="&#x40;" k="10" />
    <hkern u1="y" u2="&#x3f;" k="-23" />
    <hkern u1="y" u2="&#x2f;" k="86" />
    <hkern u1="y" u2="&#x2a;" k="-10" />
    <hkern u1="z" u2="&#x2122;" k="20" />
    <hkern u1="z" u2="\" k="51" />
    <hkern u1="&#x7b;" u2="&#x2122;" k="20" />
    <hkern u1="&#x7b;" u2="&#x20ac;" k="74" />
    <hkern u1="&#x7b;" u2="&#xbf;" k="35" />
    <hkern u1="&#x7b;" u2="&#xae;" k="41" />
    <hkern u1="&#x7b;" u2="&#xa5;" k="20" />
    <hkern u1="&#x7b;" u2="&#xa2;" k="41" />
    <hkern u1="&#x7b;" u2="x" k="4" />
    <hkern u1="&#x7b;" u2="v" k="25" />
    <hkern u1="&#x7b;" u2="j" k="-45" />
    <hkern u1="&#x7b;" u2="i" k="10" />
    <hkern u1="&#x7b;" u2="\" k="20" />
    <hkern u1="&#x7b;" u2="&#x40;" k="41" />
    <hkern u1="&#x7b;" u2="&#x3f;" k="16" />
    <hkern u1="&#x7b;" u2="&#x39;" k="45" />
    <hkern u1="&#x7b;" u2="&#x38;" k="45" />
    <hkern u1="&#x7b;" u2="&#x37;" k="10" />
    <hkern u1="&#x7b;" u2="&#x36;" k="47" />
    <hkern u1="&#x7b;" u2="&#x35;" k="27" />
    <hkern u1="&#x7b;" u2="&#x34;" k="57" />
    <hkern u1="&#x7b;" u2="&#x33;" k="16" />
    <hkern u1="&#x7b;" u2="&#x32;" k="8" />
    <hkern u1="&#x7b;" u2="&#x31;" k="16" />
    <hkern u1="&#x7b;" u2="&#x30;" k="33" />
    <hkern u1="&#x7b;" u2="&#x2a;" k="66" />
    <hkern u1="&#x7b;" u2="&#x26;" k="45" />
    <hkern u1="&#x7b;" u2="&#x24;" k="16" />
    <hkern u1="&#x7b;" u2="&#x23;" k="45" />
    <hkern u1="&#x7b;" u2="&#x21;" k="10" />
    <hkern u1="&#x7d;" u2="&#x2122;" k="37" />
    <hkern u1="&#x7d;" u2="\" k="61" />
    <hkern u1="&#x7d;" u2="&#x2f;" k="45" />
    <hkern u1="&#x7d;" u2="&#x2a;" k="14" />
    <hkern u1="&#xa1;" u2="&#x201d;" k="16" />
    <hkern u1="&#xa1;" u2="&#x201c;" k="4" />
    <hkern u1="&#xa1;" u2="&#x2019;" k="16" />
    <hkern u1="&#xa1;" u2="&#x2018;" k="4" />
    <hkern u1="&#xa1;" u2="&#x1ef3;" k="10" />
    <hkern u1="&#xa1;" u2="&#x1ef2;" k="41" />
    <hkern u1="&#xa1;" u2="&#x1e84;" k="20" />
    <hkern u1="&#xa1;" u2="&#x1e82;" k="20" />
    <hkern u1="&#xa1;" u2="&#x1e80;" k="20" />
    <hkern u1="&#xa1;" u2="&#x178;" k="41" />
    <hkern u1="&#xa1;" u2="&#x177;" k="10" />
    <hkern u1="&#xa1;" u2="&#x176;" k="41" />
    <hkern u1="&#xa1;" u2="&#x174;" k="20" />
    <hkern u1="&#xa1;" u2="&#x149;" k="16" />
    <hkern u1="&#xa1;" u2="&#xff;" k="10" />
    <hkern u1="&#xa1;" u2="&#xfd;" k="10" />
    <hkern u1="&#xa1;" u2="&#xdd;" k="41" />
    <hkern u1="&#xa1;" u2="y" k="10" />
    <hkern u1="&#xa1;" u2="Y" k="41" />
    <hkern u1="&#xa1;" u2="W" k="20" />
    <hkern u1="&#xa1;" u2="\" k="20" />
    <hkern u1="&#xa1;" u2="V" k="27" />
    <hkern u1="&#xa1;" u2="&#x39;" k="4" />
    <hkern u1="&#xa1;" u2="&#x2a;" k="35" />
    <hkern u1="&#xa2;" u2="&#x203a;" k="10" />
    <hkern u1="&#xa2;" u2="&#x2026;" k="35" />
    <hkern u1="&#xa2;" u2="&#x201e;" k="35" />
    <hkern u1="&#xa2;" u2="&#x201a;" k="35" />
    <hkern u1="&#xa2;" u2="&#xbb;" k="10" />
    <hkern u1="&#xa2;" u2="&#x7d;" k="33" />
    <hkern u1="&#xa2;" u2="]" k="33" />
    <hkern u1="&#xa2;" u2="&#x3b;" k="8" />
    <hkern u1="&#xa2;" u2="&#x3a;" k="8" />
    <hkern u1="&#xa2;" u2="&#x2e;" k="35" />
    <hkern u1="&#xa2;" u2="&#x2c;" k="35" />
    <hkern u1="&#xa2;" u2="&#x29;" k="33" />
    <hkern u1="&#xa2;" u2="&#x2122;" k="55" />
    <hkern u1="&#xa2;" u2="&#xbf;" k="20" />
    <hkern u1="&#xa2;" u2="&#xae;" k="10" />
    <hkern u1="&#xa2;" u2="\" k="92" />
    <hkern u1="&#xa2;" u2="&#x3f;" k="20" />
    <hkern u1="&#xa2;" u2="&#x37;" k="14" />
    <hkern u1="&#xa2;" u2="&#x2f;" k="76" />
    <hkern u1="&#xa3;" u2="&#x2039;" k="35" />
    <hkern u1="&#xa3;" u2="&#x2026;" k="35" />
    <hkern u1="&#xa3;" u2="&#x201e;" k="25" />
    <hkern u1="&#xa3;" u2="&#x201a;" k="25" />
    <hkern u1="&#xa3;" u2="&#x2014;" k="35" />
    <hkern u1="&#xa3;" u2="&#x2013;" k="35" />
    <hkern u1="&#xa3;" u2="&#xab;" k="35" />
    <hkern u1="&#xa3;" u2="&#x7d;" k="25" />
    <hkern u1="&#xa3;" u2="]" k="25" />
    <hkern u1="&#xa3;" u2="&#x2e;" k="35" />
    <hkern u1="&#xa3;" u2="&#x2d;" k="35" />
    <hkern u1="&#xa3;" u2="&#x2c;" k="25" />
    <hkern u1="&#xa3;" u2="&#x29;" k="25" />
    <hkern u1="&#xa3;" u2="&#xbf;" k="35" />
    <hkern u1="&#xa3;" u2="\" k="10" />
    <hkern u1="&#xa3;" u2="&#x40;" k="31" />
    <hkern u1="&#xa3;" u2="&#x36;" k="27" />
    <hkern u1="&#xa3;" u2="&#x35;" k="6" />
    <hkern u1="&#xa3;" u2="&#x34;" k="23" />
    <hkern u1="&#xa3;" u2="&#x31;" k="-35" />
    <hkern u1="&#xa3;" u2="&#x2f;" k="82" />
    <hkern u1="&#xa3;" u2="&#x26;" k="20" />
    <hkern u1="&#xa5;" u2="&#x2117;" k="20" />
    <hkern u1="&#xa5;" u2="&#x203a;" k="35" />
    <hkern u1="&#xa5;" u2="&#x2039;" k="45" />
    <hkern u1="&#xa5;" u2="&#x2026;" k="78" />
    <hkern u1="&#xa5;" u2="&#x201e;" k="72" />
    <hkern u1="&#xa5;" u2="&#x201d;" k="31" />
    <hkern u1="&#xa5;" u2="&#x201c;" k="27" />
    <hkern u1="&#xa5;" u2="&#x201a;" k="72" />
    <hkern u1="&#xa5;" u2="&#x2019;" k="31" />
    <hkern u1="&#xa5;" u2="&#x2018;" k="27" />
    <hkern u1="&#xa5;" u2="&#x2014;" k="20" />
    <hkern u1="&#xa5;" u2="&#x2013;" k="20" />
    <hkern u1="&#xa5;" u2="&#x149;" k="31" />
    <hkern u1="&#xa5;" u2="&#xbb;" k="35" />
    <hkern u1="&#xa5;" u2="&#xba;" k="41" />
    <hkern u1="&#xa5;" u2="&#xab;" k="45" />
    <hkern u1="&#xa5;" u2="&#xaa;" k="41" />
    <hkern u1="&#xa5;" u2="&#xa9;" k="20" />
    <hkern u1="&#xa5;" u2="&#x7d;" k="20" />
    <hkern u1="&#xa5;" u2="]" k="20" />
    <hkern u1="&#xa5;" u2="&#x3b;" k="41" />
    <hkern u1="&#xa5;" u2="&#x3a;" k="41" />
    <hkern u1="&#xa5;" u2="&#x2e;" k="78" />
    <hkern u1="&#xa5;" u2="&#x2d;" k="20" />
    <hkern u1="&#xa5;" u2="&#x2c;" k="72" />
    <hkern u1="&#xa5;" u2="&#x29;" k="20" />
    <hkern u1="&#xa5;" u2="&#xbf;" k="25" />
    <hkern u1="&#xa5;" u2="&#xae;" k="35" />
    <hkern u1="&#xa5;" u2="&#xa1;" k="10" />
    <hkern u1="&#xa5;" u2="\" k="16" />
    <hkern u1="&#xa5;" u2="&#x40;" k="31" />
    <hkern u1="&#xa5;" u2="&#x3f;" k="27" />
    <hkern u1="&#xa5;" u2="&#x39;" k="20" />
    <hkern u1="&#xa5;" u2="&#x38;" k="14" />
    <hkern u1="&#xa5;" u2="&#x37;" k="14" />
    <hkern u1="&#xa5;" u2="&#x35;" k="14" />
    <hkern u1="&#xa5;" u2="&#x33;" k="14" />
    <hkern u1="&#xa5;" u2="&#x32;" k="14" />
    <hkern u1="&#xa5;" u2="&#x2f;" k="133" />
    <hkern u1="&#xa5;" u2="&#x2a;" k="20" />
    <hkern u1="&#xa5;" u2="&#x26;" k="20" />
    <hkern u1="&#xa9;" u2="&#xa5;" k="20" />
    <hkern u1="&#xa9;" u2="x" k="20" />
    <hkern u1="&#xa9;" u2="\" k="82" />
    <hkern u1="&#xa9;" u2="X" k="33" />
    <hkern u1="&#xa9;" u2="V" k="41" />
    <hkern u1="&#xa9;" u2="&#x3f;" k="25" />
    <hkern u1="&#xa9;" u2="&#x37;" k="20" />
    <hkern u1="&#xa9;" u2="&#x2f;" k="82" />
    <hkern u1="&#xaa;" u2="v" k="-14" />
    <hkern u1="&#xaa;" u2="\" k="31" />
    <hkern u1="&#xaa;" u2="X" k="6" />
    <hkern u1="&#xaa;" u2="V" k="-14" />
    <hkern u1="&#xaa;" u2="&#x34;" k="14" />
    <hkern u1="&#xaa;" u2="&#x2f;" k="86" />
    <hkern u1="&#xab;" u2="&#x20ac;" k="20" />
    <hkern u1="&#xab;" u2="&#xa5;" k="35" />
    <hkern u1="&#xab;" u2="&#xa2;" k="10" />
    <hkern u1="&#xab;" u2="j" k="10" />
    <hkern u1="&#xab;" u2="\" k="72" />
    <hkern u1="&#xab;" u2="X" k="6" />
    <hkern u1="&#xab;" u2="V" k="82" />
    <hkern u1="&#xab;" u2="&#x39;" k="4" />
    <hkern u1="&#xab;" u2="&#x38;" k="20" />
    <hkern u1="&#xab;" u2="&#x37;" k="14" />
    <hkern u1="&#xab;" u2="&#x36;" k="16" />
    <hkern u1="&#xab;" u2="&#x35;" k="20" />
    <hkern u1="&#xab;" u2="&#x34;" k="14" />
    <hkern u1="&#xab;" u2="&#x33;" k="10" />
    <hkern u1="&#xab;" u2="&#x2f;" k="25" />
    <hkern u1="&#xab;" u2="&#x2a;" k="14" />
    <hkern u1="&#xab;" u2="&#x26;" k="6" />
    <hkern u1="&#xab;" u2="&#x23;" k="14" />
    <hkern u1="&#xae;" g2="Jcircumflex.salt" k="78" />
    <hkern u1="&#xae;" g2="J.salt" k="78" />
    <hkern u1="&#xae;" u2="&#xfb02;" k="-25" />
    <hkern u1="&#xae;" u2="&#xfb01;" k="-25" />
    <hkern u1="&#xae;" u2="&#x2026;" k="82" />
    <hkern u1="&#xae;" u2="&#x201d;" k="6" />
    <hkern u1="&#xae;" u2="&#x2019;" k="6" />
    <hkern u1="&#xae;" u2="&#x1ef3;" k="-35" />
    <hkern u1="&#xae;" u2="&#x1ef2;" k="35" />
    <hkern u1="&#xae;" u2="&#x1e85;" k="-35" />
    <hkern u1="&#xae;" u2="&#x1e83;" k="-35" />
    <hkern u1="&#xae;" u2="&#x1e81;" k="-35" />
    <hkern u1="&#xae;" u2="&#x219;" k="10" />
    <hkern u1="&#xae;" u2="&#x1ff;" k="14" />
    <hkern u1="&#xae;" u2="&#x1fd;" k="47" />
    <hkern u1="&#xae;" u2="&#x1fb;" k="47" />
    <hkern u1="&#xae;" u2="&#x1fa;" k="184" />
    <hkern u1="&#xae;" u2="&#x17d;" k="20" />
    <hkern u1="&#xae;" u2="&#x17b;" k="20" />
    <hkern u1="&#xae;" u2="&#x179;" k="20" />
    <hkern u1="&#xae;" u2="&#x178;" k="35" />
    <hkern u1="&#xae;" u2="&#x177;" k="-35" />
    <hkern u1="&#xae;" u2="&#x176;" k="35" />
    <hkern u1="&#xae;" u2="&#x175;" k="-35" />
    <hkern u1="&#xae;" u2="&#x163;" k="-14" />
    <hkern u1="&#xae;" u2="&#x161;" k="10" />
    <hkern u1="&#xae;" u2="&#x15f;" k="10" />
    <hkern u1="&#xae;" u2="&#x15d;" k="10" />
    <hkern u1="&#xae;" u2="&#x15b;" k="10" />
    <hkern u1="&#xae;" u2="&#x151;" k="14" />
    <hkern u1="&#xae;" u2="&#x14f;" k="14" />
    <hkern u1="&#xae;" u2="&#x14d;" k="14" />
    <hkern u1="&#xae;" u2="&#x149;" k="6" />
    <hkern u1="&#xae;" u2="&#x134;" k="279" />
    <hkern u1="&#xae;" u2="&#x123;" k="14" />
    <hkern u1="&#xae;" u2="&#x121;" k="14" />
    <hkern u1="&#xae;" u2="&#x11f;" k="14" />
    <hkern u1="&#xae;" u2="&#x11d;" k="14" />
    <hkern u1="&#xae;" u2="&#x11b;" k="14" />
    <hkern u1="&#xae;" u2="&#x119;" k="14" />
    <hkern u1="&#xae;" u2="&#x117;" k="14" />
    <hkern u1="&#xae;" u2="&#x115;" k="14" />
    <hkern u1="&#xae;" u2="&#x113;" k="14" />
    <hkern u1="&#xae;" u2="&#x111;" k="14" />
    <hkern u1="&#xae;" u2="&#x10f;" k="14" />
    <hkern u1="&#xae;" u2="&#x10d;" k="14" />
    <hkern u1="&#xae;" u2="&#x10b;" k="14" />
    <hkern u1="&#xae;" u2="&#x109;" k="14" />
    <hkern u1="&#xae;" u2="&#x107;" k="14" />
    <hkern u1="&#xae;" u2="&#x105;" k="47" />
    <hkern u1="&#xae;" u2="&#x104;" k="184" />
    <hkern u1="&#xae;" u2="&#x103;" k="47" />
    <hkern u1="&#xae;" u2="&#x102;" k="184" />
    <hkern u1="&#xae;" u2="&#x101;" k="47" />
    <hkern u1="&#xae;" u2="&#x100;" k="184" />
    <hkern u1="&#xae;" u2="&#xff;" k="-35" />
    <hkern u1="&#xae;" u2="&#xfd;" k="-35" />
    <hkern u1="&#xae;" u2="&#xf8;" k="14" />
    <hkern u1="&#xae;" u2="&#xf6;" k="14" />
    <hkern u1="&#xae;" u2="&#xf5;" k="14" />
    <hkern u1="&#xae;" u2="&#xf4;" k="14" />
    <hkern u1="&#xae;" u2="&#xf3;" k="14" />
    <hkern u1="&#xae;" u2="&#xf2;" k="14" />
    <hkern u1="&#xae;" u2="&#xeb;" k="14" />
    <hkern u1="&#xae;" u2="&#xea;" k="14" />
    <hkern u1="&#xae;" u2="&#xe9;" k="14" />
    <hkern u1="&#xae;" u2="&#xe8;" k="14" />
    <hkern u1="&#xae;" u2="&#xe7;" k="14" />
    <hkern u1="&#xae;" u2="&#xe6;" k="47" />
    <hkern u1="&#xae;" u2="&#xe5;" k="47" />
    <hkern u1="&#xae;" u2="&#xe4;" k="47" />
    <hkern u1="&#xae;" u2="&#xe3;" k="47" />
    <hkern u1="&#xae;" u2="&#xe2;" k="47" />
    <hkern u1="&#xae;" u2="&#xe1;" k="47" />
    <hkern u1="&#xae;" u2="&#xe0;" k="47" />
    <hkern u1="&#xae;" u2="&#xdd;" k="35" />
    <hkern u1="&#xae;" u2="&#xc5;" k="184" />
    <hkern u1="&#xae;" u2="&#xc4;" k="184" />
    <hkern u1="&#xae;" u2="&#xc3;" k="184" />
    <hkern u1="&#xae;" u2="&#xc2;" k="184" />
    <hkern u1="&#xae;" u2="&#xc1;" k="184" />
    <hkern u1="&#xae;" u2="&#xc0;" k="184" />
    <hkern u1="&#xae;" u2="&#x7d;" k="41" />
    <hkern u1="&#xae;" u2="y" k="-35" />
    <hkern u1="&#xae;" u2="w" k="-35" />
    <hkern u1="&#xae;" u2="t" k="-14" />
    <hkern u1="&#xae;" u2="s" k="10" />
    <hkern u1="&#xae;" u2="q" k="14" />
    <hkern u1="&#xae;" u2="o" k="14" />
    <hkern u1="&#xae;" u2="g" k="14" />
    <hkern u1="&#xae;" u2="f" k="-25" />
    <hkern u1="&#xae;" u2="e" k="14" />
    <hkern u1="&#xae;" u2="d" k="14" />
    <hkern u1="&#xae;" u2="c" k="14" />
    <hkern u1="&#xae;" u2="a" k="47" />
    <hkern u1="&#xae;" u2="]" k="41" />
    <hkern u1="&#xae;" u2="Z" k="20" />
    <hkern u1="&#xae;" u2="Y" k="35" />
    <hkern u1="&#xae;" u2="J" k="279" />
    <hkern u1="&#xae;" u2="A" k="184" />
    <hkern u1="&#xae;" u2="&#x2e;" k="82" />
    <hkern u1="&#xae;" u2="&#x29;" k="41" />
    <hkern u1="&#xae;" u2="&#xa5;" k="35" />
    <hkern u1="&#xae;" u2="&#xa3;" k="51" />
    <hkern u1="&#xae;" u2="v" k="-35" />
    <hkern u1="&#xae;" u2="\" k="25" />
    <hkern u1="&#xae;" u2="X" k="53" />
    <hkern u1="&#xae;" u2="V" k="20" />
    <hkern u1="&#xae;" u2="&#x40;" k="20" />
    <hkern u1="&#xae;" u2="&#x38;" k="27" />
    <hkern u1="&#xae;" u2="&#x37;" k="29" />
    <hkern u1="&#xae;" u2="&#x36;" k="45" />
    <hkern u1="&#xae;" u2="&#x35;" k="47" />
    <hkern u1="&#xae;" u2="&#x34;" k="78" />
    <hkern u1="&#xae;" u2="&#x33;" k="20" />
    <hkern u1="&#xae;" u2="&#x31;" k="-20" />
    <hkern u1="&#xae;" u2="&#x2f;" k="186" />
    <hkern u1="&#xae;" u2="&#x24;" k="10" />
    <hkern u1="&#xba;" u2="v" k="-14" />
    <hkern u1="&#xba;" u2="\" k="31" />
    <hkern u1="&#xba;" u2="X" k="6" />
    <hkern u1="&#xba;" u2="V" k="-14" />
    <hkern u1="&#xba;" u2="&#x34;" k="14" />
    <hkern u1="&#xba;" u2="&#x2f;" k="86" />
    <hkern u1="&#xbb;" u2="&#xa5;" k="45" />
    <hkern u1="&#xbb;" u2="x" k="47" />
    <hkern u1="&#xbb;" u2="v" k="10" />
    <hkern u1="&#xbb;" u2="\" k="109" />
    <hkern u1="&#xbb;" u2="X" k="53" />
    <hkern u1="&#xbb;" u2="V" k="109" />
    <hkern u1="&#xbb;" u2="&#x3f;" k="68" />
    <hkern u1="&#xbb;" u2="&#x39;" k="35" />
    <hkern u1="&#xbb;" u2="&#x37;" k="82" />
    <hkern u1="&#xbb;" u2="&#x36;" k="-6" />
    <hkern u1="&#xbb;" u2="&#x34;" k="-12" />
    <hkern u1="&#xbb;" u2="&#x31;" k="41" />
    <hkern u1="&#xbb;" u2="&#x2f;" k="61" />
    <hkern u1="&#xbb;" u2="&#x2a;" k="76" />
    <hkern u1="&#xbb;" u2="&#x23;" k="-27" />
    <hkern u1="&#xbb;" u2="&#x21;" k="6" />
    <hkern u1="&#xbf;" u2="&#xfb02;" k="10" />
    <hkern u1="&#xbf;" u2="&#xfb01;" k="10" />
    <hkern u1="&#xbf;" u2="&#x2030;" k="100" />
    <hkern u1="&#xbf;" u2="&#x201d;" k="197" />
    <hkern u1="&#xbf;" u2="&#x201c;" k="193" />
    <hkern u1="&#xbf;" u2="&#x2019;" k="197" />
    <hkern u1="&#xbf;" u2="&#x2018;" k="193" />
    <hkern u1="&#xbf;" u2="&#x2014;" k="35" />
    <hkern u1="&#xbf;" u2="&#x2013;" k="35" />
    <hkern u1="&#xbf;" u2="&#x1ef3;" k="68" />
    <hkern u1="&#xbf;" u2="&#x1ef2;" k="152" />
    <hkern u1="&#xbf;" u2="&#x1e85;" k="45" />
    <hkern u1="&#xbf;" u2="&#x1e84;" k="82" />
    <hkern u1="&#xbf;" u2="&#x1e83;" k="45" />
    <hkern u1="&#xbf;" u2="&#x1e82;" k="82" />
    <hkern u1="&#xbf;" u2="&#x1e81;" k="45" />
    <hkern u1="&#xbf;" u2="&#x1e80;" k="82" />
    <hkern u1="&#xbf;" u2="&#x1fa;" k="14" />
    <hkern u1="&#xbf;" u2="&#x178;" k="152" />
    <hkern u1="&#xbf;" u2="&#x177;" k="68" />
    <hkern u1="&#xbf;" u2="&#x176;" k="152" />
    <hkern u1="&#xbf;" u2="&#x175;" k="45" />
    <hkern u1="&#xbf;" u2="&#x174;" k="82" />
    <hkern u1="&#xbf;" u2="&#x164;" k="133" />
    <hkern u1="&#xbf;" u2="&#x163;" k="20" />
    <hkern u1="&#xbf;" u2="&#x162;" k="133" />
    <hkern u1="&#xbf;" u2="&#x149;" k="197" />
    <hkern u1="&#xbf;" u2="&#x104;" k="14" />
    <hkern u1="&#xbf;" u2="&#x102;" k="14" />
    <hkern u1="&#xbf;" u2="&#x100;" k="14" />
    <hkern u1="&#xbf;" u2="&#xff;" k="68" />
    <hkern u1="&#xbf;" u2="&#xfd;" k="68" />
    <hkern u1="&#xbf;" u2="&#xdd;" k="152" />
    <hkern u1="&#xbf;" u2="&#xc5;" k="14" />
    <hkern u1="&#xbf;" u2="&#xc4;" k="14" />
    <hkern u1="&#xbf;" u2="&#xc3;" k="14" />
    <hkern u1="&#xbf;" u2="&#xc2;" k="14" />
    <hkern u1="&#xbf;" u2="&#xc1;" k="14" />
    <hkern u1="&#xbf;" u2="&#xc0;" k="14" />
    <hkern u1="&#xbf;" u2="&#x7d;" k="25" />
    <hkern u1="&#xbf;" u2="y" k="68" />
    <hkern u1="&#xbf;" u2="w" k="45" />
    <hkern u1="&#xbf;" u2="t" k="20" />
    <hkern u1="&#xbf;" u2="f" k="10" />
    <hkern u1="&#xbf;" u2="]" k="25" />
    <hkern u1="&#xbf;" u2="Y" k="152" />
    <hkern u1="&#xbf;" u2="W" k="82" />
    <hkern u1="&#xbf;" u2="T" k="133" />
    <hkern u1="&#xbf;" u2="A" k="14" />
    <hkern u1="&#xbf;" u2="&#x2d;" k="35" />
    <hkern u1="&#xbf;" u2="&#x29;" k="25" />
    <hkern u1="&#xbf;" u2="&#x25;" k="100" />
    <hkern u1="&#xbf;" u2="&#xbf;" k="-10" />
    <hkern u1="&#xbf;" u2="&#xa5;" k="20" />
    <hkern u1="&#xbf;" u2="x" k="6" />
    <hkern u1="&#xbf;" u2="v" k="68" />
    <hkern u1="&#xbf;" u2="\" k="131" />
    <hkern u1="&#xbf;" u2="X" k="10" />
    <hkern u1="&#xbf;" u2="V" k="113" />
    <hkern u1="&#xbf;" u2="&#x39;" k="61" />
    <hkern u1="&#xbf;" u2="&#x37;" k="27" />
    <hkern u1="&#xbf;" u2="&#x31;" k="47" />
    <hkern u1="&#xbf;" u2="&#x2f;" k="14" />
    <hkern u1="&#xbf;" u2="&#x2a;" k="166" />
    <hkern u1="&#xc0;" u2="&#x2122;" k="252" />
    <hkern u1="&#xc0;" u2="&#xbf;" k="14" />
    <hkern u1="&#xc0;" u2="&#xae;" k="184" />
    <hkern u1="&#xc0;" u2="x" k="-20" />
    <hkern u1="&#xc0;" u2="v" k="82" />
    <hkern u1="&#xc0;" u2="\" k="154" />
    <hkern u1="&#xc0;" u2="X" k="-41" />
    <hkern u1="&#xc0;" u2="V" k="188" />
    <hkern u1="&#xc0;" u2="&#x40;" k="20" />
    <hkern u1="&#xc0;" u2="&#x3f;" k="106" />
    <hkern u1="&#xc0;" u2="&#x2a;" k="236" />
    <hkern u1="&#xc0;" u2="&#x26;" k="20" />
    <hkern u1="&#xc1;" u2="&#x2122;" k="252" />
    <hkern u1="&#xc1;" u2="&#xbf;" k="14" />
    <hkern u1="&#xc1;" u2="&#xae;" k="184" />
    <hkern u1="&#xc1;" u2="x" k="-20" />
    <hkern u1="&#xc1;" u2="v" k="82" />
    <hkern u1="&#xc1;" u2="\" k="154" />
    <hkern u1="&#xc1;" u2="X" k="-41" />
    <hkern u1="&#xc1;" u2="V" k="188" />
    <hkern u1="&#xc1;" u2="&#x40;" k="20" />
    <hkern u1="&#xc1;" u2="&#x3f;" k="106" />
    <hkern u1="&#xc1;" u2="&#x2a;" k="236" />
    <hkern u1="&#xc1;" u2="&#x26;" k="20" />
    <hkern u1="&#xc2;" u2="&#x2122;" k="252" />
    <hkern u1="&#xc2;" u2="&#xbf;" k="14" />
    <hkern u1="&#xc2;" u2="&#xae;" k="184" />
    <hkern u1="&#xc2;" u2="x" k="-20" />
    <hkern u1="&#xc2;" u2="v" k="82" />
    <hkern u1="&#xc2;" u2="\" k="154" />
    <hkern u1="&#xc2;" u2="X" k="-41" />
    <hkern u1="&#xc2;" u2="V" k="188" />
    <hkern u1="&#xc2;" u2="&#x40;" k="20" />
    <hkern u1="&#xc2;" u2="&#x3f;" k="106" />
    <hkern u1="&#xc2;" u2="&#x2a;" k="236" />
    <hkern u1="&#xc2;" u2="&#x26;" k="20" />
    <hkern u1="&#xc3;" u2="&#x2122;" k="252" />
    <hkern u1="&#xc3;" u2="&#xbf;" k="14" />
    <hkern u1="&#xc3;" u2="&#xae;" k="184" />
    <hkern u1="&#xc3;" u2="x" k="-20" />
    <hkern u1="&#xc3;" u2="v" k="82" />
    <hkern u1="&#xc3;" u2="\" k="154" />
    <hkern u1="&#xc3;" u2="X" k="-41" />
    <hkern u1="&#xc3;" u2="V" k="188" />
    <hkern u1="&#xc3;" u2="&#x40;" k="20" />
    <hkern u1="&#xc3;" u2="&#x3f;" k="106" />
    <hkern u1="&#xc3;" u2="&#x2a;" k="236" />
    <hkern u1="&#xc3;" u2="&#x26;" k="20" />
    <hkern u1="&#xc4;" u2="&#x2122;" k="252" />
    <hkern u1="&#xc4;" u2="&#xbf;" k="14" />
    <hkern u1="&#xc4;" u2="&#xae;" k="184" />
    <hkern u1="&#xc4;" u2="x" k="-20" />
    <hkern u1="&#xc4;" u2="v" k="82" />
    <hkern u1="&#xc4;" u2="\" k="154" />
    <hkern u1="&#xc4;" u2="X" k="-41" />
    <hkern u1="&#xc4;" u2="V" k="188" />
    <hkern u1="&#xc4;" u2="&#x40;" k="20" />
    <hkern u1="&#xc4;" u2="&#x3f;" k="106" />
    <hkern u1="&#xc4;" u2="&#x2a;" k="236" />
    <hkern u1="&#xc4;" u2="&#x26;" k="20" />
    <hkern u1="&#xc5;" u2="&#x2122;" k="252" />
    <hkern u1="&#xc5;" u2="&#xbf;" k="14" />
    <hkern u1="&#xc5;" u2="&#xae;" k="184" />
    <hkern u1="&#xc5;" u2="x" k="-20" />
    <hkern u1="&#xc5;" u2="v" k="82" />
    <hkern u1="&#xc5;" u2="\" k="154" />
    <hkern u1="&#xc5;" u2="X" k="-41" />
    <hkern u1="&#xc5;" u2="V" k="188" />
    <hkern u1="&#xc5;" u2="&#x40;" k="20" />
    <hkern u1="&#xc5;" u2="&#x3f;" k="106" />
    <hkern u1="&#xc5;" u2="&#x2a;" k="236" />
    <hkern u1="&#xc5;" u2="&#x26;" k="20" />
    <hkern u1="&#xc6;" u2="x" k="-10" />
    <hkern u1="&#xc6;" u2="X" k="-14" />
    <hkern u1="&#xc7;" u2="x" k="6" />
    <hkern u1="&#xc7;" u2="\" k="31" />
    <hkern u1="&#xc7;" u2="X" k="31" />
    <hkern u1="&#xc7;" u2="V" k="41" />
    <hkern u1="&#xc7;" u2="&#x2f;" k="51" />
    <hkern u1="&#xc8;" u2="x" k="-10" />
    <hkern u1="&#xc8;" u2="X" k="-14" />
    <hkern u1="&#xc9;" u2="x" k="-10" />
    <hkern u1="&#xc9;" u2="X" k="-14" />
    <hkern u1="&#xca;" u2="x" k="-10" />
    <hkern u1="&#xca;" u2="X" k="-14" />
    <hkern u1="&#xcb;" u2="x" k="-10" />
    <hkern u1="&#xcb;" u2="X" k="-14" />
    <hkern u1="&#xd0;" u2="&#x2122;" k="51" />
    <hkern u1="&#xd0;" u2="x" k="31" />
    <hkern u1="&#xd0;" u2="v" k="6" />
    <hkern u1="&#xd0;" u2="\" k="94" />
    <hkern u1="&#xd0;" u2="X" k="47" />
    <hkern u1="&#xd0;" u2="V" k="51" />
    <hkern u1="&#xd0;" u2="&#x2f;" k="55" />
    <hkern u1="&#xd0;" u2="&#x2a;" k="10" />
    <hkern u1="&#xd2;" u2="&#x2122;" k="51" />
    <hkern u1="&#xd2;" u2="x" k="31" />
    <hkern u1="&#xd2;" u2="v" k="6" />
    <hkern u1="&#xd2;" u2="\" k="94" />
    <hkern u1="&#xd2;" u2="X" k="47" />
    <hkern u1="&#xd2;" u2="V" k="51" />
    <hkern u1="&#xd2;" u2="&#x2f;" k="55" />
    <hkern u1="&#xd2;" u2="&#x2a;" k="10" />
    <hkern u1="&#xd3;" u2="&#x2122;" k="51" />
    <hkern u1="&#xd3;" u2="x" k="31" />
    <hkern u1="&#xd3;" u2="v" k="6" />
    <hkern u1="&#xd3;" u2="\" k="94" />
    <hkern u1="&#xd3;" u2="X" k="47" />
    <hkern u1="&#xd3;" u2="V" k="51" />
    <hkern u1="&#xd3;" u2="&#x2f;" k="55" />
    <hkern u1="&#xd3;" u2="&#x2a;" k="10" />
    <hkern u1="&#xd4;" u2="&#x2122;" k="51" />
    <hkern u1="&#xd4;" u2="x" k="31" />
    <hkern u1="&#xd4;" u2="v" k="6" />
    <hkern u1="&#xd4;" u2="\" k="94" />
    <hkern u1="&#xd4;" u2="X" k="47" />
    <hkern u1="&#xd4;" u2="V" k="51" />
    <hkern u1="&#xd4;" u2="&#x2f;" k="55" />
    <hkern u1="&#xd4;" u2="&#x2a;" k="10" />
    <hkern u1="&#xd5;" u2="&#x2122;" k="51" />
    <hkern u1="&#xd5;" u2="x" k="31" />
    <hkern u1="&#xd5;" u2="v" k="6" />
    <hkern u1="&#xd5;" u2="\" k="94" />
    <hkern u1="&#xd5;" u2="X" k="47" />
    <hkern u1="&#xd5;" u2="V" k="51" />
    <hkern u1="&#xd5;" u2="&#x2f;" k="55" />
    <hkern u1="&#xd5;" u2="&#x2a;" k="10" />
    <hkern u1="&#xd6;" u2="&#x2122;" k="51" />
    <hkern u1="&#xd6;" u2="x" k="31" />
    <hkern u1="&#xd6;" u2="v" k="6" />
    <hkern u1="&#xd6;" u2="\" k="94" />
    <hkern u1="&#xd6;" u2="X" k="47" />
    <hkern u1="&#xd6;" u2="V" k="51" />
    <hkern u1="&#xd6;" u2="&#x2f;" k="55" />
    <hkern u1="&#xd6;" u2="&#x2a;" k="10" />
    <hkern u1="&#xd8;" u2="&#x2122;" k="51" />
    <hkern u1="&#xd8;" u2="x" k="31" />
    <hkern u1="&#xd8;" u2="v" k="6" />
    <hkern u1="&#xd8;" u2="\" k="94" />
    <hkern u1="&#xd8;" u2="X" k="47" />
    <hkern u1="&#xd8;" u2="V" k="51" />
    <hkern u1="&#xd8;" u2="&#x2f;" k="55" />
    <hkern u1="&#xd8;" u2="&#x2a;" k="10" />
    <hkern u1="&#xd9;" u2="&#x2f;" k="41" />
    <hkern u1="&#xda;" u2="&#x2f;" k="41" />
    <hkern u1="&#xdb;" u2="&#x2f;" k="41" />
    <hkern u1="&#xdc;" u2="&#x2f;" k="41" />
    <hkern u1="&#xdd;" u2="&#x2122;" k="-37" />
    <hkern u1="&#xdd;" u2="&#xbf;" k="139" />
    <hkern u1="&#xdd;" u2="&#xae;" k="35" />
    <hkern u1="&#xdd;" u2="&#xa1;" k="41" />
    <hkern u1="&#xdd;" u2="x" k="92" />
    <hkern u1="&#xdd;" u2="v" k="72" />
    <hkern u1="&#xdd;" u2="i" k="12" />
    <hkern u1="&#xdd;" u2="&#x40;" k="119" />
    <hkern u1="&#xdd;" u2="&#x3f;" k="6" />
    <hkern u1="&#xdd;" u2="&#x2f;" k="225" />
    <hkern u1="&#xdd;" u2="&#x2a;" k="31" />
    <hkern u1="&#xdd;" u2="&#x26;" k="92" />
    <hkern u1="&#xdd;" u2="&#x21;" k="10" />
    <hkern u1="&#xdf;" u2="&#x2122;" k="88" />
    <hkern u1="&#xdf;" u2="&#xae;" k="10" />
    <hkern u1="&#xdf;" u2="x" k="10" />
    <hkern u1="&#xdf;" u2="v" k="25" />
    <hkern u1="&#xdf;" u2="\" k="113" />
    <hkern u1="&#xdf;" u2="&#x3f;" k="20" />
    <hkern u1="&#xdf;" u2="&#x2f;" k="45" />
    <hkern u1="&#xdf;" u2="&#x2a;" k="41" />
    <hkern u1="&#xe0;" u2="&#x2122;" k="129" />
    <hkern u1="&#xe0;" u2="&#xae;" k="20" />
    <hkern u1="&#xe0;" u2="v" k="16" />
    <hkern u1="&#xe0;" u2="\" k="104" />
    <hkern u1="&#xe0;" u2="&#x3f;" k="37" />
    <hkern u1="&#xe0;" u2="&#x2a;" k="31" />
    <hkern u1="&#xe1;" u2="&#x2122;" k="129" />
    <hkern u1="&#xe1;" u2="&#xae;" k="20" />
    <hkern u1="&#xe1;" u2="v" k="16" />
    <hkern u1="&#xe1;" u2="\" k="104" />
    <hkern u1="&#xe1;" u2="&#x3f;" k="37" />
    <hkern u1="&#xe1;" u2="&#x2a;" k="31" />
    <hkern u1="&#xe2;" u2="&#x2122;" k="129" />
    <hkern u1="&#xe2;" u2="&#xae;" k="20" />
    <hkern u1="&#xe2;" u2="v" k="16" />
    <hkern u1="&#xe2;" u2="\" k="104" />
    <hkern u1="&#xe2;" u2="&#x3f;" k="37" />
    <hkern u1="&#xe2;" u2="&#x2a;" k="31" />
    <hkern u1="&#xe3;" u2="&#x2122;" k="129" />
    <hkern u1="&#xe3;" u2="&#xae;" k="20" />
    <hkern u1="&#xe3;" u2="v" k="16" />
    <hkern u1="&#xe3;" u2="\" k="104" />
    <hkern u1="&#xe3;" u2="&#x3f;" k="37" />
    <hkern u1="&#xe3;" u2="&#x2a;" k="31" />
    <hkern u1="&#xe4;" u2="&#x2122;" k="129" />
    <hkern u1="&#xe4;" u2="&#xae;" k="20" />
    <hkern u1="&#xe4;" u2="v" k="16" />
    <hkern u1="&#xe4;" u2="\" k="104" />
    <hkern u1="&#xe4;" u2="&#x3f;" k="37" />
    <hkern u1="&#xe4;" u2="&#x2a;" k="31" />
    <hkern u1="&#xe5;" u2="&#x2122;" k="129" />
    <hkern u1="&#xe5;" u2="&#xae;" k="20" />
    <hkern u1="&#xe5;" u2="v" k="16" />
    <hkern u1="&#xe5;" u2="\" k="104" />
    <hkern u1="&#xe5;" u2="&#x3f;" k="37" />
    <hkern u1="&#xe5;" u2="&#x2a;" k="31" />
    <hkern u1="&#xe6;" u2="&#x2122;" k="88" />
    <hkern u1="&#xe6;" u2="&#xae;" k="20" />
    <hkern u1="&#xe6;" u2="x" k="20" />
    <hkern u1="&#xe6;" u2="v" k="20" />
    <hkern u1="&#xe6;" u2="\" k="102" />
    <hkern u1="&#xe6;" u2="&#x3f;" k="25" />
    <hkern u1="&#xe6;" u2="&#x2f;" k="25" />
    <hkern u1="&#xe6;" u2="&#x2a;" k="20" />
    <hkern u1="&#xe7;" u2="&#x2122;" k="82" />
    <hkern u1="&#xe7;" u2="&#xae;" k="10" />
    <hkern u1="&#xe7;" u2="v" k="16" />
    <hkern u1="&#xe7;" u2="\" k="102" />
    <hkern u1="&#xe7;" u2="&#x3f;" k="25" />
    <hkern u1="&#xe7;" u2="&#x2f;" k="20" />
    <hkern u1="&#xe7;" u2="&#x2a;" k="20" />
    <hkern u1="&#xe8;" u2="&#x2122;" k="88" />
    <hkern u1="&#xe8;" u2="&#xae;" k="20" />
    <hkern u1="&#xe8;" u2="x" k="20" />
    <hkern u1="&#xe8;" u2="v" k="20" />
    <hkern u1="&#xe8;" u2="\" k="102" />
    <hkern u1="&#xe8;" u2="&#x3f;" k="25" />
    <hkern u1="&#xe8;" u2="&#x2f;" k="25" />
    <hkern u1="&#xe8;" u2="&#x2a;" k="20" />
    <hkern u1="&#xe9;" u2="&#x2122;" k="88" />
    <hkern u1="&#xe9;" u2="&#xae;" k="20" />
    <hkern u1="&#xe9;" u2="x" k="20" />
    <hkern u1="&#xe9;" u2="v" k="20" />
    <hkern u1="&#xe9;" u2="\" k="102" />
    <hkern u1="&#xe9;" u2="&#x3f;" k="25" />
    <hkern u1="&#xe9;" u2="&#x2f;" k="25" />
    <hkern u1="&#xe9;" u2="&#x2a;" k="20" />
    <hkern u1="&#xea;" u2="&#x2122;" k="88" />
    <hkern u1="&#xea;" u2="&#xae;" k="20" />
    <hkern u1="&#xea;" u2="x" k="20" />
    <hkern u1="&#xea;" u2="v" k="20" />
    <hkern u1="&#xea;" u2="\" k="102" />
    <hkern u1="&#xea;" u2="&#x3f;" k="25" />
    <hkern u1="&#xea;" u2="&#x2f;" k="25" />
    <hkern u1="&#xea;" u2="&#x2a;" k="20" />
    <hkern u1="&#xeb;" u2="&#x2122;" k="88" />
    <hkern u1="&#xeb;" u2="&#xae;" k="20" />
    <hkern u1="&#xeb;" u2="x" k="20" />
    <hkern u1="&#xeb;" u2="v" k="20" />
    <hkern u1="&#xeb;" u2="\" k="102" />
    <hkern u1="&#xeb;" u2="&#x3f;" k="25" />
    <hkern u1="&#xeb;" u2="&#x2f;" k="25" />
    <hkern u1="&#xeb;" u2="&#x2a;" k="20" />
    <hkern u1="&#xf1;" u2="&#x2122;" k="117" />
    <hkern u1="&#xf1;" u2="&#xae;" k="25" />
    <hkern u1="&#xf1;" u2="v" k="10" />
    <hkern u1="&#xf1;" u2="\" k="106" />
    <hkern u1="&#xf1;" u2="&#x2a;" k="31" />
    <hkern u1="&#xf2;" u2="&#x2122;" k="82" />
    <hkern u1="&#xf2;" u2="&#xae;" k="14" />
    <hkern u1="&#xf2;" u2="x" k="27" />
    <hkern u1="&#xf2;" u2="v" k="25" />
    <hkern u1="&#xf2;" u2="\" k="117" />
    <hkern u1="&#xf2;" u2="&#x3f;" k="25" />
    <hkern u1="&#xf2;" u2="&#x2f;" k="47" />
    <hkern u1="&#xf2;" u2="&#x2a;" k="31" />
    <hkern u1="&#xf3;" u2="&#x2122;" k="82" />
    <hkern u1="&#xf3;" u2="&#xae;" k="14" />
    <hkern u1="&#xf3;" u2="x" k="27" />
    <hkern u1="&#xf3;" u2="v" k="25" />
    <hkern u1="&#xf3;" u2="\" k="117" />
    <hkern u1="&#xf3;" u2="&#x3f;" k="25" />
    <hkern u1="&#xf3;" u2="&#x2f;" k="47" />
    <hkern u1="&#xf3;" u2="&#x2a;" k="31" />
    <hkern u1="&#xf4;" u2="&#x2122;" k="82" />
    <hkern u1="&#xf4;" u2="&#xae;" k="14" />
    <hkern u1="&#xf4;" u2="x" k="27" />
    <hkern u1="&#xf4;" u2="v" k="25" />
    <hkern u1="&#xf4;" u2="\" k="117" />
    <hkern u1="&#xf4;" u2="&#x3f;" k="25" />
    <hkern u1="&#xf4;" u2="&#x2f;" k="47" />
    <hkern u1="&#xf4;" u2="&#x2a;" k="31" />
    <hkern u1="&#xf5;" u2="&#x2122;" k="82" />
    <hkern u1="&#xf5;" u2="&#xae;" k="14" />
    <hkern u1="&#xf5;" u2="x" k="27" />
    <hkern u1="&#xf5;" u2="v" k="25" />
    <hkern u1="&#xf5;" u2="\" k="117" />
    <hkern u1="&#xf5;" u2="&#x3f;" k="25" />
    <hkern u1="&#xf5;" u2="&#x2f;" k="47" />
    <hkern u1="&#xf5;" u2="&#x2a;" k="31" />
    <hkern u1="&#xf6;" u2="&#x2122;" k="82" />
    <hkern u1="&#xf6;" u2="&#xae;" k="14" />
    <hkern u1="&#xf6;" u2="x" k="27" />
    <hkern u1="&#xf6;" u2="v" k="25" />
    <hkern u1="&#xf6;" u2="\" k="117" />
    <hkern u1="&#xf6;" u2="&#x3f;" k="25" />
    <hkern u1="&#xf6;" u2="&#x2f;" k="47" />
    <hkern u1="&#xf6;" u2="&#x2a;" k="31" />
    <hkern u1="&#xf8;" u2="&#x2122;" k="82" />
    <hkern u1="&#xf8;" u2="&#xae;" k="14" />
    <hkern u1="&#xf8;" u2="x" k="27" />
    <hkern u1="&#xf8;" u2="v" k="25" />
    <hkern u1="&#xf8;" u2="\" k="117" />
    <hkern u1="&#xf8;" u2="&#x3f;" k="25" />
    <hkern u1="&#xf8;" u2="&#x2f;" k="47" />
    <hkern u1="&#xf8;" u2="&#x2a;" k="31" />
    <hkern u1="&#xf9;" u2="&#x2122;" k="14" />
    <hkern u1="&#xf9;" u2="\" k="18" />
    <hkern u1="&#xfa;" u2="&#x2122;" k="14" />
    <hkern u1="&#xfa;" u2="\" k="18" />
    <hkern u1="&#xfb;" u2="&#x2122;" k="14" />
    <hkern u1="&#xfb;" u2="\" k="18" />
    <hkern u1="&#xfc;" u2="&#x2122;" k="14" />
    <hkern u1="&#xfc;" u2="\" k="18" />
    <hkern u1="&#xfd;" u2="&#xbf;" k="41" />
    <hkern u1="&#xfd;" u2="&#xae;" k="-35" />
    <hkern u1="&#xfd;" u2="&#xa1;" k="10" />
    <hkern u1="&#xfd;" u2="\" k="16" />
    <hkern u1="&#xfd;" u2="&#x40;" k="10" />
    <hkern u1="&#xfd;" u2="&#x3f;" k="-23" />
    <hkern u1="&#xfd;" u2="&#x2f;" k="86" />
    <hkern u1="&#xfd;" u2="&#x2a;" k="-10" />
    <hkern u1="&#xfe;" u2="&#x2122;" k="82" />
    <hkern u1="&#xfe;" u2="&#xae;" k="14" />
    <hkern u1="&#xfe;" u2="x" k="27" />
    <hkern u1="&#xfe;" u2="v" k="25" />
    <hkern u1="&#xfe;" u2="\" k="117" />
    <hkern u1="&#xfe;" u2="&#x3f;" k="25" />
    <hkern u1="&#xfe;" u2="&#x2f;" k="47" />
    <hkern u1="&#xfe;" u2="&#x2a;" k="31" />
    <hkern u1="&#xff;" u2="&#xbf;" k="41" />
    <hkern u1="&#xff;" u2="&#xae;" k="-35" />
    <hkern u1="&#xff;" u2="&#xa1;" k="10" />
    <hkern u1="&#xff;" u2="\" k="16" />
    <hkern u1="&#xff;" u2="&#x40;" k="10" />
    <hkern u1="&#xff;" u2="&#x3f;" k="-23" />
    <hkern u1="&#xff;" u2="&#x2f;" k="86" />
    <hkern u1="&#xff;" u2="&#x2a;" k="-10" />
    <hkern u1="&#x100;" u2="&#x2122;" k="252" />
    <hkern u1="&#x100;" u2="&#xbf;" k="14" />
    <hkern u1="&#x100;" u2="&#xae;" k="184" />
    <hkern u1="&#x100;" u2="x" k="-20" />
    <hkern u1="&#x100;" u2="v" k="82" />
    <hkern u1="&#x100;" u2="\" k="154" />
    <hkern u1="&#x100;" u2="X" k="-41" />
    <hkern u1="&#x100;" u2="V" k="188" />
    <hkern u1="&#x100;" u2="&#x40;" k="20" />
    <hkern u1="&#x100;" u2="&#x3f;" k="106" />
    <hkern u1="&#x100;" u2="&#x2a;" k="236" />
    <hkern u1="&#x100;" u2="&#x26;" k="20" />
    <hkern u1="&#x101;" u2="&#x2122;" k="129" />
    <hkern u1="&#x101;" u2="&#xae;" k="20" />
    <hkern u1="&#x101;" u2="v" k="16" />
    <hkern u1="&#x101;" u2="\" k="104" />
    <hkern u1="&#x101;" u2="&#x3f;" k="37" />
    <hkern u1="&#x101;" u2="&#x2a;" k="31" />
    <hkern u1="&#x102;" u2="&#x2122;" k="252" />
    <hkern u1="&#x102;" u2="&#xbf;" k="14" />
    <hkern u1="&#x102;" u2="&#xae;" k="184" />
    <hkern u1="&#x102;" u2="x" k="-20" />
    <hkern u1="&#x102;" u2="v" k="82" />
    <hkern u1="&#x102;" u2="\" k="154" />
    <hkern u1="&#x102;" u2="X" k="-41" />
    <hkern u1="&#x102;" u2="V" k="188" />
    <hkern u1="&#x102;" u2="&#x40;" k="20" />
    <hkern u1="&#x102;" u2="&#x3f;" k="106" />
    <hkern u1="&#x102;" u2="&#x2a;" k="236" />
    <hkern u1="&#x102;" u2="&#x26;" k="20" />
    <hkern u1="&#x103;" u2="&#x2122;" k="129" />
    <hkern u1="&#x103;" u2="&#xae;" k="20" />
    <hkern u1="&#x103;" u2="v" k="16" />
    <hkern u1="&#x103;" u2="\" k="104" />
    <hkern u1="&#x103;" u2="&#x3f;" k="37" />
    <hkern u1="&#x103;" u2="&#x2a;" k="31" />
    <hkern u1="&#x104;" u2="&#x2122;" k="252" />
    <hkern u1="&#x104;" u2="&#xbf;" k="14" />
    <hkern u1="&#x104;" u2="&#xae;" k="184" />
    <hkern u1="&#x104;" u2="x" k="-20" />
    <hkern u1="&#x104;" u2="v" k="82" />
    <hkern u1="&#x104;" u2="\" k="154" />
    <hkern u1="&#x104;" u2="X" k="-41" />
    <hkern u1="&#x104;" u2="V" k="188" />
    <hkern u1="&#x104;" u2="&#x40;" k="20" />
    <hkern u1="&#x104;" u2="&#x3f;" k="106" />
    <hkern u1="&#x104;" u2="&#x2a;" k="236" />
    <hkern u1="&#x104;" u2="&#x26;" k="20" />
    <hkern u1="&#x105;" u2="&#x2122;" k="129" />
    <hkern u1="&#x105;" u2="&#xae;" k="20" />
    <hkern u1="&#x105;" u2="v" k="16" />
    <hkern u1="&#x105;" u2="\" k="104" />
    <hkern u1="&#x105;" u2="&#x3f;" k="37" />
    <hkern u1="&#x105;" u2="&#x2a;" k="31" />
    <hkern u1="&#x106;" u2="x" k="6" />
    <hkern u1="&#x106;" u2="\" k="31" />
    <hkern u1="&#x106;" u2="X" k="31" />
    <hkern u1="&#x106;" u2="V" k="41" />
    <hkern u1="&#x106;" u2="&#x2f;" k="51" />
    <hkern u1="&#x107;" u2="&#x2122;" k="82" />
    <hkern u1="&#x107;" u2="&#xae;" k="10" />
    <hkern u1="&#x107;" u2="v" k="16" />
    <hkern u1="&#x107;" u2="\" k="102" />
    <hkern u1="&#x107;" u2="&#x3f;" k="25" />
    <hkern u1="&#x107;" u2="&#x2f;" k="20" />
    <hkern u1="&#x107;" u2="&#x2a;" k="20" />
    <hkern u1="&#x108;" u2="x" k="6" />
    <hkern u1="&#x108;" u2="\" k="31" />
    <hkern u1="&#x108;" u2="X" k="31" />
    <hkern u1="&#x108;" u2="V" k="41" />
    <hkern u1="&#x108;" u2="&#x2f;" k="51" />
    <hkern u1="&#x109;" u2="&#x2122;" k="82" />
    <hkern u1="&#x109;" u2="&#xae;" k="10" />
    <hkern u1="&#x109;" u2="v" k="16" />
    <hkern u1="&#x109;" u2="\" k="102" />
    <hkern u1="&#x109;" u2="&#x3f;" k="25" />
    <hkern u1="&#x109;" u2="&#x2f;" k="20" />
    <hkern u1="&#x109;" u2="&#x2a;" k="20" />
    <hkern u1="&#x10a;" u2="x" k="6" />
    <hkern u1="&#x10a;" u2="\" k="31" />
    <hkern u1="&#x10a;" u2="X" k="31" />
    <hkern u1="&#x10a;" u2="V" k="41" />
    <hkern u1="&#x10a;" u2="&#x2f;" k="51" />
    <hkern u1="&#x10b;" u2="&#x2122;" k="82" />
    <hkern u1="&#x10b;" u2="&#xae;" k="10" />
    <hkern u1="&#x10b;" u2="v" k="16" />
    <hkern u1="&#x10b;" u2="\" k="102" />
    <hkern u1="&#x10b;" u2="&#x3f;" k="25" />
    <hkern u1="&#x10b;" u2="&#x2f;" k="20" />
    <hkern u1="&#x10b;" u2="&#x2a;" k="20" />
    <hkern u1="&#x10c;" u2="x" k="6" />
    <hkern u1="&#x10c;" u2="\" k="31" />
    <hkern u1="&#x10c;" u2="X" k="31" />
    <hkern u1="&#x10c;" u2="V" k="41" />
    <hkern u1="&#x10c;" u2="&#x2f;" k="51" />
    <hkern u1="&#x10d;" u2="&#x2122;" k="82" />
    <hkern u1="&#x10d;" u2="&#xae;" k="10" />
    <hkern u1="&#x10d;" u2="v" k="16" />
    <hkern u1="&#x10d;" u2="\" k="102" />
    <hkern u1="&#x10d;" u2="&#x3f;" k="25" />
    <hkern u1="&#x10d;" u2="&#x2f;" k="20" />
    <hkern u1="&#x10d;" u2="&#x2a;" k="20" />
    <hkern u1="&#x10e;" u2="&#x2122;" k="51" />
    <hkern u1="&#x10e;" u2="x" k="31" />
    <hkern u1="&#x10e;" u2="v" k="6" />
    <hkern u1="&#x10e;" u2="\" k="94" />
    <hkern u1="&#x10e;" u2="X" k="47" />
    <hkern u1="&#x10e;" u2="V" k="51" />
    <hkern u1="&#x10e;" u2="&#x2f;" k="55" />
    <hkern u1="&#x10e;" u2="&#x2a;" k="10" />
    <hkern u1="&#x10f;" u2="&#x20ac;" k="41" />
    <hkern u1="&#x10f;" u2="&#xbf;" k="246" />
    <hkern u1="&#x10f;" u2="&#xae;" k="16" />
    <hkern u1="&#x10f;" u2="&#xa3;" k="109" />
    <hkern u1="&#x10f;" u2="&#xa2;" k="47" />
    <hkern u1="&#x10f;" u2="x" k="23" />
    <hkern u1="&#x10f;" u2="j" k="4" />
    <hkern u1="&#x10f;" u2="X" k="4" />
    <hkern u1="&#x10f;" u2="V" k="-16" />
    <hkern u1="&#x10f;" u2="&#x40;" k="82" />
    <hkern u1="&#x10f;" u2="&#x39;" k="14" />
    <hkern u1="&#x10f;" u2="&#x38;" k="47" />
    <hkern u1="&#x10f;" u2="&#x36;" k="150" />
    <hkern u1="&#x10f;" u2="&#x35;" k="68" />
    <hkern u1="&#x10f;" u2="&#x34;" k="217" />
    <hkern u1="&#x10f;" u2="&#x33;" k="35" />
    <hkern u1="&#x10f;" u2="&#x32;" k="31" />
    <hkern u1="&#x10f;" u2="&#x30;" k="37" />
    <hkern u1="&#x10f;" u2="&#x2f;" k="238" />
    <hkern u1="&#x10f;" u2="&#x2a;" k="10" />
    <hkern u1="&#x10f;" u2="&#x26;" k="90" />
    <hkern u1="&#x10f;" u2="&#x24;" k="25" />
    <hkern u1="&#x10f;" u2="&#x23;" k="57" />
    <hkern u1="&#x110;" u2="&#x2122;" k="51" />
    <hkern u1="&#x110;" u2="x" k="31" />
    <hkern u1="&#x110;" u2="v" k="6" />
    <hkern u1="&#x110;" u2="\" k="94" />
    <hkern u1="&#x110;" u2="X" k="47" />
    <hkern u1="&#x110;" u2="V" k="51" />
    <hkern u1="&#x110;" u2="&#x2f;" k="55" />
    <hkern u1="&#x110;" u2="&#x2a;" k="10" />
    <hkern u1="&#x112;" u2="x" k="-10" />
    <hkern u1="&#x112;" u2="X" k="-14" />
    <hkern u1="&#x113;" u2="&#x2122;" k="88" />
    <hkern u1="&#x113;" u2="&#xae;" k="20" />
    <hkern u1="&#x113;" u2="x" k="20" />
    <hkern u1="&#x113;" u2="v" k="20" />
    <hkern u1="&#x113;" u2="\" k="102" />
    <hkern u1="&#x113;" u2="&#x3f;" k="25" />
    <hkern u1="&#x113;" u2="&#x2f;" k="25" />
    <hkern u1="&#x113;" u2="&#x2a;" k="20" />
    <hkern u1="&#x114;" u2="x" k="-10" />
    <hkern u1="&#x114;" u2="X" k="-14" />
    <hkern u1="&#x115;" u2="&#x2122;" k="88" />
    <hkern u1="&#x115;" u2="&#xae;" k="20" />
    <hkern u1="&#x115;" u2="x" k="20" />
    <hkern u1="&#x115;" u2="v" k="20" />
    <hkern u1="&#x115;" u2="\" k="102" />
    <hkern u1="&#x115;" u2="&#x3f;" k="25" />
    <hkern u1="&#x115;" u2="&#x2f;" k="25" />
    <hkern u1="&#x115;" u2="&#x2a;" k="20" />
    <hkern u1="&#x116;" u2="x" k="-10" />
    <hkern u1="&#x116;" u2="X" k="-14" />
    <hkern u1="&#x117;" u2="&#x2122;" k="88" />
    <hkern u1="&#x117;" u2="&#xae;" k="20" />
    <hkern u1="&#x117;" u2="x" k="20" />
    <hkern u1="&#x117;" u2="v" k="20" />
    <hkern u1="&#x117;" u2="\" k="102" />
    <hkern u1="&#x117;" u2="&#x3f;" k="25" />
    <hkern u1="&#x117;" u2="&#x2f;" k="25" />
    <hkern u1="&#x117;" u2="&#x2a;" k="20" />
    <hkern u1="&#x118;" u2="x" k="-10" />
    <hkern u1="&#x118;" u2="X" k="-14" />
    <hkern u1="&#x119;" u2="&#x2122;" k="88" />
    <hkern u1="&#x119;" u2="&#xae;" k="20" />
    <hkern u1="&#x119;" u2="x" k="20" />
    <hkern u1="&#x119;" u2="v" k="20" />
    <hkern u1="&#x119;" u2="\" k="102" />
    <hkern u1="&#x119;" u2="&#x3f;" k="25" />
    <hkern u1="&#x119;" u2="&#x2f;" k="25" />
    <hkern u1="&#x119;" u2="&#x2a;" k="20" />
    <hkern u1="&#x11a;" u2="x" k="-10" />
    <hkern u1="&#x11a;" u2="X" k="-14" />
    <hkern u1="&#x11b;" u2="&#x2122;" k="88" />
    <hkern u1="&#x11b;" u2="&#xae;" k="20" />
    <hkern u1="&#x11b;" u2="x" k="20" />
    <hkern u1="&#x11b;" u2="v" k="20" />
    <hkern u1="&#x11b;" u2="\" k="102" />
    <hkern u1="&#x11b;" u2="&#x3f;" k="25" />
    <hkern u1="&#x11b;" u2="&#x2f;" k="25" />
    <hkern u1="&#x11b;" u2="&#x2a;" k="20" />
    <hkern u1="&#x11c;" u2="&#x2122;" k="20" />
    <hkern u1="&#x11c;" u2="\" k="76" />
    <hkern u1="&#x11c;" u2="X" k="20" />
    <hkern u1="&#x11c;" u2="V" k="35" />
    <hkern u1="&#x11c;" u2="&#x2f;" k="57" />
    <hkern u1="&#x11d;" u2="&#x2122;" k="14" />
    <hkern u1="&#x11d;" u2="\" k="18" />
    <hkern u1="&#x11e;" u2="&#x2122;" k="20" />
    <hkern u1="&#x11e;" u2="\" k="76" />
    <hkern u1="&#x11e;" u2="X" k="20" />
    <hkern u1="&#x11e;" u2="V" k="35" />
    <hkern u1="&#x11e;" u2="&#x2f;" k="57" />
    <hkern u1="&#x11f;" u2="&#x2122;" k="14" />
    <hkern u1="&#x11f;" u2="\" k="18" />
    <hkern u1="&#x120;" u2="&#x2122;" k="20" />
    <hkern u1="&#x120;" u2="\" k="76" />
    <hkern u1="&#x120;" u2="X" k="20" />
    <hkern u1="&#x120;" u2="V" k="35" />
    <hkern u1="&#x120;" u2="&#x2f;" k="57" />
    <hkern u1="&#x121;" u2="&#x2122;" k="14" />
    <hkern u1="&#x121;" u2="\" k="18" />
    <hkern u1="&#x122;" u2="&#x2122;" k="20" />
    <hkern u1="&#x122;" u2="\" k="76" />
    <hkern u1="&#x122;" u2="X" k="20" />
    <hkern u1="&#x122;" u2="V" k="35" />
    <hkern u1="&#x122;" u2="&#x2f;" k="57" />
    <hkern u1="&#x123;" u2="&#x2122;" k="14" />
    <hkern u1="&#x123;" u2="\" k="18" />
    <hkern u1="&#x125;" u2="&#x2122;" k="117" />
    <hkern u1="&#x125;" u2="&#xae;" k="25" />
    <hkern u1="&#x125;" u2="v" k="10" />
    <hkern u1="&#x125;" u2="\" k="106" />
    <hkern u1="&#x125;" u2="&#x2a;" k="31" />
    <hkern u1="&#x127;" u2="&#x2122;" k="117" />
    <hkern u1="&#x127;" u2="&#xae;" k="25" />
    <hkern u1="&#x127;" u2="v" k="10" />
    <hkern u1="&#x127;" u2="\" k="106" />
    <hkern u1="&#x127;" u2="&#x2a;" k="31" />
    <hkern u1="&#x132;" u2="&#x2f;" k="35" />
    <hkern u1="&#x134;" u2="&#x2f;" k="35" />
    <hkern u1="&#x136;" u2="&#xbf;" k="14" />
    <hkern u1="&#x136;" u2="&#xae;" k="66" />
    <hkern u1="&#x136;" u2="x" k="-35" />
    <hkern u1="&#x136;" u2="v" k="70" />
    <hkern u1="&#x136;" u2="X" k="-27" />
    <hkern u1="&#x136;" u2="&#x40;" k="25" />
    <hkern u1="&#x136;" u2="&#x3f;" k="12" />
    <hkern u1="&#x136;" u2="&#x2f;" k="-20" />
    <hkern u1="&#x136;" u2="&#x2a;" k="41" />
    <hkern u1="&#x136;" u2="&#x26;" k="6" />
    <hkern u1="&#x137;" u2="&#x2122;" k="41" />
    <hkern u1="&#x137;" u2="&#xbf;" k="31" />
    <hkern u1="&#x137;" u2="v" k="10" />
    <hkern u1="&#x137;" u2="\" k="102" />
    <hkern u1="&#x137;" u2="&#x40;" k="27" />
    <hkern u1="&#x137;" u2="&#x3f;" k="-10" />
    <hkern u1="&#x137;" u2="&#x26;" k="6" />
    <hkern u1="&#x138;" u2="&#x2122;" k="41" />
    <hkern u1="&#x138;" u2="&#xbf;" k="31" />
    <hkern u1="&#x138;" u2="v" k="10" />
    <hkern u1="&#x138;" u2="\" k="102" />
    <hkern u1="&#x138;" u2="&#x40;" k="27" />
    <hkern u1="&#x138;" u2="&#x3f;" k="-10" />
    <hkern u1="&#x138;" u2="&#x26;" k="6" />
    <hkern u1="&#x139;" u2="&#x2122;" k="471" />
    <hkern u1="&#x139;" u2="&#xae;" k="350" />
    <hkern u1="&#x139;" u2="x" k="-10" />
    <hkern u1="&#x139;" u2="v" k="129" />
    <hkern u1="&#x139;" u2="\" k="223" />
    <hkern u1="&#x139;" u2="X" k="-14" />
    <hkern u1="&#x139;" u2="V" k="205" />
    <hkern u1="&#x139;" u2="&#x40;" k="29" />
    <hkern u1="&#x139;" u2="&#x3f;" k="119" />
    <hkern u1="&#x139;" u2="&#x2f;" k="-14" />
    <hkern u1="&#x139;" u2="&#x2a;" k="440" />
    <hkern u1="&#x139;" u2="&#x26;" k="14" />
    <hkern u1="&#x13b;" u2="&#x2122;" k="471" />
    <hkern u1="&#x13b;" u2="&#xae;" k="350" />
    <hkern u1="&#x13b;" u2="x" k="-10" />
    <hkern u1="&#x13b;" u2="v" k="129" />
    <hkern u1="&#x13b;" u2="\" k="223" />
    <hkern u1="&#x13b;" u2="X" k="-14" />
    <hkern u1="&#x13b;" u2="V" k="205" />
    <hkern u1="&#x13b;" u2="&#x40;" k="29" />
    <hkern u1="&#x13b;" u2="&#x3f;" k="119" />
    <hkern u1="&#x13b;" u2="&#x2f;" k="-14" />
    <hkern u1="&#x13b;" u2="&#x2a;" k="440" />
    <hkern u1="&#x13b;" u2="&#x26;" k="14" />
    <hkern u1="&#x13e;" u2="&#x20ac;" k="41" />
    <hkern u1="&#x13e;" u2="&#xbf;" k="246" />
    <hkern u1="&#x13e;" u2="&#xae;" k="16" />
    <hkern u1="&#x13e;" u2="&#xa3;" k="109" />
    <hkern u1="&#x13e;" u2="&#xa2;" k="47" />
    <hkern u1="&#x13e;" u2="x" k="23" />
    <hkern u1="&#x13e;" u2="j" k="4" />
    <hkern u1="&#x13e;" u2="X" k="4" />
    <hkern u1="&#x13e;" u2="V" k="-16" />
    <hkern u1="&#x13e;" u2="&#x40;" k="82" />
    <hkern u1="&#x13e;" u2="&#x39;" k="14" />
    <hkern u1="&#x13e;" u2="&#x38;" k="47" />
    <hkern u1="&#x13e;" u2="&#x36;" k="150" />
    <hkern u1="&#x13e;" u2="&#x35;" k="68" />
    <hkern u1="&#x13e;" u2="&#x34;" k="217" />
    <hkern u1="&#x13e;" u2="&#x33;" k="35" />
    <hkern u1="&#x13e;" u2="&#x32;" k="31" />
    <hkern u1="&#x13e;" u2="&#x30;" k="37" />
    <hkern u1="&#x13e;" u2="&#x2f;" k="238" />
    <hkern u1="&#x13e;" u2="&#x2a;" k="10" />
    <hkern u1="&#x13e;" u2="&#x26;" k="90" />
    <hkern u1="&#x13e;" u2="&#x24;" k="25" />
    <hkern u1="&#x13e;" u2="&#x23;" k="57" />
    <hkern u1="&#x144;" u2="&#x2122;" k="117" />
    <hkern u1="&#x144;" u2="&#xae;" k="25" />
    <hkern u1="&#x144;" u2="v" k="10" />
    <hkern u1="&#x144;" u2="\" k="106" />
    <hkern u1="&#x144;" u2="&#x2a;" k="31" />
    <hkern u1="&#x146;" u2="&#x2122;" k="117" />
    <hkern u1="&#x146;" u2="&#xae;" k="25" />
    <hkern u1="&#x146;" u2="v" k="10" />
    <hkern u1="&#x146;" u2="\" k="106" />
    <hkern u1="&#x146;" u2="&#x2a;" k="31" />
    <hkern u1="&#x148;" u2="&#x2122;" k="117" />
    <hkern u1="&#x148;" u2="&#xae;" k="25" />
    <hkern u1="&#x148;" u2="v" k="10" />
    <hkern u1="&#x148;" u2="\" k="106" />
    <hkern u1="&#x148;" u2="&#x2a;" k="31" />
    <hkern u1="&#x14b;" u2="&#x2122;" k="117" />
    <hkern u1="&#x14b;" u2="&#xae;" k="25" />
    <hkern u1="&#x14b;" u2="v" k="10" />
    <hkern u1="&#x14b;" u2="\" k="106" />
    <hkern u1="&#x14b;" u2="&#x2a;" k="31" />
    <hkern u1="&#x14c;" u2="&#x2122;" k="51" />
    <hkern u1="&#x14c;" u2="x" k="31" />
    <hkern u1="&#x14c;" u2="v" k="6" />
    <hkern u1="&#x14c;" u2="\" k="94" />
    <hkern u1="&#x14c;" u2="X" k="47" />
    <hkern u1="&#x14c;" u2="V" k="51" />
    <hkern u1="&#x14c;" u2="&#x2f;" k="55" />
    <hkern u1="&#x14c;" u2="&#x2a;" k="10" />
    <hkern u1="&#x14d;" u2="&#x2122;" k="82" />
    <hkern u1="&#x14d;" u2="&#xae;" k="14" />
    <hkern u1="&#x14d;" u2="x" k="27" />
    <hkern u1="&#x14d;" u2="v" k="25" />
    <hkern u1="&#x14d;" u2="\" k="117" />
    <hkern u1="&#x14d;" u2="&#x3f;" k="25" />
    <hkern u1="&#x14d;" u2="&#x2f;" k="47" />
    <hkern u1="&#x14d;" u2="&#x2a;" k="31" />
    <hkern u1="&#x14e;" u2="&#x2122;" k="51" />
    <hkern u1="&#x14e;" u2="x" k="31" />
    <hkern u1="&#x14e;" u2="v" k="6" />
    <hkern u1="&#x14e;" u2="\" k="94" />
    <hkern u1="&#x14e;" u2="X" k="47" />
    <hkern u1="&#x14e;" u2="V" k="51" />
    <hkern u1="&#x14e;" u2="&#x2f;" k="55" />
    <hkern u1="&#x14e;" u2="&#x2a;" k="10" />
    <hkern u1="&#x14f;" u2="&#x2122;" k="82" />
    <hkern u1="&#x14f;" u2="&#xae;" k="14" />
    <hkern u1="&#x14f;" u2="x" k="27" />
    <hkern u1="&#x14f;" u2="v" k="25" />
    <hkern u1="&#x14f;" u2="\" k="117" />
    <hkern u1="&#x14f;" u2="&#x3f;" k="25" />
    <hkern u1="&#x14f;" u2="&#x2f;" k="47" />
    <hkern u1="&#x14f;" u2="&#x2a;" k="31" />
    <hkern u1="&#x150;" u2="&#x2122;" k="51" />
    <hkern u1="&#x150;" u2="x" k="31" />
    <hkern u1="&#x150;" u2="v" k="6" />
    <hkern u1="&#x150;" u2="\" k="94" />
    <hkern u1="&#x150;" u2="X" k="47" />
    <hkern u1="&#x150;" u2="V" k="51" />
    <hkern u1="&#x150;" u2="&#x2f;" k="55" />
    <hkern u1="&#x150;" u2="&#x2a;" k="10" />
    <hkern u1="&#x151;" u2="&#x2122;" k="82" />
    <hkern u1="&#x151;" u2="&#xae;" k="14" />
    <hkern u1="&#x151;" u2="x" k="27" />
    <hkern u1="&#x151;" u2="v" k="25" />
    <hkern u1="&#x151;" u2="\" k="117" />
    <hkern u1="&#x151;" u2="&#x3f;" k="25" />
    <hkern u1="&#x151;" u2="&#x2f;" k="47" />
    <hkern u1="&#x151;" u2="&#x2a;" k="31" />
    <hkern u1="&#x153;" u2="&#x2122;" k="88" />
    <hkern u1="&#x153;" u2="&#xae;" k="20" />
    <hkern u1="&#x153;" u2="x" k="20" />
    <hkern u1="&#x153;" u2="v" k="20" />
    <hkern u1="&#x153;" u2="\" k="102" />
    <hkern u1="&#x153;" u2="&#x3f;" k="25" />
    <hkern u1="&#x153;" u2="&#x2f;" k="25" />
    <hkern u1="&#x153;" u2="&#x2a;" k="20" />
    <hkern u1="&#x154;" u2="&#x2122;" k="41" />
    <hkern u1="&#x154;" u2="&#xbf;" k="10" />
    <hkern u1="&#x154;" u2="&#xae;" k="10" />
    <hkern u1="&#x154;" u2="x" k="-27" />
    <hkern u1="&#x154;" u2="\" k="51" />
    <hkern u1="&#x154;" u2="X" k="-20" />
    <hkern u1="&#x154;" u2="V" k="45" />
    <hkern u1="&#x154;" u2="&#x40;" k="10" />
    <hkern u1="&#x154;" u2="&#x3f;" k="10" />
    <hkern u1="&#x154;" u2="&#x2a;" k="20" />
    <hkern u1="&#x154;" u2="&#x26;" k="10" />
    <hkern u1="&#x155;" u2="&#xbf;" k="16" />
    <hkern u1="&#x155;" u2="&#xae;" k="-20" />
    <hkern u1="&#x155;" u2="x" k="-18" />
    <hkern u1="&#x155;" u2="v" k="-45" />
    <hkern u1="&#x155;" u2="\" k="27" />
    <hkern u1="&#x155;" u2="&#x3f;" k="-20" />
    <hkern u1="&#x155;" u2="&#x2f;" k="102" />
    <hkern u1="&#x155;" u2="&#x2a;" k="-41" />
    <hkern u1="&#x155;" u2="&#x26;" k="16" />
    <hkern u1="&#x156;" u2="&#x2122;" k="41" />
    <hkern u1="&#x156;" u2="&#xbf;" k="10" />
    <hkern u1="&#x156;" u2="&#xae;" k="10" />
    <hkern u1="&#x156;" u2="x" k="-27" />
    <hkern u1="&#x156;" u2="\" k="51" />
    <hkern u1="&#x156;" u2="X" k="-20" />
    <hkern u1="&#x156;" u2="V" k="45" />
    <hkern u1="&#x156;" u2="&#x40;" k="10" />
    <hkern u1="&#x156;" u2="&#x3f;" k="10" />
    <hkern u1="&#x156;" u2="&#x2a;" k="20" />
    <hkern u1="&#x156;" u2="&#x26;" k="10" />
    <hkern u1="&#x157;" u2="&#xbf;" k="16" />
    <hkern u1="&#x157;" u2="&#xae;" k="-20" />
    <hkern u1="&#x157;" u2="x" k="-18" />
    <hkern u1="&#x157;" u2="v" k="-45" />
    <hkern u1="&#x157;" u2="\" k="27" />
    <hkern u1="&#x157;" u2="&#x3f;" k="-20" />
    <hkern u1="&#x157;" u2="&#x2f;" k="102" />
    <hkern u1="&#x157;" u2="&#x2a;" k="-41" />
    <hkern u1="&#x157;" u2="&#x26;" k="16" />
    <hkern u1="&#x158;" u2="&#x2122;" k="41" />
    <hkern u1="&#x158;" u2="&#xbf;" k="10" />
    <hkern u1="&#x158;" u2="&#xae;" k="10" />
    <hkern u1="&#x158;" u2="x" k="-27" />
    <hkern u1="&#x158;" u2="\" k="51" />
    <hkern u1="&#x158;" u2="X" k="-20" />
    <hkern u1="&#x158;" u2="V" k="45" />
    <hkern u1="&#x158;" u2="&#x40;" k="10" />
    <hkern u1="&#x158;" u2="&#x3f;" k="10" />
    <hkern u1="&#x158;" u2="&#x2a;" k="20" />
    <hkern u1="&#x158;" u2="&#x26;" k="10" />
    <hkern u1="&#x159;" u2="&#xbf;" k="16" />
    <hkern u1="&#x159;" u2="&#xae;" k="-20" />
    <hkern u1="&#x159;" u2="x" k="-18" />
    <hkern u1="&#x159;" u2="v" k="-45" />
    <hkern u1="&#x159;" u2="\" k="27" />
    <hkern u1="&#x159;" u2="&#x3f;" k="-20" />
    <hkern u1="&#x159;" u2="&#x2f;" k="102" />
    <hkern u1="&#x159;" u2="&#x2a;" k="-41" />
    <hkern u1="&#x159;" u2="&#x26;" k="16" />
    <hkern u1="&#x15a;" u2="&#x2122;" k="61" />
    <hkern u1="&#x15a;" u2="x" k="14" />
    <hkern u1="&#x15a;" u2="v" k="20" />
    <hkern u1="&#x15a;" u2="\" k="78" />
    <hkern u1="&#x15a;" u2="X" k="10" />
    <hkern u1="&#x15a;" u2="V" k="68" />
    <hkern u1="&#x15a;" u2="&#x3f;" k="10" />
    <hkern u1="&#x15a;" u2="&#x2f;" k="57" />
    <hkern u1="&#x15a;" u2="&#x2a;" k="31" />
    <hkern u1="&#x15b;" u2="&#x2122;" k="88" />
    <hkern u1="&#x15b;" u2="&#xae;" k="10" />
    <hkern u1="&#x15b;" u2="x" k="10" />
    <hkern u1="&#x15b;" u2="v" k="25" />
    <hkern u1="&#x15b;" u2="\" k="113" />
    <hkern u1="&#x15b;" u2="&#x3f;" k="20" />
    <hkern u1="&#x15b;" u2="&#x2f;" k="45" />
    <hkern u1="&#x15b;" u2="&#x2a;" k="41" />
    <hkern u1="&#x15c;" u2="&#x2122;" k="61" />
    <hkern u1="&#x15c;" u2="x" k="14" />
    <hkern u1="&#x15c;" u2="v" k="20" />
    <hkern u1="&#x15c;" u2="\" k="78" />
    <hkern u1="&#x15c;" u2="X" k="10" />
    <hkern u1="&#x15c;" u2="V" k="68" />
    <hkern u1="&#x15c;" u2="&#x3f;" k="10" />
    <hkern u1="&#x15c;" u2="&#x2f;" k="57" />
    <hkern u1="&#x15c;" u2="&#x2a;" k="31" />
    <hkern u1="&#x15d;" u2="&#x2122;" k="88" />
    <hkern u1="&#x15d;" u2="&#xae;" k="10" />
    <hkern u1="&#x15d;" u2="x" k="10" />
    <hkern u1="&#x15d;" u2="v" k="25" />
    <hkern u1="&#x15d;" u2="\" k="113" />
    <hkern u1="&#x15d;" u2="&#x3f;" k="20" />
    <hkern u1="&#x15d;" u2="&#x2f;" k="45" />
    <hkern u1="&#x15d;" u2="&#x2a;" k="41" />
    <hkern u1="&#x15e;" u2="&#x2122;" k="61" />
    <hkern u1="&#x15e;" u2="x" k="14" />
    <hkern u1="&#x15e;" u2="v" k="20" />
    <hkern u1="&#x15e;" u2="\" k="78" />
    <hkern u1="&#x15e;" u2="X" k="10" />
    <hkern u1="&#x15e;" u2="V" k="68" />
    <hkern u1="&#x15e;" u2="&#x3f;" k="10" />
    <hkern u1="&#x15e;" u2="&#x2f;" k="57" />
    <hkern u1="&#x15e;" u2="&#x2a;" k="31" />
    <hkern u1="&#x15f;" u2="&#x2122;" k="88" />
    <hkern u1="&#x15f;" u2="&#xae;" k="10" />
    <hkern u1="&#x15f;" u2="x" k="10" />
    <hkern u1="&#x15f;" u2="v" k="25" />
    <hkern u1="&#x15f;" u2="\" k="113" />
    <hkern u1="&#x15f;" u2="&#x3f;" k="20" />
    <hkern u1="&#x15f;" u2="&#x2f;" k="45" />
    <hkern u1="&#x15f;" u2="&#x2a;" k="41" />
    <hkern u1="&#x160;" u2="&#x2122;" k="61" />
    <hkern u1="&#x160;" u2="x" k="14" />
    <hkern u1="&#x160;" u2="v" k="20" />
    <hkern u1="&#x160;" u2="\" k="78" />
    <hkern u1="&#x160;" u2="X" k="10" />
    <hkern u1="&#x160;" u2="V" k="68" />
    <hkern u1="&#x160;" u2="&#x3f;" k="10" />
    <hkern u1="&#x160;" u2="&#x2f;" k="57" />
    <hkern u1="&#x160;" u2="&#x2a;" k="31" />
    <hkern u1="&#x161;" u2="&#x2122;" k="88" />
    <hkern u1="&#x161;" u2="&#xae;" k="10" />
    <hkern u1="&#x161;" u2="x" k="10" />
    <hkern u1="&#x161;" u2="v" k="25" />
    <hkern u1="&#x161;" u2="\" k="113" />
    <hkern u1="&#x161;" u2="&#x3f;" k="20" />
    <hkern u1="&#x161;" u2="&#x2f;" k="45" />
    <hkern u1="&#x161;" u2="&#x2a;" k="41" />
    <hkern u1="&#x162;" u2="&#x2122;" k="-14" />
    <hkern u1="&#x162;" u2="&#xbf;" k="121" />
    <hkern u1="&#x162;" u2="x" k="143" />
    <hkern u1="&#x162;" u2="v" k="127" />
    <hkern u1="&#x162;" u2="i" k="14" />
    <hkern u1="&#x162;" u2="X" k="-10" />
    <hkern u1="&#x162;" u2="V" k="-20" />
    <hkern u1="&#x162;" u2="&#x40;" k="66" />
    <hkern u1="&#x162;" u2="&#x3f;" k="-6" />
    <hkern u1="&#x162;" u2="&#x2f;" k="180" />
    <hkern u1="&#x162;" u2="&#x26;" k="35" />
    <hkern u1="&#x162;" u2="&#x21;" k="-10" />
    <hkern u1="&#x163;" u2="&#x2122;" k="27" />
    <hkern u1="&#x163;" u2="&#xae;" k="-10" />
    <hkern u1="&#x163;" u2="x" k="-25" />
    <hkern u1="&#x163;" u2="v" k="-10" />
    <hkern u1="&#x163;" u2="\" k="72" />
    <hkern u1="&#x163;" u2="&#x2f;" k="14" />
    <hkern u1="&#x164;" u2="&#x2122;" k="-14" />
    <hkern u1="&#x164;" u2="&#xbf;" k="121" />
    <hkern u1="&#x164;" u2="x" k="143" />
    <hkern u1="&#x164;" u2="v" k="127" />
    <hkern u1="&#x164;" u2="i" k="14" />
    <hkern u1="&#x164;" u2="X" k="-10" />
    <hkern u1="&#x164;" u2="V" k="-20" />
    <hkern u1="&#x164;" u2="&#x40;" k="66" />
    <hkern u1="&#x164;" u2="&#x3f;" k="-6" />
    <hkern u1="&#x164;" u2="&#x2f;" k="180" />
    <hkern u1="&#x164;" u2="&#x26;" k="35" />
    <hkern u1="&#x164;" u2="&#x21;" k="-10" />
    <hkern u1="&#x168;" u2="&#x2f;" k="41" />
    <hkern u1="&#x169;" u2="&#x2122;" k="14" />
    <hkern u1="&#x169;" u2="\" k="18" />
    <hkern u1="&#x16a;" u2="&#x2f;" k="41" />
    <hkern u1="&#x16b;" u2="&#x2122;" k="14" />
    <hkern u1="&#x16b;" u2="\" k="18" />
    <hkern u1="&#x16c;" u2="&#x2f;" k="41" />
    <hkern u1="&#x16d;" u2="&#x2122;" k="14" />
    <hkern u1="&#x16d;" u2="\" k="18" />
    <hkern u1="&#x16e;" u2="&#x2f;" k="41" />
    <hkern u1="&#x16f;" u2="&#x2122;" k="14" />
    <hkern u1="&#x16f;" u2="\" k="18" />
    <hkern u1="&#x170;" u2="&#x2f;" k="41" />
    <hkern u1="&#x171;" u2="&#x2122;" k="14" />
    <hkern u1="&#x171;" u2="\" k="18" />
    <hkern u1="&#x172;" u2="&#x2f;" k="41" />
    <hkern u1="&#x173;" u2="&#x2122;" k="14" />
    <hkern u1="&#x173;" u2="\" k="18" />
    <hkern u1="&#x174;" u2="&#x2122;" k="-27" />
    <hkern u1="&#x174;" u2="&#xbf;" k="51" />
    <hkern u1="&#x174;" u2="&#xa1;" k="20" />
    <hkern u1="&#x174;" u2="x" k="20" />
    <hkern u1="&#x174;" u2="v" k="6" />
    <hkern u1="&#x174;" u2="\" k="-4" />
    <hkern u1="&#x174;" u2="&#x40;" k="35" />
    <hkern u1="&#x174;" u2="&#x2f;" k="104" />
    <hkern u1="&#x174;" u2="&#x2a;" k="14" />
    <hkern u1="&#x174;" u2="&#x26;" k="16" />
    <hkern u1="&#x175;" u2="&#xbf;" k="45" />
    <hkern u1="&#x175;" u2="&#xae;" k="-35" />
    <hkern u1="&#x175;" u2="v" k="-6" />
    <hkern u1="&#x175;" u2="\" k="16" />
    <hkern u1="&#x175;" u2="&#x3f;" k="-27" />
    <hkern u1="&#x175;" u2="&#x2f;" k="72" />
    <hkern u1="&#x175;" u2="&#x26;" k="14" />
    <hkern u1="&#x176;" u2="&#x2122;" k="-37" />
    <hkern u1="&#x176;" u2="&#xbf;" k="139" />
    <hkern u1="&#x176;" u2="&#xae;" k="35" />
    <hkern u1="&#x176;" u2="&#xa1;" k="41" />
    <hkern u1="&#x176;" u2="x" k="92" />
    <hkern u1="&#x176;" u2="v" k="72" />
    <hkern u1="&#x176;" u2="i" k="12" />
    <hkern u1="&#x176;" u2="&#x40;" k="119" />
    <hkern u1="&#x176;" u2="&#x3f;" k="6" />
    <hkern u1="&#x176;" u2="&#x2f;" k="225" />
    <hkern u1="&#x176;" u2="&#x2a;" k="31" />
    <hkern u1="&#x176;" u2="&#x26;" k="92" />
    <hkern u1="&#x176;" u2="&#x21;" k="10" />
    <hkern u1="&#x177;" u2="&#xbf;" k="41" />
    <hkern u1="&#x177;" u2="&#xae;" k="-35" />
    <hkern u1="&#x177;" u2="&#xa1;" k="10" />
    <hkern u1="&#x177;" u2="\" k="16" />
    <hkern u1="&#x177;" u2="&#x40;" k="10" />
    <hkern u1="&#x177;" u2="&#x3f;" k="-23" />
    <hkern u1="&#x177;" u2="&#x2f;" k="86" />
    <hkern u1="&#x177;" u2="&#x2a;" k="-10" />
    <hkern u1="&#x178;" u2="&#x2122;" k="-37" />
    <hkern u1="&#x178;" u2="&#xbf;" k="139" />
    <hkern u1="&#x178;" u2="&#xae;" k="35" />
    <hkern u1="&#x178;" u2="&#xa1;" k="41" />
    <hkern u1="&#x178;" u2="x" k="92" />
    <hkern u1="&#x178;" u2="v" k="72" />
    <hkern u1="&#x178;" u2="i" k="12" />
    <hkern u1="&#x178;" u2="&#x40;" k="119" />
    <hkern u1="&#x178;" u2="&#x3f;" k="6" />
    <hkern u1="&#x178;" u2="&#x2f;" k="225" />
    <hkern u1="&#x178;" u2="&#x2a;" k="31" />
    <hkern u1="&#x178;" u2="&#x26;" k="92" />
    <hkern u1="&#x178;" u2="&#x21;" k="10" />
    <hkern u1="&#x179;" u2="&#x2122;" k="6" />
    <hkern u1="&#x179;" u2="&#xbf;" k="14" />
    <hkern u1="&#x179;" u2="&#xae;" k="27" />
    <hkern u1="&#x179;" u2="v" k="35" />
    <hkern u1="&#x179;" u2="&#x40;" k="20" />
    <hkern u1="&#x179;" u2="&#x3f;" k="16" />
    <hkern u1="&#x179;" u2="&#x2a;" k="20" />
    <hkern u1="&#x179;" u2="&#x26;" k="20" />
    <hkern u1="&#x17a;" u2="&#x2122;" k="20" />
    <hkern u1="&#x17a;" u2="\" k="51" />
    <hkern u1="&#x17b;" u2="&#x2122;" k="6" />
    <hkern u1="&#x17b;" u2="&#xbf;" k="14" />
    <hkern u1="&#x17b;" u2="&#xae;" k="27" />
    <hkern u1="&#x17b;" u2="v" k="35" />
    <hkern u1="&#x17b;" u2="&#x40;" k="20" />
    <hkern u1="&#x17b;" u2="&#x3f;" k="16" />
    <hkern u1="&#x17b;" u2="&#x2a;" k="20" />
    <hkern u1="&#x17b;" u2="&#x26;" k="20" />
    <hkern u1="&#x17c;" u2="&#x2122;" k="20" />
    <hkern u1="&#x17c;" u2="\" k="51" />
    <hkern u1="&#x17d;" u2="&#x2122;" k="6" />
    <hkern u1="&#x17d;" u2="&#xbf;" k="14" />
    <hkern u1="&#x17d;" u2="&#xae;" k="27" />
    <hkern u1="&#x17d;" u2="v" k="35" />
    <hkern u1="&#x17d;" u2="&#x40;" k="20" />
    <hkern u1="&#x17d;" u2="&#x3f;" k="16" />
    <hkern u1="&#x17d;" u2="&#x2a;" k="20" />
    <hkern u1="&#x17d;" u2="&#x26;" k="20" />
    <hkern u1="&#x17e;" u2="&#x2122;" k="20" />
    <hkern u1="&#x17e;" u2="\" k="51" />
    <hkern u1="&#x1fa;" u2="&#x2122;" k="252" />
    <hkern u1="&#x1fa;" u2="&#xbf;" k="14" />
    <hkern u1="&#x1fa;" u2="&#xae;" k="184" />
    <hkern u1="&#x1fa;" u2="x" k="-20" />
    <hkern u1="&#x1fa;" u2="v" k="82" />
    <hkern u1="&#x1fa;" u2="\" k="154" />
    <hkern u1="&#x1fa;" u2="X" k="-41" />
    <hkern u1="&#x1fa;" u2="V" k="188" />
    <hkern u1="&#x1fa;" u2="&#x40;" k="20" />
    <hkern u1="&#x1fa;" u2="&#x3f;" k="106" />
    <hkern u1="&#x1fa;" u2="&#x2a;" k="236" />
    <hkern u1="&#x1fa;" u2="&#x26;" k="20" />
    <hkern u1="&#x1fb;" u2="&#x2122;" k="129" />
    <hkern u1="&#x1fb;" u2="&#xae;" k="20" />
    <hkern u1="&#x1fb;" u2="v" k="16" />
    <hkern u1="&#x1fb;" u2="\" k="104" />
    <hkern u1="&#x1fb;" u2="&#x3f;" k="37" />
    <hkern u1="&#x1fb;" u2="&#x2a;" k="31" />
    <hkern u1="&#x1fc;" u2="x" k="-10" />
    <hkern u1="&#x1fc;" u2="X" k="-14" />
    <hkern u1="&#x1fd;" u2="&#x2122;" k="88" />
    <hkern u1="&#x1fd;" u2="&#xae;" k="20" />
    <hkern u1="&#x1fd;" u2="x" k="20" />
    <hkern u1="&#x1fd;" u2="v" k="20" />
    <hkern u1="&#x1fd;" u2="\" k="102" />
    <hkern u1="&#x1fd;" u2="&#x3f;" k="25" />
    <hkern u1="&#x1fd;" u2="&#x2f;" k="25" />
    <hkern u1="&#x1fd;" u2="&#x2a;" k="20" />
    <hkern u1="&#x1fe;" u2="&#x2122;" k="51" />
    <hkern u1="&#x1fe;" u2="x" k="31" />
    <hkern u1="&#x1fe;" u2="v" k="6" />
    <hkern u1="&#x1fe;" u2="\" k="94" />
    <hkern u1="&#x1fe;" u2="X" k="47" />
    <hkern u1="&#x1fe;" u2="V" k="51" />
    <hkern u1="&#x1fe;" u2="&#x2f;" k="55" />
    <hkern u1="&#x1fe;" u2="&#x2a;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x2122;" k="82" />
    <hkern u1="&#x1ff;" u2="&#xae;" k="14" />
    <hkern u1="&#x1ff;" u2="x" k="27" />
    <hkern u1="&#x1ff;" u2="v" k="25" />
    <hkern u1="&#x1ff;" u2="\" k="117" />
    <hkern u1="&#x1ff;" u2="&#x3f;" k="25" />
    <hkern u1="&#x1ff;" u2="&#x2f;" k="47" />
    <hkern u1="&#x1ff;" u2="&#x2a;" k="31" />
    <hkern u1="&#x219;" u2="&#x2122;" k="88" />
    <hkern u1="&#x219;" u2="&#xae;" k="10" />
    <hkern u1="&#x219;" u2="x" k="10" />
    <hkern u1="&#x219;" u2="v" k="25" />
    <hkern u1="&#x219;" u2="\" k="113" />
    <hkern u1="&#x219;" u2="&#x3f;" k="20" />
    <hkern u1="&#x219;" u2="&#x2f;" k="45" />
    <hkern u1="&#x219;" u2="&#x2a;" k="41" />
    <hkern u1="&#x1e80;" u2="&#x2122;" k="-27" />
    <hkern u1="&#x1e80;" u2="&#xbf;" k="51" />
    <hkern u1="&#x1e80;" u2="&#xa1;" k="20" />
    <hkern u1="&#x1e80;" u2="x" k="20" />
    <hkern u1="&#x1e80;" u2="v" k="6" />
    <hkern u1="&#x1e80;" u2="\" k="-4" />
    <hkern u1="&#x1e80;" u2="&#x40;" k="35" />
    <hkern u1="&#x1e80;" u2="&#x2f;" k="104" />
    <hkern u1="&#x1e80;" u2="&#x2a;" k="14" />
    <hkern u1="&#x1e80;" u2="&#x26;" k="16" />
    <hkern u1="&#x1e81;" u2="&#xbf;" k="45" />
    <hkern u1="&#x1e81;" u2="&#xae;" k="-35" />
    <hkern u1="&#x1e81;" u2="v" k="-6" />
    <hkern u1="&#x1e81;" u2="\" k="16" />
    <hkern u1="&#x1e81;" u2="&#x3f;" k="-27" />
    <hkern u1="&#x1e81;" u2="&#x2f;" k="72" />
    <hkern u1="&#x1e81;" u2="&#x26;" k="14" />
    <hkern u1="&#x1e82;" u2="&#x2122;" k="-27" />
    <hkern u1="&#x1e82;" u2="&#xbf;" k="51" />
    <hkern u1="&#x1e82;" u2="&#xa1;" k="20" />
    <hkern u1="&#x1e82;" u2="x" k="20" />
    <hkern u1="&#x1e82;" u2="v" k="6" />
    <hkern u1="&#x1e82;" u2="\" k="-4" />
    <hkern u1="&#x1e82;" u2="&#x40;" k="35" />
    <hkern u1="&#x1e82;" u2="&#x2f;" k="104" />
    <hkern u1="&#x1e82;" u2="&#x2a;" k="14" />
    <hkern u1="&#x1e82;" u2="&#x26;" k="16" />
    <hkern u1="&#x1e83;" u2="&#xbf;" k="45" />
    <hkern u1="&#x1e83;" u2="&#xae;" k="-35" />
    <hkern u1="&#x1e83;" u2="v" k="-6" />
    <hkern u1="&#x1e83;" u2="\" k="16" />
    <hkern u1="&#x1e83;" u2="&#x3f;" k="-27" />
    <hkern u1="&#x1e83;" u2="&#x2f;" k="72" />
    <hkern u1="&#x1e83;" u2="&#x26;" k="14" />
    <hkern u1="&#x1e84;" u2="&#x2122;" k="-27" />
    <hkern u1="&#x1e84;" u2="&#xbf;" k="51" />
    <hkern u1="&#x1e84;" u2="&#xa1;" k="20" />
    <hkern u1="&#x1e84;" u2="x" k="20" />
    <hkern u1="&#x1e84;" u2="v" k="6" />
    <hkern u1="&#x1e84;" u2="\" k="-4" />
    <hkern u1="&#x1e84;" u2="&#x40;" k="35" />
    <hkern u1="&#x1e84;" u2="&#x2f;" k="104" />
    <hkern u1="&#x1e84;" u2="&#x2a;" k="14" />
    <hkern u1="&#x1e84;" u2="&#x26;" k="16" />
    <hkern u1="&#x1e85;" u2="&#xbf;" k="45" />
    <hkern u1="&#x1e85;" u2="&#xae;" k="-35" />
    <hkern u1="&#x1e85;" u2="v" k="-6" />
    <hkern u1="&#x1e85;" u2="\" k="16" />
    <hkern u1="&#x1e85;" u2="&#x3f;" k="-27" />
    <hkern u1="&#x1e85;" u2="&#x2f;" k="72" />
    <hkern u1="&#x1e85;" u2="&#x26;" k="14" />
    <hkern u1="&#x1ef2;" u2="&#x2122;" k="-37" />
    <hkern u1="&#x1ef2;" u2="&#xbf;" k="139" />
    <hkern u1="&#x1ef2;" u2="&#xae;" k="35" />
    <hkern u1="&#x1ef2;" u2="&#xa1;" k="41" />
    <hkern u1="&#x1ef2;" u2="x" k="92" />
    <hkern u1="&#x1ef2;" u2="v" k="72" />
    <hkern u1="&#x1ef2;" u2="i" k="12" />
    <hkern u1="&#x1ef2;" u2="&#x40;" k="119" />
    <hkern u1="&#x1ef2;" u2="&#x3f;" k="6" />
    <hkern u1="&#x1ef2;" u2="&#x2f;" k="225" />
    <hkern u1="&#x1ef2;" u2="&#x2a;" k="31" />
    <hkern u1="&#x1ef2;" u2="&#x26;" k="92" />
    <hkern u1="&#x1ef2;" u2="&#x21;" k="10" />
    <hkern u1="&#x1ef3;" u2="&#xbf;" k="41" />
    <hkern u1="&#x1ef3;" u2="&#xae;" k="-35" />
    <hkern u1="&#x1ef3;" u2="&#xa1;" k="10" />
    <hkern u1="&#x1ef3;" u2="\" k="16" />
    <hkern u1="&#x1ef3;" u2="&#x40;" k="10" />
    <hkern u1="&#x1ef3;" u2="&#x3f;" k="-23" />
    <hkern u1="&#x1ef3;" u2="&#x2f;" k="86" />
    <hkern u1="&#x1ef3;" u2="&#x2a;" k="-10" />
    <hkern u1="&#x2013;" u2="&#x2122;" k="35" />
    <hkern u1="&#x2013;" u2="&#xa5;" k="20" />
    <hkern u1="&#x2013;" u2="x" k="102" />
    <hkern u1="&#x2013;" u2="v" k="31" />
    <hkern u1="&#x2013;" u2="\" k="119" />
    <hkern u1="&#x2013;" u2="X" k="133" />
    <hkern u1="&#x2013;" u2="V" k="123" />
    <hkern u1="&#x2013;" u2="&#x3f;" k="45" />
    <hkern u1="&#x2013;" u2="&#x39;" k="25" />
    <hkern u1="&#x2013;" u2="&#x37;" k="76" />
    <hkern u1="&#x2013;" u2="&#x36;" k="6" />
    <hkern u1="&#x2013;" u2="&#x35;" k="16" />
    <hkern u1="&#x2013;" u2="&#x34;" k="10" />
    <hkern u1="&#x2013;" u2="&#x33;" k="16" />
    <hkern u1="&#x2013;" u2="&#x32;" k="39" />
    <hkern u1="&#x2013;" u2="&#x31;" k="41" />
    <hkern u1="&#x2013;" u2="&#x2f;" k="127" />
    <hkern u1="&#x2013;" u2="&#x2a;" k="20" />
    <hkern u1="&#x2014;" u2="&#x2122;" k="35" />
    <hkern u1="&#x2014;" u2="&#xa5;" k="20" />
    <hkern u1="&#x2014;" u2="x" k="102" />
    <hkern u1="&#x2014;" u2="v" k="31" />
    <hkern u1="&#x2014;" u2="\" k="119" />
    <hkern u1="&#x2014;" u2="X" k="133" />
    <hkern u1="&#x2014;" u2="V" k="123" />
    <hkern u1="&#x2014;" u2="&#x3f;" k="45" />
    <hkern u1="&#x2014;" u2="&#x39;" k="25" />
    <hkern u1="&#x2014;" u2="&#x37;" k="76" />
    <hkern u1="&#x2014;" u2="&#x36;" k="6" />
    <hkern u1="&#x2014;" u2="&#x35;" k="16" />
    <hkern u1="&#x2014;" u2="&#x34;" k="10" />
    <hkern u1="&#x2014;" u2="&#x33;" k="16" />
    <hkern u1="&#x2014;" u2="&#x32;" k="39" />
    <hkern u1="&#x2014;" u2="&#x31;" k="41" />
    <hkern u1="&#x2014;" u2="&#x2f;" k="127" />
    <hkern u1="&#x2014;" u2="&#x2a;" k="20" />
    <hkern u1="&#x2018;" u2="&#x20ac;" k="31" />
    <hkern u1="&#x2018;" u2="&#xbf;" k="219" />
    <hkern u1="&#x2018;" u2="&#xae;" k="16" />
    <hkern u1="&#x2018;" u2="&#xa3;" k="96" />
    <hkern u1="&#x2018;" u2="&#xa2;" k="35" />
    <hkern u1="&#x2018;" u2="x" k="14" />
    <hkern u1="&#x2018;" u2="v" k="-10" />
    <hkern u1="&#x2018;" u2="i" k="6" />
    <hkern u1="&#x2018;" u2="X" k="10" />
    <hkern u1="&#x2018;" u2="V" k="-16" />
    <hkern u1="&#x2018;" u2="&#x40;" k="76" />
    <hkern u1="&#x2018;" u2="&#x39;" k="10" />
    <hkern u1="&#x2018;" u2="&#x38;" k="41" />
    <hkern u1="&#x2018;" u2="&#x36;" k="137" />
    <hkern u1="&#x2018;" u2="&#x35;" k="66" />
    <hkern u1="&#x2018;" u2="&#x34;" k="193" />
    <hkern u1="&#x2018;" u2="&#x33;" k="35" />
    <hkern u1="&#x2018;" u2="&#x32;" k="31" />
    <hkern u1="&#x2018;" u2="&#x30;" k="35" />
    <hkern u1="&#x2018;" u2="&#x2f;" k="231" />
    <hkern u1="&#x2018;" u2="&#x2a;" k="6" />
    <hkern u1="&#x2018;" u2="&#x26;" k="78" />
    <hkern u1="&#x2018;" u2="&#x24;" k="20" />
    <hkern u1="&#x2018;" u2="&#x23;" k="35" />
    <hkern u1="&#x2019;" u2="&#x20ac;" k="41" />
    <hkern u1="&#x2019;" u2="&#xbf;" k="246" />
    <hkern u1="&#x2019;" u2="&#xae;" k="16" />
    <hkern u1="&#x2019;" u2="&#xa3;" k="109" />
    <hkern u1="&#x2019;" u2="&#xa2;" k="47" />
    <hkern u1="&#x2019;" u2="x" k="23" />
    <hkern u1="&#x2019;" u2="j" k="4" />
    <hkern u1="&#x2019;" u2="X" k="4" />
    <hkern u1="&#x2019;" u2="V" k="-16" />
    <hkern u1="&#x2019;" u2="&#x40;" k="82" />
    <hkern u1="&#x2019;" u2="&#x39;" k="14" />
    <hkern u1="&#x2019;" u2="&#x38;" k="47" />
    <hkern u1="&#x2019;" u2="&#x36;" k="150" />
    <hkern u1="&#x2019;" u2="&#x35;" k="68" />
    <hkern u1="&#x2019;" u2="&#x34;" k="217" />
    <hkern u1="&#x2019;" u2="&#x33;" k="35" />
    <hkern u1="&#x2019;" u2="&#x32;" k="31" />
    <hkern u1="&#x2019;" u2="&#x30;" k="37" />
    <hkern u1="&#x2019;" u2="&#x2f;" k="238" />
    <hkern u1="&#x2019;" u2="&#x2a;" k="10" />
    <hkern u1="&#x2019;" u2="&#x26;" k="90" />
    <hkern u1="&#x2019;" u2="&#x24;" k="25" />
    <hkern u1="&#x2019;" u2="&#x23;" k="57" />
    <hkern u1="&#x201a;" u2="&#x20ac;" k="20" />
    <hkern u1="&#x201a;" u2="&#xbf;" k="14" />
    <hkern u1="&#x201a;" u2="&#xa5;" k="66" />
    <hkern u1="&#x201a;" u2="&#xa2;" k="53" />
    <hkern u1="&#x201a;" u2="x" k="37" />
    <hkern u1="&#x201a;" u2="v" k="117" />
    <hkern u1="&#x201a;" u2="\" k="211" />
    <hkern u1="&#x201a;" u2="X" k="27" />
    <hkern u1="&#x201a;" u2="V" k="211" />
    <hkern u1="&#x201a;" u2="&#x40;" k="14" />
    <hkern u1="&#x201a;" u2="&#x3f;" k="104" />
    <hkern u1="&#x201a;" u2="&#x39;" k="139" />
    <hkern u1="&#x201a;" u2="&#x38;" k="29" />
    <hkern u1="&#x201a;" u2="&#x37;" k="57" />
    <hkern u1="&#x201a;" u2="&#x36;" k="14" />
    <hkern u1="&#x201a;" u2="&#x35;" k="14" />
    <hkern u1="&#x201a;" u2="&#x34;" k="29" />
    <hkern u1="&#x201a;" u2="&#x31;" k="72" />
    <hkern u1="&#x201a;" u2="&#x30;" k="14" />
    <hkern u1="&#x201a;" u2="&#x2f;" k="6" />
    <hkern u1="&#x201a;" u2="&#x2a;" k="246" />
    <hkern u1="&#x201a;" u2="&#x23;" k="14" />
    <hkern u1="&#x201a;" u2="&#x21;" k="10" />
    <hkern u1="&#x201c;" u2="&#x20ac;" k="31" />
    <hkern u1="&#x201c;" u2="&#xbf;" k="219" />
    <hkern u1="&#x201c;" u2="&#xae;" k="16" />
    <hkern u1="&#x201c;" u2="&#xa3;" k="96" />
    <hkern u1="&#x201c;" u2="&#xa2;" k="35" />
    <hkern u1="&#x201c;" u2="x" k="14" />
    <hkern u1="&#x201c;" u2="v" k="-10" />
    <hkern u1="&#x201c;" u2="i" k="6" />
    <hkern u1="&#x201c;" u2="X" k="10" />
    <hkern u1="&#x201c;" u2="V" k="-16" />
    <hkern u1="&#x201c;" u2="&#x40;" k="76" />
    <hkern u1="&#x201c;" u2="&#x39;" k="10" />
    <hkern u1="&#x201c;" u2="&#x38;" k="41" />
    <hkern u1="&#x201c;" u2="&#x36;" k="137" />
    <hkern u1="&#x201c;" u2="&#x35;" k="66" />
    <hkern u1="&#x201c;" u2="&#x34;" k="193" />
    <hkern u1="&#x201c;" u2="&#x33;" k="35" />
    <hkern u1="&#x201c;" u2="&#x32;" k="31" />
    <hkern u1="&#x201c;" u2="&#x30;" k="35" />
    <hkern u1="&#x201c;" u2="&#x2f;" k="231" />
    <hkern u1="&#x201c;" u2="&#x2a;" k="6" />
    <hkern u1="&#x201c;" u2="&#x26;" k="78" />
    <hkern u1="&#x201c;" u2="&#x24;" k="20" />
    <hkern u1="&#x201c;" u2="&#x23;" k="35" />
    <hkern u1="&#x201d;" u2="&#x20ac;" k="41" />
    <hkern u1="&#x201d;" u2="&#xbf;" k="246" />
    <hkern u1="&#x201d;" u2="&#xae;" k="16" />
    <hkern u1="&#x201d;" u2="&#xa3;" k="109" />
    <hkern u1="&#x201d;" u2="&#xa2;" k="47" />
    <hkern u1="&#x201d;" u2="x" k="23" />
    <hkern u1="&#x201d;" u2="j" k="4" />
    <hkern u1="&#x201d;" u2="X" k="4" />
    <hkern u1="&#x201d;" u2="V" k="-16" />
    <hkern u1="&#x201d;" u2="&#x40;" k="82" />
    <hkern u1="&#x201d;" u2="&#x39;" k="14" />
    <hkern u1="&#x201d;" u2="&#x38;" k="47" />
    <hkern u1="&#x201d;" u2="&#x36;" k="150" />
    <hkern u1="&#x201d;" u2="&#x35;" k="68" />
    <hkern u1="&#x201d;" u2="&#x34;" k="217" />
    <hkern u1="&#x201d;" u2="&#x33;" k="35" />
    <hkern u1="&#x201d;" u2="&#x32;" k="31" />
    <hkern u1="&#x201d;" u2="&#x30;" k="37" />
    <hkern u1="&#x201d;" u2="&#x2f;" k="238" />
    <hkern u1="&#x201d;" u2="&#x2a;" k="10" />
    <hkern u1="&#x201d;" u2="&#x26;" k="90" />
    <hkern u1="&#x201d;" u2="&#x24;" k="25" />
    <hkern u1="&#x201d;" u2="&#x23;" k="57" />
    <hkern u1="&#x201e;" u2="&#x20ac;" k="20" />
    <hkern u1="&#x201e;" u2="&#xbf;" k="14" />
    <hkern u1="&#x201e;" u2="&#xa5;" k="66" />
    <hkern u1="&#x201e;" u2="&#xa2;" k="53" />
    <hkern u1="&#x201e;" u2="x" k="37" />
    <hkern u1="&#x201e;" u2="v" k="117" />
    <hkern u1="&#x201e;" u2="\" k="211" />
    <hkern u1="&#x201e;" u2="X" k="27" />
    <hkern u1="&#x201e;" u2="V" k="211" />
    <hkern u1="&#x201e;" u2="&#x40;" k="14" />
    <hkern u1="&#x201e;" u2="&#x3f;" k="104" />
    <hkern u1="&#x201e;" u2="&#x39;" k="139" />
    <hkern u1="&#x201e;" u2="&#x38;" k="29" />
    <hkern u1="&#x201e;" u2="&#x37;" k="57" />
    <hkern u1="&#x201e;" u2="&#x36;" k="14" />
    <hkern u1="&#x201e;" u2="&#x35;" k="14" />
    <hkern u1="&#x201e;" u2="&#x34;" k="29" />
    <hkern u1="&#x201e;" u2="&#x31;" k="72" />
    <hkern u1="&#x201e;" u2="&#x30;" k="14" />
    <hkern u1="&#x201e;" u2="&#x2f;" k="6" />
    <hkern u1="&#x201e;" u2="&#x2a;" k="246" />
    <hkern u1="&#x201e;" u2="&#x23;" k="14" />
    <hkern u1="&#x201e;" u2="&#x21;" k="10" />
    <hkern u1="&#x2026;" u2="&#x2122;" k="188" />
    <hkern u1="&#x2026;" u2="&#x20ac;" k="47" />
    <hkern u1="&#x2026;" u2="&#xae;" k="129" />
    <hkern u1="&#x2026;" u2="&#xa5;" k="78" />
    <hkern u1="&#x2026;" u2="&#xa2;" k="47" />
    <hkern u1="&#x2026;" u2="x" k="14" />
    <hkern u1="&#x2026;" u2="v" k="158" />
    <hkern u1="&#x2026;" u2="\" k="178" />
    <hkern u1="&#x2026;" u2="X" k="10" />
    <hkern u1="&#x2026;" u2="V" k="276" />
    <hkern u1="&#x2026;" u2="&#x40;" k="16" />
    <hkern u1="&#x2026;" u2="&#x3f;" k="68" />
    <hkern u1="&#x2026;" u2="&#x39;" k="129" />
    <hkern u1="&#x2026;" u2="&#x37;" k="20" />
    <hkern u1="&#x2026;" u2="&#x36;" k="10" />
    <hkern u1="&#x2026;" u2="&#x35;" k="20" />
    <hkern u1="&#x2026;" u2="&#x34;" k="53" />
    <hkern u1="&#x2026;" u2="&#x31;" k="139" />
    <hkern u1="&#x2026;" u2="&#x30;" k="10" />
    <hkern u1="&#x2026;" u2="&#x2a;" k="188" />
    <hkern u1="&#x2026;" u2="&#x26;" k="31" />
    <hkern u1="&#x2030;" u2="&#x2122;" k="125" />
    <hkern u1="&#x2030;" u2="&#xbf;" k="-27" />
    <hkern u1="&#x2030;" u2="&#xae;" k="47" />
    <hkern u1="&#x2030;" u2="\" k="129" />
    <hkern u1="&#x2030;" u2="&#x3f;" k="27" />
    <hkern u1="&#x2030;" u2="&#x39;" k="14" />
    <hkern u1="&#x2030;" u2="&#x38;" k="-6" />
    <hkern u1="&#x2030;" u2="&#x37;" k="-6" />
    <hkern u1="&#x2030;" u2="&#x36;" k="-12" />
    <hkern u1="&#x2030;" u2="&#x35;" k="-12" />
    <hkern u1="&#x2030;" u2="&#x34;" k="-12" />
    <hkern u1="&#x2030;" u2="&#x33;" k="-12" />
    <hkern u1="&#x2030;" u2="&#x32;" k="-12" />
    <hkern u1="&#x2030;" u2="&#x31;" k="6" />
    <hkern u1="&#x2030;" u2="&#x30;" k="-16" />
    <hkern u1="&#x2030;" u2="&#x2f;" k="14" />
    <hkern u1="&#x2030;" u2="&#x2a;" k="86" />
    <hkern u1="&#x2039;" u2="&#x20ac;" k="20" />
    <hkern u1="&#x2039;" u2="&#xa5;" k="35" />
    <hkern u1="&#x2039;" u2="&#xa2;" k="10" />
    <hkern u1="&#x2039;" u2="j" k="10" />
    <hkern u1="&#x2039;" u2="\" k="72" />
    <hkern u1="&#x2039;" u2="X" k="6" />
    <hkern u1="&#x2039;" u2="V" k="82" />
    <hkern u1="&#x2039;" u2="&#x39;" k="4" />
    <hkern u1="&#x2039;" u2="&#x38;" k="20" />
    <hkern u1="&#x2039;" u2="&#x37;" k="14" />
    <hkern u1="&#x2039;" u2="&#x36;" k="16" />
    <hkern u1="&#x2039;" u2="&#x35;" k="20" />
    <hkern u1="&#x2039;" u2="&#x34;" k="14" />
    <hkern u1="&#x2039;" u2="&#x33;" k="10" />
    <hkern u1="&#x2039;" u2="&#x2f;" k="25" />
    <hkern u1="&#x2039;" u2="&#x2a;" k="14" />
    <hkern u1="&#x2039;" u2="&#x26;" k="6" />
    <hkern u1="&#x2039;" u2="&#x23;" k="14" />
    <hkern u1="&#x203a;" u2="&#xa5;" k="45" />
    <hkern u1="&#x203a;" u2="x" k="47" />
    <hkern u1="&#x203a;" u2="v" k="10" />
    <hkern u1="&#x203a;" u2="\" k="109" />
    <hkern u1="&#x203a;" u2="X" k="53" />
    <hkern u1="&#x203a;" u2="V" k="109" />
    <hkern u1="&#x203a;" u2="&#x3f;" k="68" />
    <hkern u1="&#x203a;" u2="&#x39;" k="35" />
    <hkern u1="&#x203a;" u2="&#x37;" k="82" />
    <hkern u1="&#x203a;" u2="&#x36;" k="-6" />
    <hkern u1="&#x203a;" u2="&#x34;" k="-12" />
    <hkern u1="&#x203a;" u2="&#x31;" k="41" />
    <hkern u1="&#x203a;" u2="&#x2f;" k="61" />
    <hkern u1="&#x203a;" u2="&#x2a;" k="76" />
    <hkern u1="&#x203a;" u2="&#x23;" k="-27" />
    <hkern u1="&#x203a;" u2="&#x21;" k="6" />
    <hkern u1="&#x20ac;" u2="&#x203a;" k="6" />
    <hkern u1="&#x20ac;" u2="&#x201e;" k="10" />
    <hkern u1="&#x20ac;" u2="&#x201a;" k="10" />
    <hkern u1="&#x20ac;" u2="&#x2014;" k="10" />
    <hkern u1="&#x20ac;" u2="&#x2013;" k="10" />
    <hkern u1="&#x20ac;" u2="&#xbb;" k="6" />
    <hkern u1="&#x20ac;" u2="&#x7d;" k="16" />
    <hkern u1="&#x20ac;" u2="]" k="16" />
    <hkern u1="&#x20ac;" u2="&#x2d;" k="10" />
    <hkern u1="&#x20ac;" u2="&#x2c;" k="10" />
    <hkern u1="&#x20ac;" u2="&#x29;" k="16" />
    <hkern u1="&#x20ac;" u2="\" k="27" />
    <hkern u1="&#x20ac;" u2="&#x37;" k="20" />
    <hkern u1="&#x20ac;" u2="&#x31;" k="-20" />
    <hkern u1="&#x20ac;" u2="&#x2f;" k="57" />
    <hkern u1="&#x2117;" u2="&#xa5;" k="20" />
    <hkern u1="&#x2117;" u2="x" k="20" />
    <hkern u1="&#x2117;" u2="\" k="82" />
    <hkern u1="&#x2117;" u2="X" k="33" />
    <hkern u1="&#x2117;" u2="V" k="41" />
    <hkern u1="&#x2117;" u2="&#x3f;" k="25" />
    <hkern u1="&#x2117;" u2="&#x37;" k="20" />
    <hkern u1="&#x2117;" u2="&#x2f;" k="82" />
    <hkern u1="&#x2122;" g2="Jcircumflex.salt" k="55" />
    <hkern u1="&#x2122;" g2="J.salt" k="55" />
    <hkern u1="&#x2122;" u2="&#xfb02;" k="-35" />
    <hkern u1="&#x2122;" u2="&#xfb01;" k="-35" />
    <hkern u1="&#x2122;" u2="&#x2030;" k="-35" />
    <hkern u1="&#x2122;" u2="&#x2026;" k="47" />
    <hkern u1="&#x2122;" u2="&#x1ef3;" k="-35" />
    <hkern u1="&#x2122;" u2="&#x1ef2;" k="-14" />
    <hkern u1="&#x2122;" u2="&#x1e85;" k="-35" />
    <hkern u1="&#x2122;" u2="&#x1e84;" k="-20" />
    <hkern u1="&#x2122;" u2="&#x1e83;" k="-35" />
    <hkern u1="&#x2122;" u2="&#x1e82;" k="-20" />
    <hkern u1="&#x2122;" u2="&#x1e81;" k="-35" />
    <hkern u1="&#x2122;" u2="&#x1e80;" k="-20" />
    <hkern u1="&#x2122;" u2="&#x1fd;" k="35" />
    <hkern u1="&#x2122;" u2="&#x1fb;" k="35" />
    <hkern u1="&#x2122;" u2="&#x1fa;" k="174" />
    <hkern u1="&#x2122;" u2="&#x17d;" k="14" />
    <hkern u1="&#x2122;" u2="&#x17b;" k="14" />
    <hkern u1="&#x2122;" u2="&#x179;" k="14" />
    <hkern u1="&#x2122;" u2="&#x178;" k="-14" />
    <hkern u1="&#x2122;" u2="&#x177;" k="-35" />
    <hkern u1="&#x2122;" u2="&#x176;" k="-14" />
    <hkern u1="&#x2122;" u2="&#x175;" k="-35" />
    <hkern u1="&#x2122;" u2="&#x174;" k="-20" />
    <hkern u1="&#x2122;" u2="&#x164;" k="-14" />
    <hkern u1="&#x2122;" u2="&#x162;" k="-14" />
    <hkern u1="&#x2122;" u2="&#x134;" k="326" />
    <hkern u1="&#x2122;" u2="&#x105;" k="35" />
    <hkern u1="&#x2122;" u2="&#x104;" k="174" />
    <hkern u1="&#x2122;" u2="&#x103;" k="35" />
    <hkern u1="&#x2122;" u2="&#x102;" k="174" />
    <hkern u1="&#x2122;" u2="&#x101;" k="35" />
    <hkern u1="&#x2122;" u2="&#x100;" k="174" />
    <hkern u1="&#x2122;" u2="&#xff;" k="-35" />
    <hkern u1="&#x2122;" u2="&#xfd;" k="-35" />
    <hkern u1="&#x2122;" u2="&#xe6;" k="35" />
    <hkern u1="&#x2122;" u2="&#xe5;" k="35" />
    <hkern u1="&#x2122;" u2="&#xe4;" k="35" />
    <hkern u1="&#x2122;" u2="&#xe3;" k="35" />
    <hkern u1="&#x2122;" u2="&#xe2;" k="35" />
    <hkern u1="&#x2122;" u2="&#xe1;" k="35" />
    <hkern u1="&#x2122;" u2="&#xe0;" k="35" />
    <hkern u1="&#x2122;" u2="&#xdd;" k="-14" />
    <hkern u1="&#x2122;" u2="&#xc5;" k="174" />
    <hkern u1="&#x2122;" u2="&#xc4;" k="174" />
    <hkern u1="&#x2122;" u2="&#xc3;" k="174" />
    <hkern u1="&#x2122;" u2="&#xc2;" k="174" />
    <hkern u1="&#x2122;" u2="&#xc1;" k="174" />
    <hkern u1="&#x2122;" u2="&#xc0;" k="174" />
    <hkern u1="&#x2122;" u2="y" k="-35" />
    <hkern u1="&#x2122;" u2="w" k="-35" />
    <hkern u1="&#x2122;" u2="f" k="-35" />
    <hkern u1="&#x2122;" u2="a" k="35" />
    <hkern u1="&#x2122;" u2="Z" k="14" />
    <hkern u1="&#x2122;" u2="Y" k="-14" />
    <hkern u1="&#x2122;" u2="W" k="-20" />
    <hkern u1="&#x2122;" u2="T" k="-14" />
    <hkern u1="&#x2122;" u2="J" k="326" />
    <hkern u1="&#x2122;" u2="A" k="174" />
    <hkern u1="&#x2122;" u2="&#x2e;" k="47" />
    <hkern u1="&#x2122;" u2="&#x25;" k="-35" />
    <hkern u1="&#x2122;" u2="x" k="-35" />
    <hkern u1="&#x2122;" u2="v" k="-55" />
    <hkern u1="&#x2122;" u2="V" k="-20" />
    <hkern u1="&#x2122;" u2="&#x40;" k="20" />
    <hkern u1="&#x2122;" u2="&#x36;" k="35" />
    <hkern u1="&#x2122;" u2="&#x35;" k="20" />
    <hkern u1="&#x2122;" u2="&#x34;" k="61" />
    <hkern u1="&#x2122;" u2="&#x31;" k="-35" />
    <hkern u1="&#x2122;" u2="&#x2f;" k="186" />
    <hkern g1="J.salt" u2="&#x2f;" k="35" />
    <hkern g1="IJ.salt" u2="&#x2f;" k="35" />
    <hkern g1="Jcircumflex.salt" u2="&#x2f;" k="35" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="parenright,bracketright,braceright"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="ordfeminine,ordmasculine"
	k="63" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="hyphen,endash,emdash"
	k="92" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="comma,quotesinglbase,quotedblbase"
	k="-10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="J.salt,Jcircumflex.salt"
	k="6" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="napostrophe,quoteright,quotedblright"
	k="266" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="copyright,published"
	k="51" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="quoteleft,quotedblleft"
	k="256" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="guillemotright,guilsinglright"
	k="16" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="61" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="66" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="T,Tcommaaccent,Tcaron"
	k="147" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="115" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="195" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="16" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="f,fi,fl"
	k="31" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="guillemotleft,guilsinglleft"
	k="37" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="4" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="31" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="t,tcommaaccent"
	k="61" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="53" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="82" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="parenright,bracketright,braceright"
	k="25" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="ordfeminine,ordmasculine"
	k="-20" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="hyphen,endash,emdash"
	k="4" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="comma,quotesinglbase,quotedblbase"
	k="41" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="J.salt,Jcircumflex.salt"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="napostrophe,quoteright,quotedblright"
	k="-10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="55" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="quoteleft,quotedblleft"
	k="-10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="guillemotright,guilsinglright"
	k="4" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="T,Tcommaaccent,Tcaron"
	k="41" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="14" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="51" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="6" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="period,ellipsis"
	k="45" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="J,Jcircumflex"
	k="6" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,AEacute"
	g2="parenright,bracketright,braceright"
	k="-4" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,AEacute"
	g2="hyphen,endash,emdash"
	k="6" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,AEacute"
	g2="comma,quotesinglbase,quotedblbase"
	k="-8" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,AEacute"
	g2="J.salt,Jcircumflex.salt"
	k="6" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,AEacute"
	g2="napostrophe,quoteright,quotedblright"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,AEacute"
	g2="quoteleft,quotedblleft"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,AEacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="16" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,AEacute"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="14" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,AEacute"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="6" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,AEacute"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,AEacute"
	g2="J,Jcircumflex"
	k="6" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="parenright,bracketright,braceright"
	k="25" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="comma,quotesinglbase,quotedblbase"
	k="35" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="napostrophe,quoteright,quotedblright"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="57" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="quoteleft,quotedblleft"
	k="6" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="guillemotright,guilsinglright"
	k="4" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="T,Tcommaaccent,Tcaron"
	k="41" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="61" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="4" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="period,ellipsis"
	k="51" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="14" />
    <hkern g1="J,IJ,Jcircumflex,J.salt,IJ.salt,Jcircumflex.salt"
	g2="comma,quotesinglbase,quotedblbase"
	k="41" />
    <hkern g1="J,IJ,Jcircumflex,J.salt,IJ.salt,Jcircumflex.salt"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="61" />
    <hkern g1="J,IJ,Jcircumflex,J.salt,IJ.salt,Jcircumflex.salt"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="8" />
    <hkern g1="J,IJ,Jcircumflex,J.salt,IJ.salt,Jcircumflex.salt"
	g2="period,ellipsis"
	k="31" />
    <hkern g1="J,IJ,Jcircumflex,J.salt,IJ.salt,Jcircumflex.salt"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="K,Kcommaaccent"
	g2="ordfeminine,ordmasculine"
	k="20" />
    <hkern g1="K,Kcommaaccent"
	g2="hyphen,endash,emdash"
	k="164" />
    <hkern g1="K,Kcommaaccent"
	g2="comma,quotesinglbase,quotedblbase"
	k="-20" />
    <hkern g1="K,Kcommaaccent"
	g2="J.salt,Jcircumflex.salt"
	k="25" />
    <hkern g1="K,Kcommaaccent"
	g2="napostrophe,quoteright,quotedblright"
	k="41" />
    <hkern g1="K,Kcommaaccent"
	g2="copyright,published"
	k="61" />
    <hkern g1="K,Kcommaaccent"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-31" />
    <hkern g1="K,Kcommaaccent"
	g2="quoteleft,quotedblleft"
	k="51" />
    <hkern g1="K,Kcommaaccent"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="K,Kcommaaccent"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="68" />
    <hkern g1="K,Kcommaaccent"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="27" />
    <hkern g1="K,Kcommaaccent"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="16" />
    <hkern g1="K,Kcommaaccent"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="27" />
    <hkern g1="K,Kcommaaccent"
	g2="f,fi,fl"
	k="20" />
    <hkern g1="K,Kcommaaccent"
	g2="guillemotleft,guilsinglleft"
	k="76" />
    <hkern g1="K,Kcommaaccent"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="51" />
    <hkern g1="K,Kcommaaccent"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="23" />
    <hkern g1="K,Kcommaaccent"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="45" />
    <hkern g1="K,Kcommaaccent"
	g2="t,tcommaaccent"
	k="37" />
    <hkern g1="K,Kcommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="51" />
    <hkern g1="K,Kcommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="66" />
    <hkern g1="K,Kcommaaccent"
	g2="period,ellipsis"
	k="10" />
    <hkern g1="K,Kcommaaccent"
	g2="colon,semicolon"
	k="14" />
    <hkern g1="K,Kcommaaccent"
	g2="J,Jcircumflex"
	k="25" />
    <hkern g1="K,Kcommaaccent"
	g2="z,zacute,zdotaccent,zcaron"
	k="-20" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="parenright,bracketright,braceright"
	k="10" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="ordfeminine,ordmasculine"
	k="236" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="hyphen,endash,emdash"
	k="256" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="comma,quotesinglbase,quotedblbase"
	k="-16" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="J.salt,Jcircumflex.salt"
	k="20" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="napostrophe,quoteright,quotedblright"
	k="457" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="copyright,published"
	k="76" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-25" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="quoteleft,quotedblleft"
	k="455" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="guillemotright,guilsinglright"
	k="6" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="80" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="39" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="86" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="T,Tcommaaccent,Tcaron"
	k="195" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="119" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="236" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="f,fi,fl"
	k="41" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="guillemotleft,guilsinglleft"
	k="55" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="39" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="20" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="57" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="t,tcommaaccent"
	k="84" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="90" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="131" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="J,Jcircumflex"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="parenright,bracketright,braceright"
	k="37" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="comma,quotesinglbase,quotedblbase"
	k="55" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="J.salt,Jcircumflex.salt"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="napostrophe,quoteright,quotedblright"
	k="14" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="61" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="quoteleft,quotedblleft"
	k="14" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="T,Tcommaaccent,Tcaron"
	k="80" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="29" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="84" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="f,fi,fl"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="6" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="t,tcommaaccent"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="4" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="period,ellipsis"
	k="74" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="J,Jcircumflex"
	k="6" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="16" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="parenright,bracketright,braceright"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="ordfeminine,ordmasculine"
	k="6" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="hyphen,endash,emdash"
	k="49" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="J.salt,Jcircumflex.salt"
	k="14" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="napostrophe,quoteright,quotedblright"
	k="25" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="quoteleft,quotedblleft"
	k="14" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="6" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="T,Tcommaaccent,Tcaron"
	k="41" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="16" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="61" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="20" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="guillemotleft,guilsinglleft"
	k="20" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="16" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="8" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="period,ellipsis"
	k="45" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="J,Jcircumflex"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="parenright,bracketright,braceright"
	k="35" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="hyphen,endash,emdash"
	k="6" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="comma,quotesinglbase,quotedblbase"
	k="25" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="napostrophe,quoteright,quotedblright"
	k="31" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="47" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="quoteleft,quotedblleft"
	k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="guillemotright,guilsinglright"
	k="14" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="T,Tcommaaccent,Tcaron"
	k="61" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="43" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="66" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="16" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="colon,semicolon"
	k="18" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="hyphen,endash,emdash"
	k="221" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="comma,quotesinglbase,quotedblbase"
	k="231" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="J.salt,Jcircumflex.salt"
	k="55" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="copyright,published"
	k="41" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="147" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="quoteleft,quotedblleft"
	k="4" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="guillemotright,guilsinglright"
	k="123" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="80" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="23" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-25" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-25" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="209" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="f,fi,fl"
	k="59" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="guillemotleft,guilsinglleft"
	k="164" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="184" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="174" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="154" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="t,tcommaaccent"
	k="53" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="119" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="127" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="period,ellipsis"
	k="266" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="colon,semicolon"
	k="195" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="J,Jcircumflex"
	k="188" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="145" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="m,n,p,r,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="123" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="parenright,bracketright,braceright"
	k="10" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="comma,quotesinglbase,quotedblbase"
	k="41" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="66" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="6" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="period,ellipsis"
	k="92" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="ordfeminine,ordmasculine"
	k="-10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen,endash,emdash"
	k="72" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="comma,quotesinglbase,quotedblbase"
	k="115" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="J.salt,Jcircumflex.salt"
	k="41" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="napostrophe,quoteright,quotedblright"
	k="6" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="copyright,published"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="115" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="quoteleft,quotedblleft"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guillemotright,guilsinglright"
	k="41" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="29" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="T,Tcommaaccent,Tcaron"
	k="-25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-14" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="94" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="f,fi,fl"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guillemotleft,guilsinglleft"
	k="51" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="57" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="49" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="35" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="t,tcommaaccent"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="4" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="period,ellipsis"
	k="180" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="colon,semicolon"
	k="82" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="J,Jcircumflex"
	k="102" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="z,zacute,zdotaccent,zcaron"
	k="25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="m,n,p,r,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="25" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="parenright,bracketright,braceright"
	k="-10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="ordfeminine,ordmasculine"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="hyphen,endash,emdash"
	k="195" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="comma,quotesinglbase,quotedblbase"
	k="258" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="J.salt,Jcircumflex.salt"
	k="72" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="napostrophe,quoteright,quotedblright"
	k="12" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="copyright,published"
	k="35" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="195" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="quoteleft,quotedblleft"
	k="18" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guillemotright,guilsinglright"
	k="113" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="84" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="59" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="T,Tcommaaccent,Tcaron"
	k="-25" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-14" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-14" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="190" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="f,fi,fl"
	k="59" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guillemotleft,guilsinglleft"
	k="147" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="160" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="143" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="96" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="t,tcommaaccent"
	k="47" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="78" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="72" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="period,ellipsis"
	k="279" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="colon,semicolon"
	k="195" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="J,Jcircumflex"
	k="223" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="31" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="z,zacute,zdotaccent,zcaron"
	k="88" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="m,n,p,r,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="92" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="hyphen,endash,emdash"
	k="80" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="napostrophe,quoteright,quotedblright"
	k="27" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="6" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="quoteleft,quotedblleft"
	k="25" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="63" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="14" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="T,Tcommaaccent,Tcaron"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="f,fi,fl"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="35" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="33" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="t,tcommaaccent"
	k="25" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="25" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="35" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="J,Jcircumflex"
	k="35" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute"
	g2="parenright,bracketright,braceright"
	k="25" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute"
	g2="napostrophe,quoteright,quotedblright"
	k="59" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute"
	g2="quoteleft,quotedblleft"
	k="49" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute"
	g2="f,fi,fl"
	k="6" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute"
	g2="t,tcommaaccent"
	k="14" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="6" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="23" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="parenright,bracketright,braceright"
	k="31" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="comma,quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="napostrophe,quoteright,quotedblright"
	k="14" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="quoteleft,quotedblleft"
	k="20" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="guillemotright,guilsinglright"
	k="4" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="f,fi,fl"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="t,tcommaaccent"
	k="8" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="6" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="16" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="period,ellipsis"
	k="14" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="parenright,bracketright,braceright"
	k="31" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="ordfeminine,ordmasculine"
	k="-14" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="comma,quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="napostrophe,quoteright,quotedblright"
	k="25" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="quoteleft,quotedblleft"
	k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="guillemotright,guilsinglright"
	k="4" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="6" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="f,fi,fl"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="t,tcommaaccent"
	k="6" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="18" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="period,ellipsis"
	k="14" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="parenright,bracketright,braceright"
	k="31" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="ordfeminine,ordmasculine"
	k="20" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="hyphen,endash,emdash"
	k="92" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="comma,quotesinglbase,quotedblbase"
	k="-10" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="copyright,published"
	k="10" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="51" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="f,fi,fl"
	k="20" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="guillemotleft,guilsinglleft"
	k="37" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="43" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="27" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="23" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="t,tcommaaccent"
	k="16" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="period,ellipsis"
	k="10" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="parenright,bracketright,braceright"
	k="10" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="napostrophe,quoteright,quotedblright"
	k="29" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="quoteleft,quotedblleft"
	k="20" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="T,Tcommaaccent,Tcaron"
	k="106" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="t,tcommaaccent"
	k="10" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="4" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="parenright,bracketright,braceright"
	k="35" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="comma,quotesinglbase,quotedblbase"
	k="16" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="napostrophe,quoteright,quotedblright"
	k="37" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="quoteleft,quotedblleft"
	k="33" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="f,fi,fl"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="t,tcommaaccent"
	k="20" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="16" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="14" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="period,ellipsis"
	k="41" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="parenright,bracketright,braceright"
	k="25" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="ordfeminine,ordmasculine"
	k="-20" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="hyphen,endash,emdash"
	k="20" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="comma,quotesinglbase,quotedblbase"
	k="113" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="napostrophe,quoteright,quotedblright"
	k="-37" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="copyright,published"
	k="-14" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="quoteleft,quotedblleft"
	k="-37" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="61" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="guillemotleft,guilsinglleft"
	k="14" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="16" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="10" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-39" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-45" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="period,ellipsis"
	k="152" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="colon,semicolon"
	k="25" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="parenright,bracketright,braceright"
	k="35" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="napostrophe,quoteright,quotedblright"
	k="27" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="quoteleft,quotedblleft"
	k="20" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="f,fi,fl"
	k="20" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="t,tcommaaccent"
	k="23" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="23" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="25" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="t,tcommaaccent"
	g2="parenright,bracketright,braceright"
	k="10" />
    <hkern g1="t,tcommaaccent"
	g2="ordfeminine,ordmasculine"
	k="-20" />
    <hkern g1="t,tcommaaccent"
	g2="hyphen,endash,emdash"
	k="61" />
    <hkern g1="t,tcommaaccent"
	g2="quoteleft,quotedblleft"
	k="-10" />
    <hkern g1="t,tcommaaccent"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="t,tcommaaccent"
	g2="guillemotleft,guilsinglleft"
	k="12" />
    <hkern g1="t,tcommaaccent"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="10" />
    <hkern g1="t,tcommaaccent"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="6" />
    <hkern g1="t,tcommaaccent"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="t,tcommaaccent"
	g2="t,tcommaaccent"
	k="14" />
    <hkern g1="t,tcommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-10" />
    <hkern g1="t,tcommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-12" />
    <hkern g1="t,tcommaaccent"
	g2="period,ellipsis"
	k="10" />
    <hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	g2="parenright,bracketright,braceright"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="parenright,bracketright,braceright"
	k="14" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="ordfeminine,ordmasculine"
	k="-31" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="hyphen,endash,emdash"
	k="25" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="comma,quotesinglbase,quotedblbase"
	k="86" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="napostrophe,quoteright,quotedblright"
	k="-37" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="quoteleft,quotedblleft"
	k="-41" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="41" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="guillemotleft,guilsinglleft"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="16" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-16" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="period,ellipsis"
	k="117" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="colon,semicolon"
	k="37" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="parenright,bracketright,braceright"
	k="10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="ordfeminine,ordmasculine"
	k="-20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="hyphen,endash,emdash"
	k="39" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="comma,quotesinglbase,quotedblbase"
	k="129" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="napostrophe,quoteright,quotedblright"
	k="-45" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="quoteleft,quotedblleft"
	k="-45" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="72" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="guillemotleft,guilsinglleft"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="23" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="t,tcommaaccent"
	k="-10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="period,ellipsis"
	k="152" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="colon,semicolon"
	k="41" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="z,zacute,zdotaccent,zcaron"
	k="12" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="hyphen,endash,emdash"
	k="20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="comma,quotesinglbase,quotedblbase"
	k="-10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="18" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="18" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="period,ellipsis"
	g2="parenright,bracketright,braceright"
	k="31" />
    <hkern g1="period,ellipsis"
	g2="ordfeminine,ordmasculine"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="hyphen,endash,emdash"
	k="57" />
    <hkern g1="period,ellipsis"
	g2="J.salt,Jcircumflex.salt"
	k="25" />
    <hkern g1="period,ellipsis"
	g2="napostrophe,quoteright,quotedblright"
	k="160" />
    <hkern g1="period,ellipsis"
	g2="copyright,published"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="quoteleft,quotedblleft"
	k="147" />
    <hkern g1="period,ellipsis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="74" />
    <hkern g1="period,ellipsis"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="25" />
    <hkern g1="period,ellipsis"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="92" />
    <hkern g1="period,ellipsis"
	g2="T,Tcommaaccent,Tcaron"
	k="266" />
    <hkern g1="period,ellipsis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="180" />
    <hkern g1="period,ellipsis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="279" />
    <hkern g1="period,ellipsis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="6" />
    <hkern g1="period,ellipsis"
	g2="f,fi,fl"
	k="72" />
    <hkern g1="period,ellipsis"
	g2="guillemotleft,guilsinglleft"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="41" />
    <hkern g1="period,ellipsis"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="27" />
    <hkern g1="period,ellipsis"
	g2="t,tcommaaccent"
	k="57" />
    <hkern g1="period,ellipsis"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="117" />
    <hkern g1="period,ellipsis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="154" />
    <hkern g1="period,ellipsis"
	g2="J,Jcircumflex"
	k="10" />
    <hkern g1="period,ellipsis"
	g2="parenleft,bracketleft,braceleft"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="percent,perthousand"
	k="137" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="parenright,bracketright,braceright"
	k="33" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="hyphen,endash,emdash"
	k="35" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="napostrophe,quoteright,quotedblright"
	k="141" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="quoteleft,quotedblleft"
	k="147" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="guillemotright,guilsinglright"
	k="4" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="59" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="10" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="55" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="T,Tcommaaccent,Tcaron"
	k="240" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="143" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="254" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="f,fi,fl"
	k="72" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="guillemotleft,guilsinglleft"
	k="20" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="31" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="20" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="31" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="t,tcommaaccent"
	k="72" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="92" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="147" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="parenleft,bracketleft,braceleft"
	k="16" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="percent,perthousand"
	k="129" />
    <hkern g1="colon,semicolon"
	g2="parenright,bracketright,braceright"
	k="47" />
    <hkern g1="colon,semicolon"
	g2="J.salt,Jcircumflex.salt"
	k="10" />
    <hkern g1="colon,semicolon"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="10" />
    <hkern g1="colon,semicolon"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="10" />
    <hkern g1="colon,semicolon"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="colon,semicolon"
	g2="T,Tcommaaccent,Tcaron"
	k="195" />
    <hkern g1="colon,semicolon"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="82" />
    <hkern g1="colon,semicolon"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="195" />
    <hkern g1="colon,semicolon"
	g2="f,fi,fl"
	k="14" />
    <hkern g1="colon,semicolon"
	g2="t,tcommaaccent"
	k="6" />
    <hkern g1="colon,semicolon"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="37" />
    <hkern g1="colon,semicolon"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="35" />
    <hkern g1="hyphen,endash,emdash"
	g2="parenright,bracketright,braceright"
	k="106" />
    <hkern g1="hyphen,endash,emdash"
	g2="comma,quotesinglbase,quotedblbase"
	k="14" />
    <hkern g1="hyphen,endash,emdash"
	g2="J.salt,Jcircumflex.salt"
	k="29" />
    <hkern g1="hyphen,endash,emdash"
	g2="napostrophe,quoteright,quotedblright"
	k="14" />
    <hkern g1="hyphen,endash,emdash"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="92" />
    <hkern g1="hyphen,endash,emdash"
	g2="quoteleft,quotedblleft"
	k="14" />
    <hkern g1="hyphen,endash,emdash"
	g2="guillemotright,guilsinglright"
	k="47" />
    <hkern g1="hyphen,endash,emdash"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="6" />
    <hkern g1="hyphen,endash,emdash"
	g2="T,Tcommaaccent,Tcaron"
	k="221" />
    <hkern g1="hyphen,endash,emdash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="72" />
    <hkern g1="hyphen,endash,emdash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="195" />
    <hkern g1="hyphen,endash,emdash"
	g2="f,fi,fl"
	k="29" />
    <hkern g1="hyphen,endash,emdash"
	g2="guillemotleft,guilsinglleft"
	k="-20" />
    <hkern g1="hyphen,endash,emdash"
	g2="t,tcommaaccent"
	k="35" />
    <hkern g1="hyphen,endash,emdash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="25" />
    <hkern g1="hyphen,endash,emdash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="31" />
    <hkern g1="hyphen,endash,emdash"
	g2="period,ellipsis"
	k="57" />
    <hkern g1="hyphen,endash,emdash"
	g2="J,Jcircumflex"
	k="43" />
    <hkern g1="hyphen,endash,emdash"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="16" />
    <hkern g1="hyphen,endash,emdash"
	g2="z,zacute,zdotaccent,zcaron"
	k="20" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="parenright,bracketright,braceright"
	k="31" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="hyphen,endash,emdash"
	k="47" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="comma,quotesinglbase,quotedblbase"
	k="4" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="J.salt,Jcircumflex.salt"
	k="16" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="16" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="T,Tcommaaccent,Tcaron"
	k="123" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="41" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="113" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="6" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="J,Jcircumflex"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="parenleft,bracketleft,braceleft"
	k="10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="parenright,bracketright,braceright"
	k="59" />
    <hkern g1="guillemotright,guilsinglright"
	g2="hyphen,endash,emdash"
	k="-20" />
    <hkern g1="guillemotright,guilsinglright"
	g2="comma,quotesinglbase,quotedblbase"
	k="20" />
    <hkern g1="guillemotright,guilsinglright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="37" />
    <hkern g1="guillemotright,guilsinglright"
	g2="T,Tcommaaccent,Tcaron"
	k="164" />
    <hkern g1="guillemotright,guilsinglright"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="51" />
    <hkern g1="guillemotright,guilsinglright"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="147" />
    <hkern g1="guillemotright,guilsinglright"
	g2="f,fi,fl"
	k="14" />
    <hkern g1="guillemotright,guilsinglright"
	g2="t,tcommaaccent"
	k="6" />
    <hkern g1="guillemotright,guilsinglright"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="14" />
    <hkern g1="guillemotright,guilsinglright"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="guillemotright,guilsinglright"
	g2="percent,perthousand"
	k="14" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="ordfeminine,ordmasculine"
	k="35" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="hyphen,endash,emdash"
	k="106" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="comma,quotesinglbase,quotedblbase"
	k="-20" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="J.salt,Jcircumflex.salt"
	k="47" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="napostrophe,quoteright,quotedblright"
	k="25" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="copyright,published"
	k="55" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="quoteleft,quotedblleft"
	k="27" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="guillemotright,guilsinglright"
	k="31" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="37" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="35" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-10" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="57" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="f,fi,fl"
	k="31" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="guillemotleft,guilsinglleft"
	k="59" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="35" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="35" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="25" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="t,tcommaaccent"
	k="20" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="14" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="14" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="period,ellipsis"
	k="31" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="colon,semicolon"
	k="47" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="J,Jcircumflex"
	k="47" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="z,zacute,zdotaccent,zcaron"
	k="8" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="m,n,p,r,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="20" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="percent,perthousand"
	k="20" />
    <hkern g1="parenright,bracketright,braceright"
	g2="comma,quotesinglbase,quotedblbase"
	k="20" />
    <hkern g1="parenright,bracketright,braceright"
	g2="napostrophe,quoteright,quotedblright"
	k="16" />
    <hkern g1="parenright,bracketright,braceright"
	g2="quoteleft,quotedblleft"
	k="6" />
    <hkern g1="parenright,bracketright,braceright"
	g2="guillemotright,guilsinglright"
	k="35" />
    <hkern g1="parenright,bracketright,braceright"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="parenright,bracketright,braceright"
	g2="colon,semicolon"
	k="4" />
    <hkern g1="percent,perthousand"
	g2="parenright,bracketright,braceright"
	k="25" />
    <hkern g1="percent,perthousand"
	g2="napostrophe,quoteright,quotedblright"
	k="137" />
    <hkern g1="percent,perthousand"
	g2="quoteleft,quotedblleft"
	k="133" />
    <hkern g1="copyright,published"
	g2="parenright,bracketright,braceright"
	k="55" />
    <hkern g1="copyright,published"
	g2="J.salt,Jcircumflex.salt"
	k="10" />
    <hkern g1="copyright,published"
	g2="napostrophe,quoteright,quotedblright"
	k="20" />
    <hkern g1="copyright,published"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="51" />
    <hkern g1="copyright,published"
	g2="quoteleft,quotedblleft"
	k="14" />
    <hkern g1="copyright,published"
	g2="T,Tcommaaccent,Tcaron"
	k="41" />
    <hkern g1="copyright,published"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="copyright,published"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="35" />
    <hkern g1="copyright,published"
	g2="period,ellipsis"
	k="41" />
    <hkern g1="copyright,published"
	g2="J,Jcircumflex"
	k="16" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="parenright,bracketright,braceright"
	k="14" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="J.salt,Jcircumflex.salt"
	k="14" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="76" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-10" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="12" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-31" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-20" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="J,Jcircumflex"
	k="63" />
    <hkern g1="quoteleft,quotedblleft"
	g2="hyphen,endash,emdash"
	k="25" />
    <hkern g1="quoteleft,quotedblleft"
	g2="comma,quotesinglbase,quotedblbase"
	k="125" />
    <hkern g1="quoteleft,quotedblleft"
	g2="J.salt,Jcircumflex.salt"
	k="96" />
    <hkern g1="quoteleft,quotedblleft"
	g2="copyright,published"
	k="51" />
    <hkern g1="quoteleft,quotedblleft"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="287" />
    <hkern g1="quoteleft,quotedblleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="27" />
    <hkern g1="quoteleft,quotedblleft"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="31" />
    <hkern g1="quoteleft,quotedblleft"
	g2="T,Tcommaaccent,Tcaron"
	k="-16" />
    <hkern g1="quoteleft,quotedblleft"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-10" />
    <hkern g1="quoteleft,quotedblleft"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-20" />
    <hkern g1="quoteleft,quotedblleft"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="133" />
    <hkern g1="quoteleft,quotedblleft"
	g2="f,fi,fl"
	k="20" />
    <hkern g1="quoteleft,quotedblleft"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="92" />
    <hkern g1="quoteleft,quotedblleft"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="82" />
    <hkern g1="quoteleft,quotedblleft"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="31" />
    <hkern g1="quoteleft,quotedblleft"
	g2="t,tcommaaccent"
	k="10" />
    <hkern g1="quoteleft,quotedblleft"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-10" />
    <hkern g1="quoteleft,quotedblleft"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-10" />
    <hkern g1="quoteleft,quotedblleft"
	g2="period,ellipsis"
	k="174" />
    <hkern g1="quoteleft,quotedblleft"
	g2="colon,semicolon"
	k="41" />
    <hkern g1="quoteleft,quotedblleft"
	g2="J,Jcircumflex"
	k="418" />
    <hkern g1="quoteleft,quotedblleft"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="41" />
    <hkern g1="quoteleft,quotedblleft"
	g2="z,zacute,zdotaccent,zcaron"
	k="20" />
    <hkern g1="quoteleft,quotedblleft"
	g2="m,n,p,r,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="55" />
    <hkern g1="quoteleft,quotedblleft"
	g2="parenleft,bracketleft,braceleft"
	k="25" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="hyphen,endash,emdash"
	k="41" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="comma,quotesinglbase,quotedblbase"
	k="113" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="J.salt,Jcircumflex.salt"
	k="88" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="copyright,published"
	k="41" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="287" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="31" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="31" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="T,Tcommaaccent,Tcaron"
	k="-20" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-10" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-20" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="143" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="f,fi,fl"
	k="31" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="100" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="88" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="41" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="t,tcommaaccent"
	k="16" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="period,ellipsis"
	k="199" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="colon,semicolon"
	k="53" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="J,Jcircumflex"
	k="410" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="41" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="z,zacute,zdotaccent,zcaron"
	k="31" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="m,n,p,r,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="63" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="parenleft,bracketleft,braceleft"
	k="20" />
  </font>
</defs></svg>
