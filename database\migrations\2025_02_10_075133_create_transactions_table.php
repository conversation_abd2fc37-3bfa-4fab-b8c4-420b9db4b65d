<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('transactions', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()
                ->references('id')->on('users')->onDelete('cascade');
            $table->foreignId('type_id')
                ->references('id')->on('transaction_types')->onDelete('cascade');
            $table->bigInteger('debit_amount')->nullable()->index();
            $table->bigInteger('credit_amount')->nullable()->index();
            $table->string('particular')->nullable()->index();
            $table->string('ref_data')->nullable()->index();
            $table->timestamp('time')->index();

            //$table->index(['user_id','type_id','debit_amount'],'idx1');
            //$table->index(['user_id','type_id','credit_amount'],'idx2');
            //$table->index(['user_id','debit_amount'],'idx3');
            //$table->index(['user_id','credit_amount'],'idx4');

            //$table->index(['user_id','type_id','debit_amount','time'],'idx11');
            //$table->index(['user_id','type_id','credit_amount','time'],'idx12');
            //$table->index(['user_id','debit_amount','time'],'idx13');
            //$table->index(['user_id','credit_amount','time'],'idx14');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
