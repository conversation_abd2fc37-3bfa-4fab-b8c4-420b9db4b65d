<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class AdminController extends Controller
{
    public function switchUpdate(Request $request) {
        $res=['status'=>'error','message'=>'Invalid request'];
        try {
            if ($request->input('table') && $request->input('column') && $request->input('pkColumn')) {
                $table = "App\\Models\\" . $request->input('table');
                $model = $table::query()->where($request->input('pkColumn'), '=', $request->input('id'))
                    ->limit(1)->first();
                if ($model) {
                    $column = $request->input('column');
                    $val = (int)$request->input('val');
                    $model->$column = $val;
                    if ($model->update([$column])) {
                        $res['status'] = 'success';
                        $res['message'] = 'Your request saved successfully';
                    }
                } else {
                    $res['message'] = 'Invalid column selected';
                }
            }
        } catch (\Exception $e) {
            $res=['status'=>'error','message'=>$e->getMessage()];
        }
        return response()->json($res);
    }

    public function singleValueUpdate(Request $request) {
        $res=['status'=>'error','message'=>'Invalid request'];
        try {
            if ($request->input('table') && $request->input('column') && $request->input('pkColumn') && $request->input('pkValue')) {
                $table = "App\\Models\\" . $request->input('table');
                $model = $table::query()->where($request->input('pkColumn'), '=', $request->input('pkValue'))
                    ->limit(1)->first();

                $tableObj=new $table();
                $uniqueColumns = [];
                try {
                    if($tableObj->getUniqueColumns()) {
                        $uniqueColumns=$tableObj->getUniqueColumns();
                    }
                } catch (\Exception $e) {

                }

                if(count($uniqueColumns)>0) {
                    if (in_array($request->input('column'), $uniqueColumns)) {
                        $exists = $table::query()->where($request->input('column'), $request->input('updatedValue'))
                            ->where($request->input('pkColumn'), '!=', $request->input('pkValue'))
                            ->exists();

                        if ($exists) {
                            $res['message'] = ucfirst(str_replace("_"," ",$request->input('column'))) . ' already exists';
                            return response()->json($res);
                        }
                    }
                }

                if ($model) {
                    $column = $request->input('column');
                    $val = trim($request->input('updatedValue'));
                    if ($val != "") {
                        $model->$column = $val;
                        if ($model->update([$column])) {
                            $res['status'] = 'success';
                            $res['new_value'] = $val;
                            $res['selector'] = trim($request->input('table').'-'.$request->input('column').'-'.$request->input('pkValue'));
                            $res['message'] = 'Your request saved successfully';
                        }
                    } else {
                        $res['message'] = 'Invalid value entered';
                    }
                } else {
                    $res['message'] = 'Invalid column selected';
                }
            }
        } catch (\Exception $e) {
            if(stripos($e->getMessage(),'duplicate')) {
                $res['message'] = 'Record already exist';
            } else {
                $res = ['status' => 'error', 'message' => $e->getMessage()];
            }
        }
        return response()->json($res);
    }

    public function fetchUserNames(Request $request) {
        //header('Content-Type: application/json');
        $searchTerm = $request->input('q');
        $results = [];

        $users=\App\Models\User::query()
            ->where('user_role_id','=',\App\Constants\UserConstants::ROLE_USER)
            ->where('username','like' ,'%'.$searchTerm.'%')
            ->orWhere('name','like' ,'%'.$searchTerm.'%')
            ->orWhere('email','like' ,'%'.$searchTerm.'%')
            ->orderBy("username")->get();
        if(count($users)>0) {
            foreach ($users as $user) {
                $results[]=['id'=>$user->id,'text'=>$user->profileName];
            }
        }
        echo json_encode(['items' => $results]);
    }

    public function fetchProviderServices(Request $request) {
        //header('Content-Type: application/json');
        $provider = $request->input('provider');
        $results = [];

        if($provider) {
            $services = \App\Models\ProviderService::query()
                ->where('provider_id', '=', $provider)
                ->orderBy("name")->get();
            if (count($services) > 0) {
                foreach ($services as $service) {
                    $results[] = ['id' => $service->id, 'text' => $service->displayName];
                }
            }
        }
        echo json_encode(['items' => $results]);
    }

    public function dashboard()
    {
        return view('admin.dashboard');
    }
    public function imeiStats()
    {
        return view('admin.imeiStats');
    }
    public function dailyStats()
    {
        return view('admin.dailyStats');
    }
    public function topServices()
    {
        return view('admin.topServices');
    }
    public function topUsers()
    {
        return view('admin.topUsers');
    }
    public function invalidPrices()
    {
        return view('admin.invalidPrices');
    }
    public function systemTransactions()
    {
        return view('admin.systemTransactions');
    }
    public function customPrices()
    {
        return view('admin.customPrices');
    }
    public function dbStats()
    {
        return view('admin.dbStats');
    }
}
