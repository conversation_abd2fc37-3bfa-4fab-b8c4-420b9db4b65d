<?php
$identity=auth()->guest() ? null : auth()->user();
?>
<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="ltr">
<head>
    <meta charset="UTF-8">
    <base href="{{\App\Components\Helper::withAppUrl('backend/')}}">
    <title>{{ config('app.name', 'Laravel') }}</title>
    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <!-- Favicon -->
    <link rel="icon" href="{{\App\Components\Helper::frontendWithBaseUrl('favicon.ico','')}}" type="image/x-icon"/>
    <link rel="shortcut icon" type="image/x-icon" href="{{\App\Components\Helper::frontendWithBaseUrl('favicon.ico','')}}" />


    <link rel="stylesheet" href="font/iconsmind-s/css/iconsminds.css" />
    <link rel="stylesheet" href="font/simple-line-icons/css/simple-line-icons.css" />

    <link rel="stylesheet" href="css/vendor/bootstrap.min.css" />
    <link rel="stylesheet" href="css/vendor/bootstrap.rtl.only.min.css" />
    <link rel="stylesheet" href="css/vendor/component-custom-switch.min.css" />
    <link rel="stylesheet" href="css/vendor/perfect-scrollbar.css" />
    <link rel="stylesheet" href="css/vendor/glide.core.min.css" />
    <link rel="stylesheet" href="css/vendor/select2.min.css" />
    <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/select2/4.0.6-rc.0/css/select2.min.css" />

    <link rel="stylesheet" href="css/main.css" />


    <style>
        .dataTables_wrapper .tableButton button {
            border-radius: 50px !important;
            background-color: #00365a !important;
            border-color: #00365a !important;
            color: #fff !important;
            padding: 7px 25px 4px;
            text-transform: uppercase;
        }
        .dataTables_wrapper .tableButton {
            margin-bottom: 35px !important;
        }
        .tableFooterStart{
            width: 100% !important;
            margin-top: 15px !important;
        }
        .dataTables_info {
            display: block !important;
            width: 50% !important;
        }
        .dataTables_length {
            text-align: right !important;
            margin-left: auto !important;
            width: 50% !important;
        }
        .dataTables_paginate.paging_full_numbers{
            text-align: center !important;
            margin-top: 5px !important;
            float: unset !important;
        }
        .tableArea {
            overflow: auto;
        }
        .dataTable td, .dataTable th{
            vertical-align: middle;
        }
        .pxp-dashboard-table-options ul{
            margin: 0;
        }

        .logo {
            width: 190px;
            height: 42px;
            background: url('<?php echo \App\Components\Helper::withAppUrl('logo-dark.png') ?>') no-repeat;
            background-size: contain;
        }
        .logo-mobile {
            background: url('<?=\App\Components\Helper::frontendWithBaseUrl('favicon.ico','') ?>') no-repeat;
        }
        .theme-dark .logo{
            background: url('<?php echo \App\Components\Helper::withAppUrl('logo-white.png') ?>') no-repeat;
            background-size: contain !important;
        }
        .invalid-feedback{
            display: block !important;
        }



        @media (max-width: 767px) {
            .dataTables_info {
                display: block !important;
                width: 100% !important;
                text-align: center !important;
            }
            .dataTables_length {
                margin-top: 15px;
                margin-left: auto !important;
                width: 100% !important;
                text-align: center !important;
            }
            .dataTable td, .dataTable th{
                min-width: 100px;
            }

        }
    </style>

    <style>
        .select2-results__options {
            max-height: 300px !important;
            overflow: auto !important;
        }
        .select2-container--default .select2-selection--single .select2-selection__clear {
            margin-right: 8px !important;
        }
    </style>

    @yield('pageCss')
</head>

<body id="app-container" class="menu-default show-spinner">
<nav class="navbar fixed-top">
    <div class="d-flex align-items-center navbar-left">
        <a href="#" class="menu-button d-none d-md-block">
            <svg class="main" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 9 17">
                <rect x="0.48" y="0.5" width="7" height="1" />
                <rect x="0.48" y="7.5" width="7" height="1" />
                <rect x="0.48" y="15.5" width="7" height="1" />
            </svg>
            <svg class="sub" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 17">
                <rect x="1.56" y="0.5" width="16" height="1" />
                <rect x="1.56" y="7.5" width="16" height="1" />
                <rect x="1.56" y="15.5" width="16" height="1" />
            </svg>
        </a>

        <a href="#" class="menu-button-mobile d-xs-block d-sm-block d-md-none">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 26 17">
                <rect x="0.5" y="0.5" width="25" height="1" />
                <rect x="0.5" y="7.5" width="25" height="1" />
                <rect x="0.5" y="15.5" width="25" height="1" />
            </svg>
        </a>


    </div>


    <a class="navbar-logo" href="{{route('home')}}">
        <span class="logo d-none d-xs-block"></span>
        <span class="logo-mobile d-block d-xs-none"></span>
    </a>

    <div class="navbar-right">
        <div class="header-icons d-inline-block align-middle">
            <div class="d-none d-md-inline-block align-text-bottom mr-3">
                <div class="custom-switch custom-switch-primary-inverse custom-switch-small pl-1"
                     data-toggle="tooltip" data-placement="left" title="Dark Mode">
                    <input class="custom-switch-input" id="switchDark" type="checkbox" checked>
                    <label class="custom-switch-btn" for="switchDark"></label>
                </div>
            </div>

            <div class="position-relative d-none d-sm-inline-block">
                <button class="header-icon btn btn-empty" type="button" id="iconMenuButton" data-toggle="dropdown"
                        aria-haspopup="true" aria-expanded="false">
                    <i class="simple-icon-grid"></i>
                </button>
                <div class="dropdown-menu dropdown-menu-right mt-3  position-absolute" id="iconMenuDropdown">
                    <a href="#" class="icon-menu-item">
                        <i class="iconsminds-equalizer d-block"></i>
                        <span>Settings</span>
                    </a>

                    <a href="#" class="icon-menu-item">
                        <i class="iconsminds-male-female d-block"></i>
                        <span>Users</span>
                    </a>

                    <a href="#" class="icon-menu-item">
                        <i class="iconsminds-puzzle d-block"></i>
                        <span>Components</span>
                    </a>

                    <a href="#" class="icon-menu-item">
                        <i class="iconsminds-bar-chart-4 d-block"></i>
                        <span>Profits</span>
                    </a>

                    <a href="#" class="icon-menu-item">
                        <i class="iconsminds-file d-block"></i>
                        <span>Surveys</span>
                    </a>

                    <a href="#" class="icon-menu-item">
                        <i class="iconsminds-suitcase d-block"></i>
                        <span>Tasks</span>
                    </a>

                </div>
            </div>

            <div class="position-relative d-inline-block">
                <button class="header-icon btn btn-empty" type="button" id="notificationButton"
                        data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="simple-icon-bell"></i>
                    <span class="count">3</span>
                </button>
                <div class="dropdown-menu dropdown-menu-right mt-3 position-absolute" id="notificationDropdown">
                    <div class="scroll">
                        <div class="d-flex flex-row mb-3 pb-3 border-bottom">
                            <a href="#">
                                <img src="img/profiles/l-2.jpg" alt="Notification Image"
                                     class="img-thumbnail list-thumbnail xsmall border-0 rounded-circle" />
                            </a>
                            <div class="pl-3">
                                <a href="#">
                                    <p class="font-weight-medium mb-1">Joisse Kaycee just sent a new comment!</p>
                                    <p class="text-muted mb-0 text-small">09.04.2018 - 12:45</p>
                                </a>
                            </div>
                        </div>
                        <div class="d-flex flex-row mb-3 pb-3 border-bottom">
                            <a href="#">
                                <img src="img/notifications/1.jpg" alt="Notification Image"
                                     class="img-thumbnail list-thumbnail xsmall border-0 rounded-circle" />
                            </a>
                            <div class="pl-3">
                                <a href="#">
                                    <p class="font-weight-medium mb-1">1 item is out of stock!</p>
                                    <p class="text-muted mb-0 text-small">09.04.2018 - 12:45</p>
                                </a>
                            </div>
                        </div>
                        <div class="d-flex flex-row mb-3 pb-3 border-bottom">
                            <a href="#">
                                <img src="img/notifications/2.jpg" alt="Notification Image"
                                     class="img-thumbnail list-thumbnail xsmall border-0 rounded-circle" />
                            </a>
                            <div class="pl-3">
                                <a href="#">
                                    <p class="font-weight-medium mb-1">New order received! It is total $147,20.</p>
                                    <p class="text-muted mb-0 text-small">09.04.2018 - 12:45</p>
                                </a>
                            </div>
                        </div>
                        <div class="d-flex flex-row mb-3 pb-3 ">
                            <a href="#">
                                <img src="img/notifications/3.jpg" alt="Notification Image"
                                     class="img-thumbnail list-thumbnail xsmall border-0 rounded-circle" />
                            </a>
                            <div class="pl-3">
                                <a href="#">
                                    <p class="font-weight-medium mb-1">3 items just added to wish list by a user!
                                    </p>
                                    <p class="text-muted mb-0 text-small">09.04.2018 - 12:45</p>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <button class="header-icon btn btn-empty d-none d-sm-inline-block" type="button" id="fullScreenButton">
                <i class="simple-icon-size-fullscreen"></i>
                <i class="simple-icon-size-actual"></i>
            </button>

        </div>

        <div class="user d-inline-block">
            <button class="btn btn-empty p-0" type="button" data-toggle="dropdown" aria-haspopup="true"
                    aria-expanded="false">
                <span class="name">{{__($identity->name)}}</span>
                <span>
                        <img alt="Profile Picture" src="img/profiles/l-1.jpg" />
                    </span>
            </button>

            <div class="dropdown-menu dropdown-menu-right mt-3">
                <a class="dropdown-item" href="{{route('account')}}">Account</a>
                <form id="logout-form" method="POST" action="{{ route('logout') }}">
                    @csrf
                </form>
                <a class="dropdown-item" href="javascript:void(0);" onclick="document.getElementById('logout-form').submit();">Logout</a>
            </div>
        </div>
    </div>
</nav>

<div class="menu">
    <div class="main-menu">
        <div class="scroll">
            <ul class="list-unstyled">
                <li>
                    <a href="{{route('home')}}">
                        <i class="iconsminds-home"></i>
                        <span>Home</span>
                    </a>
                </li>
                <?php
                if($identity) {
                    if($identity->isAdmin()) {
                        ?>
                        <li class="">
                            <a href="#menu-dashboard">
                                <i class="iconsminds-shop-4"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>
                        <li>
                            <a href="javascrip:void(0);admin/db-stats">
                                <i class="iconsminds-billing"></i>
                                <span>IMEI/SN Stats</span>
                            </a>
                        </li>
                        <li class="<?=\App\Components\Helper::activeOpenedUrl([route('user.index')])?>">
                            <a href="#menu-users">
                                <i class="iconsminds-user"></i>
                                <span>Users</span>
                            </a>
                        </li>
                        <li class="<?=\App\Components\Helper::activeOpenedUrl([route('user-order.index')])?>">
                            <a href="#menu-orders">
                                <i class="iconsminds-shopping-basket"></i>
                                <span>Orders</span>
                            </a>
                        </li>
                        <li>
                            <a href="javascrip:void(0);admin/faqs">
                                <i class="iconsminds-tag"></i>
                                <span>FAQs</span>
                            </a>
                        </li>
                        <li class="<?=\App\Components\Helper::activeOpenedUrl([route('provider-service.index'),route('service.index'),route('provider.index'),route('service-user-price.index'),route('service-plan-price.index')])?>">
                            <a href="#menu-services">
                                <i class="iconsminds-pantone"></i>
                                <span>Services</span>
                            </a>
                        </li>
                        <li>
                            <a href="javascrip:void(0);admin/contact">
                                <i class="iconsminds-support"></i>
                                <span>Contact Us</span>
                            </a>
                        </li>
                        <li>
                            <a href="#menu-settings">
                                <i class="iconsminds-digital-drawing"></i>
                                <span>Site Settings</span>
                            </a>
                        </li>
                        <li>
                            <a href="javascrip:void(0);admin/blog">
                                <i class="iconsminds-shop-2"></i>
                                <span>Blog Posts</span>
                            </a>
                        </li>
                        <li>
                            <a href="javascrip:void(0);admin/tickets">
                                <i class="iconsminds-tag"></i>
                                <span>Tickets</span>
                            </a>
                        </li>
                        <li>
                            <a href="javascrip:void(0);admin/iphone-models">
                                <i class="iconsminds-apple-bite"></i>
                                <span>iPhone Serials</span>
                            </a>
                        </li>
                        <?php
                    } else {
                        ?>
                        <li class="active">
                            <a href="{{route('dashboard')}}">
                                <i class="iconsminds-shop-4"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>

                        <li class="">
                            <a class="" href="javascrip:void(0);user/services">
                                <i class="iconsminds-pantone"></i>
                                <span>Services</span>
                            </a>
                        </li>
                        <li class="">
                            <a class="" href="javascrip:void(0);user/requests">
                                <i class="iconsminds-shopping-basket"></i>
                                <span>Orders</span>
                            </a>
                        </li>
                        <li class="">
                            <a class="" href="javascrip:void(0);user/api">
                                <i class="iconsminds-three-arrow-fork"></i>
                                <span>API</span>
                            </a>
                        </li>
                        <li class="">
                            <a class="" href="javascrip:void(0);user/transactions">
                                <i class="iconsminds-library"></i>
                                <span>Transactions</span>
                            </a>
                        </li>
                        <li class="">
                            <a class="" href="javascrip:void(0);user/add-money">
                                <i class="iconsminds-money-bag"></i>
                                <span>Add Funds</span>
                            </a>
                        </li>
                        <li class="">
                            <a class="" href="javascrip:void(0);user/tickets">
                                <i class="iconsminds-tag"></i>
                                <span>Support Tickets</span>
                            </a>
                        </li>
                        <li class="">
                            <a class="" href="javascrip:void(0);user/contact">
                                <i class="iconsminds-support"></i>
                                <span>Contact Us</span>
                            </a>
                        </li>
                        <?php
                    }
                }
                ?>
            </ul>
        </div>
    </div>

    <div class="sub-menu">
        <div class="scroll">
            <?php
            if($identity) {
                if($identity->isAdmin()) {
                    ?>
                    <ul class="list-unstyled" data-link="menu-dashboard">
                        <li class="<?=\App\Components\Helper::activeOpenedUrl(route('admin.dashboard'))?>">
                            <a href="{{route('admin.dashboard')}}">
                                <span class="d-inline-block">Dashboard</span>
                            </a>
                        </li>
                        <li class="">
                            <a href="javascrip:void(0);admin/imei-stats">
                                <span class="d-inline-block">IMEI/SN Stats</span>
                            </a>
                        </li>
                        <li class="">
                            <a href="javascrip:void(0);admin/daily-stats">
                                <span class="d-inline-block">Daily Stats</span>
                            </a>
                        </li>
                        <li class="">
                            <a href="javascrip:void(0);admin/top-services">
                                <span class="d-inline-block">Top Services</span>
                            </a>
                        </li>
                        <li class="">
                            <a href="javascrip:void(0);admin/top-users">
                                <span class="d-inline-block">Top Users</span>
                            </a>
                        </li>
                        <li class="">
                            <a href="javascrip:void(0);admin/invalid-prices">
                                <span class="d-inline-block">Invalid Prices</span>
                            </a>
                        </li>
                        <li class="">
                            <a href="javascrip:void(0);admin/system-transactions">
                                <span class="d-inline-block">System Transactions</span>
                            </a>
                        </li>
                        <li class="">
                            <a href="javascrip:void(0);admin/custom-prices">
                                <span class="d-inline-block">Custom Prices</span>
                            </a>
                        </li>
                    </ul>
                    <ul class="list-unstyled" data-link="menu-users">
                        <li class="<?=\App\Components\Helper::activeOpenedUrl(route('user.index'))?>">
                            <a href="{{route('user.index',['role'=>\App\Constants\UserConstants::ROLE_USER])}}">
                                <span class="d-inline-block">Users</span>
                            </a>
                        </li>
                        <li class="<?=\App\Components\Helper::activeOpenedUrl(route('user.create'))?>">
                            <a href="{{route('user.create')}}">
                                <span class="d-inline-block">Add New User</span>
                            </a>
                        </li>
                        <li class="<?=\App\Components\Helper::activeOpenedUrl(route('admin-adjustment.index'))?>">
                            <a href="{{route('admin-adjustment.index')}}">
                                <span class="d-inline-block">Admin Adjustments</span>
                            </a>
                        </li>
                        <li class="<?=\App\Components\Helper::activeOpenedUrl(route('user-invoice.index'))?>">
                            <a href="{{route('user-invoice.index')}}">
                                <span class="d-inline-block">Invoices</span>
                            </a>
                        </li>
                        <li class="<?=\App\Components\Helper::activeOpenedUrl(route('plan.index'))?>">
                            <a href="{{route('plan.index')}}">
                                <span class="d-inline-block">User Groups</span>
                            </a>
                        </li>
                        <li class="">
                            <a href="javascript:void(0);">
                                <span class="d-inline-block">Deposits</span>
                            </a>
                        </li>
                        <li class="<?=\App\Components\Helper::activeOpenedUrl(route('transaction.index'))?>">
                            <a href="{{route('transaction.index')}}">
                                <span class="d-inline-block">Transactions</span>
                            </a>
                        </li>
                        <li class="<?=\App\Components\Helper::activeOpenedUrl(route('user-login-history.index'))?>">
                            <a href="{{route('user-login-history.index')}}">
                                <span class="d-inline-block">Login History</span>
                            </a>
                        </li>
                    </ul>
                    <ul class="list-unstyled" data-link="menu-orders">
                        <li class="<?=\App\Components\Helper::activeOpenedUrl(route('user-order.index'))?>">
                            <a href="{{route('user-order.index')}}">
                                <span class="d-inline-block">Orders</span>
                            </a>
                        </li>
                        <li class="<?=\App\Components\Helper::activeOpenedUrl(route('user-order-log.index'))?>">
                            <a href="{{route('user-order-log.index')}}">
                                <span class="d-inline-block">Order Logs</span>
                            </a>
                        </li>
                        <li class="<?=\App\Components\Helper::activeOpenedUrl(route('user-order-log.topUser'))?>">
                            <a href="{{route('user-order-log.topUser')}}">
                                <span class="d-inline-block">Top Users</span>
                            </a>
                        </li>
                        <li class="<?=\App\Components\Helper::activeOpenedUrl(route('user-order-log.topService'))?>">
                            <a href="{{route('user-order-log.topService')}}">
                                <span class="d-inline-block">Top Services</span>
                            </a>
                        </li>
                    </ul>
                    <ul class="list-unstyled" data-link="menu-services">
                        <li class="<?=\App\Components\Helper::activeOpenedUrl(route('provider.index'))?>">
                            <a href="{{route('provider.index')}}">
                                <span class="d-inline-block">Providers</span>
                            </a>
                        </li>
                        <li class="<?=\App\Components\Helper::activeOpenedUrl(route('provider-service.index'))?>">
                            <a href="{{route('provider-service.index')}}">
                                <span class="d-inline-block">Provider Services</span>
                            </a>
                        </li>
                        <li class="<?=\App\Components\Helper::activeOpenedUrl(route('service.index'))?>">
                            <a href="{{route('service.index')}}">
                                <span class="d-inline-block">Services</span>
                            </a>
                        </li>
                        <li class="<?=\App\Components\Helper::activeOpenedUrl(route('service-plan-price.index'))?>">
                            <a href="{{route('service-plan-price.index')}}">
                                <span class="d-inline-block">Service Plan Price</span>
                            </a>
                        </li>
                        <li class="<?=\App\Components\Helper::activeOpenedUrl(route('service-user-price.index'))?>">
                            <a href="{{route('service-user-price.index')}}">
                                <span class="d-inline-block">Service User Price</span>
                            </a>
                        </li>
                        <li class="<?=\App\Components\Helper::activeOpenedUrl(route('category.index'))?>">
                            <a href="{{route('category.index')}}">
                                <span class="d-inline-block">Categories</span>
                            </a>
                        </li>
                        <li class="<?=\App\Components\Helper::activeOpenedUrl(route('operating-system.index'))?>">
                            <a href="{{route('operating-system.index')}}">
                                <span class="d-inline-block">Operating Systems</span>
                            </a>
                        </li>
                        <li class="">
                            <a href="javascrip:void(0);admin/translation">
                                <span class="d-inline-block">Translation</span>
                            </a>
                        </li>
                    </ul>
                    <ul class="list-unstyled" data-link="menu-settings">
                        <li class="">
                            <a href="javascrip:void(0);admin/site-settings">
                                <span class="d-inline-block">Site Settings</span>
                            </a>
                        </li>
                        <li class="">
                            <a href="javascrip:void(0);admin/payment-settings">
                                <span class="d-inline-block">Payment Settings</span>
                            </a>
                        </li>
                        <li class="">
                            <a href="javascrip:void(0);admin/meta-tags">
                                <span class="d-inline-block">Meta Tags</span>
                            </a>
                        </li>
                        <li class="">
                            <a href="javascrip:void(0);admin/pages">
                                <span class="d-inline-block">Pages</span>
                            </a>
                        </li>
                        <li class="">
                            <a href="javascrip:void(0);admin/testimonials">
                                <span class="d-inline-block">Testimonials</span>
                            </a>
                        </li>
                    </ul>
                    <?php
                } else {
                    ?>
                    <?php
                }
            }
            ?>
        </div>
    </div>
</div>


        @yield('content')


<!-- Back to top -->
<a href="#top" id="back-to-top"><i class="fa fa-angle-up"></i></a>

<footer class="page-footer">
    <div class="footer-content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12 col-sm-6">
                    <p class="mb-0 text-muted">Copyright © {{date("Y")}}</p>
                </div>
                <div class="col-sm-6 d-none d-sm-block">
                    <ul class="breadcrumb pt-0 pr-0 float-right">
                        <li class="breadcrumb-item mb-0">
                            <a href="#" class="btn-link">Review</a>
                        </li>
                        <li class="breadcrumb-item mb-0">
                            <a href="#" class="btn-link">Purchase</a>
                        </li>
                        <li class="breadcrumb-item mb-0">
                            <a href="#" class="btn-link">Docs</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</footer>

<div id="updateModal" class="modal fade bd-example-modal-sm" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm">

        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Update Details #<span class="popup-pk-ref"></span></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{{route('admin.singleValueUpdate')}}" id="form-single-value-update" method="post">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="pop-val" class="popup-label"></label>
                        <input required type="text" name="updatedValue" id="pop-val" class="form-control popup-input">

                    </div>
                    <div class="form-group text-right">
                        <input type="hidden" required class="popup-table" name="table" value="">
                        <input type="hidden" required class="popup-column" name="column" value="">
                        <input type="hidden" required class="popup-pk" name="pkColumn" value="">
                        <input type="hidden" required class="popup-pk-id" name="pkValue" value="">
                        <button type="submit" class="btn btn-primary">Update</button>
                    </div>
                </div>
            </form>
        </div>

    </div>
</div>

<div class="modal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">

    </div>
</div>



<script src="js/vendor/jquery-3.3.1.min.js"></script>
<script src="js/vendor/bootstrap.bundle.min.js"></script>
<script src="js/vendor/perfect-scrollbar.min.js"></script>
<script src="js/vendor/mousetrap.min.js"></script>
<script src="js/vendor/glide.min.js"></script>
<script src="js/dore.script.js"></script>
<script src="js/vendor/select2.full.js"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/select2/4.0.6-rc.0/js/select2.min.js"></script>
<script src="js/scripts.js?v=12"></script>

<script>
    const MAIN_APP_URL = "{{env('APP_URL')}}";
    let csrfToken = $('meta[name="csrf-token"]').attr("content");
</script>

@include('layouts._backend')


@yield('pageJs')

<script type="text/javascript">
    <?php
    if($identity) {
        if($identity->isAdmin()) {
            ?>
            (function (){

                if(document.getElementsByClassName("search-user-ajax")!=null) {
                    $(document).ready(function(){
                        setTimeout(function (){
                            $('.search-user-ajax').select2({
                                ajax: {
                                    url: '{{ route('admin.fetchUserNames') }}', // Replace with your API endpoint
                                    dataType: 'json',
                                    delay: 250,
                                    type:'post',
                                    data: function (params) {
                                        return {
                                            q: params.term // search term
                                        };
                                    },
                                    processResults: function (data) {
                                        return {
                                            results: $.map(data.items, function (item) {
                                                return {
                                                    id: item.id,
                                                    text: item.text
                                                };
                                            })
                                        };
                                    },
                                    cache: false
                                },
                                minimumInputLength: 1,
                                placeholder: 'Search Username',
                                allowClear: true
                            });
                        },2000);
                    });
                }

                if(document.getElementsByClassName("select2")!=null) {
                    $(document).ready(function(){
                        setTimeout(function (){
                            $('.select2').select2();
                        },1500);
                    });
                }

                $('#form-single-value-update').on('submit', function(e) {
                    e.preventDefault(); // Prevent normal form submission
                    $.ajax({
                        url: $(this).attr('action'),
                        method: $(this).attr('method'),
                        data: $(this).serialize(),
                        success: function(response) {
                            if(response.status=="success") {
                                toastr.success(response.message, 'Greet!', toastrConfiguration);
                                $("#"+response.selector).html(response.new_value);
                                $("#"+response.selector).parent().find("a").attr("data-value",response.new_value);
                            } else {
                                toastr.error(response.message, 'Oops!', toastrConfiguration);
                            }
                        },
                        error: function() {
                            toastr.error('Something went wrong. Try later.', 'Oops!', toastrConfiguration);
                        }
                    });
                });

                $(document).on("change",".switch-can-update",function (){
                    let obj=$(this);
                    let val=(obj.prop('checked')?1:0);
                    let table=obj.attr("data-table");
                    let column=obj.attr("data-column");
                    let pkColumn=obj.attr("data-pk");
                    let id=obj.attr("data-id");

                    $.post("{{route('admin.switchUpdate')}}",{table,column,val,id,pkColumn},function (r){
                        if(r.status=="success") {
                            toastr.success(r.message, 'Greet!', toastrConfiguration);
                        } else {
                            toastr.error(r.message, 'Oops!', toastrConfiguration);
                        }
                    });
                });

                $(document).on("change",".fetch-provider-services",function (){
                    let obj=$(this);
                    let val=obj.val();

                    let ele =$(".set-provider-services");
                    ele.html('<option value="">Loading</option>');
                    $.post("{{route('admin.fetchProviderServices')}}",{provider:val},function (data){
                        data = $.parseJSON(data);
                        ele.html('<option value="">Select</option>');
                        $.map(data.items, function (item) {
                            ele.append('<option value="'+item.id+'">'+item.text+'</option>');
                        });
                    });
                });

                $(document).on('click','.edit-element',function(){
                    let obj=$(this);
                    $('.popup-table').val(obj.attr('data-table'));
                    $('.popup-column').val(obj.attr('data-column'));
                    $('.popup-pk').val(obj.attr('data-pk'));
                    $('.popup-pk-id').val(obj.attr('data-pk-id'));
                    $('.popup-pk-ref').html(obj.attr('data-pk-id'));
                    $('.popup-label').html(obj.attr('data-label'));
                    $('.popup-input').val(obj.attr('data-value'));

                    $('#updateModal').modal('show');
                });
            })();
            <?php
        }
    }
    ?>
</script>
</body>
</html>
