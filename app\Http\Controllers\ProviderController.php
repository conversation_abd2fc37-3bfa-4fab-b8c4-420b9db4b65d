<?php

namespace App\Http\Controllers;

use App\Base\Model\BaseModel;
use App\Components\Helper;
use App\Constants\CommonConstants;
use App\Constants\UserConstants;
use App\Models\Provider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Yajra\DataTables\Facades\DataTables;

class ProviderController extends Controller
{
    public function index()
    {
        return view('provider.index');
    }

    public function show(Provider $provider,Request $request)
    {
        if($request->input('type')) {
            switch (trim(strtolower($request->input('type')))) {
                case "re-api":
                    if($provider->generateNewApiKey()) {
                        return redirect()->route('provider.show',$provider->id)->with('success', 'New API key generated successfully.');
                    }
                    break;
            }
            return redirect()->route('provider.show',$provider->id)->with('error', 'Invalid request.');
        }
        return view('provider.show', compact('provider'));
    }

    public function create()
    {
        $provider=new Provider();
        return view('provider.create', compact('provider'));
    }

    public function edit(Provider $provider)
    {
        return view('provider.edit', compact('provider'));
    }

    public function store(Request $request)
    {
        $provider = new Provider();
        return $this->save($request, $provider);
    }

    public function update(Request $request, Provider $provider)
    {
        return $this->save($request, $provider);
    }

    private function save(Request $request, Provider $provider)
    {
        $isNewRecord = true;
        if ($provider->id != null) {
            $isNewRecord = false;
        }

        $rules = [
            'name' => ['required', 'string', 'max:255', Rule::unique('providers')->ignore($provider->id)],
            'api_url' => ['required', 'string'],
            'api_key' => ['required', 'string'],
            'is_active' => ['required', 'integer'],
        ];

        if ($isNewRecord) {

        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            if ($isNewRecord) {
                return redirect()->route('provider.create')->withErrors($validator)->withInput();
            } else {
                return redirect()->route('provider.edit', $provider->id)->withErrors($validator)->withInput();
            }
        }

        $provider->name = trim($request->input('name'));
        $provider->api_url = trim($request->input('api_url'));
        $provider->api_key = trim($request->input('api_key'));
        $provider->api_username = trim($request->input('api_username'));
        if($provider->api_username=="") {
            $provider->api_username=null;
        }
        $provider->is_active = (int)$request->input('is_active');
        if ($isNewRecord) {
            $provider->save();
        } else {
            $provider->update();
        }

        \App\Models\Provider::generateCacheFile();
        resetServicesCache();
        return redirect()->route('provider.index')->with('success', 'Provider saved successfully.');
    }

    public function dataTable(Request $request)
    {
        try {
            if ($request->ajax()) {
                $query = Provider::query();
                BaseModel::buildFilterQuery($query, [
                    'q' => ['name','api_url','api_key','api_username'],
                    'is_active',
                ]);
                return Datatables::eloquent($query)
                    ->addColumn('checkboxes', function ($row) {
                        return '<input type="checkbox" name="pdr_checkbox[]" class="pdr_checkbox" value="' . $row->id . '" />';
                    })
                    ->addColumn('name', function ($row) {
                        return Helper::editPopupStructure($row,'name');
                    })
                    ->addColumn('created_at', function ($row) {
                        return Helper::displayTime($row->created_at);
                    })
                    ->addColumn('services', function ($row) {
                        return '<a class="btn btn-sm btn-info" href="'.route('provider-service.index',['provider'=>$row->id]).'">Total: '.$row->servicesCount.'</a>';
                    })
                    ->addColumn('updated_at', function ($row) {
                        return Helper::displayTime($row->updated_at);
                    })
                    ->addColumn('is_active', function ($row) use ($query) {
                        $columnName = 'is_active';
                        return Helper::onOffButton($row, $columnName, $query);
                    })
                    ->addColumn('is_invoice', function ($row) {
                        return Helper::printYesNoBadge(!($row->is_invoice == null));
                    })
                    ->addColumn('actions', function ($row) {
                        $buttons = [];
                        //$buttons['Login']=['url' => route('user.loginAs', $row->id), 'icon' => 'las la-sign-in-alt'];
                        $buttons['view'] = ['url' => route('provider.show', $row->id)];
                        $buttons['edit'] = ['url' => route('provider.edit', $row->id)];
                        return Helper::getActionButtons($buttons);
                    })
                    ->rawColumns(['checkboxes', 'actions','services', 'is_active','name'])
                    ->make(true);
            }
        } catch (\Exception $e) {
            //print_r(['message'=>$e->getMessage(),'trace'=>$e->getTraceAsString()]);
            die();
        }
    }
}
