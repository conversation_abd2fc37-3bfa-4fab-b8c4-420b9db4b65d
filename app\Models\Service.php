<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use App\Casts\AmountCast;
use App\Components\Helper;
use App\Constants\CommonConstants;
use Illuminate\Database\Eloquent\Model;

class Service extends BaseModel
{
    protected $fillable = [
        'custom_id',
        'display_order',
        'operating_system_id',
        'category_id',
        'provider_id',
        'provider_service_id',
        'name',
        'price',
        'type',
        'order_format',
        'download_format',
        'is_active',
        'created_at',
        'updated_at',
    ];

    public function getUniqueColumns() {
        return ['custom_id'];
    }

    protected $casts = [
        'price' => AmountCast::class,
    ];

    public static function generateCustomID() {
        $last=self::query()->orderByDesc('custom_id')->first();
        if($last) {
            return ($last->custom_id+1);
        }
        return 1;
    }

    public function getDisplayNameAttribute() {
        return $this->name;
    }

    public function getDisplayDetailsAttribute() {
        $html=$this->name;
        if($this->provider) {
            $html.=' ('.$this->provider->name;
            if($this->providerService) {
                $html.=' - '.$this->providerService->sid.' ';
            }
            $html.=')';
        }
        return $html;
    }

    public function onSaving()
    {
        if($this->providerService) {
            $this->type=$this->providerService->type;
        }

        if($this->custom_id=="" || $this->custom_id==null) {
            $this->custom_id=self::generateCustomID();
        }

        if($this->display_order=="" || $this->display_order==null) {
            $this->display_order=self::generateDisplayOrder();
        }
        parent::onSaving();
    }

    public static function generateDisplayOrder() {
        $model=self::query()->orderByDesc('display_order')->first();
        if($model) {
            return $model->display_order+1;
        } else {
            return 1;
        }
    }

    public function getTypeNameAttribute() {
        if($this->type>0) {
            switch ($this->type) {
                case CommonConstants::TYPE_IMEI:
                    return "IMEI";
                    break;
                case CommonConstants::TYPE_SN:
                    return "SN";
                    break;
            }
        }
        return "Both (IMEI/SN)";
    }

    public function userPrice($user=null) {
        if($user) {
            $systemCache=self::fetchDetailsFromCache($this->id);
            if(is_array($systemCache) && count($systemCache)>0) {
                if(array_key_exists('prices',$systemCache)) {
                    if(array_key_exists('users',$systemCache['prices'])) {
                        if(array_key_exists($user->id,$systemCache['prices']['users'])) {
                            return $systemCache['prices']['users'][$user->id]['price'];
                        }
                    }
                    if(array_key_exists('plans',$systemCache['prices'])) {
                        if(array_key_exists($user->plan_id,$systemCache['prices']['plans'])) {
                            return $systemCache['prices']['plans'][$user->plan_id]['price'];
                        }
                    }
                }
            }
        }
        return $this->price;
    }

    public function provider()
    {
        return $this->hasOne(Provider::class, 'id', 'provider_id');
    }

    public function providerService()
    {
        return $this->hasOne(ProviderService::class, 'id', 'provider_service_id');
    }

    public function category()
    {
        return $this->hasOne(Category::class, 'id', 'category_id');
    }

    public function operatingSystem()
    {
        return $this->hasOne(OperatingSystem::class, 'id', 'operating_system_id');
    }

    public static function defaultQuery() {
        return self::query()
            ->where('is_active','=',CommonConstants::YES)
            ->orderBy("display_order");
    }

    public static function getCacheFileName() {
        try {
            $cacheDirectoryName=base_path().DIRECTORY_SEPARATOR.CommonConstants::CACHE_DIRECTORY_NAME;
            if(!is_dir($cacheDirectoryName)) {
                if(!mkdir($cacheDirectoryName, 0755, true)) {
                    throw new \Exception('Cache directory not generated');
                } else {
                    Helper::updateSystemCachePermissions();
                }
            }
            $cacheFileName=$cacheDirectoryName.DIRECTORY_SEPARATOR."services.json";
            if(!is_file($cacheFileName)) {
                if(!file_put_contents($cacheFileName,json_encode([]))) {
                    throw new \Exception('Cache file not generated');
                } else {
                    Helper::updateSystemCachePermissions();
                }
            }
            return $cacheFileName;
        } catch (\Exception $e) {
            Log::insertLog([
                'user_id'=>CommonConstants::ADMINISTRATIVE,
                'type'=>'service_cache_file_generate_error',
                'particulars'=>'Unable to generate service cache file',
                'data'=>['message'=>$e->getMessage(),'trace'=>$e->getTraceAsString()],
            ]);
        }
        return null;
    }

    public static function fetchDetailsFromCache($serviceID) {
        try {
            $fileName=self::getCacheFileName();
            if($fileName) {
                $cacheData=json_decode(file_get_contents($fileName),true);
                if(is_array($cacheData) && count($cacheData)>0) {
                    if(array_key_exists($serviceID,$cacheData)) {
                        return $cacheData[$serviceID];
                    }
                }
            }
        } catch (\Exception $e) {

        }
        return null;
    }

    public static function fetchDetailsFromCustomIDCache($customID) {
        try {
            $fileName=self::getCacheFileName();
            if($fileName) {
                $cacheData=json_decode(file_get_contents($fileName),true);
                if(is_array($cacheData) && count($cacheData)>0) {
                    foreach ($cacheData as $cache) {
                        if($cache['custom_id']==$customID) {
                            return $cache;
                        }
                    }
                }
            }
        } catch (\Exception $e) {

        }
        return null;
    }

    public static function generateCacheFile() {
        $rawData=[];
        try {
            $fileName=self::getCacheFileName();
            if($fileName) {
                $rows=self::defaultQuery()->orderBy("id")->get();
                if(count($rows)>0) {
                    foreach ($rows as $row) {
                        $plans=[];
                        $users=[];

                        $planServices=ServicePlanPrice::defaultQuery()->where('service_id','=',$row->id)->orderBy("id")->get();
                        if(count($planServices)>0) {
                            foreach ($planServices as $planService) {
                                $plans[$planService->plan_id]=[
                                    'id'=>$planService->id,
                                    'plan_id'=>$planService->plan_id,
                                    'plan'=>$planService->plan->name,
                                    'price'=>$planService->price,
                                ];
                            }
                        }

                        $userServices=ServiceUserPrice::defaultQuery()->where('service_id','=',$row->id)->orderBy("id")->get();
                        if(count($userServices)>0) {
                            foreach ($userServices as $userService) {
                                $users[$userService->user_id]=[
                                    'id'=>$userService->id,
                                    'user_id'=>$userService->user_id,
                                    'price'=>$userService->price,
                                ];
                            }
                        }

                        $rawData[$row->id]=[
                            'id'=>$row->id,
                            'custom_id'=>$row->custom_id,
                            'category_id'=>$row->category_id,
                            'category'=>$row->category->name,
                            'provider_id'=>$row->provider_id,
                            'provider'=>$row->provider->name,
                            'provider_service_id'=>$row->provider_service_id,
                            'provider_service'=>$row->providerService->name,
                            'name'=>$row->name,
                            'prices'=>[
                                'default_price'=>$row->price,
                                'plans'=>$plans,
                                'users'=>$users,
                            ],
                        ];
                    }
                }

                if(count($rawData)>0) {
                    file_put_contents($fileName,json_encode($rawData));
                    Helper::updateSystemCachePermissions();
                }
            }

            resetServicesCache();
            return true;
        } catch (\Exception $e) {
            Log::insertLog([
                'user_id'=>CommonConstants::ADMINISTRATIVE,
                'type'=>'service_cache_file_generate_error',
                'particulars'=>'Unable to generate service cache file',
                'data'=>['message'=>$e->getMessage(),'trace'=>$e->getTraceAsString()],
            ]);
        }
        return false;
    }

    public function onSaved()
    {
        self::generateCacheFile();
        parent::onSaved();
    }

    public static function printOptionsHTML($id=null) {
        $html='<option value="">Select</option>';
        $categories=\App\Models\Category::defaultQuery()->get();
        if(count($categories)>0) {
            foreach ($categories as $category) {
                $services=\App\Models\Service::defaultQuery()->where('category_id','=',$category->id)->get();
                $html.='<optgroup label="'.$category->name.'">';
                if(count($services)>0) {
                    foreach ($services as $service) {
                        $html.='<option '.($id && $id==$service->id ? 'selected=""' : '').' value="'.$service->id.'">'.$service->name.'</option>';
                    }
                }
                $html.='</optgroup>';
            }
        }
        return $html;
    }
}
