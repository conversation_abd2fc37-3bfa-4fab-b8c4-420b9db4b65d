<div class="row mb-4">
    <div class="col-lg-12 col-md-12 mb-4">
        <div class="card">
            <div class="card-body">

<div class="rounded-16 form-box bg-white -dark-bg-dark-1 shadow-4 h-100">
    <form action="{{ $plan->id ==null ? route('plan.store') : route('plan.update', $plan) }}" method="POST"
          class="normal-form">
        @csrf

        @if( $plan->id != null )
            @method('PUT')
        @endif

        <div class="row">
            <div class="col-md-8 col-12">
                <div class="form-group required-field text-left @error('name') is-invalid @enderror">
                    <label for="name">{{ __('Name') }}</label>
                    <input id="name" type="text" required
                           class="form-control @error('name') is-invalid @enderror"
                           name="name" placeholder="{{ __('Enter name') }}"
                           value="{{old('name', $plan->name)}}">
                    @error('name')
                    <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                    @enderror
                </div>
            </div>

            <div class="col-md-4 col-12">
                <div class="form-group required-field text-left @error('price') is-invalid @enderror">
                    <label for="price">{{ __('Price') }}</label>
                    <input required id="price" type="text" class="form-control @error('price') is-invalid @enderror"
                           name="price" placeholder="{{ __('Enter price') }}"
                           value="{{old('price', $plan->price)}}">
                    @error('price')
                    <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                    @enderror
                </div>
            </div>

            <div class="col-md-3 col-12">
                <div class="form-group required-field text-left @error('is_default') is-invalid @enderror">
                    <label for="is_default">{{ __('Default') }}</label>
                    <select name="is_default" id="is_default" class="form-control @error('is_default') is-invalid @enderror">
                        <option <?=($plan->is_default==\App\Constants\CommonConstants::NO ? 'selected=""' : '')?> value="<?=\App\Constants\CommonConstants::NO?>">No</option>
                        <option <?=($plan->is_default==\App\Constants\CommonConstants::YES ? 'selected=""' : '')?>value="<?=\App\Constants\CommonConstants::YES?>">Yes</option>
                    </select>
                    @error('is_default')
                    <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                    @enderror
                </div>
            </div>
            <div class="col-md-3 col-12">
                <div class="form-group required-field text-left @error('is_best_seller') is-invalid @enderror">
                    <label for="is_best_seller">{{ __('Best Seller') }}</label>
                    <select name="is_best_seller" id="is_best_seller" class="form-control @error('is_best_seller') is-invalid @enderror">
                        <option <?=($plan->is_best_seller==\App\Constants\CommonConstants::NO ? 'selected=""' : '')?> value="<?=\App\Constants\CommonConstants::NO?>">No</option>
                        <option <?=($plan->is_best_seller==\App\Constants\CommonConstants::YES ? 'selected=""' : '')?>value="<?=\App\Constants\CommonConstants::YES?>">Yes</option>
                    </select>
                    @error('is_best_seller')
                    <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                    @enderror
                </div>
            </div>
            <div class="col-md-3 col-12">
                <div class="form-group required-field text-left @error('is_visible_to_guest') is-invalid @enderror">
                    <label for="is_visible_to_guest">{{ __('Visible To Guest') }}</label>
                    <select name="is_visible_to_guest" id="is_visible_to_guest" class="form-control @error('is_visible_to_guest') is-invalid @enderror">
                        <option <?=($plan->is_visible_to_guest==\App\Constants\CommonConstants::NO ? 'selected=""' : '')?> value="<?=\App\Constants\CommonConstants::NO?>">No</option>
                        <option <?=($plan->is_visible_to_guest==\App\Constants\CommonConstants::YES ? 'selected=""' : '')?>value="<?=\App\Constants\CommonConstants::YES?>">Yes</option>
                    </select>
                    @error('is_visible_to_guest')
                    <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                    @enderror
                </div>
            </div>
            <div class="col-md-3 col-12">
                <div class="form-group required-field text-left @error('is_active') is-invalid @enderror">
                    <label for="is_active">{{ __('Active') }}</label>
                    <select name="is_active" id="is_active" class="form-control @error('is_active') is-invalid @enderror">
                        <option <?=($plan->is_active==\App\Constants\CommonConstants::NO ? 'selected=""' : '')?> value="<?=\App\Constants\CommonConstants::NO?>">No</option>
                        <option <?=($plan->is_active==\App\Constants\CommonConstants::YES ? 'selected=""' : '')?>value="<?=\App\Constants\CommonConstants::YES?>">Yes</option>
                    </select>
                    @error('is_active')
                    <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                    @enderror
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-12 formBtn">
                @if( $plan->id == null )
                    <button type="submit" class="btn btn-primary">{{ __('Save') }}</button>
                @else
                    <button type="submit" class="btn btn-primary">{{ __('Update') }}</button>
                @endif
            </div>
        </div>

    </form>
</div>
            </div>
        </div>
    </div>
</div>

@section('pageJs')

    <script>

    </script>

@endsection
