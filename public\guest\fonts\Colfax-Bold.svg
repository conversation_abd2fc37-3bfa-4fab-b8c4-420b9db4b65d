<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20170731 at Sat Sep 26 15:10:29 2020
 By Aleksey,,,
Copyright (c) 2012 by Process Type Foundry, LLC. All rights reserved.
</metadata>
<defs>
<font id="Colfax-Bold" horiz-adv-x="610" >
  <font-face 
    font-family="Colfax"
    font-weight="700"
    font-stretch="normal"
    units-per-em="2048"
    panose-1="2 0 0 0 0 0 0 0 0 0"
    ascent="1536"
    descent="-512"
    x-height="1073"
    cap-height="1434"
    bbox="-119 -512 2603 1978"
    underline-thickness="108"
    underline-position="-323"
    unicode-range="U+0020-FB02"
  />
<missing-glyph horiz-adv-x="1425" 
d="M195 0v1434h1036v-1434h-1036zM233 37h959v1360h-959v-1360zM995 891l-172 -174l172 -170l-110 -111l-172 172l-170 -172l-111 111l172 170l-172 174l109 108l172 -172l174 172z" />
    <glyph glyph-name=".notdef" horiz-adv-x="1425" 
d="M195 0v1434h1036v-1434h-1036zM233 37h959v1360h-959v-1360zM995 891l-172 -174l172 -170l-110 -111l-172 172l-170 -172l-111 111l172 170l-172 174l109 108l172 -172l174 172z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="682" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="491" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="647" 
d="M504 881l-82 -420h-197l-82 420v553h361v-553zM131 174q0 82 54.5 136.5t138.5 54.5t138 -54.5t54 -136.5t-54 -136t-138 -54t-138.5 54t-54.5 136z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="1044" 
d="M133 844v590h297v-590h-297zM614 844v590h297v-590h-297z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="1421" 
d="M786 0l39 373h-286l-39 -373h-258l39 373h-199v231h223l27 248h-207v231h231l39 351h258l-39 -351h287l39 351h258l-39 -351h191v-231h-215l-27 -248h199v-231h-224l-39 -373h-258zM590 852l-27 -248h287l27 248h-287z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="1222" 
d="M1141 444q0 -156 -97.5 -269.5t-255.5 -154.5v-245h-337v243q-170 37 -261.5 147.5t-91.5 276.5v31h332v-33q0 -85 50 -128.5t141 -43.5q81 0 130.5 40t49.5 112q0 96 -107 133l-248 84q-87 30 -144.5 60.5t-103.5 74.5t-67.5 105t-21.5 143q0 159 90.5 259.5
t242.5 135.5v244h338v-246q150 -38 237 -140t87 -255v-35h-328v29q0 71 -43.5 109t-124.5 38q-76 0 -119 -30.5t-43 -90.5q0 -45 26 -67t81 -43l233 -82q64 -23 110.5 -44.5t95.5 -56t79.5 -74.5t50 -98t19.5 -129z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="1855" 
d="M442 0l736 1434h245l-735 -1434h-246zM782 1126v-161q0 -148 -94.5 -238t-255.5 -90t-255.5 90t-94.5 238v161q0 148 94.5 238t255.5 90t255.5 -90t94.5 -238zM543 944v203q0 50 -29.5 78t-81.5 28t-81 -28t-29 -78v-203q0 -50 29 -78t81 -28t81.5 28t29.5 78zM1784 469
v-162q0 -148 -94.5 -237.5t-255.5 -89.5t-256 89.5t-95 237.5v162q0 148 95 238t256 90t255.5 -90t94.5 -238zM1544 287v202q0 50 -29 78.5t-81 28.5t-81.5 -28.5t-29.5 -78.5v-202q0 -50 29.5 -78.5t81.5 -28.5t81 28.5t29 78.5z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="1423" 
d="M1409 174l-199 -203l-135 131q-159 -131 -409 -131q-165 0 -296.5 53.5t-209.5 159.5t-78 248q0 276 248 393q-60 65 -89.5 127.5t-29.5 145.5q0 166 122 265t314 99q190 0 310 -101.5t120 -264.5v-70h-286v64q0 62 -35 101.5t-105 39.5q-68 0 -105.5 -33t-37.5 -88
q0 -37 18.5 -66.5t57.5 -68.5l428 -416v168h258v-166q0 -129 -35 -221zM680 203q144 0 225 63l-403 391q-113 -85 -113 -219q0 -107 79 -171t212 -64z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="563" 
d="M133 844v590h297v-590h-297z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="722" 
d="M373 1602h329q-116 -286 -171.5 -505t-55.5 -433t55.5 -433t171.5 -505h-329q-250 461 -250 938t250 938z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="722" 
d="M20 1602h330q250 -461 250 -938t-250 -938h-330q116 287 172 506t56 432t-56 432t-172 506z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="911" 
d="M102 1190l70 121l215 -125v248h139v-246l213 123l70 -121l-215 -125l215 -123l-70 -121l-213 123v-246h-139v246l-215 -123l-70 121l213 123z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="1228" 
d="M1096 592h-318v-359h-323v359h-322v291h322v358h323v-358h318v-291z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="589" 
d="M37 -281l106 588h392l-207 -588h-291z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="831" 
d="M733 510h-635v307h635v-307z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="569" 
d="M92 174q0 82 54.5 136.5t138.5 54.5t138 -54.5t54 -136.5t-54 -136t-138 -54t-138.5 54t-54.5 136z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="1019" 
d="M51 -215l578 1751h340l-578 -1751h-340z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="1318" 
d="M659 -29q-252 0 -406.5 141.5t-154.5 370.5v467q0 229 154.5 370.5t406.5 141.5t407 -141.5t155 -370.5v-467q0 -229 -155 -370.5t-407 -141.5zM872 950q0 115 -56.5 171.5t-156.5 56.5t-156.5 -56.5t-56.5 -171.5v-467q0 -115 56.5 -171t156.5 -56t156.5 56t56.5 171
v467z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="862" 
d="M709 0h-349v1083l-309 -100v307l369 144h289v-1434z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="1251" 
d="M455 967h-334v43q0 219 133.5 335.5t368.5 116.5q233 0 374.5 -119.5t141.5 -318.5q0 -64 -18 -124t-43 -104.5t-70.5 -95t-81 -83t-92.5 -80.5l-297 -252h587v-285h-1018v264l566 541q60 59 89.5 109t29.5 108q0 73 -46 114.5t-120 41.5q-86 0 -128 -48t-42 -137v-26z
" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="1265" 
d="M168 1155v279h936v-281l-318 -297q78 -12 143.5 -41.5t120.5 -78.5t86 -126t31 -174q0 -132 -74 -239t-199.5 -166.5t-275.5 -59.5q-111 0 -206.5 29.5t-168 86.5t-114.5 146.5t-43 202.5h326q5 -85 60.5 -135.5t148.5 -50.5t147.5 50t54.5 126q0 82 -51.5 127t-153.5 45
h-217v272l279 285h-512z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="1269" 
d="M502 1434h315l-397 -889h295v289h319v-289h174v-262h-174v-283h-319v283h-633v252z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="1284" 
d="M1196 483q0 -105 -41.5 -199t-115 -163t-179.5 -109.5t-229 -40.5q-99 0 -187.5 26t-161 77t-120.5 134t-60 189h332q11 -65 64.5 -104t132.5 -39q102 0 161.5 61.5t59.5 157.5q0 94 -58 154.5t-159 60.5q-71 0 -121 -27t-72 -75h-319l137 848h803v-277h-528l-43 -254
q87 66 235 66q127 0 232.5 -56t171 -168.5t65.5 -261.5z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="1271" 
d="M584 1434h372l-374 -514q65 43 170 43q90 0 170.5 -32.5t142.5 -93t98.5 -153t36.5 -205.5q0 -142 -72.5 -259t-199 -183t-279.5 -66q-257 0 -410 141.5t-153 368.5q0 234 141 437zM645 698q-100 0 -157.5 -61t-57.5 -162t55 -164t160 -63q95 0 155 65.5t60 159.5
q0 101 -56.5 163t-158.5 62z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="1150" 
d="M1079 1434v-248l-575 -1186h-357l572 1149h-627v285h987z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="1294" 
d="M647 -29q-250 0 -397.5 120.5t-147.5 320.5q0 71 20 130t54 98.5t70.5 65t79.5 42.5q-164 106 -164 309q0 128 65 221t173.5 138.5t246.5 45.5t246.5 -45.5t174 -138.5t65.5 -221q0 -203 -164 -309q43 -17 79.5 -42.5t70.5 -65t53.5 -98.5t19.5 -130
q0 -200 -147.5 -320.5t-397.5 -120.5zM647 236q91 0 149 46.5t58 131.5q0 88 -55.5 135t-151.5 47t-151.5 -47t-55.5 -135q0 -85 58 -131.5t149 -46.5zM647 860q74 0 117 46.5t43 123.5q0 80 -41.5 124t-118.5 44t-118.5 -44t-41.5 -124q0 -77 43 -123.5t117 -46.5z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="1271" 
d="M690 0h-379l375 514q-65 -43 -166 -43q-90 0 -170.5 32.5t-142.5 92.5t-98.5 152.5t-36.5 205.5q0 226 152 367t399 141q257 0 410 -138.5t153 -371.5q0 -246 -127 -424zM627 735q100 0 157.5 61t57.5 162t-55 164.5t-160 63.5q-97 0 -156 -62t-59 -163t56.5 -163.5
t158.5 -62.5z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="569" 
d="M92 770q0 82 54.5 136.5t138.5 54.5t138 -54.5t54 -136.5t-54 -136t-138 -54t-138.5 54t-54.5 136zM92 174q0 82 54.5 136.5t138.5 54.5t138 -54.5t54 -136.5t-54 -136t-138 -54t-138.5 54t-54.5 136z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="589" 
d="M0 -281l106 588h392l-207 -588h-291zM123 770q0 82 54 136.5t138 54.5t138.5 -54.5t54.5 -136.5t-54.5 -136t-138.5 -54t-138 54t-54 136z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="1198" 
d="M102 846l963 416v-306l-543 -219l543 -219v-305l-963 416v217z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="1269" 
d="M1116 860h-962v270h962v-270zM1116 344h-962v270h962v-270z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="1198" 
d="M133 1262l963 -416v-217l-963 -416v305l543 219l-543 219v306z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="1099" 
d="M365 467v94q0 211 124 301l89 66q9 6 28 19.5t26 19t20.5 16.5t18.5 18.5t12 19t9 23.5t2 27q0 52 -43.5 83.5t-115.5 31.5q-168 0 -168 -142v-40h-316v45q0 108 36 188.5t102.5 129t154 72t197.5 23.5q141 0 253.5 -47t178 -137t65.5 -207q0 -109 -40.5 -178.5
t-118.5 -124.5l-103 -72q-58 -39 -82 -77.5t-24 -104.5v-47h-305zM326 174q0 82 54 136.5t138 54.5t138.5 -54.5t54.5 -136.5t-54.5 -136t-138.5 -54t-138 54t-54 136z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="1912" 
d="M1151 1006h238l-80 -449q-6 -35 -6 -55q0 -64 31 -102t89 -38q52 0 91.5 36.5t61 95t32 122t10.5 127.5q0 236 -163 386.5t-437 150.5q-148 0 -276 -46.5t-225 -135t-152.5 -228t-55.5 -315.5q0 -132 44.5 -245t126 -195t203.5 -129t269 -47q210 0 391 53l-28 -185
q-77 -23 -184 -38t-203 -15q-188 0 -346.5 63t-265 171t-165.5 253.5t-59 309.5q0 277 116.5 484t328.5 317t493 110q183 0 336 -57.5t253.5 -155.5t156 -228t55.5 -276q0 -80 -15.5 -157.5t-50 -149.5t-84 -126t-123 -86.5t-161.5 -32.5q-96 0 -169.5 46.5t-94.5 125.5
q-24 -53 -99 -99.5t-178 -46.5q-143 0 -233.5 87.5t-90.5 219.5q0 26 7 66l18 104q27 158 119.5 240t234.5 82q81 0 137.5 -30.5t86.5 -72.5zM807 674l-16 -92q-5 -30 -5 -37q0 -58 33 -92.5t92 -34.5t97 41t49 108l16 95q4 24 4 34q0 55 -33 90t-92 35q-64 0 -98.5 -36.5
t-46.5 -110.5z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="1466" 
d="M1073 0l-82 266h-518l-80 -266h-352l512 1434h360l512 -1434h-352zM733 1116l-174 -567h346z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="1404" 
d="M1331 401q0 -182 -138.5 -291.5t-354.5 -109.5h-695v1434h686q195 0 304.5 -104t109.5 -263q0 -225 -188 -326q276 -76 276 -340zM983 434q0 72 -43.5 113t-120.5 41h-332v-312h338q74 0 116 44t42 114zM487 862h263q68 0 105.5 41.5t37.5 112.5q0 66 -39.5 103.5
t-108.5 37.5h-258v-295z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="1449" 
d="M451 606q0 -160 73 -252t226 -92q134 0 206.5 75.5t77.5 190.5h326q-2 -114 -44 -215t-118 -177t-191 -120.5t-253 -44.5q-162 0 -288.5 49t-206 137.5t-120.5 206t-41 259.5v188q0 142 41 259.5t120.5 205.5t206 137t288.5 49q138 0 253 -44.5t191 -120.5t118 -177
t44 -215h-326q-5 115 -77.5 190.5t-206.5 75.5q-153 0 -226 -92t-73 -252v-221z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="1466" 
d="M143 1434h627q193 0 329.5 -74.5t202.5 -201.5t66 -294v-295q0 -167 -66 -294t-202.5 -201t-329.5 -74h-627v1434zM1016 567v299q0 279 -309 279h-213v-856h213q309 0 309 278z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="1282" 
d="M143 0v1434h1039v-293h-686v-269h624v-292h-624v-287h688v-293h-1041z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="1222" 
d="M496 0h-353v1434h1008v-293h-655v-310h594v-292h-594v-539z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="1468" 
d="M1356 938h-336q-5 97 -77 165t-195 68q-297 0 -297 -335v-226q0 -165 71.5 -260.5t225.5 -95.5q123 0 197 63t79 164h-279v285h625v-203q0 -266 -176 -429t-455 -163q-125 0 -230 32.5t-180 91.5t-127.5 140t-78 177.5t-25.5 205.5v201q0 298 173 470.5t466 172.5
q282 0 447.5 -145t171.5 -379z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="1490" 
d="M1348 0h-353v561h-499v-561h-353v1434h353v-557h499v557h353v-1434z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="659" 
d="M506 1434v-1434h-352v1434h352z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="1271" 
d="M780 1434h353v-994q0 -206 -149.5 -337.5t-385.5 -131.5q-243 0 -384.5 128.5t-141.5 344.5v19h336v-19q0 -83 45.5 -131.5t140.5 -48.5q91 0 138.5 47.5t47.5 128.5v994z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="1380" 
d="M496 0h-353v1434h353v-684l428 684h405l-473 -711l487 -723h-413l-434 696v-696z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="1200" 
d="M143 1434h353v-1141h632v-293h-985v1434z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="1689" 
d="M844 653l379 781h323v-1434h-338v758l-225 -439h-278l-224 439v-758h-338v1434h324z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="1503" 
d="M1360 0h-336l-537 838v-838h-344v1434h334l539 -838v838h344v-1434z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="1511" 
d="M1413 625q0 -142 -42 -260.5t-123 -206.5t-207 -137.5t-285 -49.5t-285 49.5t-207 137.5t-123.5 206.5t-42.5 260.5v184q0 142 42.5 260.5t123.5 206.5t207 137t285 49t285 -49t207 -137t123 -206.5t42 -260.5v-184zM1061 827q0 158 -77.5 250t-227.5 92t-227.5 -92
t-77.5 -250v-221q0 -158 77.5 -250t227.5 -92t227.5 92t77.5 250v221z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="1361" 
d="M756 432h-260v-432h-353v1434h613q254 0 405.5 -135t151.5 -361t-153.5 -366t-403.5 -140zM961 938q0 96 -56 149.5t-170 53.5h-239v-416h239q113 0 169.5 58t56.5 155z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="1511" 
d="M1413 625q0 -158 -52.5 -288.5t-154.5 -217.5l103 -158l-244 -154l-119 185q-86 -21 -190 -21q-159 0 -285 49.5t-207 137.5t-123.5 206.5t-42.5 260.5v184q0 142 42.5 260.5t123.5 206.5t207 137t285 49t285 -49t207 -137t123 -206.5t42 -260.5v-184zM1061 827
q0 158 -77.5 250t-227.5 92t-227.5 -92t-77.5 -250v-221q0 -158 77.5 -250t227.5 -92h16l-88 139l236 158l98 -153q43 82 43 198v221z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="1452" 
d="M971 0l-244 426h-231v-426h-353v1434h674q254 0 395.5 -140t141.5 -366q0 -152 -70.5 -268t-196.5 -177l293 -483h-409zM1001 924q0 105 -58.5 166t-166.5 61h-280v-446h280q108 0 166.5 59.5t58.5 159.5z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="1341" 
d="M1260 440q0 -140 -77.5 -248t-210 -164.5t-298.5 -56.5q-262 0 -419 120t-157 333v39h338v-29q0 -93 60.5 -143.5t179.5 -50.5q111 0 176.5 44t65.5 126q0 102 -99 127l-358 90q-175 43 -256.5 130.5t-81.5 256.5q0 213 143.5 330.5t399.5 117.5q240 0 383 -119.5
t143 -316.5v-35h-326v25q0 79 -48 127.5t-152 48.5q-201 0 -201 -145q0 -94 92 -117l381 -103q169 -45 245.5 -140t76.5 -247z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="1228" 
d="M51 1434h1127v-297h-387v-1137h-353v1137h-387v297z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="1486" 
d="M1001 526v908h353v-899q0 -254 -166 -409t-445 -155t-444.5 155t-165.5 409v899h352v-908q0 -127 69.5 -194.5t188.5 -67.5t188.5 67.5t69.5 194.5z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="1376" 
d="M1352 1434l-475 -1434h-377l-475 1434h364l299 -1047l299 1047h365z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="1994" 
d="M1161 1434l269 -940l178 940h346l-344 -1434h-330l-283 1001l-282 -1001h-330l-344 1434h346l178 -940l269 940h327z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="1425" 
d="M1350 1434l-453 -705l471 -729h-369l-286 444l-285 -444h-371l471 731l-450 703h364l271 -420l270 420h367z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="1339" 
d="M1327 1434l-481 -932v-502h-352v502l-482 932h383l275 -596l274 596h383z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="1327" 
d="M152 1141v293h1040v-293l-662 -848h678v-293h-1085v293l659 848h-630z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="808" 
d="M758 -274h-625v1876h625v-283h-281v-1311h281v-282z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="1019" 
d="M391 1536l578 -1751h-340l-578 1751h340z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="808" 
d="M676 -274h-625v282h281v1311h-281v283h625v-1876z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="1138" 
d="M717 1434l350 -594h-336l-162 301l-161 -301h-336l350 594h295z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="1083" 
d="M1085 -297h-1087v184h1087v-184z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="851" 
d="M709 1184h-342l-224 327h426z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="1239" 
d="M1126 0h-329v90q-46 -54 -130 -84.5t-188 -30.5q-182 0 -287.5 94t-105.5 244q0 162 112 248t304 86q91 0 164 -22.5t116 -57.5v125q0 62 -39.5 100.5t-109.5 38.5q-114 0 -156 -77h-313q34 161 162 252.5t317 91.5q227 0 355 -115.5t128 -314.5v-668zM594 195
q79 0 133.5 35.5t54.5 88.5q0 54 -52.5 89.5t-131.5 35.5q-90 0 -140 -31.5t-50 -93.5q0 -60 51 -92t135 -32z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="1277" 
d="M467 1495v-512q38 46 113 80.5t165 34.5q89 0 163.5 -25t128 -68.5t90.5 -103t55 -128.5t18 -144v-185q0 -75 -18 -144t-55 -128.5t-90.5 -103t-128 -68.5t-163.5 -25q-102 0 -184 43.5t-110 94.5v-113h-328v1495h344zM856 606q0 98 -49 153.5t-148 55.5
q-81 0 -136.5 -53t-55.5 -150v-151q0 -97 55.5 -150t136.5 -53q98 0 147.5 56.5t49.5 152.5v139z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="1179" 
d="M602 -25q-247 0 -385.5 136t-138.5 356v139q0 220 138.5 356t385.5 136q220 0 359.5 -123t144.5 -316h-311q-5 71 -53 118.5t-134 47.5q-93 0 -139.5 -53t-46.5 -145v-181q0 -92 46.5 -145t139.5 -53q86 0 134 47.5t53 118.5h311q-5 -193 -144.5 -316t-359.5 -123z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="1277" 
d="M1155 1495v-1495h-328v113q-28 -51 -110.5 -94.5t-184.5 -43.5q-110 0 -198 39.5t-143 106.5t-84 151t-29 177v176q0 93 29 177t84 150.5t143 106t198 39.5q90 0 164.5 -34t114.5 -81v512h344zM811 461v151q0 97 -56 150t-137 53q-98 0 -147 -55.5t-49 -153.5v-139
q0 -96 49 -152.5t147 -56.5q81 0 137 53t56 150z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="1163" 
d="M1077 428h-667v-25q0 -83 48 -134.5t136 -51.5q67 0 111 29t63 73h303q-28 -150 -161.5 -247t-315.5 -97q-235 0 -375.5 129.5t-140.5 356.5v133q0 229 134 366.5t374 137.5q119 0 214 -37.5t154.5 -103t91 -150.5t31.5 -182v-197zM410 651v-22h352v39q0 84 -43.5 135
t-130.5 51q-91 0 -134.5 -53.5t-43.5 -149.5z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="761" 
d="M526 0h-344v807h-125v266h125v76q0 181 80 263.5t268 82.5h201v-270h-113q-50 0 -71 -20.5t-21 -67.5v-64h193v-266h-193v-807z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="1267" 
d="M1145 1073v-1040q0 -177 -151 -295.5t-386 -118.5q-201 0 -347 93t-173 251h334q20 -43 69 -65.5t117 -22.5q89 0 141 46.5t52 121.5v127q-41 -49 -118.5 -83t-162.5 -34q-108 0 -194 37.5t-139 101t-81 143t-28 167.5v147q0 91 28 171t82 142.5t140 99t194 36.5
q102 0 184.5 -43t110.5 -94v112h328zM801 633q0 88 -56 137t-137 49q-93 0 -141.5 -52.5t-48.5 -139.5v-103q0 -88 48 -140t140 -52q89 0 142 49.5t53 136.5v115z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="1241" 
d="M467 0h-344v1495h344v-537q37 62 115 101t170 39q182 0 279 -108.5t97 -297.5v-692h-344v639q0 75 -39.5 117.5t-115.5 42.5q-73 0 -117.5 -39t-44.5 -125v-635z" />
    <glyph glyph-name="i" unicode="i" 
d="M477 0h-344v1073h344v-1073zM115 1348q0 82 52 130t138 48q87 0 139 -48.5t52 -129.5q0 -82 -52.5 -130.5t-138.5 -48.5t-138 48.5t-52 130.5z" />
    <glyph glyph-name="j" unicode="j" 
d="M133 1073h344v-1161q0 -134 -74.5 -201t-218.5 -67h-170v260h62q57 0 57 51v1118zM115 1348q0 82 52 130t138 48q87 0 139 -48.5t52 -129.5q0 -82 -52.5 -130.5t-138.5 -48.5t-138 48.5t-52 130.5z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="1171" 
d="M467 0h-344v1495h344v-920l274 498h383l-307 -526l318 -547h-383l-285 516v-516z" />
    <glyph glyph-name="l" unicode="l" 
d="M477 0h-344v1495h344v-1495z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="1882" 
d="M1276 799q-69 0 -113.5 -40t-44.5 -124v-635h-344v639q0 160 -149 160q-71 0 -114.5 -40t-43.5 -124v-635h-344v1073h330v-125q31 69 108 109.5t180 40.5q215 0 312 -152q129 152 340 152q182 0 279 -108.5t97 -297.5v-692h-344v639q0 160 -149 160z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="1241" 
d="M467 0h-344v1073h330v-125q41 73 118.5 111.5t180.5 38.5q182 0 279 -108.5t97 -297.5v-692h-344v639q0 75 -39.5 117.5t-115.5 42.5q-73 0 -117.5 -39t-44.5 -125v-635z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="1200" 
d="M1122 608v-143q0 -218 -138.5 -354t-383.5 -136t-383.5 136t-138.5 354v143q0 218 138.5 354t383.5 136t383.5 -136t138.5 -354zM788 627q0 87 -47 142.5t-141 55.5t-141 -55.5t-47 -142.5v-181q0 -87 47 -142.5t141 -55.5t141 55.5t47 142.5v181z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="1277" 
d="M123 -356v1429h328v-112q28 51 110 94t184 43q110 0 198.5 -39.5t143.5 -106t84 -150.5t29 -177v-176q0 -93 -29 -177t-84 -151t-143.5 -106.5t-198.5 -39.5q-90 0 -165 34t-113 79v-444h-344zM467 612v-151q0 -97 55.5 -150t136.5 -53q99 0 148 55.5t49 153.5v139
q0 96 -49.5 152.5t-147.5 56.5q-81 0 -136.5 -53t-55.5 -150z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="1277" 
d="M1155 1073v-1429h-344v444q-40 -45 -115 -79t-164 -34q-110 0 -198 39.5t-143 106.5t-84 151t-29 177v176q0 93 29 177t84 150.5t143 106t198 39.5q102 0 184.5 -43t110.5 -94v112h328zM811 461v151q0 97 -56 150t-137 53q-98 0 -147 -56.5t-49 -152.5v-139
q0 -98 49 -153.5t147 -55.5q81 0 137 53t56 150z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="802" 
d="M758 1081v-338h-45q-124 0 -185 -58t-61 -193v-492h-344v1073h326v-188q23 113 94.5 154.5t197.5 41.5h17z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="1091" 
d="M1030 350q0 -163 -133 -269t-346 -106q-216 0 -344.5 91.5t-128.5 252.5v23h303v-14q0 -56 46.5 -85.5t125.5 -29.5q71 0 116.5 25t45.5 69q0 30 -16.5 47.5t-55.5 26.5l-297 68q-124 28 -188 101t-64 187q0 170 122.5 265.5t322.5 95.5q194 0 315 -93.5t121 -252.5v-19
h-297v19q0 108 -135 108q-129 0 -129 -88q0 -31 16 -47.5t51 -24.5l314 -75q111 -27 173 -97t62 -178z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="808" 
d="M190 1362h345v-289h206v-266h-206v-457q0 -71 77 -71h113v-279h-227q-151 0 -229.5 83.5t-78.5 233.5v490h-139v266h139v289z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="1230" 
d="M764 1073h344v-1073h-330v125q-41 -73 -118.5 -111.5t-180.5 -38.5q-177 0 -271.5 108.5t-94.5 297.5v692h344v-649q0 -75 37.5 -117.5t109.5 -42.5t116 38.5t44 125.5v645z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="1089" 
d="M715 0h-340l-365 1073h340l195 -721l194 721h340z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="1630" 
d="M963 1073l196 -651l121 651h334l-277 -1073h-311l-211 649l-211 -649h-311l-277 1073h334l121 -651l197 651h295z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="1134" 
d="M1096 1073l-355 -528l369 -545h-350l-193 287l-192 -287h-350l368 545l-354 528h346l182 -270l183 270h346z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="1083" 
d="M627 -356h-336l104 356l-385 1073h342l205 -725l172 725h344z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="1067" 
d="M985 0h-893v240l471 577h-452v256h860v-239l-473 -578h487v-256z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="890" 
d="M604 1174v-254q0 -104 -54 -168t-169 -88q115 -24 169 -88t54 -168v-254q0 -76 34.5 -111t109.5 -35h92v-282h-162q-220 0 -319 97t-99 302v252q0 145 -162 145h-57v283h57q162 0 162 145v252q0 205 99 302.5t319 97.5h162v-283h-92q-75 0 -109.5 -34.5t-34.5 -110.5z
" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="704" 
d="M500 -307h-295v1945h295v-1945z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="890" 
d="M287 920v254q0 76 -34.5 110.5t-109.5 34.5h-92v283h162q220 0 319 -97.5t99 -302.5v-252q0 -145 162 -145h57v-283h-57q-162 0 -162 -145v-252q0 -205 -99 -302t-319 -97h-162v282h92q75 0 109.5 35t34.5 111v254q0 104 54 168t169 88q-115 24 -169 88t-54 168z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="1359" 
d="M989 973l289 -99q-22 -86 -48 -150t-65 -119t-95.5 -84t-129.5 -29q-57 0 -121.5 22.5t-112 50t-101.5 50t-91 22.5q-27 0 -47 -9.5t-31.5 -21t-20.5 -35t-12.5 -38.5t-9.5 -44l-301 84q33 206 120.5 310t246.5 104q70 0 157.5 -36.5t159 -72.5t101.5 -36q18 0 32 3
t25 14t17 17.5t14.5 28t11 30t12.5 38.5z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="491" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="647" 
d="M143 553l82 420h197l82 -420v-553h-361v553zM516 1260q0 -82 -54 -136.5t-138 -54.5t-138.5 54.5t-54.5 136.5t54.5 136t138.5 54t138 -54t54 -136z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="1196" 
d="M1094 817h-320q-3 59 -47.5 98t-120.5 39q-83 0 -126.5 -49t-43.5 -137v-102q0 -88 43.5 -137.5t126.5 -49.5q76 0 120.5 39t47.5 98h320q-3 -138 -90 -245t-234 -148v-223h-340v225q-155 43 -241.5 159t-86.5 282v102q0 165 87 280t241 158v228h340v-226
q148 -41 234.5 -147t89.5 -244z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="1388" 
d="M694 823h363v-276h-363v-271h516v-276h-1087v276h227v271h-200v276h200v211q0 210 132 319t347 109q220 0 349 -116.5t129 -311.5v-45h-318v49q0 62 -36 102t-111 40q-148 0 -148 -142v-215z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="1306" 
d="M221 666v90q0 62 17.5 119.5t48.5 93.5l-174 278h344l69 -121q54 15 127 15t125 -15l72 121h344l-174 -276q67 -86 67 -215v-90q0 -136 -67 -215l174 -277h-344l-72 121q-55 -14 -125 -14q-72 0 -127 14l-69 -121h-344l174 277q-66 87 -66 215zM795 647v127
q0 56 -37.5 92.5t-104.5 36.5t-103 -36.5t-36 -92.5v-127q0 -57 36 -93t103 -36t104.5 36.5t37.5 92.5z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="1302" 
d="M156 250v254h321l-61 143h-260v254h151l-225 533h373l196 -592l197 592h373l-228 -533h154v-254h-262l-62 -143h324v-254h-324v-250h-344v250h-323z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="704" 
d="M500 1638v-798h-295v798h295zM500 -307h-295v799h295v-799z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="1290" 
d="M1188 573q0 -84 -37 -161t-90 -131q59 -71 59 -193q0 -161 -132.5 -268t-350.5 -107q-211 0 -348 102.5t-137 282.5v29h301v-14q0 -71 45.5 -110.5t138.5 -39.5q74 0 122 27.5t48 79.5q0 40 -23.5 61.5t-72.5 36.5l-328 106q-138 45 -204 121t-66 211q0 164 131 281
q-64 76 -64 198q0 181 125 279t348 98q226 0 351.5 -102.5t125.5 -286.5v-24h-296v28q0 135 -175 135q-165 0 -165 -104q0 -41 20.5 -61.5t75.5 -38.5l303 -101q145 -45 220 -123.5t75 -210.5zM545 729l-72 25q-14 -17 -21 -27.5t-15.5 -35.5t-8.5 -52q0 -42 19.5 -70.5
t68.5 -44.5l240 -78l69 -22q47 51 47 115q0 42 -23.5 69.5t-82.5 46.5z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="1044" 
d="M143 1346q0 68 47.5 113.5t122.5 45.5q73 0 119.5 -45.5t46.5 -113.5t-46.5 -114t-119.5 -46q-75 0 -122.5 45.5t-47.5 114.5zM565 1346q0 68 47.5 113.5t122.5 45.5q73 0 119.5 -45.5t46.5 -113.5t-46.5 -114t-119.5 -46q-75 0 -122.5 45.5t-47.5 114.5z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="1697" 
d="M102 717q0 152 55 288.5t151.5 236t237 158t302.5 58.5q164 0 304.5 -58.5t237 -158.5t151 -235.5t54.5 -288.5t-54.5 -289t-151 -236t-237 -158.5t-304.5 -58.5q-162 0 -302.5 58.5t-237 158.5t-151.5 236.5t-55 288.5zM1497 717q0 103 -28.5 200t-85 180t-133.5 145
t-181 97t-221 35q-145 0 -268 -54.5t-205 -145t-128 -209.5t-46 -248t46 -248t128 -210t205 -145.5t268 -54.5q146 0 270 54.5t206 145.5t127.5 210t45.5 248zM1169 803h-176q-18 125 -139 125q-72 0 -105.5 -44.5t-33.5 -115.5v-102q0 -71 33.5 -115.5t105.5 -44.5
q121 0 139 125h176q-6 -122 -93.5 -203.5t-221.5 -81.5q-81 0 -145 26t-103 71.5t-59.5 102.5t-20.5 122v98q0 88 34.5 159t110.5 116.5t183 45.5q134 0 221.5 -81t93.5 -203z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="890" 
d="M115 434v201h673v-201h-673zM776 745h-221v64q-26 -34 -78.5 -56t-116.5 -22q-120 0 -189 60.5t-69 158.5q0 110 74 165.5t199 55.5q106 0 170 -47v62q0 42 -26 65t-70 23q-66 0 -95 -45h-202q26 109 108 167t205 58q146 0 228.5 -78.5t82.5 -206.5v-424zM434 885
q47 0 79 21.5t32 54.5t-29 54t-78 21q-110 0 -110 -75q0 -35 28.5 -55.5t77.5 -20.5z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="1456" 
d="M754 1016l-322 -447l322 -446h-400l-323 446l323 447h400zM1374 1016l-321 -447l321 -446h-399l-324 446l324 447h399z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="1187" 
d="M92 592v291h963v-650h-324v359h-639z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="1081" 
d="M82 1012q0 121 53.5 221.5t159.5 162.5t246 62q105 0 193 -36t145 -97t88.5 -142t31.5 -171q0 -88 -31.5 -168.5t-88.5 -142.5t-145 -99t-193 -37q-139 0 -245.5 63.5t-160 164.5t-53.5 219zM166 1012q0 -160 104.5 -267.5t270.5 -107.5t270 107.5t104 267.5
q0 161 -104 267.5t-270 106.5t-270.5 -106.5t-104.5 -267.5zM608 829l-61 117h-51v-117h-105v394h180q70 0 109 -38.5t39 -101.5q0 -80 -68 -118l78 -136h-121zM496 1028h57q61 0 61 55q0 54 -61 54h-57v-109z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="989" 
d="M846 1225h-703v260h703v-260z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="831" 
d="M416 1454q148 0 241 -94.5t93 -233.5t-93 -233t-241 -94t-241 94t-93 233t93 233.5t241 94.5zM416 1303q-76 0 -120 -50.5t-44 -126.5t44 -126t120 -50t120 50t44 126t-44 126.5t-120 50.5z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="1249" 
d="M1106 0h-963v270h322v322h-322v291h322v358h323v-358h318v-291h-318v-322h318v-270z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="854" 
d="M340 1454h-217v14q0 123 85 195t216 72q138 0 227.5 -71.5t89.5 -188.5q0 -80 -44.5 -139.5t-125.5 -112.5l-141 -93h307v-192h-624v176l331 270q3 2 9.5 8t9 8.5t8.5 7.5t8 7.5l6 7.5t6 8.5t4.5 8.5t3.5 9t2 9.5t1 11.5q0 31 -22.5 49.5t-57.5 18.5q-39 0 -60.5 -20
t-21.5 -56v-8z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="854" 
d="M156 1536v178h536v-178l-155 -121q86 -12 145 -68t59 -155q0 -114 -96 -191t-233 -77q-79 0 -148 29.5t-115 91.5t-47 143h207q3 -41 32.5 -64.5t74.5 -23.5q44 0 71 23t27 59q0 31 -23.5 51t-64.5 20h-147v164l135 119h-258z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="851" 
d="M485 1184h-342l140 327h426z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="1241" 
d="M774 1073h344v-1073h-330v125q-41 -73 -118.5 -111.5t-180.5 -38.5h-22v-331h-344v1429h344v-649q0 -75 37.5 -117.5t109.5 -42.5t116 38.5t44 125.5v645z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="1294" 
d="M1141 -356h-232v1583h-217v-930q-127 0 -235.5 36.5t-190 105.5t-128 177t-46.5 244q0 274 161 424t439 150h449v-1790z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="589" 
d="M113 586q0 82 54 136t138 54t138.5 -54t54.5 -136t-54.5 -136.5t-138.5 -54.5t-138 54.5t-54 136.5z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="684" 
d="M541 -215q0 -74 -67.5 -120t-180.5 -46q-89 0 -150 12v125q54 -8 82 -8q72 0 72 43q0 19 -18 27.5t-54 11.5l-55 6l39 164h151l-12 -68l35 -4q74 -8 116 -43.5t42 -99.5z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="671" 
d="M268 938v551l-186 -49v200l229 74h197v-776h-240z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="884" 
d="M782 1130v-86q0 -141 -92 -228t-248 -87t-248 87t-92 228v86q0 141 92 228.5t248 87.5t248 -87.5t92 -228.5zM123 434v201h641v-201h-641zM563 1036v103q0 127 -121 127q-120 0 -120 -127v-103q0 -127 120 -127q121 0 121 127z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="1456" 
d="M1024 569l-322 447h400l323 -447l-323 -446h-400zM403 569l-321 447h399l324 -447l-324 -446h-399z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="1751" 
d="M346 0l735 1434h246l-735 -1434h-246zM1186 776h221l-225 -461h149v144h219v-144h99v-178h-99v-137h-219v137h-387v170zM289 657v551l-187 -49v201l230 74h196v-777h-239z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="1777" 
d="M1264 516h-217v14q0 123 85 195t216 72q138 0 227.5 -71.5t89.5 -188.5q0 -80 -44.5 -139.5t-125.5 -112.5l-141 -92h307v-193h-625v176l332 270q40 35 48 51q9 15 9 35q0 31 -22 49.5t-57 18.5q-39 0 -60.5 -20t-21.5 -56v-8zM346 0l735 1434h246l-735 -1434h-246z
M289 657v551l-187 -49v201l230 74h196v-777h-239z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="1843" 
d="M166 1255v179h536v-179l-155 -120q86 -12 145.5 -68.5t59.5 -155.5q0 -114 -96.5 -191t-233.5 -77q-123 0 -215.5 71.5t-93.5 192.5h206q3 -41 32.5 -64.5t74.5 -23.5q44 0 71 23t27 59q0 31 -24 51.5t-64 20.5h-147v164l135 118h-258zM438 0l736 1434h245l-735 -1434
h-246zM1278 776h221l-225 -461h149v144h219v-144h99v-178h-99v-137h-219v137h-387v170z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="1099" 
d="M735 967v-95q0 -210 -125 -301l-88 -65q-8 -6 -28 -20t-26 -19t-20 -16.5t-19 -19t-12 -19t-9 -23.5t-2 -27q0 -52 43.5 -83t115.5 -31q168 0 168 141v41h316v-45q0 -108 -36 -188.5t-102.5 -129.5t-154 -72.5t-197.5 -23.5q-141 0 -253.5 47t-178.5 137t-66 207
q0 109 41 179t119 125l103 71q57 39 81.5 78t24.5 105v47h305zM774 1260q0 -82 -54 -136.5t-138 -54.5t-138.5 54.5t-54.5 136.5t54.5 136t138.5 54t138 -54t54 -136z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="1466" 
d="M1073 0l-82 266h-518l-80 -266h-352l512 1434h360l512 -1434h-352zM733 1116l-174 -567h346zM895 1536h-348l-211 303h422z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="1466" 
d="M1073 0l-82 266h-518l-80 -266h-352l512 1434h360l512 -1434h-352zM733 1116l-174 -567h346zM924 1536h-349l138 303h422z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="1466" 
d="M1073 0l-82 266h-518l-80 -266h-352l512 1434h360l512 -1434h-352zM733 1116l-174 -567h346zM903 1839l244 -303h-324l-90 119l-90 -119h-324l244 303h340z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="1466" 
d="M1073 0l-82 266h-518l-80 -266h-352l512 1434h360l512 -1434h-352zM733 1116l-174 -567h346zM922 1536q-56 0 -176 31.5t-166 31.5q-31 0 -49 -18.5t-29 -56.5l-199 51q31 156 85 222.5t173 66.5q74 0 199.5 -32t150.5 -32q12 0 21.5 2t17.5 8t12.5 9t10.5 13.5t7.5 13.5
t6.5 16t6 14l197 -59q-29 -140 -88 -210.5t-180 -70.5z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="1466" 
d="M1073 0l-82 266h-518l-80 -266h-352l512 1434h360l512 -1434h-352zM733 1116l-174 -567h346zM332 1696q0 68 47.5 113.5t122.5 45.5q73 0 119.5 -45.5t46.5 -113.5t-46.5 -114t-119.5 -46q-75 0 -122.5 45.5t-47.5 114.5zM799 1696q0 68 47.5 113.5t122.5 45.5
q73 0 119.5 -45.5t46.5 -113.5t-46.5 -114t-119.5 -46q-75 0 -122.5 45.5t-47.5 114.5z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="1466" 
d="M961 1518q0 -64 -37 -113l501 -1405h-352l-82 266h-518l-80 -266h-352l502 1403q-39 48 -39 115q0 82 63 140t166 58t165.5 -58t62.5 -140zM733 1116l-174 -567h346zM641 1518q0 -37 25 -60.5t67 -23.5t68 23.5t26 60.5q0 38 -25.5 61t-68.5 23t-67.5 -23t-24.5 -61z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="2234" 
d="M1096 0v266h-551l-166 -266h-379l950 1434h1184v-293h-686v-269h625v-292h-625v-287h688v-293h-1040zM723 549h373v592z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="1449" 
d="M997 -215q0 -74 -67 -120t-180 -46q-89 0 -150 12v125q54 -8 82 -8q72 0 72 43q0 19 -18 27.5t-54 11.5l-55 6l32 141q-185 17 -313 106t-188 226.5t-60 313.5v188q0 142 41 259.5t120.5 205.5t206 137t288.5 49q138 0 253 -44.5t191 -120.5t118 -177t44 -215h-326
q-5 115 -77.5 190.5t-206.5 75.5q-153 0 -226 -92t-73 -252v-221q0 -160 73 -252t226 -92q134 0 206.5 75.5t77.5 190.5h326q-2 -108 -40 -204.5t-106.5 -171.5t-172.5 -122.5t-230 -56.5l-6 -41l35 -4q73 -8 115 -43.5t42 -99.5z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="1282" 
d="M143 0v1434h1039v-293h-686v-269h624v-292h-624v-287h688v-293h-1041zM868 1536h-348l-211 303h422z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="1282" 
d="M143 0v1434h1039v-293h-686v-269h624v-292h-624v-287h688v-293h-1041zM811 1536h-348l137 303h422z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="1282" 
d="M143 0v1434h1039v-293h-686v-269h624v-292h-624v-287h688v-293h-1041zM834 1839l243 -303h-323l-90 119l-91 -119h-323l244 303h340z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="1282" 
d="M143 0v1434h1039v-293h-686v-269h624v-292h-624v-287h688v-293h-1041zM262 1696q0 68 47.5 113.5t122.5 45.5q73 0 119.5 -45.5t46.5 -113.5t-46.5 -114t-119.5 -46q-75 0 -122.5 45.5t-47.5 114.5zM729 1696q0 68 47.5 113.5t122.5 45.5q73 0 119.5 -45.5t46.5 -113.5
t-46.5 -114t-119.5 -46q-75 0 -122.5 45.5t-47.5 114.5z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="659" 
d="M506 1434v-1434h-352v1434h352zM506 1536h-348l-211 303h422z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="659" 
d="M506 1434v-1434h-352v1434h352zM504 1536h-348l137 303h422z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="659" 
d="M506 1434v-1434h-352v1434h352zM500 1839l243 -303h-323l-90 119l-90 -119h-324l244 303h340z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="659" 
d="M506 1434v-1434h-352v1434h352zM-72 1696q0 68 47.5 113.5t122.5 45.5q73 0 119.5 -45.5t46.5 -113.5t-46.5 -114t-119.5 -46q-75 0 -122.5 45.5t-47.5 114.5zM395 1696q0 68 47.5 113.5t122.5 45.5q73 0 119.5 -45.5t46.5 -113.5t-46.5 -114t-119.5 -46
q-75 0 -122.5 45.5t-47.5 114.5z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="1529" 
d="M61 571v293h146v570h627q193 0 329.5 -74.5t202.5 -201.5t66 -294v-295q0 -167 -66 -294t-202.5 -201t-329.5 -74h-627v571h-146zM1079 567v299q0 279 -309 279h-213v-281h277v-293h-277v-282h213q309 0 309 278z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="1503" 
d="M1360 0h-336l-537 838v-838h-344v1434h334l539 -838v838h344v-1434zM922 1536q-56 0 -176 31.5t-166 31.5q-31 0 -49 -18.5t-29 -56.5l-199 51q31 156 85 222.5t173 66.5q74 0 199.5 -32t150.5 -32q12 0 21.5 2t17.5 8t12.5 9t10.5 13.5t7.5 13.5t6.5 16t6 14l197 -59
q-29 -140 -88 -210.5t-180 -70.5z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="1511" 
d="M1413 625q0 -142 -42 -260.5t-123 -206.5t-207 -137.5t-285 -49.5t-285 49.5t-207 137.5t-123.5 206.5t-42.5 260.5v184q0 142 42.5 260.5t123.5 206.5t207 137t285 49t285 -49t207 -137t123 -206.5t42 -260.5v-184zM1061 827q0 158 -77.5 250t-227.5 92t-227.5 -92
t-77.5 -250v-221q0 -158 77.5 -250t227.5 -92t227.5 92t77.5 250v221zM922 1536h-349l-211 303h422z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="1511" 
d="M1413 625q0 -142 -42 -260.5t-123 -206.5t-207 -137.5t-285 -49.5t-285 49.5t-207 137.5t-123.5 206.5t-42.5 260.5v184q0 142 42.5 260.5t123.5 206.5t207 137t285 49t285 -49t207 -137t123 -206.5t42 -260.5v-184zM1061 827q0 158 -77.5 250t-227.5 92t-227.5 -92
t-77.5 -250v-221q0 -158 77.5 -250t227.5 -92t227.5 92t77.5 250v221zM922 1536h-349l138 303h422z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="1511" 
d="M1413 625q0 -142 -42 -260.5t-123 -206.5t-207 -137.5t-285 -49.5t-285 49.5t-207 137.5t-123.5 206.5t-42.5 260.5v184q0 142 42.5 260.5t123.5 206.5t207 137t285 49t285 -49t207 -137t123 -206.5t42 -260.5v-184zM1061 827q0 158 -77.5 250t-227.5 92t-227.5 -92
t-77.5 -250v-221q0 -158 77.5 -250t227.5 -92t227.5 92t77.5 250v221zM926 1839l243 -303h-323l-90 119l-90 -119h-324l244 303h340z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="1511" 
d="M1413 625q0 -142 -42 -260.5t-123 -206.5t-207 -137.5t-285 -49.5t-285 49.5t-207 137.5t-123.5 206.5t-42.5 260.5v184q0 142 42.5 260.5t123.5 206.5t207 137t285 49t285 -49t207 -137t123 -206.5t42 -260.5v-184zM1061 827q0 158 -77.5 250t-227.5 92t-227.5 -92
t-77.5 -250v-221q0 -158 77.5 -250t227.5 -92t227.5 92t77.5 250v221zM930 1536q-56 0 -176 31.5t-166 31.5q-31 0 -49 -18.5t-29 -56.5l-199 51q31 156 85 222.5t173 66.5q74 0 200 -32t151 -32q12 0 21.5 2t17 7.5t12.5 9.5t10 13.5t7.5 13.5t7 15.5t5.5 14.5l197 -59
q-29 -140 -88 -210.5t-180 -70.5z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="1511" 
d="M1413 625q0 -142 -42 -260.5t-123 -206.5t-207 -137.5t-285 -49.5t-285 49.5t-207 137.5t-123.5 206.5t-42.5 260.5v184q0 142 42.5 260.5t123.5 206.5t207 137t285 49t285 -49t207 -137t123 -206.5t42 -260.5v-184zM1061 827q0 158 -77.5 250t-227.5 92t-227.5 -92
t-77.5 -250v-221q0 -158 77.5 -250t227.5 -92t227.5 92t77.5 250v221zM354 1696q0 68 47.5 113.5t122.5 45.5q73 0 119.5 -45.5t46.5 -113.5t-46.5 -114t-119.5 -46q-75 0 -122.5 45.5t-47.5 114.5zM821 1696q0 68 47.5 113.5t122.5 45.5q73 0 119.5 -45.5t46.5 -113.5
t-46.5 -114t-119.5 -46q-75 0 -122.5 45.5t-47.5 114.5z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="1277" 
d="M928 227l-289 289l-289 -289l-217 217l289 289l-289 289l217 217l289 -289l289 289l217 -217l-289 -289l289 -289z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="1511" 
d="M1413 625q0 -142 -42 -260.5t-123 -206.5t-207 -137.5t-285 -49.5q-185 0 -322 64l-90 -137h-246l168 258q-168 174 -168 469v184q0 142 42.5 260.5t123.5 206.5t207 137t285 49q185 0 321 -65l90 139h246l-168 -258q168 -177 168 -469v-184zM451 606q0 -76 18 -141
l438 672q-69 32 -151 32q-150 0 -227.5 -92t-77.5 -250v-221zM1061 827q0 75 -19 140l-438 -670q59 -33 152 -33q150 0 227.5 92t77.5 250v221z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="1486" 
d="M1001 526v908h353v-899q0 -254 -166 -409t-445 -155t-444.5 155t-165.5 409v899h352v-908q0 -127 69.5 -194.5t188.5 -67.5t188.5 67.5t69.5 194.5zM954 1536h-348l-211 303h422z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="1486" 
d="M1001 526v908h353v-899q0 -254 -166 -409t-445 -155t-444.5 155t-165.5 409v899h352v-908q0 -127 69.5 -194.5t188.5 -67.5t188.5 67.5t69.5 194.5zM883 1536h-348l137 303h422z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="1486" 
d="M1001 526v908h353v-899q0 -254 -166 -409t-445 -155t-444.5 155t-165.5 409v899h352v-908q0 -127 69.5 -194.5t188.5 -67.5t188.5 67.5t69.5 194.5zM913 1839l244 -303h-323l-91 119l-90 -119h-323l243 303h340z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="1486" 
d="M1001 526v908h353v-899q0 -254 -166 -409t-445 -155t-444.5 155t-165.5 409v899h352v-908q0 -127 69.5 -194.5t188.5 -67.5t188.5 67.5t69.5 194.5zM342 1696q0 68 47.5 113.5t122.5 45.5q73 0 119.5 -45.5t46.5 -113.5t-46.5 -114t-119.5 -46q-75 0 -122.5 45.5
t-47.5 114.5zM809 1696q0 68 47.5 113.5t122.5 45.5q73 0 119.5 -45.5t46.5 -113.5t-46.5 -114t-119.5 -46q-75 0 -122.5 45.5t-47.5 114.5z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="1339" 
d="M1327 1434l-481 -932v-502h-352v502l-482 932h383l275 -596l274 596h383zM838 1536h-349l138 303h422z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="1361" 
d="M756 233h-260v-233h-353v1434h353v-199h260q254 0 405.5 -135t151.5 -361t-153.5 -366t-403.5 -140zM961 739q0 96 -56 149.5t-170 53.5h-239v-416h239q113 0 169.5 58t56.5 155z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="1292" 
d="M631 1227q-78 0 -121 -42t-43 -126v-1059h-344v1042q0 226 134.5 343.5t371.5 117.5q235 0 377.5 -112.5t142.5 -298.5q0 -111 -48 -196.5t-134 -135.5q122 -34 193 -119.5t71 -224.5q0 -203 -137.5 -309.5t-388.5 -106.5h-66v272h51q197 0 197 170q0 71 -51 112.5
t-140 41.5h-59v260q77 6 122.5 61t45.5 134q0 83 -43 129.5t-131 46.5z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="1239" 
d="M1126 0h-329v90q-46 -54 -130 -84.5t-188 -30.5q-182 0 -287.5 94t-105.5 244q0 162 112 248t304 86q91 0 164 -22.5t116 -57.5v125q0 62 -39.5 100.5t-109.5 38.5q-114 0 -156 -77h-313q34 161 162 252.5t317 91.5q227 0 355 -115.5t128 -314.5v-668zM594 195
q79 0 133.5 35.5t54.5 88.5q0 54 -52.5 89.5t-131.5 35.5q-90 0 -140 -31.5t-50 -93.5q0 -60 51 -92t135 -32zM817 1184h-342l-223 327h426z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="1239" 
d="M1126 0h-329v90q-46 -54 -130 -84.5t-188 -30.5q-182 0 -287.5 94t-105.5 244q0 162 112 248t304 86q91 0 164 -22.5t116 -57.5v125q0 62 -39.5 100.5t-109.5 38.5q-114 0 -156 -77h-313q34 161 162 252.5t317 91.5q227 0 355 -115.5t128 -314.5v-668zM594 195
q79 0 133.5 35.5t54.5 88.5q0 54 -52.5 89.5t-131.5 35.5q-90 0 -140 -31.5t-50 -93.5q0 -60 51 -92t135 -32zM805 1184h-342l139 327h426z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="1239" 
d="M1126 0h-329v90q-46 -54 -130 -84.5t-188 -30.5q-182 0 -287.5 94t-105.5 244q0 162 112 248t304 86q91 0 164 -22.5t116 -57.5v125q0 62 -39.5 100.5t-109.5 38.5q-114 0 -156 -77h-313q34 161 162 252.5t317 91.5q227 0 355 -115.5t128 -314.5v-668zM594 195
q79 0 133.5 35.5t54.5 88.5q0 54 -52.5 89.5t-131.5 35.5q-90 0 -140 -31.5t-50 -93.5q0 -60 51 -92t135 -32zM834 1511l231 -327h-311l-86 133l-86 -133h-312l232 327h332z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="1239" 
d="M1126 0h-329v90q-46 -54 -130 -84.5t-188 -30.5q-182 0 -287.5 94t-105.5 244q0 162 112 248t304 86q91 0 164 -22.5t116 -57.5v125q0 62 -39.5 100.5t-109.5 38.5q-114 0 -156 -77h-313q34 161 162 252.5t317 91.5q227 0 355 -115.5t128 -314.5v-668zM594 195
q79 0 133.5 35.5t54.5 88.5q0 54 -52.5 89.5t-131.5 35.5q-90 0 -140 -31.5t-50 -93.5q0 -60 51 -92t135 -32zM823 1184q-46 0 -154.5 32.5t-154.5 32.5q-30 0 -46.5 -18t-31.5 -60l-190 52q22 141 78 214.5t168 73.5q62 0 176 -34.5t135 -34.5q24 0 41 13.5t23 26.5t16 38
l188 -60q-12 -50 -23.5 -86t-31.5 -74t-45 -61.5t-62.5 -39t-85.5 -15.5z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="1239" 
d="M1126 0h-329v90q-46 -54 -130 -84.5t-188 -30.5q-182 0 -287.5 94t-105.5 244q0 162 112 248t304 86q91 0 164 -22.5t116 -57.5v125q0 62 -39.5 100.5t-109.5 38.5q-114 0 -156 -77h-313q34 161 162 252.5t317 91.5q227 0 355 -115.5t128 -314.5v-668zM594 195
q79 0 133.5 35.5t54.5 88.5q0 54 -52.5 89.5t-131.5 35.5q-90 0 -140 -31.5t-50 -93.5q0 -60 51 -92t135 -32zM276 1346q0 68 47.5 113.5t122.5 45.5q73 0 119.5 -45.5t46.5 -113.5t-46.5 -114t-119.5 -46q-75 0 -122.5 45.5t-47.5 114.5zM698 1346q0 68 47.5 113.5
t122.5 45.5q73 0 119.5 -45.5t46.5 -113.5t-46.5 -114t-119.5 -46q-75 0 -122.5 45.5t-47.5 114.5z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="1239" 
d="M1126 0h-329v90q-46 -54 -130 -84.5t-188 -30.5q-182 0 -287.5 94t-105.5 244q0 162 112 248t304 86q91 0 164 -22.5t116 -57.5v125q0 62 -39.5 100.5t-109.5 38.5q-114 0 -156 -77h-313q34 161 162 252.5t317 91.5q227 0 355 -115.5t128 -314.5v-668zM594 195
q79 0 133.5 35.5t54.5 88.5q0 54 -52.5 89.5t-131.5 35.5q-90 0 -140 -31.5t-50 -93.5q0 -60 51 -92t135 -32zM881 1382q0 -82 -62.5 -140t-165.5 -58t-166 58t-63 140t63 140.5t166 58.5t165.5 -58.5t62.5 -140.5zM561 1382q0 -37 25 -60.5t67 -23.5t68.5 23.5t26.5 60.5
q0 38 -26 61t-69 23t-67.5 -23t-24.5 -61z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="1890" 
d="M1794 428h-668v-25q0 -82 48.5 -134t136.5 -52q67 0 111 29t63 73h303q-28 -150 -161.5 -247t-315.5 -97q-255 0 -381 144q-52 -64 -161 -104t-232 -40q-67 0 -129.5 10.5t-122 36t-103 63.5t-70 97t-26.5 133q0 109 57.5 186.5t149.5 112.5t209 35q175 0 280 -74v105
q0 69 -40.5 110t-112.5 41q-107 0 -152 -79h-315q40 169 164 257.5t317 88.5q94 0 178.5 -30.5t136.5 -78.5q57 50 146 79.5t199 29.5q119 0 214 -37.5t154.5 -103t91 -150.5t31.5 -182v-197zM588 195q84 0 139 32t55 88q0 55 -50 92t-132 37q-197 0 -197 -125q0 -58 50 -91
t135 -33zM1126 651v-22h353v39q0 84 -43.5 135t-130.5 51q-91 0 -135 -53.5t-44 -149.5z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="1179" 
d="M844 -215q0 -74 -67.5 -120t-180.5 -46q-88 0 -150 12v125q54 -8 82 -8q72 0 72 43q0 19 -18 27.5t-54 11.5l-55 6l35 146q-205 26 -317.5 156.5t-112.5 328.5v139q0 220 138.5 356t385.5 136q220 0 359.5 -123t144.5 -316h-311q-5 71 -53 118.5t-134 47.5
q-93 0 -139.5 -53t-46.5 -145v-181q0 -92 46.5 -145t139.5 -53q86 0 134 47.5t53 118.5h311q-5 -180 -127 -300t-320 -137l-8 -45l35 -4q74 -8 116 -43.5t42 -99.5z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="1163" 
d="M1077 428h-667v-25q0 -83 48 -134.5t136 -51.5q67 0 111 29t63 73h303q-28 -150 -161.5 -247t-315.5 -97q-235 0 -375.5 129.5t-140.5 356.5v133q0 229 134 366.5t374 137.5q119 0 214 -37.5t154.5 -103t91 -150.5t31.5 -182v-197zM410 651v-22h352v39q0 84 -43.5 135
t-130.5 51q-91 0 -134.5 -53.5t-43.5 -149.5zM758 1184h-342l-223 327h425z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="1163" 
d="M1077 428h-667v-25q0 -83 48 -134.5t136 -51.5q67 0 111 29t63 73h303q-28 -150 -161.5 -247t-315.5 -97q-235 0 -375.5 129.5t-140.5 356.5v133q0 229 134 366.5t374 137.5q119 0 214 -37.5t154.5 -103t91 -150.5t31.5 -182v-197zM410 651v-22h352v39q0 84 -43.5 135
t-130.5 51q-91 0 -134.5 -53.5t-43.5 -149.5zM752 1184h-342l139 327h426z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="1163" 
d="M1077 428h-667v-25q0 -83 48 -134.5t136 -51.5q67 0 111 29t63 73h303q-28 -150 -161.5 -247t-315.5 -97q-235 0 -375.5 129.5t-140.5 356.5v133q0 229 134 366.5t374 137.5q119 0 214 -37.5t154.5 -103t91 -150.5t31.5 -182v-197zM410 651v-22h352v39q0 84 -43.5 135
t-130.5 51q-91 0 -134.5 -53.5t-43.5 -149.5zM754 1511l231 -327h-311l-86 133l-86 -133h-312l232 327h332z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="1163" 
d="M1077 428h-667v-25q0 -83 48 -134.5t136 -51.5q67 0 111 29t63 73h303q-28 -150 -161.5 -247t-315.5 -97q-235 0 -375.5 129.5t-140.5 356.5v133q0 229 134 366.5t374 137.5q119 0 214 -37.5t154.5 -103t91 -150.5t31.5 -182v-197zM410 651v-22h352v39q0 84 -43.5 135
t-130.5 51q-91 0 -134.5 -53.5t-43.5 -149.5zM211 1346q0 68 47.5 113.5t122.5 45.5q73 0 119.5 -45.5t46.5 -113.5t-46.5 -114t-119.5 -46q-75 0 -122.5 45.5t-47.5 114.5zM633 1346q0 68 47.5 113.5t122.5 45.5q73 0 119.5 -45.5t46.5 -113.5t-46.5 -114t-119.5 -46
q-75 0 -122.5 45.5t-47.5 114.5z" />
    <glyph glyph-name="igrave" unicode="&#xec;" 
d="M477 0h-344v1073h344v-1073zM477 1184h-342l-223 327h426z" />
    <glyph glyph-name="iacute" unicode="&#xed;" 
d="M477 0h-344v1073h344v-1073zM483 1184h-342l140 327h426z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" 
d="M477 0h-344v1073h344v-1073zM471 1511l231 -327h-311l-86 133l-86 -133h-311l231 327h332z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" 
d="M477 0h-344v1073h344v-1073zM-74 1346q0 68 47.5 113.5t122.5 45.5q73 0 119.5 -45.5t46.5 -113.5t-46.5 -114t-119.5 -46q-75 0 -122.5 45.5t-47.5 114.5zM348 1346q0 68 47.5 113.5t122.5 45.5q73 0 119.5 -45.5t46.5 -113.5t-46.5 -114t-119.5 -46q-75 0 -122.5 45.5
t-47.5 114.5z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="1196" 
d="M301 1067l-70 182l179 68q-115 88 -267 178h406q71 -40 125 -78l225 86l72 -182l-129 -49q128 -133 202 -298t74 -351v-160q0 -213 -147.5 -352.5t-382.5 -139.5q-228 0 -369 136.5t-141 343.5v127q0 218 117.5 342t314.5 124q141 0 229 -81q-62 104 -164 206zM598 793
q-184 0 -184 -209v-164q0 -81 47.5 -131.5t132.5 -50.5q91 0 139.5 50t48.5 134v164q0 207 -184 207z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="1241" 
d="M467 0h-344v1073h330v-125q41 73 118.5 111.5t180.5 38.5q182 0 279 -108.5t97 -297.5v-692h-344v639q0 75 -39.5 117.5t-115.5 42.5q-73 0 -117.5 -39t-44.5 -125v-635zM791 1184q-46 0 -155 32.5t-155 32.5q-30 0 -46.5 -18t-31.5 -60l-190 52q22 141 78 214.5
t168 73.5q63 0 177 -34.5t134 -34.5q24 0 41 13.5t23 26.5t16 38l188 -60q-15 -64 -31 -106.5t-43.5 -85t-70.5 -63.5t-102 -21z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="1200" 
d="M1122 608v-143q0 -218 -138.5 -354t-383.5 -136t-383.5 136t-138.5 354v143q0 218 138.5 354t383.5 136t383.5 -136t138.5 -354zM788 627q0 87 -47 142.5t-141 55.5t-141 -55.5t-47 -142.5v-181q0 -87 47 -142.5t141 -55.5t141 55.5t47 142.5v181zM770 1184h-342
l-223 327h426z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="1200" 
d="M1122 608v-143q0 -218 -138.5 -354t-383.5 -136t-383.5 136t-138.5 354v143q0 218 138.5 354t383.5 136t383.5 -136t138.5 -354zM788 627q0 87 -47 142.5t-141 55.5t-141 -55.5t-47 -142.5v-181q0 -87 47 -142.5t141 -55.5t141 55.5t47 142.5v181zM758 1184h-342l139 327
h426z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="1200" 
d="M1122 608v-143q0 -218 -138.5 -354t-383.5 -136t-383.5 136t-138.5 354v143q0 218 138.5 354t383.5 136t383.5 -136t138.5 -354zM788 627q0 87 -47 142.5t-141 55.5t-141 -55.5t-47 -142.5v-181q0 -87 47 -142.5t141 -55.5t141 55.5t47 142.5v181zM766 1511l231 -327
h-311l-86 133l-86 -133h-311l231 327h332z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="1200" 
d="M1122 608v-143q0 -218 -138.5 -354t-383.5 -136t-383.5 136t-138.5 354v143q0 218 138.5 354t383.5 136t383.5 -136t138.5 -354zM788 627q0 87 -47 142.5t-141 55.5t-141 -55.5t-47 -142.5v-181q0 -87 47 -142.5t141 -55.5t141 55.5t47 142.5v181zM766 1184
q-46 0 -154.5 32.5t-154.5 32.5q-30 0 -46.5 -18t-31.5 -60l-191 52q22 141 78 214.5t168 73.5q62 0 176 -34.5t135 -34.5q24 0 41 13.5t23 26.5t16 38l189 -60q-12 -50 -23.5 -86t-31.5 -74t-45 -61.5t-62.5 -39t-85.5 -15.5z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="1200" 
d="M1122 608v-143q0 -218 -138.5 -354t-383.5 -136t-383.5 136t-138.5 354v143q0 218 138.5 354t383.5 136t383.5 -136t138.5 -354zM788 627q0 87 -47 142.5t-141 55.5t-141 -55.5t-47 -142.5v-181q0 -87 47 -142.5t141 -55.5t141 55.5t47 142.5v181zM221 1346
q0 68 47.5 113.5t122.5 45.5q73 0 119.5 -45.5t46.5 -113.5t-46.5 -114t-119.5 -46q-75 0 -122.5 45.5t-47.5 114.5zM643 1346q0 68 47.5 113.5t122.5 45.5q73 0 119.5 -45.5t46.5 -113.5t-46.5 -114t-119.5 -46q-75 0 -122.5 45.5t-47.5 114.5z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="1228" 
d="M133 592v291h963v-291h-963zM438 313q0 74 50 124t126 50t126.5 -50t50.5 -124t-50.5 -124t-126.5 -50t-126 50t-50 124zM438 1161q0 74 50 124t126 50t126.5 -50t50.5 -124t-50.5 -124t-126.5 -50t-126 50t-50 124z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="1200" 
d="M1122 608v-143q0 -218 -138.5 -354t-383.5 -136q-145 0 -258 52l-88 -129h-176l143 208q-143 134 -143 359v143q0 218 138.5 354t383.5 136q146 0 260 -54l88 132h174l-143 -211q143 -131 143 -357zM412 446q0 -21 4 -53l280 412q-45 20 -96 20q-94 0 -141 -55.5
t-47 -142.5v-181zM788 627q0 23 -4 51l-278 -410q36 -20 94 -20q94 0 141 55.5t47 142.5v181z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="1230" 
d="M764 1073h344v-1073h-330v125q-41 -73 -118.5 -111.5t-180.5 -38.5q-177 0 -271.5 108.5t-94.5 297.5v692h344v-649q0 -75 37.5 -117.5t109.5 -42.5t116 38.5t44 125.5v645zM815 1184h-342l-223 327h426z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="1230" 
d="M764 1073h344v-1073h-330v125q-41 -73 -118.5 -111.5t-180.5 -38.5q-177 0 -271.5 108.5t-94.5 297.5v692h344v-649q0 -75 37.5 -117.5t109.5 -42.5t116 38.5t44 125.5v645zM784 1184h-342l140 327h426z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="1230" 
d="M764 1073h344v-1073h-330v125q-41 -73 -118.5 -111.5t-180.5 -38.5q-177 0 -271.5 108.5t-94.5 297.5v692h344v-649q0 -75 37.5 -117.5t109.5 -42.5t116 38.5t44 125.5v645zM776 1511l232 -327h-312l-86 133l-86 -133h-311l231 327h332z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="1230" 
d="M764 1073h344v-1073h-330v125q-41 -73 -118.5 -111.5t-180.5 -38.5q-177 0 -271.5 108.5t-94.5 297.5v692h344v-649q0 -75 37.5 -117.5t109.5 -42.5t116 38.5t44 125.5v645zM231 1346q0 68 47.5 113.5t122.5 45.5q73 0 119.5 -45.5t46.5 -113.5t-46.5 -114t-119.5 -46
q-75 0 -122.5 45.5t-47.5 114.5zM653 1346q0 68 47.5 113.5t122.5 45.5q73 0 119.5 -45.5t46.5 -113.5t-46.5 -114t-119.5 -46q-75 0 -122.5 45.5t-47.5 114.5z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="1083" 
d="M627 -356h-336l104 356l-385 1073h342l205 -725l172 725h344zM713 1184h-342l139 327h426z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="1277" 
d="M123 -356v1851h344v-512q38 46 113 80.5t165 34.5q110 0 198.5 -39.5t143.5 -106t84 -150.5t29 -177v-176q0 -93 -29 -177t-84 -151t-143.5 -106.5t-198.5 -39.5q-90 0 -165 34t-113 79v-444h-344zM467 623v-162q0 -97 55.5 -150t136.5 -53q99 0 148 55.5t49 153.5v139
q0 96 -49.5 152.5t-147.5 56.5q-80 0 -136 -50.5t-56 -141.5z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="1083" 
d="M627 -356h-336l104 356l-385 1073h342l205 -725l172 725h344zM172 1346q0 68 47.5 113.5t122.5 45.5q73 0 119.5 -45.5t46.5 -113.5t-46.5 -114t-119.5 -46q-75 0 -122.5 45.5t-47.5 114.5zM594 1346q0 68 47.5 113.5t122.5 45.5q73 0 119.5 -45.5t46.5 -113.5
t-46.5 -114t-119.5 -46q-75 0 -122.5 45.5t-47.5 114.5z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="1466" 
d="M1073 0l-82 266h-518l-80 -266h-352l512 1434h360l512 -1434h-352zM733 1116l-174 -567h346zM1112 1554h-756v267h756v-267z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="1239" 
d="M1126 0h-329v90q-46 -54 -130 -84.5t-188 -30.5q-182 0 -287.5 94t-105.5 244q0 162 112 248t304 86q91 0 164 -22.5t116 -57.5v125q0 62 -39.5 100.5t-109.5 38.5q-114 0 -156 -77h-313q34 161 162 252.5t317 91.5q227 0 355 -115.5t128 -314.5v-668zM594 195
q79 0 133.5 35.5t54.5 88.5q0 54 -52.5 89.5t-131.5 35.5q-90 0 -140 -31.5t-50 -93.5q0 -60 51 -92t135 -32zM1012 1225h-703v260h703v-260z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="1466" 
d="M1073 0l-82 266h-518l-80 -266h-352l512 1434h360l512 -1434h-352zM733 1116l-174 -567h346zM1122 1839q0 -99 -56 -169.5t-142 -102t-191 -31.5t-191 31.5t-142 102t-56 169.5h264q0 -49 34.5 -77.5t90.5 -28.5t90.5 28.5t34.5 77.5h264z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="1239" 
d="M1126 0h-329v90q-46 -54 -130 -84.5t-188 -30.5q-182 0 -287.5 94t-105.5 244q0 162 112 248t304 86q91 0 164 -22.5t116 -57.5v125q0 62 -39.5 100.5t-109.5 38.5q-114 0 -156 -77h-313q34 161 162 252.5t317 91.5q227 0 355 -115.5t128 -314.5v-668zM594 195
q79 0 133.5 35.5t54.5 88.5q0 54 -52.5 89.5t-131.5 35.5q-90 0 -140 -31.5t-50 -93.5q0 -60 51 -92t135 -32zM1030 1511q0 -158 -104.5 -242.5t-276.5 -84.5q-170 0 -274.5 84.5t-104.5 242.5h246q0 -62 34 -96.5t99 -34.5q67 0 101 34t34 97h246z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="1466" 
d="M1419 -184v-174q-90 -23 -176 -23q-110 0 -169.5 57t-59.5 152q0 89 59 172l-82 266h-518l-80 -266h-352l512 1434h360l512 -1434q-29 -22 -47.5 -38t-36.5 -45t-18 -58q0 -49 53 -49q25 0 43 6zM733 1116l-174 -567h346z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="1239" 
d="M1126 0q-29 -22 -47.5 -38t-36.5 -45t-18 -58q0 -49 53 -49q25 0 43 6v-174q-90 -23 -182 -23q-105 0 -164 51.5t-59 141.5q0 106 82 188v90q-46 -54 -130 -84.5t-188 -30.5q-182 0 -287.5 94t-105.5 244q0 162 112 248t304 86q91 0 164 -22.5t116 -57.5v125
q0 62 -39.5 100.5t-109.5 38.5q-114 0 -156 -77h-313q34 161 162 252.5t317 91.5q227 0 355 -115.5t128 -314.5v-668zM594 195q79 0 133.5 35.5t54.5 88.5q0 54 -52.5 89.5t-131.5 35.5q-90 0 -140 -31.5t-50 -93.5q0 -60 51 -92t135 -32z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="1449" 
d="M451 606q0 -160 73 -252t226 -92q134 0 206.5 75.5t77.5 190.5h326q-2 -114 -44 -215t-118 -177t-191 -120.5t-253 -44.5q-162 0 -288.5 49t-206 137.5t-120.5 206t-41 259.5v188q0 142 41 259.5t120.5 205.5t206 137t288.5 49q138 0 253 -44.5t191 -120.5t118 -177
t44 -215h-326q-5 115 -77.5 190.5t-206.5 75.5q-153 0 -226 -92t-73 -252v-221zM909 1536h-348l137 303h422z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="1179" 
d="M602 -25q-247 0 -385.5 136t-138.5 356v139q0 220 138.5 356t385.5 136q220 0 359.5 -123t144.5 -316h-311q-5 71 -53 118.5t-134 47.5q-93 0 -139.5 -53t-46.5 -145v-181q0 -92 46.5 -145t139.5 -53q86 0 134 47.5t53 118.5h311q-5 -193 -144.5 -316t-359.5 -123z
M766 1184h-342l139 327h426z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="1449" 
d="M451 606q0 -160 73 -252t226 -92q134 0 206.5 75.5t77.5 190.5h326q-2 -114 -44 -215t-118 -177t-191 -120.5t-253 -44.5q-162 0 -288.5 49t-206 137.5t-120.5 206t-41 259.5v188q0 142 41 259.5t120.5 205.5t206 137t288.5 49q138 0 253 -44.5t191 -120.5t118 -177
t44 -215h-326q-5 115 -77.5 190.5t-206.5 75.5q-153 0 -226 -92t-73 -252v-221zM918 1839l243 -303h-323l-90 119l-91 -119h-323l244 303h340z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="1179" 
d="M602 -25q-247 0 -385.5 136t-138.5 356v139q0 220 138.5 356t385.5 136q220 0 359.5 -123t144.5 -316h-311q-5 71 -53 118.5t-134 47.5q-93 0 -139.5 -53t-46.5 -145v-181q0 -92 46.5 -145t139.5 -53q86 0 134 47.5t53 118.5h311q-5 -193 -144.5 -316t-359.5 -123z
M766 1511l231 -327h-311l-86 133l-86 -133h-311l231 327h332z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="1449" 
d="M451 606q0 -160 73 -252t226 -92q134 0 206.5 75.5t77.5 190.5h326q-2 -114 -44 -215t-118 -177t-191 -120.5t-253 -44.5q-162 0 -288.5 49t-206 137.5t-120.5 206t-41 259.5v188q0 142 41 259.5t120.5 205.5t206 137t288.5 49q138 0 253 -44.5t191 -120.5t118 -177
t44 -215h-326q-5 115 -77.5 190.5t-206.5 75.5q-153 0 -226 -92t-73 -252v-221zM553 1714q0 82 52 130t138 48q87 0 139 -48.5t52 -129.5t-52 -129.5t-139 -48.5q-86 0 -138 48t-52 130z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="1179" 
d="M602 -25q-247 0 -385.5 136t-138.5 356v139q0 220 138.5 356t385.5 136q220 0 359.5 -123t144.5 -316h-311q-5 71 -53 118.5t-134 47.5q-93 0 -139.5 -53t-46.5 -145v-181q0 -92 46.5 -145t139.5 -53q86 0 134 47.5t53 118.5h311q-5 -193 -144.5 -316t-359.5 -123z
M410 1362q0 82 52 130t138 48q87 0 139 -48.5t52 -129.5t-52 -129.5t-139 -48.5q-86 0 -138 48t-52 130z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="1449" 
d="M451 606q0 -160 73 -252t226 -92q134 0 206.5 75.5t77.5 190.5h326q-2 -114 -44 -215t-118 -177t-191 -120.5t-253 -44.5q-162 0 -288.5 49t-206 137.5t-120.5 206t-41 259.5v188q0 142 41 259.5t120.5 205.5t206 137t288.5 49q138 0 253 -44.5t191 -120.5t118 -177
t44 -215h-326q-5 115 -77.5 190.5t-206.5 75.5q-153 0 -226 -92t-73 -252v-221zM1159 1839l-244 -303h-340l-243 303h323l90 -119l91 119h323z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="1179" 
d="M602 -25q-247 0 -385.5 136t-138.5 356v139q0 220 138.5 356t385.5 136q220 0 359.5 -123t144.5 -316h-311q-5 71 -53 118.5t-134 47.5q-93 0 -139.5 -53t-46.5 -145v-181q0 -92 46.5 -145t139.5 -53q86 0 134 47.5t53 118.5h311q-5 -193 -144.5 -316t-359.5 -123z
M999 1511l-231 -327h-332l-231 327h311l86 -133l86 133h311z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="1466" 
d="M143 1434h627q193 0 329.5 -74.5t202.5 -201.5t66 -294v-295q0 -167 -66 -294t-202.5 -201t-329.5 -74h-627v1434zM1016 567v299q0 279 -309 279h-213v-856h213q309 0 309 278zM1104 1839l-244 -303h-340l-244 303h324l90 -119l90 119h324z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="1685" 
d="M1155 1495v-1495h-328v113q-28 -51 -110.5 -94.5t-184.5 -43.5q-110 0 -198 39.5t-143 106.5t-84 151t-29 177v176q0 93 29 177t84 150.5t143 106t198 39.5q90 0 164.5 -34t114.5 -81v512h344zM811 461v151q0 97 -56 150t-137 53q-98 0 -147 -55.5t-49 -153.5v-139
q0 -96 49 -152.5t147 -56.5q81 0 137 53t56 150zM1292 1008l82 487h373l-178 -487h-277z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="1529" 
d="M61 571v293h146v570h627q193 0 329.5 -74.5t202.5 -201.5t66 -294v-295q0 -167 -66 -294t-202.5 -201t-329.5 -74h-627v571h-146zM1079 567v299q0 279 -309 279h-213v-281h277v-293h-277v-282h213q309 0 309 278z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="1329" 
d="M1155 1495v-133h102v-229h-102v-1133h-328v113q-28 -51 -110.5 -94.5t-184.5 -43.5q-110 0 -198 39.5t-143 106.5t-84 151t-29 177v176q0 93 29 177t84 150.5t143 106t198 39.5q90 0 164.5 -34t114.5 -81v150h-170v229h170v133h344zM811 461v151q0 97 -56 150t-137 53
q-98 0 -147 -55.5t-49 -153.5v-139q0 -96 49 -152.5t147 -56.5q81 0 137 53t56 150z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="1282" 
d="M143 0v1434h1039v-293h-686v-269h624v-292h-624v-287h688v-293h-1041zM1042 1554h-755v267h755v-267z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="1163" 
d="M1077 428h-667v-25q0 -83 48 -134.5t136 -51.5q67 0 111 29t63 73h303q-28 -150 -161.5 -247t-315.5 -97q-235 0 -375.5 129.5t-140.5 356.5v133q0 229 134 366.5t374 137.5q119 0 214 -37.5t154.5 -103t91 -150.5t31.5 -182v-197zM410 651v-22h352v39q0 84 -43.5 135
t-130.5 51q-91 0 -134.5 -53.5t-43.5 -149.5zM946 1225h-702v260h702v-260z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="1282" 
d="M143 0v1434h1039v-293h-686v-269h624v-292h-624v-287h688v-293h-1041zM1071 1839q0 -99 -56 -169.5t-142 -102t-191 -31.5t-191 31.5t-142 102t-56 169.5h264q0 -49 34.5 -77.5t90.5 -28.5t90.5 28.5t34.5 77.5h264z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="1163" 
d="M1077 428h-667v-25q0 -83 48 -134.5t136 -51.5q67 0 111 29t63 73h303q-28 -150 -161.5 -247t-315.5 -97q-235 0 -375.5 129.5t-140.5 356.5v133q0 229 134 366.5t374 137.5q119 0 214 -37.5t154.5 -103t91 -150.5t31.5 -182v-197zM410 651v-22h352v39q0 84 -43.5 135
t-130.5 51q-91 0 -134.5 -53.5t-43.5 -149.5zM977 1511q0 -158 -104.5 -242.5t-276.5 -84.5q-170 0 -274.5 84.5t-104.5 242.5h246q0 -62 34 -96.5t99 -34.5q66 0 100.5 34t34.5 97h246z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="1282" 
d="M143 0v1434h1039v-293h-686v-269h624v-292h-624v-287h688v-293h-1041zM473 1714q0 81 52 129.5t139 48.5q86 0 138 -48t52 -130t-52 -130t-138 -48q-87 0 -139 48.5t-52 129.5z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="1163" 
d="M1077 428h-667v-25q0 -83 48 -134.5t136 -51.5q67 0 111 29t63 73h303q-28 -150 -161.5 -247t-315.5 -97q-235 0 -375.5 129.5t-140.5 356.5v133q0 229 134 366.5t374 137.5q119 0 214 -37.5t154.5 -103t91 -150.5t31.5 -182v-197zM410 651v-22h352v39q0 84 -43.5 135
t-130.5 51q-91 0 -134.5 -53.5t-43.5 -149.5zM401 1362q0 81 52 129.5t139 48.5q86 0 138 -48t52 -130t-52 -130t-138 -48q-87 0 -139 48.5t-52 129.5z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="1282" 
d="M143 0v1434h1039v-293h-686v-269h624v-292h-624v-287h688v-293q-30 -22 -47 -37t-36.5 -45t-19.5 -59q0 -49 54 -49q25 0 43 6v-174q-90 -23 -183 -23q-105 0 -164 50.5t-59 140.5q0 108 82 190h-711z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="1163" 
d="M877 -184v-174q-90 -23 -183 -23q-105 0 -164 50.5t-59 140.5q0 57 34.5 105.5t74.5 68.5q-230 6 -366 132t-136 345v133q0 229 134 366.5t374 137.5q119 0 214 -37.5t154.5 -103t91 -150.5t31.5 -182v-197h-667v-25q0 -83 48 -134.5t136 -51.5q67 0 111 29t63 73h303
q-31 -131 -143 -233q-77 -71 -101 -101q-47 -61 -47 -126q0 -49 54 -49q25 0 43 6zM410 651v-22h352v39q0 84 -43.5 135t-130.5 51q-91 0 -134.5 -53.5t-43.5 -149.5z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="1282" 
d="M143 0v1434h1039v-293h-686v-269h624v-292h-624v-287h688v-293h-1041zM1077 1839l-243 -303h-340l-244 303h323l91 -119l90 119h323z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="1163" 
d="M1077 428h-667v-25q0 -83 48 -134.5t136 -51.5q67 0 111 29t63 73h303q-28 -150 -161.5 -247t-315.5 -97q-235 0 -375.5 129.5t-140.5 356.5v133q0 229 134 366.5t374 137.5q119 0 214 -37.5t154.5 -103t91 -150.5t31.5 -182v-197zM410 651v-22h352v39q0 84 -43.5 135
t-130.5 51q-91 0 -134.5 -53.5t-43.5 -149.5zM989 1511l-231 -327h-332l-231 327h311l86 -133l86 133h311z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="1468" 
d="M1356 938h-336q-5 97 -77 165t-195 68q-297 0 -297 -335v-226q0 -165 71.5 -260.5t225.5 -95.5q123 0 197 63t79 164h-279v285h625v-203q0 -266 -176 -429t-455 -163q-125 0 -230 32.5t-180 91.5t-127.5 140t-78 177.5t-25.5 205.5v201q0 298 173 470.5t466 172.5
q282 0 447.5 -145t171.5 -379zM903 1839l244 -303h-324l-90 119l-90 -119h-324l244 303h340z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="1267" 
d="M1145 1073v-1040q0 -177 -151 -295.5t-386 -118.5q-201 0 -347 93t-173 251h334q20 -43 69 -65.5t117 -22.5q89 0 141 46.5t52 121.5v127q-41 -49 -118.5 -83t-162.5 -34q-108 0 -194 37.5t-139 101t-81 143t-28 167.5v147q0 91 28 171t82 142.5t140 99t194 36.5
q102 0 184.5 -43t110.5 -94v112h328zM801 633q0 88 -56 137t-137 49q-93 0 -141.5 -52.5t-48.5 -139.5v-103q0 -88 48 -140t140 -52q89 0 142 49.5t53 136.5v115zM815 1511l232 -327h-312l-86 133l-86 -133h-311l231 327h332z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="1468" 
d="M1356 938h-336q-5 97 -77 165t-195 68q-297 0 -297 -335v-226q0 -165 71.5 -260.5t225.5 -95.5q123 0 197 63t79 164h-279v285h625v-203q0 -266 -176 -429t-455 -163q-125 0 -230 32.5t-180 91.5t-127.5 140t-78 177.5t-25.5 205.5v201q0 298 173 470.5t466 172.5
q282 0 447.5 -145t171.5 -379zM1128 1839q0 -99 -56 -169.5t-142 -102t-191 -31.5t-191 31.5t-142 102t-56 169.5h264q0 -49 34.5 -77.5t90.5 -28.5t90.5 28.5t34.5 77.5h264z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="1267" 
d="M1145 1073v-1040q0 -177 -151 -295.5t-386 -118.5q-201 0 -347 93t-173 251h334q20 -43 69 -65.5t117 -22.5q89 0 141 46.5t52 121.5v127q-41 -49 -118.5 -83t-162.5 -34q-108 0 -194 37.5t-139 101t-81 143t-28 167.5v147q0 91 28 171t82 142.5t140 99t194 36.5
q102 0 184.5 -43t110.5 -94v112h328zM801 633q0 88 -56 137t-137 49q-93 0 -141.5 -52.5t-48.5 -139.5v-103q0 -88 48 -140t140 -52q89 0 142 49.5t53 136.5v115zM1030 1511q0 -158 -104.5 -242.5t-276.5 -84.5q-170 0 -274.5 84.5t-104.5 242.5h246q0 -62 34 -96.5
t99 -34.5q67 0 101 34t34 97h246z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="1468" 
d="M1356 938h-336q-5 97 -77 165t-195 68q-297 0 -297 -335v-226q0 -165 71.5 -260.5t225.5 -95.5q123 0 197 63t79 164h-279v285h625v-203q0 -266 -176 -429t-455 -163q-125 0 -230 32.5t-180 91.5t-127.5 140t-78 177.5t-25.5 205.5v201q0 298 173 470.5t466 172.5
q282 0 447.5 -145t171.5 -379zM543 1714q0 82 52 130t138 48q87 0 139 -48.5t52 -129.5t-52 -129.5t-139 -48.5q-86 0 -138 48t-52 130z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="1267" 
d="M1145 1073v-1040q0 -177 -151 -295.5t-386 -118.5q-201 0 -347 93t-173 251h334q20 -43 69 -65.5t117 -22.5q89 0 141 46.5t52 121.5v127q-41 -49 -118.5 -83t-162.5 -34q-108 0 -194 37.5t-139 101t-81 143t-28 167.5v147q0 91 28 171t82 142.5t140 99t194 36.5
q102 0 184.5 -43t110.5 -94v112h328zM801 633q0 88 -56 137t-137 49q-93 0 -141.5 -52.5t-48.5 -139.5v-103q0 -88 48 -140t140 -52q89 0 142 49.5t53 136.5v115zM444 1362q0 81 52 129.5t139 48.5q86 0 138 -48t52 -130t-52 -130t-138 -48q-87 0 -139 48.5t-52 129.5z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="1468" 
d="M1356 938h-336q-5 97 -77 165t-195 68q-297 0 -297 -335v-226q0 -165 71.5 -260.5t225.5 -95.5q123 0 197 63t79 164h-279v285h625v-203q0 -266 -176 -429t-455 -163q-125 0 -230 32.5t-180 91.5t-127.5 140t-78 177.5t-25.5 205.5v201q0 298 173 470.5t466 172.5
q282 0 447.5 -145t171.5 -379zM518 -512l55 369h392l-156 -369h-291z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="1267" 
d="M1145 1073v-1040q0 -177 -151 -295.5t-386 -118.5q-201 0 -347 93t-173 251h334q20 -43 69 -65.5t117 -22.5q89 0 141 46.5t52 121.5v127q-41 -49 -118.5 -83t-162.5 -34q-108 0 -194 37.5t-139 101t-81 143t-28 167.5v147q0 91 28 171t82 142.5t140 99t194 36.5
q102 0 184.5 -43t110.5 -94v112h328zM801 633q0 88 -56 137t-137 49q-93 0 -141.5 -52.5t-48.5 -139.5v-103q0 -88 48 -140t140 -52q89 0 142 49.5t53 136.5v115zM592 1511h342l-88 -327h-426z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="1490" 
d="M1348 0h-353v561h-499v-561h-353v1434h353v-557h499v557h353v-1434zM915 1839l244 -303h-323l-91 119l-90 -119h-323l243 303h340z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="1241" 
d="M467 0h-344v1495h344v-537q37 62 115 101t170 39q182 0 279 -108.5t97 -297.5v-692h-344v639q0 75 -39.5 117.5t-115.5 42.5q-73 0 -117.5 -39t-44.5 -125v-635zM465 1903l244 -304h-324l-90 119l-90 -119h-324l244 304h340z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="1626" 
d="M1415 0h-352v561h-500v-561h-352v1028h-150v211h150v195h352v-195h500v195h352v-195h150v-211h-150v-1028zM563 877h500v151h-500v-151z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="1292" 
d="M518 0h-344v1133h-102v229h102v133h344v-133h170v-229h-170v-175q37 62 114.5 101t170.5 39q182 0 279.5 -108.5t97.5 -297.5v-692h-344v639q0 75 -40 117.5t-116 42.5q-73 0 -117.5 -39t-44.5 -125v-635z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="659" 
d="M506 1434v-1434h-352v1434h352zM512 1536q-56 0 -176 31.5t-166 31.5q-31 0 -49 -18.5t-29 -56.5l-198 51q31 156 85 222.5t173 66.5q74 0 199.5 -32t150.5 -32q12 0 21.5 2t17.5 8t12.5 9t10.5 13.5t7.5 13.5t6.5 16t6 14l196 -59q-29 -140 -88 -210.5t-180 -70.5z" />
    <glyph glyph-name="itilde" unicode="&#x129;" 
d="M477 0h-344v1073h344v-1073zM475 1184q-46 0 -154.5 32.5t-154.5 32.5q-30 0 -46.5 -18t-31.5 -60l-190 52q22 141 77.5 214.5t167.5 73.5q62 0 176.5 -34.5t135.5 -34.5q24 0 41 13.5t23 26.5t16 38l188 -60q-12 -50 -23.5 -86t-31.5 -74t-45 -61.5t-62.5 -39
t-85.5 -15.5z" />
    <glyph glyph-name="uni012A" unicode="&#x12a;" horiz-adv-x="659" 
d="M506 1434v-1434h-352v1434h352zM709 1554h-756v267h756v-267z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" 
d="M477 0h-344v1073h344v-1073zM657 1225h-702v260h702v-260z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="659" 
d="M506 1434v-1434h-352v1434h352zM719 1839q0 -99 -56 -169.5t-142 -102t-191 -31.5t-191 31.5t-142 102t-56 169.5h264q0 -49 34.5 -77.5t90.5 -28.5t90.5 28.5t34.5 77.5h264z" />
    <glyph glyph-name="uni012D" unicode="&#x12d;" 
d="M477 0h-344v1073h344v-1073zM686 1511q0 -158 -104.5 -242.5t-276.5 -84.5q-170 0 -274.5 84.5t-104.5 242.5h246q0 -62 34 -96.5t99 -34.5q67 0 101 34t34 97h246z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="659" 
d="M506 1434v-1434q-92 -74 -92 -137q0 -53 53 -53q17 0 33 4v-176q-74 -19 -154 -19q-112 0 -177 57t-65 160q0 88 50 164v1434h352z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" 
d="M477 0q-92 -74 -92 -137q0 -53 53 -53q17 0 33 4v-176q-74 -19 -154 -19q-112 0 -176.5 51.5t-64.5 145.5q0 99 57 184v1073h344v-1073zM115 1348q0 82 52 130t138 48q87 0 139 -48.5t52 -129.5q0 -82 -52.5 -130.5t-138.5 -48.5t-138 48.5t-52 130.5z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="659" 
d="M506 1434v-1434h-352v1434h352zM139 1714q0 81 52 129.5t139 48.5q86 0 138 -48t52 -130t-52 -130t-138 -48q-87 0 -139 48.5t-52 129.5z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" 
d="M477 0h-344v1073h344v-1073z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="1931" 
d="M506 1434v-1434h-352v1434h352zM1440 1434h352v-994q0 -206 -149.5 -337.5t-385.5 -131.5q-243 0 -384.5 128.5t-141.5 344.5v19h336v-19q0 -83 45.5 -131.5t140.5 -48.5q91 0 139 47.5t48 128.5v994z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="1220" 
d="M477 0h-344v1073h344v-1073zM115 1348q0 82 52 130t138 48q87 0 139 -48.5t52 -129.5q0 -82 -52.5 -130.5t-138.5 -48.5t-138 48.5t-52 130.5zM743 1073h344v-1161q0 -268 -292 -268h-170v260h61q57 0 57 51v1118zM725 1348q0 82 52 130t138 48q87 0 139 -48.5t52 -129.5
q0 -82 -52.5 -130.5t-138.5 -48.5t-138 48.5t-52 130.5z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="1271" 
d="M780 1434h353v-994q0 -206 -149.5 -337.5t-385.5 -131.5q-243 0 -384.5 128.5t-141.5 344.5v19h336v-19q0 -83 45.5 -131.5t140.5 -48.5q91 0 138.5 47.5t47.5 128.5v994zM1122 1839l244 -303h-324l-90 119l-90 -119h-323l243 303h340z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" 
d="M133 1073h344v-1161q0 -134 -74.5 -201t-218.5 -67h-170v260h62q57 0 57 51v1118zM471 1511l231 -327h-311l-86 133l-86 -133h-311l231 327h332z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="1380" 
d="M496 0h-353v1434h353v-684l428 684h405l-473 -711l487 -723h-413l-434 696v-696zM489 -512l56 369h391l-156 -369h-291z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="1171" 
d="M467 0h-344v1495h344v-920l274 498h383l-307 -526l318 -547h-383l-285 516v-516zM373 -512l55 369h391l-155 -369h-291z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="1171" 
d="M467 0h-344v1073h344v-498l274 498h383l-307 -526l318 -547h-383l-285 516v-516z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="1200" 
d="M143 1434h353v-1141h632v-293h-985v1434zM502 1536h-348l137 303h422z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" 
d="M469 1606h-324l113 282h408zM477 0h-344v1495h344v-1495z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="1200" 
d="M143 1434h353v-1141h632v-293h-985v1434zM406 -512l55 369h391l-156 -369h-290z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" 
d="M477 0h-344v1495h344v-1495zM53 -512l56 369h391l-156 -369h-291z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="1200" 
d="M143 1434h353v-1141h632v-293h-985v1434zM668 946l82 488h372l-178 -488h-276z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="976" 
d="M477 0h-344v1495h344v-1495zM614 1008l82 487h373l-178 -487h-277z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="1200" 
d="M143 1434h353v-1141h632v-293h-985v1434zM651 727q0 82 54.5 136.5t138.5 54.5t138 -54.5t54 -136.5t-54 -136t-138 -54t-138.5 54t-54.5 136z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="958" 
d="M477 0h-344v1495h344v-1495zM584 596q0 82 54 136t138 54t138.5 -54t54.5 -136t-54.5 -136t-138.5 -54t-138 54t-54 136z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="1232" 
d="M176 1434h352v-486l242 146v-314l-242 -145v-342h633v-293h-985v424l-131 -78v313l131 78v697z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="645" 
d="M485 0h-344v414l-90 -72v317l90 72v764h344v-489l109 88v-318l-109 -88v-688z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="1503" 
d="M1360 0h-336l-537 838v-838h-344v1434h334l539 -838v838h344v-1434zM881 1536h-349l138 303h422z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="1241" 
d="M467 0h-344v1073h330v-125q41 73 118.5 111.5t180.5 38.5q182 0 279 -108.5t97 -297.5v-692h-344v639q0 75 -39.5 117.5t-115.5 42.5q-73 0 -117.5 -39t-44.5 -125v-635zM776 1184h-342l139 327h426z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="1503" 
d="M1360 0h-336l-537 838v-838h-344v1434h334l539 -838v838h344v-1434zM528 -512l56 369h391l-156 -369h-291z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" horiz-adv-x="1241" 
d="M467 0h-344v1073h330v-125q41 73 118.5 111.5t180.5 38.5q182 0 279 -108.5t97 -297.5v-692h-344v639q0 75 -39.5 117.5t-115.5 42.5q-73 0 -117.5 -39t-44.5 -125v-635zM389 -512l55 369h392l-156 -369h-291z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="1503" 
d="M1360 0h-336l-537 838v-838h-344v1434h334l539 -838v838h344v-1434zM1165 1839l-243 -303h-340l-244 303h324l90 -119l90 119h323z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="1241" 
d="M467 0h-344v1073h330v-125q41 73 118.5 111.5t180.5 38.5q182 0 279 -108.5t97 -297.5v-692h-344v639q0 75 -39.5 117.5t-115.5 42.5q-73 0 -117.5 -39t-44.5 -125v-635zM1022 1511l-231 -327h-332l-232 327h312l86 -133l86 133h311z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" horiz-adv-x="1597" 
d="M823 0h-344v1073h330v-125q41 73 118.5 111.5t180.5 38.5q182 0 279.5 -108.5t97.5 -297.5v-692h-344v639q0 75 -40 117.5t-116 42.5q-73 0 -117.5 -39t-44.5 -125v-635zM20 1008l82 487h373l-178 -487h-277z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="1503" 
d="M909 -96q107 0 107 86v22l-529 826v-838h-344v1434h334l539 -838v838h344v-1448q0 -175 -100 -258.5t-324 -83.5h-111v260h84z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="1241" 
d="M467 0h-344v1073h330v-125q41 73 118.5 111.5t180.5 38.5q182 0 279 -108.5t97 -297.5v-706q0 -173 -108.5 -257.5t-335.5 -84.5h-80v260h80q53 0 76.5 20.5t23.5 65.5v649q0 75 -39.5 117.5t-115.5 42.5q-73 0 -117.5 -39t-44.5 -125v-635z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="1511" 
d="M1413 625q0 -142 -42 -260.5t-123 -206.5t-207 -137.5t-285 -49.5t-285 49.5t-207 137.5t-123.5 206.5t-42.5 260.5v184q0 142 42.5 260.5t123.5 206.5t207 137t285 49t285 -49t207 -137t123 -206.5t42 -260.5v-184zM1061 827q0 158 -77.5 250t-227.5 92t-227.5 -92
t-77.5 -250v-221q0 -158 77.5 -250t227.5 -92t227.5 92t77.5 250v221zM1135 1554h-756v267h756v-267z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="1200" 
d="M1122 608v-143q0 -218 -138.5 -354t-383.5 -136t-383.5 136t-138.5 354v143q0 218 138.5 354t383.5 136t383.5 -136t138.5 -354zM788 627q0 87 -47 142.5t-141 55.5t-141 -55.5t-47 -142.5v-181q0 -87 47 -142.5t141 -55.5t141 55.5t47 142.5v181zM952 1225h-702v260h702
v-260z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="1511" 
d="M1413 625q0 -142 -42 -260.5t-123 -206.5t-207 -137.5t-285 -49.5t-285 49.5t-207 137.5t-123.5 206.5t-42.5 260.5v184q0 142 42.5 260.5t123.5 206.5t207 137t285 49t285 -49t207 -137t123 -206.5t42 -260.5v-184zM1061 827q0 158 -77.5 250t-227.5 92t-227.5 -92
t-77.5 -250v-221q0 -158 77.5 -250t227.5 -92t227.5 92t77.5 250v221zM1145 1839q0 -99 -56 -169.5t-142 -102t-191 -31.5t-191 31.5t-142 102t-56 169.5h264q0 -49 34.5 -77.5t90.5 -28.5t90.5 28.5t34.5 77.5h264z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="1200" 
d="M1122 608v-143q0 -218 -138.5 -354t-383.5 -136t-383.5 136t-138.5 354v143q0 218 138.5 354t383.5 136t383.5 -136t138.5 -354zM788 627q0 87 -47 142.5t-141 55.5t-141 -55.5t-47 -142.5v-181q0 -87 47 -142.5t141 -55.5t141 55.5t47 142.5v181zM981 1511
q0 -158 -104.5 -242.5t-276.5 -84.5q-170 0 -274.5 84.5t-104.5 242.5h246q0 -62 34 -96.5t99 -34.5q66 0 100.5 34t34.5 97h246z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="1511" 
d="M1413 625q0 -142 -42 -260.5t-123 -206.5t-207 -137.5t-285 -49.5t-285 49.5t-207 137.5t-123.5 206.5t-42.5 260.5v184q0 142 42.5 260.5t123.5 206.5t207 137t285 49t285 -49t207 -137t123 -206.5t42 -260.5v-184zM1061 827q0 158 -77.5 250t-227.5 92t-227.5 -92
t-77.5 -250v-221q0 -158 77.5 -250t227.5 -92t227.5 92t77.5 250v221zM1333 1839l-182 -303h-324l115 303h391zM829 1839l-129 -303h-311l78 303h362z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="1200" 
d="M1122 608v-143q0 -218 -138.5 -354t-383.5 -136t-383.5 136t-138.5 354v143q0 218 138.5 354t383.5 136t383.5 -136t138.5 -354zM788 627q0 87 -47 142.5t-141 55.5t-141 -55.5t-47 -142.5v-181q0 -87 47 -142.5t141 -55.5t141 55.5t47 142.5v181zM1190 1511l-193 -327
h-323l117 327h399zM702 1511l-147 -327h-311l88 327h370z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="2166" 
d="M782 0q-331 0 -507.5 178.5t-176.5 483.5v110q0 305 176.5 483.5t507.5 178.5h1284v-293h-686v-269h625v-292h-625v-287h688v-293h-1286zM451 645q0 -165 83.5 -258.5t249.5 -93.5h244v848h-244q-166 0 -249.5 -94t-83.5 -259v-143z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="1884" 
d="M1788 428h-668v-25q0 -82 48.5 -134t136.5 -52q67 0 111 29t63 73h303q-28 -150 -161.5 -247t-315.5 -97q-108 0 -200 28.5t-153 82.5q-58 -52 -150.5 -81.5t-201.5 -29.5q-245 0 -383.5 136t-138.5 354v143q0 218 138.5 354t383.5 136q206 0 352 -113q125 113 344 113
q119 0 214 -37.5t155 -103t91.5 -150.5t31.5 -182v-197zM788 627q0 87 -47 142.5t-141 55.5t-141 -55.5t-47 -142.5v-181q0 -87 47 -142.5t141 -55.5t141 55.5t47 142.5v181zM1120 651v-22h353v39q0 84 -44 135t-131 51q-91 0 -134.5 -53.5t-43.5 -149.5z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="1452" 
d="M971 0l-244 426h-231v-426h-353v1434h674q254 0 395.5 -140t141.5 -366q0 -152 -70.5 -268t-196.5 -177l293 -483h-409zM1001 924q0 105 -58.5 166t-166.5 61h-280v-446h280q108 0 166.5 59.5t58.5 159.5zM813 1536h-348l137 303h422z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="802" 
d="M758 1081v-338h-45q-124 0 -185 -58t-61 -193v-492h-344v1073h326v-188q23 113 94.5 154.5t197.5 41.5h17zM588 1184h-342l139 327h426z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="1452" 
d="M971 0l-244 426h-231v-426h-353v1434h674q254 0 395.5 -140t141.5 -366q0 -152 -70.5 -268t-196.5 -177l293 -483h-409zM1001 924q0 105 -58.5 166t-166.5 61h-280v-446h280q108 0 166.5 59.5t58.5 159.5zM496 -512l55 369h391l-156 -369h-290z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="802" 
d="M758 1081v-338h-45q-124 0 -185 -58t-61 -193v-492h-344v1073h326v-188q23 113 94.5 154.5t197.5 41.5h17zM45 -512l55 369h392l-156 -369h-291z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="1452" 
d="M971 0l-244 426h-231v-426h-353v1434h674q254 0 395.5 -140t141.5 -366q0 -152 -70.5 -268t-196.5 -177l293 -483h-409zM1001 924q0 105 -58.5 166t-166.5 61h-280v-446h280q108 0 166.5 59.5t58.5 159.5zM1094 1839l-244 -303h-340l-244 303h324l90 -119l90 119h324z
" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="802" 
d="M758 1081v-338h-45q-124 0 -185 -58t-61 -193v-492h-344v1073h326v-188q23 113 94.5 154.5t197.5 41.5h17zM852 1511l-231 -327h-332l-232 327h312l86 -133l86 133h311z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="1341" 
d="M1260 440q0 -140 -77.5 -248t-210 -164.5t-298.5 -56.5q-262 0 -419 120t-157 333v39h338v-29q0 -93 60.5 -143.5t179.5 -50.5q111 0 176.5 44t65.5 126q0 102 -99 127l-358 90q-175 43 -256.5 130.5t-81.5 256.5q0 213 143.5 330.5t399.5 117.5q240 0 383 -119.5
t143 -316.5v-35h-326v25q0 79 -48 127.5t-152 48.5q-201 0 -201 -145q0 -94 92 -117l381 -103q169 -45 245.5 -140t76.5 -247zM811 1536h-348l137 303h422z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="1091" 
d="M1030 350q0 -163 -133 -269t-346 -106q-216 0 -344.5 91.5t-128.5 252.5v23h303v-14q0 -56 46.5 -85.5t125.5 -29.5q71 0 116.5 25t45.5 69q0 30 -16.5 47.5t-55.5 26.5l-297 68q-124 28 -188 101t-64 187q0 170 122.5 265.5t322.5 95.5q194 0 315 -93.5t121 -252.5v-19
h-297v19q0 108 -135 108q-129 0 -129 -88q0 -31 16 -47.5t51 -24.5l314 -75q111 -27 173 -97t62 -178zM696 1184h-342l140 327h426z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="1341" 
d="M1260 440q0 -140 -77.5 -248t-210 -164.5t-298.5 -56.5q-262 0 -419 120t-157 333v39h338v-29q0 -93 60.5 -143.5t179.5 -50.5q111 0 176.5 44t65.5 126q0 102 -99 127l-358 90q-175 43 -256.5 130.5t-81.5 256.5q0 213 143.5 330.5t399.5 117.5q240 0 383 -119.5
t143 -316.5v-35h-326v25q0 79 -48 127.5t-152 48.5q-201 0 -201 -145q0 -94 92 -117l381 -103q169 -45 245.5 -140t76.5 -247zM844 1839l243 -303h-323l-90 119l-90 -119h-324l244 303h340z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="1091" 
d="M1030 350q0 -163 -133 -269t-346 -106q-216 0 -344.5 91.5t-128.5 252.5v23h303v-14q0 -56 46.5 -85.5t125.5 -29.5q71 0 116.5 25t45.5 69q0 30 -16.5 47.5t-55.5 26.5l-297 68q-124 28 -188 101t-64 187q0 170 122.5 265.5t322.5 95.5q194 0 315 -93.5t121 -252.5v-19
h-297v19q0 108 -135 108q-129 0 -129 -88q0 -31 16 -47.5t51 -24.5l314 -75q111 -27 173 -97t62 -178zM719 1511l231 -327h-311l-86 133l-86 -133h-311l231 327h332z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="1341" 
d="M936 -215q0 -74 -67.5 -120t-180.5 -46q-88 0 -149 12v125q54 -8 82 -8q71 0 71 43q0 19 -17.5 27.5t-53.5 11.5l-56 6l33 139q-232 18 -366 134.5t-134 314.5v39h338v-29q0 -93 60.5 -143.5t179.5 -50.5q111 0 176.5 44t65.5 126q0 102 -99 127l-358 90
q-175 43 -256.5 130.5t-81.5 256.5q0 213 143.5 330.5t399.5 117.5q240 0 383 -119.5t143 -316.5v-35h-326v25q0 79 -48 127.5t-152 48.5q-201 0 -201 -145q0 -94 92 -117l381 -103q169 -45 245.5 -140t76.5 -247q0 -194 -142 -320t-366 -145l-9 -43l35 -4q74 -8 116 -43.5
t42 -99.5z" />
    <glyph glyph-name="Scedilla" unicode="&#xf6c1;" horiz-adv-x="1341" 
d="M936 -215q0 -74 -67.5 -120t-180.5 -46q-88 0 -149 12v125q54 -8 82 -8q71 0 71 43q0 19 -17.5 27.5t-53.5 11.5l-56 6l33 139q-232 18 -366 134.5t-134 314.5v39h338v-29q0 -93 60.5 -143.5t179.5 -50.5q111 0 176.5 44t65.5 126q0 102 -99 127l-358 90
q-175 43 -256.5 130.5t-81.5 256.5q0 213 143.5 330.5t399.5 117.5q240 0 383 -119.5t143 -316.5v-35h-326v25q0 79 -48 127.5t-152 48.5q-201 0 -201 -145q0 -94 92 -117l381 -103q169 -45 245.5 -140t76.5 -247q0 -194 -142 -320t-366 -145l-9 -43l35 -4q74 -8 116 -43.5
t42 -99.5z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="1091" 
d="M799 -215q0 -74 -67.5 -120t-180.5 -46q-89 0 -150 12v125q54 -8 82 -8q72 0 72 43q0 19 -18 27.5t-54 11.5l-55 6l33 146q-178 18 -280.5 105.5t-102.5 231.5v23h303v-14q0 -56 46.5 -85.5t125.5 -29.5q71 0 116.5 25t45.5 69q0 30 -16.5 47.5t-55.5 26.5l-297 68
q-124 28 -188 101t-64 187q0 170 122.5 265.5t322.5 95.5q194 0 315 -93.5t121 -252.5v-19h-297v19q0 108 -135 108q-129 0 -129 -88q0 -31 16 -47.5t51 -24.5l314 -75q111 -27 173 -97t62 -178q0 -150 -114.5 -252.5t-301.5 -117.5l-8 -48l35 -4q74 -8 116 -43.5t42 -99.5z
" />
    <glyph glyph-name="scedilla" unicode="&#xf6c2;" horiz-adv-x="1091" 
d="M799 -215q0 -74 -67.5 -120t-180.5 -46q-89 0 -150 12v125q54 -8 82 -8q72 0 72 43q0 19 -18 27.5t-54 11.5l-55 6l33 146q-178 18 -280.5 105.5t-102.5 231.5v23h303v-14q0 -56 46.5 -85.5t125.5 -29.5q71 0 116.5 25t45.5 69q0 30 -16.5 47.5t-55.5 26.5l-297 68
q-124 28 -188 101t-64 187q0 170 122.5 265.5t322.5 95.5q194 0 315 -93.5t121 -252.5v-19h-297v19q0 108 -135 108q-129 0 -129 -88q0 -31 16 -47.5t51 -24.5l314 -75q111 -27 173 -97t62 -178q0 -150 -114.5 -252.5t-301.5 -117.5l-8 -48l35 -4q74 -8 116 -43.5t42 -99.5z
" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="1341" 
d="M1260 440q0 -140 -77.5 -248t-210 -164.5t-298.5 -56.5q-262 0 -419 120t-157 333v39h338v-29q0 -93 60.5 -143.5t179.5 -50.5q111 0 176.5 44t65.5 126q0 102 -99 127l-358 90q-175 43 -256.5 130.5t-81.5 256.5q0 213 143.5 330.5t399.5 117.5q240 0 383 -119.5
t143 -316.5v-35h-326v25q0 79 -48 127.5t-152 48.5q-201 0 -201 -145q0 -94 92 -117l381 -103q169 -45 245.5 -140t76.5 -247zM1077 1839l-243 -303h-340l-244 303h323l91 -119l90 119h323z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="1091" 
d="M1030 350q0 -163 -133 -269t-346 -106q-216 0 -344.5 91.5t-128.5 252.5v23h303v-14q0 -56 46.5 -85.5t125.5 -29.5q71 0 116.5 25t45.5 69q0 30 -16.5 47.5t-55.5 26.5l-297 68q-124 28 -188 101t-64 187q0 170 122.5 265.5t322.5 95.5q194 0 315 -93.5t121 -252.5v-19
h-297v19q0 108 -135 108q-129 0 -129 -88q0 -31 16 -47.5t51 -24.5l314 -75q111 -27 173 -97t62 -178zM938 1511l-231 -327h-332l-232 327h312l86 -133l86 133h311z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x162;" horiz-adv-x="1228" 
d="M51 1434h1127v-297h-387v-1137h-353v1137h-387v297zM362 -512l56 369h391l-156 -369h-291z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x21a;" horiz-adv-x="1228" 
d="M51 1434h1127v-297h-387v-1137h-353v1137h-387v297zM362 -512l56 369h391l-156 -369h-291z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x163;" horiz-adv-x="808" 
d="M190 1362h345v-289h206v-266h-206v-457q0 -71 77 -71h113v-279h-227q-151 0 -229.5 83.5t-78.5 233.5v490h-139v266h139v289zM236 -512l55 369h391l-156 -369h-290z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x21b;" horiz-adv-x="808" 
d="M190 1362h345v-289h206v-266h-206v-457q0 -71 77 -71h113v-279h-227q-151 0 -229.5 83.5t-78.5 233.5v490h-139v266h139v289zM236 -512l55 369h391l-156 -369h-290z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="1228" 
d="M51 1434h1127v-297h-387v-1137h-353v1137h-387v297zM1028 1839l-244 -303h-340l-243 303h323l90 -119l91 119h323z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="870" 
d="M608 1167l43 349h346l-116 -349h-273zM190 1362h345v-289h206v-266h-206v-457q0 -71 77 -71h113v-279h-227q-151 0 -229.5 83.5t-78.5 233.5v490h-139v266h139v289z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="1228" 
d="M51 1434h1127v-297h-387v-334h243v-285h-243v-518h-353v518h-243v285h243v334h-387v297z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="808" 
d="M190 1362h345v-289h206v-266h-206v-135h178v-250h-178v-72q0 -71 77 -71h113v-279h-227q-151 0 -229.5 83.5t-78.5 233.5v105h-135v250h135v135h-139v266h139v289z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="1486" 
d="M1001 526v908h353v-899q0 -254 -166 -409t-445 -155t-444.5 155t-165.5 409v899h352v-908q0 -127 69.5 -194.5t188.5 -67.5t188.5 67.5t69.5 194.5zM932 1536q-56 0 -176 31.5t-166 31.5q-31 0 -49 -18.5t-29 -56.5l-199 51q31 156 85 222.5t173 66.5q74 0 200 -32
t151 -32q12 0 21.5 2t17.5 8t12.5 9t10.5 13.5t7.5 13.5t6.5 16t6 14l196 -59q-29 -140 -88 -210.5t-180 -70.5z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="1230" 
d="M764 1073h344v-1073h-330v125q-41 -73 -118.5 -111.5t-180.5 -38.5q-177 0 -271.5 108.5t-94.5 297.5v692h344v-649q0 -75 37.5 -117.5t109.5 -42.5t116 38.5t44 125.5v645zM782 1184q-46 0 -154.5 32.5t-154.5 32.5q-30 0 -46.5 -18t-31.5 -60l-190 52q22 141 78 214.5
t168 73.5q63 0 177 -34.5t134 -34.5q24 0 41 13.5t23 26.5t16 38l188 -60q-12 -50 -23.5 -86t-31.5 -74t-45 -61.5t-62.5 -39t-85.5 -15.5z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="1486" 
d="M1001 526v908h353v-899q0 -254 -166 -409t-445 -155t-444.5 155t-165.5 409v899h352v-908q0 -127 69.5 -194.5t188.5 -67.5t188.5 67.5t69.5 194.5zM1122 1554h-755v267h755v-267z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="1230" 
d="M764 1073h344v-1073h-330v125q-41 -73 -118.5 -111.5t-180.5 -38.5q-177 0 -271.5 108.5t-94.5 297.5v692h344v-649q0 -75 37.5 -117.5t109.5 -42.5t116 38.5t44 125.5v645zM963 1225h-703v260h703v-260z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="1486" 
d="M1001 526v908h353v-899q0 -254 -166 -409t-445 -155t-444.5 155t-165.5 409v899h352v-908q0 -127 69.5 -194.5t188.5 -67.5t188.5 67.5t69.5 194.5zM1133 1839q0 -99 -56 -169.5t-142.5 -102t-191.5 -31.5t-191 31.5t-142 102t-56 169.5h264q0 -49 34.5 -77.5t90.5 -28.5
t90.5 28.5t34.5 77.5h265z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="1230" 
d="M764 1073h344v-1073h-330v125q-41 -73 -118.5 -111.5t-180.5 -38.5q-177 0 -271.5 108.5t-94.5 297.5v692h344v-649q0 -75 37.5 -117.5t109.5 -42.5t116 38.5t44 125.5v645zM1006 1511q0 -158 -104.5 -242.5t-276.5 -84.5q-170 0 -274.5 84.5t-104.5 242.5h246
q0 -62 34 -96.5t99 -34.5q67 0 101 34t34 97h246z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="1486" 
d="M1001 526v908h353v-899q0 -254 -166 -409t-445 -155t-444.5 155t-165.5 409v899h352v-908q0 -127 69.5 -194.5t188.5 -67.5t188.5 67.5t69.5 194.5zM971 1735q0 -84 -58.5 -141.5t-169.5 -57.5t-170 57.5t-59 141.5q0 85 59 141.5t170 56.5t169.5 -56.5t58.5 -141.5z
M662 1735q0 -37 21.5 -57.5t59.5 -20.5q40 0 62 20.5t22 57.5q0 77 -84 77q-81 0 -81 -77z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="1230" 
d="M764 1073h344v-1073h-330v125q-41 -73 -118.5 -111.5t-180.5 -38.5q-177 0 -271.5 108.5t-94.5 297.5v692h344v-649q0 -75 37.5 -117.5t109.5 -42.5t116 38.5t44 125.5v645zM838 1382q0 -82 -62.5 -140t-165.5 -58t-166 58t-63 140t63 140.5t166 58.5t165.5 -58.5
t62.5 -140.5zM518 1382q0 -37 25 -60.5t67 -23.5t68.5 23.5t26.5 60.5q0 38 -26 61t-69 23t-67.5 -23t-24.5 -61z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="1486" 
d="M1001 526v908h353v-899q0 -254 -166 -409t-445 -155t-444.5 155t-165.5 409v899h352v-908q0 -127 69.5 -194.5t188.5 -67.5t188.5 67.5t69.5 194.5zM1305 1839l-183 -303h-323l114 303h392zM801 1839l-129 -303h-312l78 303h363z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="1230" 
d="M764 1073h344v-1073h-330v125q-41 -73 -118.5 -111.5t-180.5 -38.5q-177 0 -271.5 108.5t-94.5 297.5v692h344v-649q0 -75 37.5 -117.5t109.5 -42.5t116 38.5t44 125.5v645zM1174 1511l-193 -327h-324l117 327h400zM686 1511l-147 -327h-312l88 327h371z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="1486" 
d="M1016 -184v-174q-90 -23 -182 -23q-105 0 -164.5 51.5t-59.5 141.5q0 91 74 161q-254 17 -402.5 169.5t-148.5 392.5v899h352v-908q0 -127 69.5 -194.5t188.5 -67.5t188.5 67.5t69.5 194.5v908h353v-899q0 -310 -273 -486q-36 -23 -56.5 -38t-49 -40.5t-42 -53.5
t-13.5 -58q0 -49 53 -49q25 0 43 6z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="1230" 
d="M1102 -184v-174q-90 -23 -182 -23q-104 0 -164 54.5t-60 144.5q0 42 16 81.5t29 56.5t37 44v125q-41 -73 -118.5 -111.5t-180.5 -38.5q-177 0 -271.5 108.5t-94.5 297.5v692h344v-649q0 -75 37.5 -117.5t109.5 -42.5t116 38.5t44 125.5v645h344v-1073q-29 -22 -47.5 -38
t-36.5 -45t-18 -58q0 -49 53 -49q25 0 43 6z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="1994" 
d="M1161 1434l269 -940l178 940h346l-344 -1434h-330l-283 1001l-282 -1001h-330l-344 1434h346l178 -940l269 940h327zM1167 1839l244 -303h-324l-90 119l-90 -119h-323l243 303h340z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="1630" 
d="M963 1073l196 -651l121 651h334l-277 -1073h-311l-211 649l-211 -649h-311l-277 1073h334l121 -651l197 651h295zM981 1511l231 -327h-311l-86 133l-86 -133h-311l231 327h332z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="1339" 
d="M1327 1434l-481 -932v-502h-352v502l-482 932h383l275 -596l274 596h383zM840 1839l243 -303h-323l-90 119l-90 -119h-324l244 303h340z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="1083" 
d="M627 -356h-336l104 356l-385 1073h342l205 -725l172 725h344zM723 1511l231 -327h-311l-86 133l-86 -133h-311l231 327h332z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="1339" 
d="M1327 1434l-481 -932v-502h-352v502l-482 932h383l275 -596l274 596h383zM268 1696q0 68 47.5 113.5t122.5 45.5q73 0 119.5 -45.5t46.5 -113.5t-46.5 -114t-119.5 -46q-75 0 -122.5 45.5t-47.5 114.5zM735 1696q0 68 47.5 113.5t122.5 45.5q73 0 119.5 -45.5
t46.5 -113.5t-46.5 -114t-119.5 -46q-75 0 -122.5 45.5t-47.5 114.5z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="1327" 
d="M152 1141v293h1040v-293l-662 -848h678v-293h-1085v293l659 848h-630zM827 1536h-348l137 303h422z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="1067" 
d="M985 0h-893v240l471 577h-452v256h860v-239l-473 -578h487v-256zM698 1184h-342l140 327h426z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="1327" 
d="M152 1141v293h1040v-293l-662 -848h678v-293h-1085v293l659 848h-630zM483 1714q0 81 52 129.5t139 48.5q86 0 138 -48t52 -130t-52 -130t-138 -48q-87 0 -139 48.5t-52 129.5z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="1067" 
d="M985 0h-893v240l471 577h-452v256h860v-239l-473 -578h487v-256zM348 1362q0 81 52 129.5t139 48.5q86 0 138 -48t52 -130t-52 -130t-138 -48q-87 0 -139 48.5t-52 129.5z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="1327" 
d="M152 1141v293h1040v-293l-662 -848h678v-293h-1085v293l659 848h-630zM1106 1839l-244 -303h-340l-243 303h323l90 -119l90 119h324z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="1067" 
d="M985 0h-893v240l471 577h-452v256h860v-239l-473 -578h487v-256zM936 1511l-231 -327h-332l-232 327h312l86 -133l86 133h311z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="940" 
d="M281 641h-131v281h155l17 182q15 168 95 249t236 81h226v-281h-129q-43 0 -62 -19t-22 -59l-15 -153h166v-281h-190l-60 -686q-14 -162 -95 -236.5t-247 -74.5h-184v280h100q78 0 84 78z" />
    <glyph glyph-name="Aringacute" unicode="&#x1fa;" horiz-adv-x="1466" 
d="M1073 0l-82 266h-518l-80 -266h-352l500 1403q-37 46 -37 106q0 77 63 133t166 56t165.5 -56t62.5 -133q0 -56 -37 -106l501 -1403h-352zM733 1116l-174 -567h346zM928 1745h-348l86 233h421zM641 1509q0 -33 25 -54t67 -21t68 21t26 54q0 34 -25.5 55t-68.5 21
q-42 0 -67 -21t-25 -55z" />
    <glyph glyph-name="aringacute" unicode="&#x1fb;" horiz-adv-x="1239" 
d="M854 1604h-348l106 260h422zM881 1358q0 -77 -62.5 -133t-165.5 -56t-166 56t-63 133t63 132.5t166 55.5t165.5 -55.5t62.5 -132.5zM561 1358q0 -33 25 -54.5t67 -21.5t68.5 21.5t26.5 54.5q0 34 -26 55t-69 21q-42 0 -67 -21t-25 -55zM1126 0h-329v90
q-46 -54 -130 -84.5t-188 -30.5q-182 0 -287.5 94t-105.5 244q0 162 112 248t304 86q91 0 164 -22.5t116 -57.5v125q0 62 -39.5 100.5t-109.5 38.5q-114 0 -156 -77h-313q34 161 162 252.5t317 91.5q227 0 355 -115.5t128 -314.5v-668zM594 195q79 0 133.5 35.5t54.5 88.5
q0 54 -52.5 89.5t-131.5 35.5q-90 0 -140 -31.5t-50 -93.5q0 -60 51 -92t135 -32z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="2234" 
d="M1391 1536h-349l138 303h422zM1096 0v266h-551l-166 -266h-379l950 1434h1184v-293h-686v-269h625v-292h-625v-287h688v-293h-1040zM723 549h373v592z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="1890" 
d="M1118 1184h-342l139 327h426zM1794 428h-668v-25q0 -82 48.5 -134t136.5 -52q67 0 111 29t63 73h303q-28 -150 -161.5 -247t-315.5 -97q-255 0 -381 144q-52 -64 -161 -104t-232 -40q-67 0 -129.5 10.5t-122 36t-103 63.5t-70 97t-26.5 133q0 109 57.5 186.5t149.5 112.5
t209 35q175 0 280 -74v105q0 69 -40.5 110t-112.5 41q-107 0 -152 -79h-315q40 169 164 257.5t317 88.5q94 0 178.5 -30.5t136.5 -78.5q57 50 146 79.5t199 29.5q119 0 214 -37.5t154.5 -103t91 -150.5t31.5 -182v-197zM588 195q84 0 139 32t55 88q0 55 -50 92t-132 37
q-197 0 -197 -125q0 -58 50 -91t135 -33zM1126 651v-22h353v39q0 84 -43.5 135t-130.5 51q-91 0 -135 -53.5t-44 -149.5z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="1511" 
d="M911 1536h-348l137 303h422zM1413 625q0 -142 -42 -260.5t-123 -206.5t-207 -137.5t-285 -49.5q-185 0 -322 64l-90 -137h-246l168 258q-168 174 -168 469v184q0 142 42.5 260.5t123.5 206.5t207 137t285 49q185 0 321 -65l90 139h246l-168 -258q168 -177 168 -469v-184z
M451 606q0 -76 18 -141l438 672q-69 32 -151 32q-150 0 -227.5 -92t-77.5 -250v-221zM1061 827q0 75 -19 140l-438 -670q59 -33 152 -33q150 0 227.5 92t77.5 250v221z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" horiz-adv-x="1200" 
d="M762 1184h-342l139 327h426zM1122 608v-143q0 -218 -138.5 -354t-383.5 -136q-145 0 -258 52l-88 -129h-176l143 208q-143 134 -143 359v143q0 218 138.5 354t383.5 136q146 0 260 -54l88 132h174l-143 -211q143 -131 143 -357zM412 446q0 -21 4 -53l280 412
q-45 20 -96 20q-94 0 -141 -55.5t-47 -142.5v-181zM788 627q0 23 -4 51l-278 -410q36 -20 94 -20q94 0 141 55.5t47 142.5v181z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="1341" 
d="M1260 440q0 -140 -77.5 -248t-210 -164.5t-298.5 -56.5q-262 0 -419 120t-157 333v39h338v-29q0 -93 60.5 -143.5t179.5 -50.5q111 0 176.5 44t65.5 126q0 102 -99 127l-358 90q-175 43 -256.5 130.5t-81.5 256.5q0 213 143.5 330.5t399.5 117.5q240 0 383 -119.5
t143 -316.5v-35h-326v25q0 79 -48 127.5t-152 48.5q-201 0 -201 -145q0 -94 92 -117l381 -103q169 -45 245.5 -140t76.5 -247zM446 -512l56 369h391l-156 -369h-291z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="1091" 
d="M1030 350q0 -163 -133 -269t-346 -106q-216 0 -344.5 91.5t-128.5 252.5v23h303v-14q0 -56 46.5 -85.5t125.5 -29.5q71 0 116.5 25t45.5 69q0 30 -16.5 47.5t-55.5 26.5l-297 68q-124 28 -188 101t-64 187q0 170 122.5 265.5t322.5 95.5q194 0 315 -93.5t121 -252.5v-19
h-297v19q0 108 -135 108q-129 0 -129 -88q0 -31 16 -47.5t51 -24.5l314 -75q111 -27 173 -97t62 -178zM315 -512l56 369h391l-156 -369h-291z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="1081" 
d="M707 1511l231 -327h-311l-86 133l-86 -133h-312l232 327h332z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="1081" 
d="M938 1511l-231 -327h-332l-232 327h312l86 -133l86 133h311z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="1046" 
d="M903 1511q0 -158 -104.5 -242.5t-276.5 -84.5q-170 0 -274.5 84.5t-104.5 242.5h246q0 -62 34 -96.5t99 -34.5q66 0 100.5 34t34.5 97h246z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="630" 
d="M125 1362q0 82 52 130t138 48q87 0 139 -48.5t52 -129.5t-52 -129.5t-139 -48.5q-86 0 -138 48t-52 130z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="743" 
d="M600 1382q0 -82 -62 -140t-165 -58t-166.5 58.5t-63.5 139.5q0 82 63.5 140.5t166.5 58.5t165 -58.5t62 -140.5zM281 1382q0 -37 25 -60.5t67 -23.5t68 23.5t26 60.5q0 38 -25.5 61t-68.5 23t-67.5 -23t-24.5 -61z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="698" 
d="M549 -184v-174q-90 -23 -182 -23q-105 0 -164.5 51t-59.5 140q0 47 15.5 88t29.5 59t37 43h330q-29 -22 -47.5 -38t-36.5 -45t-18 -58q0 -49 53 -49q25 0 43 6z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="1112" 
d="M721 1184q-46 0 -154.5 32.5t-154.5 32.5q-30 0 -46.5 -18t-31.5 -60l-191 52q22 141 78 214.5t168 73.5q63 0 177 -34.5t134 -34.5q24 0 41 13.5t23 26.5t16 38l189 -60q-12 -50 -23.5 -86t-31.5 -74t-45 -61.5t-62.5 -39t-85.5 -15.5z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="1232" 
d="M1090 1511l-193 -327h-324l117 327h400zM602 1511l-147 -327h-312l88 327h371z" />
    <glyph glyph-name="uni0312" unicode="&#x312;" horiz-adv-x="800" 
d="M315 1511h342l-88 -327h-426z" />
    <glyph glyph-name="uni0315" unicode="&#x315;" horiz-adv-x="741" 
d="M143 1008l82 487h373l-178 -487h-277z" />
    <glyph glyph-name="uni0326" unicode="&#x326;" horiz-adv-x="733" 
d="M143 -512l56 369h391l-156 -369h-291z" />
    <glyph glyph-name="Omega" unicode="&#x3a9;" horiz-adv-x="1544" 
d="M1432 813v-65q0 -128 -46.5 -250t-123.5 -217h159v-281h-512v281q170 212 170 454v58q0 376 -307 376t-307 -376v-58q0 -242 170 -454v-281h-512v281h160q-77 95 -123.5 217t-46.5 250v65q0 188 74 333t225 230.5t360 85.5q158 0 284 -49t208 -136.5t125 -205.5t43 -258z
" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="1519" 
d="M1438 803h-232v-438q0 -44 23.5 -64t73.5 -20h120v-281h-215q-170 0 -258 87t-88 247v469h-241v-803h-345v803h-194v270h1356v-270z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="1994" 
d="M1161 1434l269 -940l178 940h346l-344 -1434h-330l-283 1001l-282 -1001h-330l-344 1434h346l178 -940l269 940h327zM1163 1536h-348l-211 303h422z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="1630" 
d="M963 1073l196 -651l121 651h334l-277 -1073h-311l-211 649l-211 -649h-311l-277 1073h334l121 -651l197 651h295zM973 1184h-342l-223 327h426z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="1994" 
d="M1161 1434l269 -940l178 940h346l-344 -1434h-330l-283 1001l-282 -1001h-330l-344 1434h346l178 -940l269 940h327zM1186 1536h-348l137 303h422z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="1630" 
d="M963 1073l196 -651l121 651h334l-277 -1073h-311l-211 649l-211 -649h-311l-277 1073h334l121 -651l197 651h295zM997 1184h-342l140 327h426z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="1994" 
d="M1161 1434l269 -940l178 940h346l-344 -1434h-330l-283 1001l-282 -1001h-330l-344 1434h346l178 -940l269 940h327zM596 1696q0 68 47.5 113.5t122.5 45.5q73 0 119.5 -45.5t46.5 -113.5t-46.5 -114t-119.5 -46q-75 0 -122.5 45.5t-47.5 114.5zM1063 1696
q0 68 47.5 113.5t122.5 45.5q73 0 119.5 -45.5t46.5 -113.5t-46.5 -114t-119.5 -46q-75 0 -122.5 45.5t-47.5 114.5z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="1630" 
d="M963 1073l196 -651l121 651h334l-277 -1073h-311l-211 649l-211 -649h-311l-277 1073h334l121 -651l197 651h295zM436 1346q0 68 47.5 113.5t122.5 45.5q73 0 119.5 -45.5t46.5 -113.5t-46.5 -114t-119.5 -46q-75 0 -122.5 45.5t-47.5 114.5zM858 1346q0 68 47.5 113.5
t122.5 45.5q73 0 119.5 -45.5t46.5 -113.5t-46.5 -114t-119.5 -46q-75 0 -122.5 45.5t-47.5 114.5z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="1339" 
d="M1327 1434l-481 -932v-502h-352v502l-482 932h383l275 -596l274 596h383zM840 1536h-348l-211 303h421z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="1083" 
d="M627 -356h-336l104 356l-385 1073h342l205 -725l172 725h344zM719 1184h-342l-223 327h426z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="1257" 
d="M1159 510h-1061v307h1061v-307z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="1869" 
d="M1772 510h-1674v307h1674v-307z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" 
d="M549 1434l-107 -588h-391l207 588h291z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" 
d="M61 846l107 588h391l-207 -588h-291z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="589" 
d="M37 -281l106 588h392l-207 -588h-291z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="1161" 
d="M1100 1434l-107 -588h-391l207 588h291zM549 1434l-107 -588h-391l207 588h291z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="1161" 
d="M61 846l107 588h391l-207 -588h-291zM612 846l107 588h391l-207 -588h-291z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="1140" 
d="M37 -281l106 588h392l-207 -588h-291zM588 -281l106 588h391l-206 -588h-291z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="987" 
d="M102 868v238h254v328h275v-328h254v-238h-254v-1073h-275v1073h-254z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="1007" 
d="M113 123v237h254v508h-254v238h254v328h274v-328h254v-238h-254v-508h254v-237h-254v-328h-274v328h-254z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="888" 
d="M745 729q0 -126 -83.5 -210.5t-219.5 -84.5q-134 0 -216.5 84.5t-82.5 210.5q0 127 82.5 211t216.5 84q137 0 220 -84t83 -211z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="1708" 
d="M92 174q0 82 54.5 136.5t138.5 54.5t138 -54.5t54 -136.5t-54 -136t-138 -54t-138.5 54t-54.5 136zM662 174q0 82 54 136.5t138 54.5t138.5 -54.5t54.5 -136.5t-54.5 -136t-138.5 -54t-138 54t-54 136zM1231 174q0 82 54 136.5t138 54.5t138.5 -54.5t54.5 -136.5
t-54.5 -136t-138.5 -54t-138 54t-54 136z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="2674" 
d="M442 0l736 1434h245l-735 -1434h-246zM432 637q-155 0 -252.5 93t-97.5 235v161q0 142 97.5 235t252.5 93t252.5 -93t97.5 -235v-161q0 -142 -97.5 -235t-252.5 -93zM432 838q52 0 81.5 28t29.5 78v203q0 50 -29.5 78t-81.5 28t-81 -28t-29 -78v-203q0 -50 29 -78t81 -28
zM1434 -20q-155 0 -253 92.5t-98 234.5v162q0 142 98 235t253 93t252.5 -93t97.5 -235v-162q0 -142 -97.5 -234.5t-252.5 -92.5zM1434 180q52 0 81 28.5t29 78.5v202q0 50 -29 78.5t-81 28.5t-81.5 -28.5t-29.5 -78.5v-202q0 -50 29.5 -78.5t81.5 -28.5zM2253 -20
q-155 0 -252.5 92.5t-97.5 234.5v162q0 142 97.5 235t252.5 93t252.5 -93t97.5 -235v-162q0 -142 -97.5 -234.5t-252.5 -92.5zM2253 180q52 0 81 28.5t29 78.5v202q0 50 -29 78.5t-81 28.5t-81.5 -28.5t-29.5 -78.5v-202q0 -50 29.5 -78.5t81.5 -28.5z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="835" 
d="M754 1016l-322 -447l322 -446h-400l-323 446l323 447h400z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="835" 
d="M403 569l-321 447h399l324 -447l-324 -446h-399z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="851" 
d="M944 1434l-758 -1434h-278l758 1434h278z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="1394" 
d="M1303 987h-312q-2 89 -56.5 146t-150.5 57t-147 -52.5t-53 -138.5h252v-227h-252v-110h252v-228h-252q2 -87 52.5 -138.5t147.5 -51.5q96 0 150.5 56.5t56.5 145.5h312q-2 -96 -40 -182t-104.5 -151.5t-164.5 -103.5t-214 -38q-123 0 -223.5 36t-167 99.5t-105 147
t-44.5 180.5h-138v228h138v110h-138v227h138q6 97 44.5 180.5t105 147t167 99.5t223.5 36q116 0 214 -38t164.5 -103.5t104.5 -151.5t40 -182z" />
    <glyph glyph-name="published" unicode="&#x2117;" horiz-adv-x="1697" 
d="M102 717q0 152 55 288.5t151.5 236t237 158t302.5 58.5q164 0 304.5 -58.5t237 -158.5t151 -235.5t54.5 -288.5t-54.5 -289t-151 -236t-237 -158.5t-304.5 -58.5q-162 0 -302.5 58.5t-237 158.5t-151.5 236.5t-55 288.5zM1497 717q0 103 -28.5 200t-85 180t-133.5 145
t-181 97t-221 35q-145 0 -268 -54.5t-205 -145t-128 -209.5t-46 -248t46 -248t128 -210t205 -145.5t268 -54.5q146 0 270 54.5t206 145.5t127.5 210t45.5 248zM913 561h-131v-207h-188v725h319q127 0 204 -71.5t77 -186.5q0 -117 -77.5 -188.5t-203.5 -71.5zM782 715h111
q55 0 84 29t29 75q0 47 -29 77t-84 30h-111v-211z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="1357" 
d="M51 1434h508v-134h-178v-481h-152v481h-178v134zM1118 1139l-90 -191h-135l-90 191v-320h-148v615h140l166 -349l165 349h140v-615h-148v320z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" horiz-adv-x="1220" 
d="M1139 592v-88q0 -151 -67 -272t-190.5 -191t-281.5 -70q-234 0 -378 134.5t-144 340.5v68q0 213 119 335t317 122q192 0 274 -92q-55 134 -190 201t-340 67h-45v287h61q421 0 643 -216t222 -626zM610 711q-96 0 -144 -52t-48 -139v-82q0 -82 49 -136t139 -54
q88 0 140.5 52t52.5 132v88q0 90 -52 140.5t-137 50.5z" />
    <glyph glyph-name="Delta" unicode="&#x2206;" horiz-adv-x="1429" 
d="M870 1434l467 -1158v-276h-1245v276l467 1158h311zM453 295h524l-262 760z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="1722" 
d="M678 1153v-1509h-344v1509h-262v281h1579v-281h-262v-1509h-345v1509h-366z" />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="1232" 
d="M889 539l-393 -615h645v-280h-1028v280l405 615l-405 614v281h1028v-281h-645z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="1208" 
d="M123 592v291h962v-291h-962z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="1562" 
d="M102 446v322h473l211 -621l336 1389h338l-454 -1741h-402l-244 651h-258z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="1912" 
d="M956 516q-65 -87 -181 -145t-249 -58q-191 0 -305.5 113.5t-114.5 310.5t114.5 310.5t305.5 113.5q133 0 249 -58t181 -145q65 87 181 145t249 58q191 0 305.5 -113.5t114.5 -310.5t-114.5 -310.5t-305.5 -113.5q-133 0 -249 58t-181 145zM573 582q145 0 244 155
q-99 156 -244 156q-73 0 -115 -41t-42 -115t42 -114.5t115 -40.5zM1339 582q73 0 115.5 40.5t42.5 114.5t-42.5 115t-115.5 41q-144 0 -243 -156q45 -71 102.5 -113t140.5 -42z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="888" 
d="M256 1085q0 349 354 349h187v-283h-107q-46 0 -68 -21.5t-22 -68.5v-1069q0 -348 -354 -348h-174v282h94q46 0 68 21.5t22 68.5v1069z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="1357" 
d="M299 330l-186 170q55 71 101 113t116.5 70t158.5 28q45 0 92 -12t75 -23.5t89 -40.5q12 -6 39.5 -19.5t38.5 -18t30.5 -12t35 -10t33.5 -2.5q97 0 170 76l163 -176q-57 -73 -143.5 -122.5t-187.5 -49.5q-45 0 -94.5 12.5t-73 22.5t-88.5 39q-19 8 -53.5 24.5t-50.5 23
t-39.5 13t-43.5 6.5q-28 0 -53.5 -9t-53 -31.5t-37 -32t-38.5 -39.5zM299 795l-186 170q55 71 101 113t116.5 70t158.5 28q45 0 92 -12t75 -23.5t89 -40.5q12 -6 39.5 -19.5t38.5 -18t30.5 -12t35 -10t33.5 -2.5q97 0 170 76l163 -176q-57 -73 -143.5 -122.5t-187.5 -49.5
q-45 0 -94.5 12.5t-73 22.5t-88.5 39q-19 8 -53.5 24.5t-50.5 23t-39.5 13t-43.5 6.5q-28 0 -53.5 -9t-53 -31.5t-37 -32t-38.5 -39.5z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="1290" 
d="M219 139l94 205h-149v270h272l113 246h-385v270h510l94 205h305l-94 -205h147v-270h-272l-113 -246h385v-270h-508l-94 -205h-305z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="1218" 
d="M1075 356l-962 303v279l962 303v-301l-555 -141l555 -142v-301zM1075 0h-962v291h962v-291z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="1218" 
d="M1075 659l-962 -303v301l555 142l-555 141v301l962 -303v-279zM1075 0h-962v291h962v-291z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" horiz-adv-x="1339" 
d="M821 1434l457 -717l-457 -717h-303l-457 717l457 717h303zM426 717l244 -416l243 416l-243 416z" />
    <glyph glyph-name="apple" unicode="&#xf8ff;" horiz-adv-x="1679" 
d="M184 334v741q0 152 103.5 255.5t255.5 103.5h717q152 0 255 -102.5t103 -254.5t-103 -254t-255 -102h-359v-387q0 -152 -103 -255.5t-255 -103.5t-255.5 103.5t-103.5 255.5z" />
    <glyph glyph-name="fi" unicode="&#xfb01;" horiz-adv-x="1372" 
d="M526 0h-344v807h-125v266h125v76q0 181 80 263.5t268 82.5h201v-270h-113q-50 0 -71 -20.5t-21 -67.5v-64h193v-266h-193v-807zM1239 0h-344v1073h344v-1073zM877 1348q0 82 52 130t138 48t138 -48t52 -130t-52 -130.5t-138 -48.5t-138 48.5t-52 130.5z" />
    <glyph glyph-name="fl" unicode="&#xfb02;" horiz-adv-x="1372" 
d="M526 0h-344v807h-125v266h125v76q0 181 80 263.5t268 82.5h201v-270h-113q-50 0 -71 -20.5t-21 -67.5v-64h193v-266h-193v-807zM1239 0h-344v1495h344v-1495z" />
    <glyph glyph-name="grave.case" horiz-adv-x="845" 
d="M702 1536h-348l-211 303h422z" />
    <glyph glyph-name="acute.case" horiz-adv-x="845" 
d="M492 1536h-349l138 303h421z" />
    <glyph glyph-name="circumflex.case" horiz-adv-x="1114" 
d="M727 1839l244 -303h-324l-90 119l-90 -119h-324l244 303h340z" />
    <glyph glyph-name="tilde.case" horiz-adv-x="1173" 
d="M762 1536q-56 0 -176 31.5t-166 31.5q-31 0 -49 -18.5t-29 -56.5l-199 51q31 156 85 222.5t173 66.5q74 0 200 -32t151 -32q12 0 21.5 2t17.5 8t12.5 9t10.5 13.5t7.5 13.5t6.5 16t6 14l196 -59q-29 -140 -88 -210.5t-180 -70.5z" />
    <glyph glyph-name="dieresis.case" horiz-adv-x="1062" 
d="M129 1696q0 68 47.5 113.5t122.5 45.5q73 0 119.5 -45.5t46.5 -113.5t-46.5 -114t-119.5 -46q-75 0 -122.5 45.5t-47.5 114.5zM596 1696q0 68 47.5 113.5t122.5 45.5q73 0 119.5 -45.5t46.5 -113.5t-46.5 -114t-119.5 -46q-75 0 -122.5 45.5t-47.5 114.5z" />
    <glyph glyph-name="macron.case" horiz-adv-x="1042" 
d="M899 1554h-756v267h756v-267z" />
    <glyph glyph-name="breve.case" horiz-adv-x="1064" 
d="M922 1839q0 -99 -56 -169.5t-142.5 -102t-191.5 -31.5t-191 31.5t-142 102t-56 169.5h265q0 -49 34 -77.5t90 -28.5t90.5 28.5t34.5 77.5h265z" />
    <glyph glyph-name="dotaccent.case" horiz-adv-x="630" 
d="M125 1714q0 82 52 130t138 48q87 0 139 -48.5t52 -129.5t-52 -129.5t-139 -48.5q-86 0 -138 48t-52 130z" />
    <glyph glyph-name="caron.case" horiz-adv-x="1114" 
d="M971 1839l-244 -303h-340l-244 303h324l90 -119l90 119h324z" />
    <glyph glyph-name="hungarumlaut.case" horiz-adv-x="1230" 
d="M1087 1839l-182 -303h-323l114 303h391zM584 1839l-129 -303h-312l78 303h363z" />
    <glyph glyph-name="ring.case" horiz-adv-x="743" 
d="M600 1735q0 -84 -58 -141.5t-169 -57.5t-170.5 57.5t-59.5 141.5q0 85 59.5 141.5t170.5 56.5t169 -56.5t58 -141.5zM291 1735q0 -37 22 -57.5t60 -20.5q40 0 62 20.5t22 57.5q0 77 -84 77q-39 0 -60.5 -19.5t-21.5 -57.5z" />
    <glyph glyph-name="J.salt" horiz-adv-x="1271" 
d="M236 1434h897v-994q0 -206 -149.5 -337.5t-385.5 -131.5q-243 0 -384.5 128.5t-141.5 344.5v19h336v-19q0 -83 45.5 -131.5t140.5 -48.5q91 0 138.5 47.5t47.5 128.5v701h-544v293z" />
    <glyph glyph-name="IJ.salt" horiz-adv-x="1931" 
d="M506 1434v-1434h-352v1434h352zM895 1434h897v-994q0 -206 -149.5 -337.5t-385.5 -131.5q-243 0 -384.5 128.5t-141.5 344.5v19h336v-19q0 -83 45.5 -131.5t140.5 -48.5q91 0 139 47.5t48 128.5v701h-545v293z" />
    <glyph glyph-name="Jcircumflex.salt" horiz-adv-x="1271" 
d="M236 1434h897v-994q0 -206 -149.5 -337.5t-385.5 -131.5q-243 0 -384.5 128.5t-141.5 344.5v19h336v-19q0 -83 45.5 -131.5t140.5 -48.5q91 0 138.5 47.5t47.5 128.5v701h-544v293zM870 1839l244 -303h-323l-91 119l-90 -119h-323l243 303h340z" />
    <glyph glyph-name="ornm01.ornm" horiz-adv-x="2232" 
d="M164 571v291h1153v-291h-1153zM995 1434h418l717 -717l-717 -717h-418l717 717z" />
    <glyph glyph-name="ornm02.ornm" horiz-adv-x="2232" 
d="M2068 862v-291h-1153v291h1153zM819 1434h418l-717 -717l717 -717h-418l-717 717z" />
    <glyph glyph-name="ornm03.ornm" horiz-adv-x="1843" 
d="M389 0l-225 225l739 740l225 -226zM1618 420l-315 -295v1014h-1014l295 295h1034v-1014z" />
    <glyph glyph-name="ornm04.ornm" horiz-adv-x="1843" 
d="M1679 225l-225 -225l-739 739l225 226zM541 125l-316 295v1014h1035l294 -295h-1013v-1014z" />
    <glyph glyph-name="ornm05.ornm" horiz-adv-x="1843" 
d="M164 1208l225 226l739 -740l-225 -225zM1303 1309l315 -295v-1014h-1034l-295 295h1014v1014z" />
    <glyph glyph-name="ornm06.ornm" horiz-adv-x="1843" 
d="M1454 1434l225 -226l-739 -739l-225 225zM225 1014l316 295v-1014h1013l-294 -295h-1035v1014z" />
    <glyph glyph-name="ornm07.ornm" horiz-adv-x="1761" 
d="M1040 -356h-319v995h319v-995zM164 328v417l717 717l716 -717v-417l-716 716z" />
    <glyph glyph-name="ornm08.ornm" horiz-adv-x="1761" 
d="M721 1434h319v-992h-319v992zM164 336v418l717 -717l716 717v-418l-716 -717z" />
    <glyph glyph-name="ornm09.ornm" horiz-adv-x="2027" 
d="M143 465v291h963v-291h-963zM1946 610l-582 -581h-418l582 581l-582 582h418z" />
    <glyph glyph-name="ornm10.ornm" horiz-adv-x="2027" 
d="M1884 756v-291h-962v291h962zM664 29l-582 581l582 582h417l-581 -582l581 -581h-417z" />
    <glyph glyph-name="ornm11.ornm" horiz-adv-x="1730" 
d="M348 -295l-205 205l680 680l205 -205zM1526 1073v-809l-301 -295v824h-824l295 280h830z" />
    <glyph glyph-name="ornm12.ornm" horiz-adv-x="1730" 
d="M1587 -90l-205 -205l-680 680l205 205zM205 264v809h829l295 -280h-823v-824z" />
    <glyph glyph-name="ornm13.ornm" horiz-adv-x="1730" 
d="M143 1163l205 205l680 -680l-205 -205zM1526 809v-809h-830l-295 281h824v823z" />
    <glyph glyph-name="ornm14.ornm" horiz-adv-x="1730" 
d="M1382 1368l205 -205l-680 -680l-205 205zM205 0v809l301 295v-823h823l-295 -281h-829z" />
    <glyph glyph-name="ornm15.ornm" horiz-adv-x="1490" 
d="M899 -356h-307v766h307v-766zM164 668l581 581l582 -581v-418l-582 581l-581 -581v418z" />
    <glyph glyph-name="ornm16.ornm" horiz-adv-x="1490" 
d="M592 1229h307v-766h-307v766zM745 -377l-581 582v418l581 -582l582 582v-418z" />
    <glyph glyph-name="ornm17.ornm" horiz-adv-x="1497" 
d="M475 1520h43q17 -15 25.5 -43.5t14 -68t7.5 -48.5q18 -10 81.5 -25t84.5 -35q23 20 58.5 26t67.5 0.5t61 -17.5q-10 -17 -7 -21t27 -4q0 -27 12 -47q7 1 11.5 12t12 17t23.5 0q14 -13 50 -36t51 -40q31 0 65 23.5t49 27.5q6 -21 28.5 -27.5t47.5 -3t51.5 0t38.5 -20.5
q5 0 23 2t33 2t22 -4q-30 -35 -101.5 -62t-92.5 -41q-36 -22 -80 -78.5t-52 -64.5q-19 -18 -53 -43.5t-59 -48t-39 -47.5q0 -19 6 -64t4 -73q-11 -11 -40.5 -29.5t-44.5 -37t-17 -48.5q4 -4 15.5 -15t17 -19.5t8.5 -18.5q-16 -21 -19.5 -52.5t-1 -59t-4.5 -62.5
q13 -10 68.5 -46.5t86.5 -64t52 -61.5q26 26 56.5 9t37.5 -44q-11 -50 -4 -119h-985q0 325 12 477q-56 29 -71 84q3 4 27 21t26 36q11 39 -10 105.5t-23 81.5q-3 32 0.5 121.5t-4.5 130.5q-2 28 -23 80.5t-24 72.5q-1 30 5 80t4 85t-21 59q60 2 181 -2.5t177 -1.5
q5 9 6 22.5t-0.5 36t-1.5 31.5z" />
    <glyph glyph-name="ornm18.ornm" horiz-adv-x="1812" 
d="M100 227q-13 -1 -16.5 10t6.5 17q83 24 135 70l33 30q34 49 57 107q4 12 17 48t16 54l-4 119q-6 75 -6 84q0 49 17 90t38.5 66.5t57 48.5t55 32t49.5 21q7 3 10 4q139 47 283 47q47 0 141 -8q31 -4 62 -6t46 -2h15q18 0 43 4q12 3 23.5 14.5t19.5 24t17 33t13 31.5t10 31
l7 20q19 65 84 139q2 4 5 37t9 41q9 2 31.5 0.5t26.5 -4.5q2 0 57 -11q25 0 29 -6q4 -3 9.5 -10.5t10.5 -11.5q15 -3 25 -13q2 -4 27 -30q4 -4 10 -16t6 -17q0 -11 51 -43q11 -8 74 -47q35 -17 29 -37q-5 -13 -41.5 -25t-55.5 -12t-49 6q-27 7 -63 8t-50 -6q-6 -1 -17 -26
t-13 -35q-5 -58 -15 -103q-11 -43 -29.5 -105t-21.5 -75q-2 -10 -5 -34t-7 -45.5t-10 -39.5q-7 -13 -17 -29t-15 -25t-7 -15q-11 -13 -27 -112.5t-20 -149.5q-9 -93 -6 -224q0 -17 19.5 -41.5t35.5 -21.5q11 0 19.5 -3t11.5 -6l2 -3q3 -4 2 -18t-7 -17l-8 -4l-41 2
q2 -1 5 -3.5t7 -8t1 -9.5q-7 -7 -9 -8h-86q-10 3 -24 31q-1 2 -4.5 18.5t-8 37t-6.5 26.5q-3 9 -11 14t-9 10q0 4 3 26.5t3 49.5q0 4 -6 10.5t-10 14.5q-2 6 -2 47v90t-2 69q0 7 -5 51.5t-6 61.5q-4 17 1 32t5 17q-1 5 -5 7.5t-11 3t-13.5 0t-16 -1.5t-15.5 -1
q-35 0 -91.5 18t-103.5 52q-24 18 -54 52t-52.5 54.5t-44.5 20.5t-46.5 -10t-37.5 -25q-16 -14 -33 -71q-2 -8 -2 -43q-3 -30 -20 -99q-3 -13 -29 -88q-4 -12 -17 -31.5t-24 -33.5l-10 -13q-8 -11 -21 -24.5t-21 -22.5t-9 -14q-6 -9 -11.5 -24t-7.5 -26l-2 -10
q-2 -110 0 -126q0 -12 3 -22t3 -11q6 -3 14 -10t15 -11q2 0 7.5 -1t8.5 -2t6.5 -3.5t4.5 -5.5q2 -8 -0.5 -18t-8.5 -11h-53q-13 4 -29 15l-20 18q-2 2 -5 33t-5.5 64t-3.5 36l-1 8q-1 8 -1.5 19.5t-1.5 25.5t-0.5 29.5t2.5 26.5q0 18 -7 18q-3 0 -10 -10t-19 -24.5
t-16 -20.5q-4 -9 -24 -72.5t-21 -79.5v-84l17 -16q16 -7 16 -21q0 -15 -14 -24h-39q-16 0 -39 29q-6 11 0 104q0 9 2 30t2 38q0 45 4 77q-56 -41 -119 -47z" />
    <hkern u1="&#x20;" u2="&#x1ef3;" k="10" />
    <hkern u1="&#x20;" u2="&#x1ef2;" k="31" />
    <hkern u1="&#x20;" u2="&#x1e84;" k="20" />
    <hkern u1="&#x20;" u2="&#x1e82;" k="20" />
    <hkern u1="&#x20;" u2="&#x1e80;" k="20" />
    <hkern u1="&#x20;" u2="&#x178;" k="31" />
    <hkern u1="&#x20;" u2="&#x177;" k="10" />
    <hkern u1="&#x20;" u2="&#x176;" k="31" />
    <hkern u1="&#x20;" u2="&#x174;" k="20" />
    <hkern u1="&#x20;" u2="&#x164;" k="57" />
    <hkern u1="&#x20;" u2="&#x162;" k="57" />
    <hkern u1="&#x20;" u2="&#xff;" k="10" />
    <hkern u1="&#x20;" u2="&#xfd;" k="10" />
    <hkern u1="&#x20;" u2="&#xdd;" k="31" />
    <hkern u1="&#x20;" u2="y" k="10" />
    <hkern u1="&#x20;" u2="Y" k="31" />
    <hkern u1="&#x20;" u2="W" k="20" />
    <hkern u1="&#x20;" u2="T" k="57" />
    <hkern u1="&#x20;" u2="V" k="61" />
    <hkern u1="&#x21;" g2="Jcircumflex.salt" k="10" />
    <hkern u1="&#x21;" g2="J.salt" k="10" />
    <hkern u1="&#x21;" u2="&#x2039;" k="16" />
    <hkern u1="&#x21;" u2="&#x201e;" k="20" />
    <hkern u1="&#x21;" u2="&#x201a;" k="20" />
    <hkern u1="&#x21;" u2="&#x164;" k="-10" />
    <hkern u1="&#x21;" u2="&#x162;" k="-10" />
    <hkern u1="&#x21;" u2="&#x134;" k="10" />
    <hkern u1="&#x21;" u2="&#xab;" k="16" />
    <hkern u1="&#x21;" u2="&#x7d;" k="10" />
    <hkern u1="&#x21;" u2="]" k="10" />
    <hkern u1="&#x21;" u2="T" k="-10" />
    <hkern u1="&#x21;" u2="J" k="10" />
    <hkern u1="&#x21;" u2="&#x2c;" k="20" />
    <hkern u1="&#x21;" u2="&#x29;" k="10" />
    <hkern u1="&#x21;" u2="\" k="16" />
    <hkern u1="&#x21;" u2="&#x37;" k="8" />
    <hkern u1="&#x21;" u2="&#x36;" k="16" />
    <hkern u1="&#x21;" u2="&#x35;" k="10" />
    <hkern u1="&#x21;" u2="&#x2f;" k="31" />
    <hkern u1="&#x23;" u2="&#x203a;" k="18" />
    <hkern u1="&#x23;" u2="&#x2026;" k="31" />
    <hkern u1="&#x23;" u2="&#x201e;" k="51" />
    <hkern u1="&#x23;" u2="&#x201c;" k="-16" />
    <hkern u1="&#x23;" u2="&#x201a;" k="51" />
    <hkern u1="&#x23;" u2="&#x2018;" k="-16" />
    <hkern u1="&#x23;" u2="&#xbb;" k="18" />
    <hkern u1="&#x23;" u2="&#x7d;" k="41" />
    <hkern u1="&#x23;" u2="]" k="41" />
    <hkern u1="&#x23;" u2="&#x2e;" k="31" />
    <hkern u1="&#x23;" u2="&#x2c;" k="51" />
    <hkern u1="&#x23;" u2="&#x29;" k="41" />
    <hkern u1="&#x23;" u2="&#x2122;" k="20" />
    <hkern u1="&#x23;" u2="\" k="41" />
    <hkern u1="&#x23;" u2="&#x40;" k="12" />
    <hkern u1="&#x23;" u2="&#x39;" k="-14" />
    <hkern u1="&#x23;" u2="&#x34;" k="-16" />
    <hkern u1="&#x23;" u2="&#x31;" k="-16" />
    <hkern u1="&#x23;" u2="&#x2f;" k="102" />
    <hkern u1="&#x24;" u2="&#x201e;" k="31" />
    <hkern u1="&#x24;" u2="&#x201d;" k="31" />
    <hkern u1="&#x24;" u2="&#x201c;" k="20" />
    <hkern u1="&#x24;" u2="&#x201a;" k="31" />
    <hkern u1="&#x24;" u2="&#x2019;" k="31" />
    <hkern u1="&#x24;" u2="&#x2018;" k="20" />
    <hkern u1="&#x24;" u2="&#x149;" k="31" />
    <hkern u1="&#x24;" u2="&#x7d;" k="10" />
    <hkern u1="&#x24;" u2="]" k="10" />
    <hkern u1="&#x24;" u2="&#x2c;" k="31" />
    <hkern u1="&#x24;" u2="&#x29;" k="10" />
    <hkern u1="&#x24;" u2="\" k="82" />
    <hkern u1="&#x24;" u2="&#x2f;" k="61" />
    <hkern u1="&#x25;" u2="&#x2122;" k="143" />
    <hkern u1="&#x25;" u2="&#xbf;" k="-23" />
    <hkern u1="&#x25;" u2="&#xae;" k="57" />
    <hkern u1="&#x25;" u2="\" k="154" />
    <hkern u1="&#x25;" u2="&#x3f;" k="37" />
    <hkern u1="&#x25;" u2="&#x39;" k="4" />
    <hkern u1="&#x25;" u2="&#x38;" k="-16" />
    <hkern u1="&#x25;" u2="&#x37;" k="-16" />
    <hkern u1="&#x25;" u2="&#x36;" k="-31" />
    <hkern u1="&#x25;" u2="&#x35;" k="-31" />
    <hkern u1="&#x25;" u2="&#x34;" k="-31" />
    <hkern u1="&#x25;" u2="&#x33;" k="-31" />
    <hkern u1="&#x25;" u2="&#x32;" k="-31" />
    <hkern u1="&#x25;" u2="&#x31;" k="20" />
    <hkern u1="&#x25;" u2="&#x30;" k="-20" />
    <hkern u1="&#x25;" u2="&#x2f;" k="20" />
    <hkern u1="&#x25;" u2="&#x2a;" k="70" />
    <hkern u1="&#x26;" u2="&#x203a;" k="16" />
    <hkern u1="&#x26;" u2="&#x2039;" k="16" />
    <hkern u1="&#x26;" u2="&#x2030;" k="23" />
    <hkern u1="&#x26;" u2="&#x2026;" k="-10" />
    <hkern u1="&#x26;" u2="&#x201d;" k="113" />
    <hkern u1="&#x26;" u2="&#x201c;" k="88" />
    <hkern u1="&#x26;" u2="&#x2019;" k="113" />
    <hkern u1="&#x26;" u2="&#x2018;" k="88" />
    <hkern u1="&#x26;" u2="&#x2014;" k="51" />
    <hkern u1="&#x26;" u2="&#x2013;" k="51" />
    <hkern u1="&#x26;" u2="&#x1ef3;" k="39" />
    <hkern u1="&#x26;" u2="&#x1ef2;" k="172" />
    <hkern u1="&#x26;" u2="&#x1e85;" k="18" />
    <hkern u1="&#x26;" u2="&#x1e84;" k="86" />
    <hkern u1="&#x26;" u2="&#x1e83;" k="18" />
    <hkern u1="&#x26;" u2="&#x1e82;" k="86" />
    <hkern u1="&#x26;" u2="&#x1e81;" k="18" />
    <hkern u1="&#x26;" u2="&#x1e80;" k="86" />
    <hkern u1="&#x26;" u2="&#x178;" k="172" />
    <hkern u1="&#x26;" u2="&#x177;" k="39" />
    <hkern u1="&#x26;" u2="&#x176;" k="172" />
    <hkern u1="&#x26;" u2="&#x175;" k="18" />
    <hkern u1="&#x26;" u2="&#x174;" k="86" />
    <hkern u1="&#x26;" u2="&#x164;" k="123" />
    <hkern u1="&#x26;" u2="&#x162;" k="123" />
    <hkern u1="&#x26;" u2="&#x149;" k="113" />
    <hkern u1="&#x26;" u2="&#xff;" k="39" />
    <hkern u1="&#x26;" u2="&#xfd;" k="39" />
    <hkern u1="&#x26;" u2="&#xdd;" k="172" />
    <hkern u1="&#x26;" u2="&#xbb;" k="16" />
    <hkern u1="&#x26;" u2="&#xab;" k="16" />
    <hkern u1="&#x26;" u2="&#x7d;" k="41" />
    <hkern u1="&#x26;" u2="y" k="39" />
    <hkern u1="&#x26;" u2="w" k="18" />
    <hkern u1="&#x26;" u2="]" k="41" />
    <hkern u1="&#x26;" u2="Y" k="172" />
    <hkern u1="&#x26;" u2="W" k="86" />
    <hkern u1="&#x26;" u2="T" k="123" />
    <hkern u1="&#x26;" u2="&#x2e;" k="-10" />
    <hkern u1="&#x26;" u2="&#x2d;" k="51" />
    <hkern u1="&#x26;" u2="&#x29;" k="41" />
    <hkern u1="&#x26;" u2="&#x25;" k="23" />
    <hkern u1="&#x26;" u2="&#xa5;" k="4" />
    <hkern u1="&#x26;" u2="&#xa2;" k="20" />
    <hkern u1="&#x26;" u2="v" k="41" />
    <hkern u1="&#x26;" u2="\" k="164" />
    <hkern u1="&#x26;" u2="X" k="-2" />
    <hkern u1="&#x26;" u2="V" k="123" />
    <hkern u1="&#x26;" u2="&#x40;" k="10" />
    <hkern u1="&#x26;" u2="&#x3f;" k="61" />
    <hkern u1="&#x26;" u2="&#x39;" k="23" />
    <hkern u1="&#x26;" u2="&#x37;" k="20" />
    <hkern u1="&#x26;" u2="&#x35;" k="10" />
    <hkern u1="&#x26;" u2="&#x31;" k="20" />
    <hkern u1="&#x26;" u2="&#x2f;" k="20" />
    <hkern u1="&#x26;" u2="&#x2a;" k="80" />
    <hkern u1="&#x28;" u2="&#x20ac;" k="86" />
    <hkern u1="&#x28;" u2="&#xbf;" k="39" />
    <hkern u1="&#x28;" u2="&#xae;" k="20" />
    <hkern u1="&#x28;" u2="&#xa5;" k="20" />
    <hkern u1="&#x28;" u2="&#xa2;" k="51" />
    <hkern u1="&#x28;" u2="x" k="10" />
    <hkern u1="&#x28;" u2="v" k="31" />
    <hkern u1="&#x28;" u2="j" k="-41" />
    <hkern u1="&#x28;" u2="i" k="10" />
    <hkern u1="&#x28;" u2="V" k="-10" />
    <hkern u1="&#x28;" u2="&#x40;" k="41" />
    <hkern u1="&#x28;" u2="&#x3f;" k="20" />
    <hkern u1="&#x28;" u2="&#x39;" k="51" />
    <hkern u1="&#x28;" u2="&#x38;" k="51" />
    <hkern u1="&#x28;" u2="&#x37;" k="10" />
    <hkern u1="&#x28;" u2="&#x36;" k="61" />
    <hkern u1="&#x28;" u2="&#x35;" k="41" />
    <hkern u1="&#x28;" u2="&#x34;" k="51" />
    <hkern u1="&#x28;" u2="&#x33;" k="10" />
    <hkern u1="&#x28;" u2="&#x31;" k="10" />
    <hkern u1="&#x28;" u2="&#x30;" k="31" />
    <hkern u1="&#x28;" u2="&#x2a;" k="72" />
    <hkern u1="&#x28;" u2="&#x26;" k="51" />
    <hkern u1="&#x28;" u2="&#x24;" k="20" />
    <hkern u1="&#x28;" u2="&#x23;" k="51" />
    <hkern u1="&#x28;" u2="&#x21;" k="10" />
    <hkern u1="&#x29;" u2="\" k="72" />
    <hkern u1="&#x29;" u2="&#x2f;" k="41" />
    <hkern u1="&#x29;" u2="&#x2a;" k="10" />
    <hkern u1="&#x2a;" g2="Jcircumflex.salt" k="86" />
    <hkern u1="&#x2a;" g2="J.salt" k="86" />
    <hkern u1="&#x2a;" u2="&#x203a;" k="20" />
    <hkern u1="&#x2a;" u2="&#x2039;" k="41" />
    <hkern u1="&#x2a;" u2="&#x2026;" k="225" />
    <hkern u1="&#x2a;" u2="&#x201e;" k="215" />
    <hkern u1="&#x2a;" u2="&#x201c;" k="8" />
    <hkern u1="&#x2a;" u2="&#x201a;" k="215" />
    <hkern u1="&#x2a;" u2="&#x2018;" k="8" />
    <hkern u1="&#x2a;" u2="&#x2014;" k="29" />
    <hkern u1="&#x2a;" u2="&#x2013;" k="29" />
    <hkern u1="&#x2a;" u2="&#x1ef3;" k="-20" />
    <hkern u1="&#x2a;" u2="&#x1ef2;" k="29" />
    <hkern u1="&#x2a;" u2="&#x1e85;" k="-10" />
    <hkern u1="&#x2a;" u2="&#x1e84;" k="18" />
    <hkern u1="&#x2a;" u2="&#x1e83;" k="-10" />
    <hkern u1="&#x2a;" u2="&#x1e82;" k="18" />
    <hkern u1="&#x2a;" u2="&#x1e81;" k="-10" />
    <hkern u1="&#x2a;" u2="&#x1e80;" k="18" />
    <hkern u1="&#x2a;" u2="&#x219;" k="31" />
    <hkern u1="&#x2a;" u2="&#x1ff;" k="31" />
    <hkern u1="&#x2a;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x2a;" u2="&#x1fd;" k="59" />
    <hkern u1="&#x2a;" u2="&#x1fb;" k="59" />
    <hkern u1="&#x2a;" u2="&#x1fa;" k="205" />
    <hkern u1="&#x2a;" u2="&#x17d;" k="39" />
    <hkern u1="&#x2a;" u2="&#x17b;" k="39" />
    <hkern u1="&#x2a;" u2="&#x179;" k="39" />
    <hkern u1="&#x2a;" u2="&#x178;" k="29" />
    <hkern u1="&#x2a;" u2="&#x177;" k="-20" />
    <hkern u1="&#x2a;" u2="&#x176;" k="29" />
    <hkern u1="&#x2a;" u2="&#x175;" k="-10" />
    <hkern u1="&#x2a;" u2="&#x174;" k="18" />
    <hkern u1="&#x2a;" u2="&#x161;" k="31" />
    <hkern u1="&#x2a;" u2="&#x160;" k="20" />
    <hkern u1="&#x2a;" u2="&#x15f;" k="31" />
    <hkern u1="&#x2a;" u2="&#x15e;" k="20" />
    <hkern u1="&#x2a;" u2="&#x15d;" k="31" />
    <hkern u1="&#x2a;" u2="&#x15c;" k="20" />
    <hkern u1="&#x2a;" u2="&#x15b;" k="31" />
    <hkern u1="&#x2a;" u2="&#x15a;" k="20" />
    <hkern u1="&#x2a;" u2="&#x159;" k="10" />
    <hkern u1="&#x2a;" u2="&#x157;" k="10" />
    <hkern u1="&#x2a;" u2="&#x155;" k="10" />
    <hkern u1="&#x2a;" u2="&#x152;" k="10" />
    <hkern u1="&#x2a;" u2="&#x151;" k="31" />
    <hkern u1="&#x2a;" u2="&#x150;" k="10" />
    <hkern u1="&#x2a;" u2="&#x14f;" k="31" />
    <hkern u1="&#x2a;" u2="&#x14e;" k="10" />
    <hkern u1="&#x2a;" u2="&#x14d;" k="31" />
    <hkern u1="&#x2a;" u2="&#x14c;" k="10" />
    <hkern u1="&#x2a;" u2="&#x14b;" k="10" />
    <hkern u1="&#x2a;" u2="&#x148;" k="10" />
    <hkern u1="&#x2a;" u2="&#x146;" k="10" />
    <hkern u1="&#x2a;" u2="&#x144;" k="10" />
    <hkern u1="&#x2a;" u2="&#x134;" k="369" />
    <hkern u1="&#x2a;" u2="&#x123;" k="31" />
    <hkern u1="&#x2a;" u2="&#x122;" k="10" />
    <hkern u1="&#x2a;" u2="&#x121;" k="31" />
    <hkern u1="&#x2a;" u2="&#x120;" k="10" />
    <hkern u1="&#x2a;" u2="&#x11f;" k="31" />
    <hkern u1="&#x2a;" u2="&#x11e;" k="10" />
    <hkern u1="&#x2a;" u2="&#x11d;" k="31" />
    <hkern u1="&#x2a;" u2="&#x11c;" k="10" />
    <hkern u1="&#x2a;" u2="&#x11b;" k="31" />
    <hkern u1="&#x2a;" u2="&#x119;" k="31" />
    <hkern u1="&#x2a;" u2="&#x117;" k="31" />
    <hkern u1="&#x2a;" u2="&#x115;" k="31" />
    <hkern u1="&#x2a;" u2="&#x113;" k="31" />
    <hkern u1="&#x2a;" u2="&#x111;" k="31" />
    <hkern u1="&#x2a;" u2="&#x10f;" k="31" />
    <hkern u1="&#x2a;" u2="&#x10d;" k="31" />
    <hkern u1="&#x2a;" u2="&#x10c;" k="10" />
    <hkern u1="&#x2a;" u2="&#x10b;" k="31" />
    <hkern u1="&#x2a;" u2="&#x10a;" k="10" />
    <hkern u1="&#x2a;" u2="&#x109;" k="31" />
    <hkern u1="&#x2a;" u2="&#x108;" k="10" />
    <hkern u1="&#x2a;" u2="&#x107;" k="31" />
    <hkern u1="&#x2a;" u2="&#x106;" k="10" />
    <hkern u1="&#x2a;" u2="&#x105;" k="59" />
    <hkern u1="&#x2a;" u2="&#x104;" k="205" />
    <hkern u1="&#x2a;" u2="&#x103;" k="59" />
    <hkern u1="&#x2a;" u2="&#x102;" k="205" />
    <hkern u1="&#x2a;" u2="&#x101;" k="59" />
    <hkern u1="&#x2a;" u2="&#x100;" k="205" />
    <hkern u1="&#x2a;" u2="&#xff;" k="-20" />
    <hkern u1="&#x2a;" u2="&#xfd;" k="-20" />
    <hkern u1="&#x2a;" u2="&#xf8;" k="31" />
    <hkern u1="&#x2a;" u2="&#xf6;" k="31" />
    <hkern u1="&#x2a;" u2="&#xf5;" k="31" />
    <hkern u1="&#x2a;" u2="&#xf4;" k="31" />
    <hkern u1="&#x2a;" u2="&#xf3;" k="31" />
    <hkern u1="&#x2a;" u2="&#xf2;" k="31" />
    <hkern u1="&#x2a;" u2="&#xeb;" k="31" />
    <hkern u1="&#x2a;" u2="&#xea;" k="31" />
    <hkern u1="&#x2a;" u2="&#xe9;" k="31" />
    <hkern u1="&#x2a;" u2="&#xe8;" k="31" />
    <hkern u1="&#x2a;" u2="&#xe7;" k="31" />
    <hkern u1="&#x2a;" u2="&#xe6;" k="59" />
    <hkern u1="&#x2a;" u2="&#xe5;" k="59" />
    <hkern u1="&#x2a;" u2="&#xe4;" k="59" />
    <hkern u1="&#x2a;" u2="&#xe3;" k="59" />
    <hkern u1="&#x2a;" u2="&#xe2;" k="59" />
    <hkern u1="&#x2a;" u2="&#xe1;" k="59" />
    <hkern u1="&#x2a;" u2="&#xe0;" k="59" />
    <hkern u1="&#x2a;" u2="&#xdd;" k="29" />
    <hkern u1="&#x2a;" u2="&#xd8;" k="10" />
    <hkern u1="&#x2a;" u2="&#xd6;" k="10" />
    <hkern u1="&#x2a;" u2="&#xd5;" k="10" />
    <hkern u1="&#x2a;" u2="&#xd4;" k="10" />
    <hkern u1="&#x2a;" u2="&#xd3;" k="10" />
    <hkern u1="&#x2a;" u2="&#xd2;" k="10" />
    <hkern u1="&#x2a;" u2="&#xc7;" k="10" />
    <hkern u1="&#x2a;" u2="&#xc5;" k="205" />
    <hkern u1="&#x2a;" u2="&#xc4;" k="205" />
    <hkern u1="&#x2a;" u2="&#xc3;" k="205" />
    <hkern u1="&#x2a;" u2="&#xc2;" k="205" />
    <hkern u1="&#x2a;" u2="&#xc1;" k="205" />
    <hkern u1="&#x2a;" u2="&#xc0;" k="205" />
    <hkern u1="&#x2a;" u2="&#xbb;" k="20" />
    <hkern u1="&#x2a;" u2="&#xab;" k="41" />
    <hkern u1="&#x2a;" u2="&#x7d;" k="72" />
    <hkern u1="&#x2a;" u2="&#x7b;" k="20" />
    <hkern u1="&#x2a;" u2="y" k="-20" />
    <hkern u1="&#x2a;" u2="w" k="-10" />
    <hkern u1="&#x2a;" u2="s" k="31" />
    <hkern u1="&#x2a;" u2="r" k="10" />
    <hkern u1="&#x2a;" u2="q" k="31" />
    <hkern u1="&#x2a;" u2="p" k="10" />
    <hkern u1="&#x2a;" u2="o" k="31" />
    <hkern u1="&#x2a;" u2="n" k="10" />
    <hkern u1="&#x2a;" u2="m" k="10" />
    <hkern u1="&#x2a;" u2="g" k="31" />
    <hkern u1="&#x2a;" u2="e" k="31" />
    <hkern u1="&#x2a;" u2="d" k="31" />
    <hkern u1="&#x2a;" u2="c" k="31" />
    <hkern u1="&#x2a;" u2="a" k="59" />
    <hkern u1="&#x2a;" u2="]" k="72" />
    <hkern u1="&#x2a;" u2="[" k="20" />
    <hkern u1="&#x2a;" u2="Z" k="39" />
    <hkern u1="&#x2a;" u2="Y" k="29" />
    <hkern u1="&#x2a;" u2="W" k="18" />
    <hkern u1="&#x2a;" u2="S" k="20" />
    <hkern u1="&#x2a;" u2="Q" k="10" />
    <hkern u1="&#x2a;" u2="O" k="10" />
    <hkern u1="&#x2a;" u2="J" k="369" />
    <hkern u1="&#x2a;" u2="G" k="10" />
    <hkern u1="&#x2a;" u2="C" k="10" />
    <hkern u1="&#x2a;" u2="A" k="205" />
    <hkern u1="&#x2a;" u2="&#x3b;" k="39" />
    <hkern u1="&#x2a;" u2="&#x3a;" k="39" />
    <hkern u1="&#x2a;" u2="&#x2e;" k="225" />
    <hkern u1="&#x2a;" u2="&#x2d;" k="29" />
    <hkern u1="&#x2a;" u2="&#x2c;" k="215" />
    <hkern u1="&#x2a;" u2="&#x29;" k="72" />
    <hkern u1="&#x2a;" u2="&#x28;" k="20" />
    <hkern u1="&#x2a;" u2="&#xbf;" k="143" />
    <hkern u1="&#x2a;" u2="&#xa5;" k="20" />
    <hkern u1="&#x2a;" u2="&#xa3;" k="51" />
    <hkern u1="&#x2a;" u2="&#xa1;" k="39" />
    <hkern u1="&#x2a;" u2="v" k="-31" />
    <hkern u1="&#x2a;" u2="\" k="20" />
    <hkern u1="&#x2a;" u2="X" k="86" />
    <hkern u1="&#x2a;" u2="V" k="16" />
    <hkern u1="&#x2a;" u2="&#x40;" k="41" />
    <hkern u1="&#x2a;" u2="&#x38;" k="41" />
    <hkern u1="&#x2a;" u2="&#x37;" k="10" />
    <hkern u1="&#x2a;" u2="&#x36;" k="72" />
    <hkern u1="&#x2a;" u2="&#x35;" k="41" />
    <hkern u1="&#x2a;" u2="&#x34;" k="102" />
    <hkern u1="&#x2a;" u2="&#x33;" k="20" />
    <hkern u1="&#x2a;" u2="&#x32;" k="20" />
    <hkern u1="&#x2a;" u2="&#x2f;" k="231" />
    <hkern u1="&#x2a;" u2="&#x26;" k="57" />
    <hkern u1="&#x2a;" u2="&#x23;" k="29" />
    <hkern u1="&#x2c;" u2="&#x20ac;" k="20" />
    <hkern u1="&#x2c;" u2="&#xbf;" k="4" />
    <hkern u1="&#x2c;" u2="&#xa5;" k="41" />
    <hkern u1="&#x2c;" u2="&#xa2;" k="31" />
    <hkern u1="&#x2c;" u2="x" k="20" />
    <hkern u1="&#x2c;" u2="v" k="102" />
    <hkern u1="&#x2c;" u2="j" k="10" />
    <hkern u1="&#x2c;" u2="\" k="174" />
    <hkern u1="&#x2c;" u2="X" k="41" />
    <hkern u1="&#x2c;" u2="V" k="215" />
    <hkern u1="&#x2c;" u2="&#x3f;" k="113" />
    <hkern u1="&#x2c;" u2="&#x39;" k="135" />
    <hkern u1="&#x2c;" u2="&#x38;" k="10" />
    <hkern u1="&#x2c;" u2="&#x37;" k="31" />
    <hkern u1="&#x2c;" u2="&#x31;" k="113" />
    <hkern u1="&#x2c;" u2="&#x30;" k="10" />
    <hkern u1="&#x2c;" u2="&#x2a;" k="205" />
    <hkern u1="&#x2c;" u2="&#x23;" k="4" />
    <hkern u1="&#x2c;" u2="&#x21;" k="16" />
    <hkern u1="&#x2d;" u2="&#x2122;" k="39" />
    <hkern u1="&#x2d;" u2="&#xa5;" k="20" />
    <hkern u1="&#x2d;" u2="x" k="82" />
    <hkern u1="&#x2d;" u2="v" k="20" />
    <hkern u1="&#x2d;" u2="\" k="113" />
    <hkern u1="&#x2d;" u2="X" k="174" />
    <hkern u1="&#x2d;" u2="V" k="113" />
    <hkern u1="&#x2d;" u2="&#x3f;" k="20" />
    <hkern u1="&#x2d;" u2="&#x39;" k="14" />
    <hkern u1="&#x2d;" u2="&#x37;" k="72" />
    <hkern u1="&#x2d;" u2="&#x33;" k="10" />
    <hkern u1="&#x2d;" u2="&#x32;" k="20" />
    <hkern u1="&#x2d;" u2="&#x31;" k="31" />
    <hkern u1="&#x2d;" u2="&#x2f;" k="133" />
    <hkern u1="&#x2d;" u2="&#x2a;" k="20" />
    <hkern u1="&#x2e;" u2="&#x2122;" k="213" />
    <hkern u1="&#x2e;" u2="&#x20ac;" k="57" />
    <hkern u1="&#x2e;" u2="&#xae;" k="152" />
    <hkern u1="&#x2e;" u2="&#xa5;" k="51" />
    <hkern u1="&#x2e;" u2="&#xa2;" k="61" />
    <hkern u1="&#x2e;" u2="x" k="20" />
    <hkern u1="&#x2e;" u2="v" k="164" />
    <hkern u1="&#x2e;" u2="j" k="10" />
    <hkern u1="&#x2e;" u2="\" k="225" />
    <hkern u1="&#x2e;" u2="X" k="31" />
    <hkern u1="&#x2e;" u2="V" k="287" />
    <hkern u1="&#x2e;" u2="&#x40;" k="10" />
    <hkern u1="&#x2e;" u2="&#x3f;" k="92" />
    <hkern u1="&#x2e;" u2="&#x39;" k="152" />
    <hkern u1="&#x2e;" u2="&#x37;" k="41" />
    <hkern u1="&#x2e;" u2="&#x36;" k="10" />
    <hkern u1="&#x2e;" u2="&#x35;" k="10" />
    <hkern u1="&#x2e;" u2="&#x34;" k="10" />
    <hkern u1="&#x2e;" u2="&#x31;" k="123" />
    <hkern u1="&#x2e;" u2="&#x30;" k="10" />
    <hkern u1="&#x2e;" u2="&#x2f;" k="20" />
    <hkern u1="&#x2e;" u2="&#x2a;" k="164" />
    <hkern u1="&#x2e;" u2="&#x26;" k="10" />
    <hkern u1="&#x2e;" u2="&#x23;" k="20" />
    <hkern u1="&#x2f;" g2="Jcircumflex.salt" k="92" />
    <hkern u1="&#x2f;" g2="J.salt" k="92" />
    <hkern u1="&#x2f;" u2="&#xfb02;" k="41" />
    <hkern u1="&#x2f;" u2="&#xfb01;" k="41" />
    <hkern u1="&#x2f;" u2="&#x2117;" k="80" />
    <hkern u1="&#x2f;" u2="&#x203a;" k="113" />
    <hkern u1="&#x2f;" u2="&#x2039;" k="133" />
    <hkern u1="&#x2f;" u2="&#x2030;" k="20" />
    <hkern u1="&#x2f;" u2="&#x2026;" k="297" />
    <hkern u1="&#x2f;" u2="&#x201e;" k="266" />
    <hkern u1="&#x2f;" u2="&#x201a;" k="266" />
    <hkern u1="&#x2f;" u2="&#x2014;" k="154" />
    <hkern u1="&#x2f;" u2="&#x2013;" k="154" />
    <hkern u1="&#x2f;" u2="&#x1ef3;" k="31" />
    <hkern u1="&#x2f;" u2="&#x1ef2;" k="-20" />
    <hkern u1="&#x2f;" u2="&#x1e85;" k="31" />
    <hkern u1="&#x2f;" u2="&#x1e84;" k="-10" />
    <hkern u1="&#x2f;" u2="&#x1e83;" k="31" />
    <hkern u1="&#x2f;" u2="&#x1e82;" k="-10" />
    <hkern u1="&#x2f;" u2="&#x1e81;" k="31" />
    <hkern u1="&#x2f;" u2="&#x1e80;" k="-10" />
    <hkern u1="&#x2f;" u2="&#x219;" k="123" />
    <hkern u1="&#x2f;" u2="&#x1ff;" k="143" />
    <hkern u1="&#x2f;" u2="&#x1fe;" k="72" />
    <hkern u1="&#x2f;" u2="&#x1fd;" k="184" />
    <hkern u1="&#x2f;" u2="&#x1fb;" k="184" />
    <hkern u1="&#x2f;" u2="&#x1fa;" k="184" />
    <hkern u1="&#x2f;" u2="&#x17e;" k="61" />
    <hkern u1="&#x2f;" u2="&#x17c;" k="61" />
    <hkern u1="&#x2f;" u2="&#x17a;" k="61" />
    <hkern u1="&#x2f;" u2="&#x178;" k="-20" />
    <hkern u1="&#x2f;" u2="&#x177;" k="31" />
    <hkern u1="&#x2f;" u2="&#x176;" k="-20" />
    <hkern u1="&#x2f;" u2="&#x175;" k="31" />
    <hkern u1="&#x2f;" u2="&#x174;" k="-10" />
    <hkern u1="&#x2f;" u2="&#x173;" k="102" />
    <hkern u1="&#x2f;" u2="&#x171;" k="102" />
    <hkern u1="&#x2f;" u2="&#x16f;" k="102" />
    <hkern u1="&#x2f;" u2="&#x16d;" k="102" />
    <hkern u1="&#x2f;" u2="&#x16b;" k="102" />
    <hkern u1="&#x2f;" u2="&#x169;" k="102" />
    <hkern u1="&#x2f;" u2="&#x164;" k="-20" />
    <hkern u1="&#x2f;" u2="&#x163;" k="31" />
    <hkern u1="&#x2f;" u2="&#x162;" k="-20" />
    <hkern u1="&#x2f;" u2="&#x161;" k="123" />
    <hkern u1="&#x2f;" u2="&#x160;" k="72" />
    <hkern u1="&#x2f;" u2="&#x15f;" k="123" />
    <hkern u1="&#x2f;" u2="&#x15e;" k="72" />
    <hkern u1="&#x2f;" u2="&#x15d;" k="123" />
    <hkern u1="&#x2f;" u2="&#x15c;" k="72" />
    <hkern u1="&#x2f;" u2="&#x15b;" k="123" />
    <hkern u1="&#x2f;" u2="&#x15a;" k="72" />
    <hkern u1="&#x2f;" u2="&#x159;" k="92" />
    <hkern u1="&#x2f;" u2="&#x157;" k="92" />
    <hkern u1="&#x2f;" u2="&#x155;" k="92" />
    <hkern u1="&#x2f;" u2="&#x152;" k="72" />
    <hkern u1="&#x2f;" u2="&#x151;" k="143" />
    <hkern u1="&#x2f;" u2="&#x150;" k="72" />
    <hkern u1="&#x2f;" u2="&#x14f;" k="143" />
    <hkern u1="&#x2f;" u2="&#x14e;" k="72" />
    <hkern u1="&#x2f;" u2="&#x14d;" k="143" />
    <hkern u1="&#x2f;" u2="&#x14c;" k="72" />
    <hkern u1="&#x2f;" u2="&#x14b;" k="92" />
    <hkern u1="&#x2f;" u2="&#x148;" k="92" />
    <hkern u1="&#x2f;" u2="&#x146;" k="92" />
    <hkern u1="&#x2f;" u2="&#x144;" k="92" />
    <hkern u1="&#x2f;" u2="&#x134;" k="174" />
    <hkern u1="&#x2f;" u2="&#x123;" k="143" />
    <hkern u1="&#x2f;" u2="&#x122;" k="72" />
    <hkern u1="&#x2f;" u2="&#x121;" k="143" />
    <hkern u1="&#x2f;" u2="&#x120;" k="72" />
    <hkern u1="&#x2f;" u2="&#x11f;" k="143" />
    <hkern u1="&#x2f;" u2="&#x11e;" k="72" />
    <hkern u1="&#x2f;" u2="&#x11d;" k="143" />
    <hkern u1="&#x2f;" u2="&#x11c;" k="72" />
    <hkern u1="&#x2f;" u2="&#x11b;" k="143" />
    <hkern u1="&#x2f;" u2="&#x119;" k="143" />
    <hkern u1="&#x2f;" u2="&#x117;" k="143" />
    <hkern u1="&#x2f;" u2="&#x115;" k="143" />
    <hkern u1="&#x2f;" u2="&#x113;" k="143" />
    <hkern u1="&#x2f;" u2="&#x111;" k="143" />
    <hkern u1="&#x2f;" u2="&#x10f;" k="143" />
    <hkern u1="&#x2f;" u2="&#x10d;" k="143" />
    <hkern u1="&#x2f;" u2="&#x10c;" k="72" />
    <hkern u1="&#x2f;" u2="&#x10b;" k="143" />
    <hkern u1="&#x2f;" u2="&#x10a;" k="72" />
    <hkern u1="&#x2f;" u2="&#x109;" k="143" />
    <hkern u1="&#x2f;" u2="&#x108;" k="72" />
    <hkern u1="&#x2f;" u2="&#x107;" k="143" />
    <hkern u1="&#x2f;" u2="&#x106;" k="72" />
    <hkern u1="&#x2f;" u2="&#x105;" k="184" />
    <hkern u1="&#x2f;" u2="&#x104;" k="184" />
    <hkern u1="&#x2f;" u2="&#x103;" k="184" />
    <hkern u1="&#x2f;" u2="&#x102;" k="184" />
    <hkern u1="&#x2f;" u2="&#x101;" k="184" />
    <hkern u1="&#x2f;" u2="&#x100;" k="184" />
    <hkern u1="&#x2f;" u2="&#xff;" k="31" />
    <hkern u1="&#x2f;" u2="&#xfd;" k="31" />
    <hkern u1="&#x2f;" u2="&#xfc;" k="102" />
    <hkern u1="&#x2f;" u2="&#xfb;" k="102" />
    <hkern u1="&#x2f;" u2="&#xfa;" k="102" />
    <hkern u1="&#x2f;" u2="&#xf9;" k="102" />
    <hkern u1="&#x2f;" u2="&#xf8;" k="143" />
    <hkern u1="&#x2f;" u2="&#xf6;" k="143" />
    <hkern u1="&#x2f;" u2="&#xf5;" k="143" />
    <hkern u1="&#x2f;" u2="&#xf4;" k="143" />
    <hkern u1="&#x2f;" u2="&#xf3;" k="143" />
    <hkern u1="&#x2f;" u2="&#xf2;" k="143" />
    <hkern u1="&#x2f;" u2="&#xeb;" k="143" />
    <hkern u1="&#x2f;" u2="&#xea;" k="143" />
    <hkern u1="&#x2f;" u2="&#xe9;" k="143" />
    <hkern u1="&#x2f;" u2="&#xe8;" k="143" />
    <hkern u1="&#x2f;" u2="&#xe7;" k="143" />
    <hkern u1="&#x2f;" u2="&#xe6;" k="184" />
    <hkern u1="&#x2f;" u2="&#xe5;" k="184" />
    <hkern u1="&#x2f;" u2="&#xe4;" k="184" />
    <hkern u1="&#x2f;" u2="&#xe3;" k="184" />
    <hkern u1="&#x2f;" u2="&#xe2;" k="184" />
    <hkern u1="&#x2f;" u2="&#xe1;" k="184" />
    <hkern u1="&#x2f;" u2="&#xe0;" k="184" />
    <hkern u1="&#x2f;" u2="&#xdd;" k="-20" />
    <hkern u1="&#x2f;" u2="&#xd8;" k="72" />
    <hkern u1="&#x2f;" u2="&#xd6;" k="72" />
    <hkern u1="&#x2f;" u2="&#xd5;" k="72" />
    <hkern u1="&#x2f;" u2="&#xd4;" k="72" />
    <hkern u1="&#x2f;" u2="&#xd3;" k="72" />
    <hkern u1="&#x2f;" u2="&#xd2;" k="72" />
    <hkern u1="&#x2f;" u2="&#xc7;" k="72" />
    <hkern u1="&#x2f;" u2="&#xc5;" k="184" />
    <hkern u1="&#x2f;" u2="&#xc4;" k="184" />
    <hkern u1="&#x2f;" u2="&#xc3;" k="184" />
    <hkern u1="&#x2f;" u2="&#xc2;" k="184" />
    <hkern u1="&#x2f;" u2="&#xc1;" k="184" />
    <hkern u1="&#x2f;" u2="&#xc0;" k="184" />
    <hkern u1="&#x2f;" u2="&#xbb;" k="113" />
    <hkern u1="&#x2f;" u2="&#xba;" k="31" />
    <hkern u1="&#x2f;" u2="&#xab;" k="133" />
    <hkern u1="&#x2f;" u2="&#xaa;" k="31" />
    <hkern u1="&#x2f;" u2="&#xa9;" k="80" />
    <hkern u1="&#x2f;" u2="&#x7b;" k="51" />
    <hkern u1="&#x2f;" u2="z" k="61" />
    <hkern u1="&#x2f;" u2="y" k="31" />
    <hkern u1="&#x2f;" u2="w" k="31" />
    <hkern u1="&#x2f;" u2="u" k="102" />
    <hkern u1="&#x2f;" u2="t" k="31" />
    <hkern u1="&#x2f;" u2="s" k="123" />
    <hkern u1="&#x2f;" u2="r" k="92" />
    <hkern u1="&#x2f;" u2="q" k="143" />
    <hkern u1="&#x2f;" u2="p" k="92" />
    <hkern u1="&#x2f;" u2="o" k="143" />
    <hkern u1="&#x2f;" u2="n" k="92" />
    <hkern u1="&#x2f;" u2="m" k="92" />
    <hkern u1="&#x2f;" u2="g" k="143" />
    <hkern u1="&#x2f;" u2="f" k="41" />
    <hkern u1="&#x2f;" u2="e" k="143" />
    <hkern u1="&#x2f;" u2="d" k="143" />
    <hkern u1="&#x2f;" u2="c" k="143" />
    <hkern u1="&#x2f;" u2="a" k="184" />
    <hkern u1="&#x2f;" u2="[" k="51" />
    <hkern u1="&#x2f;" u2="Y" k="-20" />
    <hkern u1="&#x2f;" u2="W" k="-10" />
    <hkern u1="&#x2f;" u2="T" k="-20" />
    <hkern u1="&#x2f;" u2="S" k="72" />
    <hkern u1="&#x2f;" u2="Q" k="72" />
    <hkern u1="&#x2f;" u2="O" k="72" />
    <hkern u1="&#x2f;" u2="J" k="174" />
    <hkern u1="&#x2f;" u2="G" k="72" />
    <hkern u1="&#x2f;" u2="C" k="72" />
    <hkern u1="&#x2f;" u2="A" k="184" />
    <hkern u1="&#x2f;" u2="&#x3b;" k="174" />
    <hkern u1="&#x2f;" u2="&#x3a;" k="174" />
    <hkern u1="&#x2f;" u2="&#x2e;" k="297" />
    <hkern u1="&#x2f;" u2="&#x2d;" k="154" />
    <hkern u1="&#x2f;" u2="&#x2c;" k="266" />
    <hkern u1="&#x2f;" u2="&#x28;" k="51" />
    <hkern u1="&#x2f;" u2="&#x25;" k="20" />
    <hkern u1="&#x2f;" u2="&#x2122;" k="-35" />
    <hkern u1="&#x2f;" u2="&#x20ac;" k="92" />
    <hkern u1="&#x2f;" u2="&#xbf;" k="143" />
    <hkern u1="&#x2f;" u2="&#xae;" k="57" />
    <hkern u1="&#x2f;" u2="&#xa5;" k="20" />
    <hkern u1="&#x2f;" u2="&#xa3;" k="133" />
    <hkern u1="&#x2f;" u2="&#xa2;" k="82" />
    <hkern u1="&#x2f;" u2="&#xa1;" k="20" />
    <hkern u1="&#x2f;" u2="x" k="51" />
    <hkern u1="&#x2f;" u2="v" k="20" />
    <hkern u1="&#x2f;" u2="V" k="-10" />
    <hkern u1="&#x2f;" u2="&#x40;" k="154" />
    <hkern u1="&#x2f;" u2="&#x3f;" k="31" />
    <hkern u1="&#x2f;" u2="&#x39;" k="41" />
    <hkern u1="&#x2f;" u2="&#x38;" k="61" />
    <hkern u1="&#x2f;" u2="&#x36;" k="123" />
    <hkern u1="&#x2f;" u2="&#x35;" k="72" />
    <hkern u1="&#x2f;" u2="&#x34;" k="102" />
    <hkern u1="&#x2f;" u2="&#x33;" k="51" />
    <hkern u1="&#x2f;" u2="&#x32;" k="61" />
    <hkern u1="&#x2f;" u2="&#x31;" k="10" />
    <hkern u1="&#x2f;" u2="&#x30;" k="55" />
    <hkern u1="&#x2f;" u2="&#x2a;" k="20" />
    <hkern u1="&#x2f;" u2="&#x26;" k="133" />
    <hkern u1="&#x2f;" u2="&#x24;" k="61" />
    <hkern u1="&#x2f;" u2="&#x23;" k="82" />
    <hkern u1="&#x30;" u2="&#x2026;" k="10" />
    <hkern u1="&#x30;" u2="&#x201e;" k="35" />
    <hkern u1="&#x30;" u2="&#x201a;" k="35" />
    <hkern u1="&#x30;" u2="&#x7d;" k="31" />
    <hkern u1="&#x30;" u2="]" k="31" />
    <hkern u1="&#x30;" u2="&#x2e;" k="10" />
    <hkern u1="&#x30;" u2="&#x2c;" k="35" />
    <hkern u1="&#x30;" u2="&#x29;" k="31" />
    <hkern u1="&#x30;" u2="&#x2122;" k="29" />
    <hkern u1="&#x30;" u2="\" k="31" />
    <hkern u1="&#x30;" u2="&#x37;" k="20" />
    <hkern u1="&#x30;" u2="&#x2f;" k="49" />
    <hkern u1="&#x32;" u2="&#x203a;" k="10" />
    <hkern u1="&#x32;" u2="&#x2039;" k="10" />
    <hkern u1="&#x32;" u2="&#x201e;" k="10" />
    <hkern u1="&#x32;" u2="&#x201d;" k="20" />
    <hkern u1="&#x32;" u2="&#x201c;" k="31" />
    <hkern u1="&#x32;" u2="&#x201a;" k="10" />
    <hkern u1="&#x32;" u2="&#x2019;" k="20" />
    <hkern u1="&#x32;" u2="&#x2018;" k="31" />
    <hkern u1="&#x32;" u2="&#x2014;" k="31" />
    <hkern u1="&#x32;" u2="&#x2013;" k="31" />
    <hkern u1="&#x32;" u2="&#x149;" k="20" />
    <hkern u1="&#x32;" u2="&#xbb;" k="10" />
    <hkern u1="&#x32;" u2="&#xab;" k="10" />
    <hkern u1="&#x32;" u2="&#x7d;" k="10" />
    <hkern u1="&#x32;" u2="]" k="10" />
    <hkern u1="&#x32;" u2="&#x2d;" k="31" />
    <hkern u1="&#x32;" u2="&#x2c;" k="10" />
    <hkern u1="&#x32;" u2="&#x29;" k="10" />
    <hkern u1="&#x32;" u2="&#x2122;" k="20" />
    <hkern u1="&#x32;" u2="\" k="51" />
    <hkern u1="&#x32;" u2="&#x39;" k="10" />
    <hkern u1="&#x32;" u2="&#x37;" k="10" />
    <hkern u1="&#x32;" u2="&#x36;" k="20" />
    <hkern u1="&#x32;" u2="&#x35;" k="20" />
    <hkern u1="&#x32;" u2="&#x34;" k="16" />
    <hkern u1="&#x32;" u2="&#x33;" k="10" />
    <hkern u1="&#x32;" u2="&#x32;" k="10" />
    <hkern u1="&#x32;" u2="&#x30;" k="20" />
    <hkern u1="&#x32;" u2="&#x2f;" k="51" />
    <hkern u1="&#x32;" u2="&#x2a;" k="10" />
    <hkern u1="&#x32;" u2="&#x23;" k="4" />
    <hkern u1="&#x33;" u2="&#x203a;" k="47" />
    <hkern u1="&#x33;" u2="&#x2030;" k="23" />
    <hkern u1="&#x33;" u2="&#x201e;" k="41" />
    <hkern u1="&#x33;" u2="&#x201d;" k="51" />
    <hkern u1="&#x33;" u2="&#x201c;" k="72" />
    <hkern u1="&#x33;" u2="&#x201a;" k="41" />
    <hkern u1="&#x33;" u2="&#x2019;" k="51" />
    <hkern u1="&#x33;" u2="&#x2018;" k="72" />
    <hkern u1="&#x33;" u2="&#x2014;" k="10" />
    <hkern u1="&#x33;" u2="&#x2013;" k="10" />
    <hkern u1="&#x33;" u2="&#x149;" k="51" />
    <hkern u1="&#x33;" u2="&#xbb;" k="47" />
    <hkern u1="&#x33;" u2="&#x7d;" k="31" />
    <hkern u1="&#x33;" u2="]" k="31" />
    <hkern u1="&#x33;" u2="&#x3b;" k="20" />
    <hkern u1="&#x33;" u2="&#x3a;" k="20" />
    <hkern u1="&#x33;" u2="&#x2d;" k="10" />
    <hkern u1="&#x33;" u2="&#x2c;" k="41" />
    <hkern u1="&#x33;" u2="&#x29;" k="31" />
    <hkern u1="&#x33;" u2="&#x25;" k="23" />
    <hkern u1="&#x33;" u2="&#x2122;" k="29" />
    <hkern u1="&#x33;" u2="&#xae;" k="23" />
    <hkern u1="&#x33;" u2="\" k="31" />
    <hkern u1="&#x33;" u2="&#x3f;" k="20" />
    <hkern u1="&#x33;" u2="&#x39;" k="37" />
    <hkern u1="&#x33;" u2="&#x37;" k="25" />
    <hkern u1="&#x33;" u2="&#x32;" k="14" />
    <hkern u1="&#x33;" u2="&#x31;" k="16" />
    <hkern u1="&#x33;" u2="&#x2f;" k="82" />
    <hkern u1="&#x33;" u2="&#x2a;" k="20" />
    <hkern u1="&#x34;" u2="&#x203a;" k="18" />
    <hkern u1="&#x34;" u2="&#x2039;" k="-37" />
    <hkern u1="&#x34;" u2="&#x2030;" k="43" />
    <hkern u1="&#x34;" u2="&#x201d;" k="154" />
    <hkern u1="&#x34;" u2="&#x201c;" k="133" />
    <hkern u1="&#x34;" u2="&#x2019;" k="154" />
    <hkern u1="&#x34;" u2="&#x2018;" k="133" />
    <hkern u1="&#x34;" u2="&#x149;" k="154" />
    <hkern u1="&#x34;" u2="&#xbb;" k="18" />
    <hkern u1="&#x34;" u2="&#xba;" k="10" />
    <hkern u1="&#x34;" u2="&#xab;" k="-37" />
    <hkern u1="&#x34;" u2="&#xaa;" k="10" />
    <hkern u1="&#x34;" u2="&#x7d;" k="31" />
    <hkern u1="&#x34;" u2="]" k="31" />
    <hkern u1="&#x34;" u2="&#x29;" k="31" />
    <hkern u1="&#x34;" u2="&#x25;" k="43" />
    <hkern u1="&#x34;" u2="&#x2122;" k="176" />
    <hkern u1="&#x34;" u2="&#xae;" k="106" />
    <hkern u1="&#x34;" u2="\" k="123" />
    <hkern u1="&#x34;" u2="&#x3f;" k="57" />
    <hkern u1="&#x34;" u2="&#x39;" k="57" />
    <hkern u1="&#x34;" u2="&#x37;" k="43" />
    <hkern u1="&#x34;" u2="&#x36;" k="2" />
    <hkern u1="&#x34;" u2="&#x35;" k="6" />
    <hkern u1="&#x34;" u2="&#x31;" k="74" />
    <hkern u1="&#x34;" u2="&#x30;" k="6" />
    <hkern u1="&#x34;" u2="&#x2f;" k="20" />
    <hkern u1="&#x34;" u2="&#x2a;" k="102" />
    <hkern u1="&#x35;" u2="&#x203a;" k="35" />
    <hkern u1="&#x35;" u2="&#x2039;" k="-6" />
    <hkern u1="&#x35;" u2="&#x2030;" k="23" />
    <hkern u1="&#x35;" u2="&#x2026;" k="10" />
    <hkern u1="&#x35;" u2="&#x201e;" k="41" />
    <hkern u1="&#x35;" u2="&#x201d;" k="72" />
    <hkern u1="&#x35;" u2="&#x201c;" k="72" />
    <hkern u1="&#x35;" u2="&#x201a;" k="41" />
    <hkern u1="&#x35;" u2="&#x2019;" k="72" />
    <hkern u1="&#x35;" u2="&#x2018;" k="72" />
    <hkern u1="&#x35;" u2="&#x149;" k="72" />
    <hkern u1="&#x35;" u2="&#xbb;" k="35" />
    <hkern u1="&#x35;" u2="&#xab;" k="-6" />
    <hkern u1="&#x35;" u2="&#x7d;" k="51" />
    <hkern u1="&#x35;" u2="]" k="51" />
    <hkern u1="&#x35;" u2="&#x3b;" k="20" />
    <hkern u1="&#x35;" u2="&#x3a;" k="20" />
    <hkern u1="&#x35;" u2="&#x2e;" k="10" />
    <hkern u1="&#x35;" u2="&#x2c;" k="41" />
    <hkern u1="&#x35;" u2="&#x29;" k="51" />
    <hkern u1="&#x35;" u2="&#x25;" k="23" />
    <hkern u1="&#x35;" u2="&#x2122;" k="57" />
    <hkern u1="&#x35;" u2="&#xae;" k="61" />
    <hkern u1="&#x35;" u2="\" k="51" />
    <hkern u1="&#x35;" u2="&#x3f;" k="31" />
    <hkern u1="&#x35;" u2="&#x39;" k="35" />
    <hkern u1="&#x35;" u2="&#x37;" k="80" />
    <hkern u1="&#x35;" u2="&#x36;" k="10" />
    <hkern u1="&#x35;" u2="&#x35;" k="10" />
    <hkern u1="&#x35;" u2="&#x33;" k="14" />
    <hkern u1="&#x35;" u2="&#x32;" k="25" />
    <hkern u1="&#x35;" u2="&#x31;" k="61" />
    <hkern u1="&#x35;" u2="&#x2f;" k="72" />
    <hkern u1="&#x35;" u2="&#x2a;" k="51" />
    <hkern u1="&#x35;" u2="&#x21;" k="10" />
    <hkern u1="&#x36;" u2="&#x203a;" k="27" />
    <hkern u1="&#x36;" u2="&#x2039;" k="-31" />
    <hkern u1="&#x36;" u2="&#x2030;" k="4" />
    <hkern u1="&#x36;" u2="&#x2026;" k="10" />
    <hkern u1="&#x36;" u2="&#x201e;" k="31" />
    <hkern u1="&#x36;" u2="&#x201d;" k="82" />
    <hkern u1="&#x36;" u2="&#x201c;" k="72" />
    <hkern u1="&#x36;" u2="&#x201a;" k="31" />
    <hkern u1="&#x36;" u2="&#x2019;" k="82" />
    <hkern u1="&#x36;" u2="&#x2018;" k="72" />
    <hkern u1="&#x36;" u2="&#x149;" k="82" />
    <hkern u1="&#x36;" u2="&#xbb;" k="27" />
    <hkern u1="&#x36;" u2="&#xab;" k="-31" />
    <hkern u1="&#x36;" u2="&#x7d;" k="61" />
    <hkern u1="&#x36;" u2="]" k="61" />
    <hkern u1="&#x36;" u2="&#x2e;" k="10" />
    <hkern u1="&#x36;" u2="&#x2c;" k="31" />
    <hkern u1="&#x36;" u2="&#x29;" k="61" />
    <hkern u1="&#x36;" u2="&#x25;" k="4" />
    <hkern u1="&#x36;" u2="&#x2122;" k="100" />
    <hkern u1="&#x36;" u2="&#xae;" k="59" />
    <hkern u1="&#x36;" u2="\" k="82" />
    <hkern u1="&#x36;" u2="&#x3f;" k="51" />
    <hkern u1="&#x36;" u2="&#x39;" k="31" />
    <hkern u1="&#x36;" u2="&#x37;" k="31" />
    <hkern u1="&#x36;" u2="&#x32;" k="10" />
    <hkern u1="&#x36;" u2="&#x31;" k="57" />
    <hkern u1="&#x36;" u2="&#x2f;" k="61" />
    <hkern u1="&#x36;" u2="&#x2a;" k="41" />
    <hkern u1="&#x36;" u2="&#x21;" k="8" />
    <hkern u1="&#x37;" u2="&#x2117;" k="41" />
    <hkern u1="&#x37;" u2="&#x203a;" k="35" />
    <hkern u1="&#x37;" u2="&#x2039;" k="102" />
    <hkern u1="&#x37;" u2="&#x2026;" k="287" />
    <hkern u1="&#x37;" u2="&#x201e;" k="287" />
    <hkern u1="&#x37;" u2="&#x201d;" k="10" />
    <hkern u1="&#x37;" u2="&#x201c;" k="20" />
    <hkern u1="&#x37;" u2="&#x201a;" k="287" />
    <hkern u1="&#x37;" u2="&#x2019;" k="10" />
    <hkern u1="&#x37;" u2="&#x2018;" k="20" />
    <hkern u1="&#x37;" u2="&#x2014;" k="113" />
    <hkern u1="&#x37;" u2="&#x2013;" k="113" />
    <hkern u1="&#x37;" u2="&#x149;" k="10" />
    <hkern u1="&#x37;" u2="&#xbb;" k="35" />
    <hkern u1="&#x37;" u2="&#xba;" k="10" />
    <hkern u1="&#x37;" u2="&#xab;" k="102" />
    <hkern u1="&#x37;" u2="&#xaa;" k="10" />
    <hkern u1="&#x37;" u2="&#xa9;" k="41" />
    <hkern u1="&#x37;" u2="&#x7d;" k="10" />
    <hkern u1="&#x37;" u2="]" k="10" />
    <hkern u1="&#x37;" u2="&#x3b;" k="123" />
    <hkern u1="&#x37;" u2="&#x3a;" k="123" />
    <hkern u1="&#x37;" u2="&#x2e;" k="287" />
    <hkern u1="&#x37;" u2="&#x2d;" k="113" />
    <hkern u1="&#x37;" u2="&#x2c;" k="287" />
    <hkern u1="&#x37;" u2="&#x29;" k="10" />
    <hkern u1="&#x37;" u2="&#x2122;" k="-39" />
    <hkern u1="&#x37;" u2="&#x20ac;" k="14" />
    <hkern u1="&#x37;" u2="&#xbf;" k="127" />
    <hkern u1="&#x37;" u2="&#xae;" k="10" />
    <hkern u1="&#x37;" u2="&#xa3;" k="72" />
    <hkern u1="&#x37;" u2="&#xa2;" k="51" />
    <hkern u1="&#x37;" u2="&#x40;" k="82" />
    <hkern u1="&#x37;" u2="&#x39;" k="16" />
    <hkern u1="&#x37;" u2="&#x38;" k="61" />
    <hkern u1="&#x37;" u2="&#x37;" k="14" />
    <hkern u1="&#x37;" u2="&#x36;" k="123" />
    <hkern u1="&#x37;" u2="&#x35;" k="61" />
    <hkern u1="&#x37;" u2="&#x34;" k="113" />
    <hkern u1="&#x37;" u2="&#x33;" k="37" />
    <hkern u1="&#x37;" u2="&#x32;" k="41" />
    <hkern u1="&#x37;" u2="&#x30;" k="41" />
    <hkern u1="&#x37;" u2="&#x2f;" k="205" />
    <hkern u1="&#x37;" u2="&#x26;" k="70" />
    <hkern u1="&#x37;" u2="&#x23;" k="16" />
    <hkern u1="&#x38;" u2="&#x203a;" k="27" />
    <hkern u1="&#x38;" u2="&#x201e;" k="31" />
    <hkern u1="&#x38;" u2="&#x201d;" k="72" />
    <hkern u1="&#x38;" u2="&#x201c;" k="61" />
    <hkern u1="&#x38;" u2="&#x201a;" k="31" />
    <hkern u1="&#x38;" u2="&#x2019;" k="72" />
    <hkern u1="&#x38;" u2="&#x2018;" k="61" />
    <hkern u1="&#x38;" u2="&#x149;" k="72" />
    <hkern u1="&#x38;" u2="&#xbb;" k="27" />
    <hkern u1="&#x38;" u2="&#x7d;" k="51" />
    <hkern u1="&#x38;" u2="]" k="51" />
    <hkern u1="&#x38;" u2="&#x2c;" k="31" />
    <hkern u1="&#x38;" u2="&#x29;" k="51" />
    <hkern u1="&#x38;" u2="&#x2122;" k="59" />
    <hkern u1="&#x38;" u2="&#xae;" k="20" />
    <hkern u1="&#x38;" u2="\" k="72" />
    <hkern u1="&#x38;" u2="&#x3f;" k="20" />
    <hkern u1="&#x38;" u2="&#x39;" k="12" />
    <hkern u1="&#x38;" u2="&#x37;" k="31" />
    <hkern u1="&#x38;" u2="&#x31;" k="45" />
    <hkern u1="&#x38;" u2="&#x2f;" k="61" />
    <hkern u1="&#x38;" u2="&#x2a;" k="41" />
    <hkern u1="&#x39;" u2="&#x203a;" k="20" />
    <hkern u1="&#x39;" u2="&#x2039;" k="31" />
    <hkern u1="&#x39;" u2="&#x2026;" k="154" />
    <hkern u1="&#x39;" u2="&#x201e;" k="195" />
    <hkern u1="&#x39;" u2="&#x201d;" k="-10" />
    <hkern u1="&#x39;" u2="&#x201c;" k="-10" />
    <hkern u1="&#x39;" u2="&#x201a;" k="195" />
    <hkern u1="&#x39;" u2="&#x2019;" k="-10" />
    <hkern u1="&#x39;" u2="&#x2018;" k="-10" />
    <hkern u1="&#x39;" u2="&#x2014;" k="16" />
    <hkern u1="&#x39;" u2="&#x2013;" k="16" />
    <hkern u1="&#x39;" u2="&#x149;" k="-10" />
    <hkern u1="&#x39;" u2="&#xbb;" k="20" />
    <hkern u1="&#x39;" u2="&#xab;" k="31" />
    <hkern u1="&#x39;" u2="&#x7d;" k="51" />
    <hkern u1="&#x39;" u2="]" k="51" />
    <hkern u1="&#x39;" u2="&#x3b;" k="10" />
    <hkern u1="&#x39;" u2="&#x3a;" k="10" />
    <hkern u1="&#x39;" u2="&#x2e;" k="154" />
    <hkern u1="&#x39;" u2="&#x2d;" k="16" />
    <hkern u1="&#x39;" u2="&#x2c;" k="195" />
    <hkern u1="&#x39;" u2="&#x29;" k="51" />
    <hkern u1="&#x39;" u2="&#x2122;" k="2" />
    <hkern u1="&#x39;" u2="&#xbf;" k="43" />
    <hkern u1="&#x39;" u2="&#xa3;" k="10" />
    <hkern u1="&#x39;" u2="&#xa1;" k="8" />
    <hkern u1="&#x39;" u2="\" k="41" />
    <hkern u1="&#x39;" u2="&#x40;" k="10" />
    <hkern u1="&#x39;" u2="&#x38;" k="20" />
    <hkern u1="&#x39;" u2="&#x37;" k="31" />
    <hkern u1="&#x39;" u2="&#x36;" k="41" />
    <hkern u1="&#x39;" u2="&#x35;" k="31" />
    <hkern u1="&#x39;" u2="&#x34;" k="41" />
    <hkern u1="&#x39;" u2="&#x33;" k="20" />
    <hkern u1="&#x39;" u2="&#x32;" k="20" />
    <hkern u1="&#x39;" u2="&#x2f;" k="154" />
    <hkern u1="&#x39;" u2="&#x26;" k="20" />
    <hkern u1="&#x3a;" u2="&#xa5;" k="51" />
    <hkern u1="&#x3a;" u2="&#xa2;" k="10" />
    <hkern u1="&#x3a;" u2="x" k="10" />
    <hkern u1="&#x3a;" u2="v" k="35" />
    <hkern u1="&#x3a;" u2="\" k="133" />
    <hkern u1="&#x3a;" u2="X" k="41" />
    <hkern u1="&#x3a;" u2="V" k="143" />
    <hkern u1="&#x3a;" u2="&#x3f;" k="20" />
    <hkern u1="&#x3a;" u2="&#x39;" k="10" />
    <hkern u1="&#x3a;" u2="&#x37;" k="41" />
    <hkern u1="&#x3a;" u2="&#x36;" k="12" />
    <hkern u1="&#x3a;" u2="&#x35;" k="12" />
    <hkern u1="&#x3a;" u2="&#x31;" k="41" />
    <hkern u1="&#x3a;" u2="&#x2f;" k="41" />
    <hkern u1="&#x3a;" u2="&#x2a;" k="29" />
    <hkern u1="&#x3b;" u2="&#xa5;" k="51" />
    <hkern u1="&#x3b;" u2="&#xa2;" k="10" />
    <hkern u1="&#x3b;" u2="x" k="10" />
    <hkern u1="&#x3b;" u2="v" k="35" />
    <hkern u1="&#x3b;" u2="\" k="133" />
    <hkern u1="&#x3b;" u2="X" k="41" />
    <hkern u1="&#x3b;" u2="V" k="143" />
    <hkern u1="&#x3b;" u2="&#x3f;" k="20" />
    <hkern u1="&#x3b;" u2="&#x39;" k="10" />
    <hkern u1="&#x3b;" u2="&#x37;" k="41" />
    <hkern u1="&#x3b;" u2="&#x36;" k="12" />
    <hkern u1="&#x3b;" u2="&#x35;" k="12" />
    <hkern u1="&#x3b;" u2="&#x31;" k="41" />
    <hkern u1="&#x3b;" u2="&#x2f;" k="41" />
    <hkern u1="&#x3b;" u2="&#x2a;" k="29" />
    <hkern u1="&#x3f;" g2="Jcircumflex.salt" k="51" />
    <hkern u1="&#x3f;" g2="J.salt" k="51" />
    <hkern u1="&#x3f;" u2="&#xfb02;" k="-41" />
    <hkern u1="&#x3f;" u2="&#xfb01;" k="-41" />
    <hkern u1="&#x3f;" u2="&#x2039;" k="39" />
    <hkern u1="&#x3f;" u2="&#x2026;" k="154" />
    <hkern u1="&#x3f;" u2="&#x201e;" k="195" />
    <hkern u1="&#x3f;" u2="&#x201a;" k="195" />
    <hkern u1="&#x3f;" u2="&#x2014;" k="41" />
    <hkern u1="&#x3f;" u2="&#x2013;" k="41" />
    <hkern u1="&#x3f;" u2="&#x1ef3;" k="-35" />
    <hkern u1="&#x3f;" u2="&#x1ef2;" k="16" />
    <hkern u1="&#x3f;" u2="&#x1e85;" k="-20" />
    <hkern u1="&#x3f;" u2="&#x1e83;" k="-20" />
    <hkern u1="&#x3f;" u2="&#x1e81;" k="-20" />
    <hkern u1="&#x3f;" u2="&#x219;" k="20" />
    <hkern u1="&#x3f;" u2="&#x1ff;" k="31" />
    <hkern u1="&#x3f;" u2="&#x1fd;" k="92" />
    <hkern u1="&#x3f;" u2="&#x1fb;" k="92" />
    <hkern u1="&#x3f;" u2="&#x1fa;" k="143" />
    <hkern u1="&#x3f;" u2="&#x17d;" k="18" />
    <hkern u1="&#x3f;" u2="&#x17b;" k="18" />
    <hkern u1="&#x3f;" u2="&#x179;" k="18" />
    <hkern u1="&#x3f;" u2="&#x178;" k="16" />
    <hkern u1="&#x3f;" u2="&#x177;" k="-35" />
    <hkern u1="&#x3f;" u2="&#x176;" k="16" />
    <hkern u1="&#x3f;" u2="&#x175;" k="-20" />
    <hkern u1="&#x3f;" u2="&#x164;" k="-16" />
    <hkern u1="&#x3f;" u2="&#x162;" k="-16" />
    <hkern u1="&#x3f;" u2="&#x161;" k="20" />
    <hkern u1="&#x3f;" u2="&#x15f;" k="20" />
    <hkern u1="&#x3f;" u2="&#x15d;" k="20" />
    <hkern u1="&#x3f;" u2="&#x15b;" k="20" />
    <hkern u1="&#x3f;" u2="&#x151;" k="31" />
    <hkern u1="&#x3f;" u2="&#x14f;" k="31" />
    <hkern u1="&#x3f;" u2="&#x14d;" k="31" />
    <hkern u1="&#x3f;" u2="&#x134;" k="143" />
    <hkern u1="&#x3f;" u2="&#x123;" k="31" />
    <hkern u1="&#x3f;" u2="&#x121;" k="31" />
    <hkern u1="&#x3f;" u2="&#x11f;" k="31" />
    <hkern u1="&#x3f;" u2="&#x11d;" k="31" />
    <hkern u1="&#x3f;" u2="&#x11b;" k="31" />
    <hkern u1="&#x3f;" u2="&#x119;" k="31" />
    <hkern u1="&#x3f;" u2="&#x117;" k="31" />
    <hkern u1="&#x3f;" u2="&#x115;" k="31" />
    <hkern u1="&#x3f;" u2="&#x113;" k="31" />
    <hkern u1="&#x3f;" u2="&#x111;" k="31" />
    <hkern u1="&#x3f;" u2="&#x10f;" k="31" />
    <hkern u1="&#x3f;" u2="&#x10d;" k="31" />
    <hkern u1="&#x3f;" u2="&#x10b;" k="31" />
    <hkern u1="&#x3f;" u2="&#x109;" k="31" />
    <hkern u1="&#x3f;" u2="&#x107;" k="31" />
    <hkern u1="&#x3f;" u2="&#x105;" k="92" />
    <hkern u1="&#x3f;" u2="&#x104;" k="143" />
    <hkern u1="&#x3f;" u2="&#x103;" k="92" />
    <hkern u1="&#x3f;" u2="&#x102;" k="143" />
    <hkern u1="&#x3f;" u2="&#x101;" k="92" />
    <hkern u1="&#x3f;" u2="&#x100;" k="143" />
    <hkern u1="&#x3f;" u2="&#xff;" k="-35" />
    <hkern u1="&#x3f;" u2="&#xfd;" k="-35" />
    <hkern u1="&#x3f;" u2="&#xf8;" k="31" />
    <hkern u1="&#x3f;" u2="&#xf6;" k="31" />
    <hkern u1="&#x3f;" u2="&#xf5;" k="31" />
    <hkern u1="&#x3f;" u2="&#xf4;" k="31" />
    <hkern u1="&#x3f;" u2="&#xf3;" k="31" />
    <hkern u1="&#x3f;" u2="&#xf2;" k="31" />
    <hkern u1="&#x3f;" u2="&#xeb;" k="31" />
    <hkern u1="&#x3f;" u2="&#xea;" k="31" />
    <hkern u1="&#x3f;" u2="&#xe9;" k="31" />
    <hkern u1="&#x3f;" u2="&#xe8;" k="31" />
    <hkern u1="&#x3f;" u2="&#xe7;" k="31" />
    <hkern u1="&#x3f;" u2="&#xe6;" k="92" />
    <hkern u1="&#x3f;" u2="&#xe5;" k="92" />
    <hkern u1="&#x3f;" u2="&#xe4;" k="92" />
    <hkern u1="&#x3f;" u2="&#xe3;" k="92" />
    <hkern u1="&#x3f;" u2="&#xe2;" k="92" />
    <hkern u1="&#x3f;" u2="&#xe1;" k="92" />
    <hkern u1="&#x3f;" u2="&#xe0;" k="92" />
    <hkern u1="&#x3f;" u2="&#xdd;" k="16" />
    <hkern u1="&#x3f;" u2="&#xc5;" k="143" />
    <hkern u1="&#x3f;" u2="&#xc4;" k="143" />
    <hkern u1="&#x3f;" u2="&#xc3;" k="143" />
    <hkern u1="&#x3f;" u2="&#xc2;" k="143" />
    <hkern u1="&#x3f;" u2="&#xc1;" k="143" />
    <hkern u1="&#x3f;" u2="&#xc0;" k="143" />
    <hkern u1="&#x3f;" u2="&#xab;" k="39" />
    <hkern u1="&#x3f;" u2="&#x7d;" k="27" />
    <hkern u1="&#x3f;" u2="y" k="-35" />
    <hkern u1="&#x3f;" u2="w" k="-20" />
    <hkern u1="&#x3f;" u2="s" k="20" />
    <hkern u1="&#x3f;" u2="q" k="31" />
    <hkern u1="&#x3f;" u2="o" k="31" />
    <hkern u1="&#x3f;" u2="g" k="31" />
    <hkern u1="&#x3f;" u2="f" k="-41" />
    <hkern u1="&#x3f;" u2="e" k="31" />
    <hkern u1="&#x3f;" u2="d" k="31" />
    <hkern u1="&#x3f;" u2="c" k="31" />
    <hkern u1="&#x3f;" u2="a" k="92" />
    <hkern u1="&#x3f;" u2="]" k="27" />
    <hkern u1="&#x3f;" u2="Z" k="18" />
    <hkern u1="&#x3f;" u2="Y" k="16" />
    <hkern u1="&#x3f;" u2="T" k="-16" />
    <hkern u1="&#x3f;" u2="J" k="143" />
    <hkern u1="&#x3f;" u2="A" k="143" />
    <hkern u1="&#x3f;" u2="&#x3b;" k="20" />
    <hkern u1="&#x3f;" u2="&#x3a;" k="20" />
    <hkern u1="&#x3f;" u2="&#x2e;" k="154" />
    <hkern u1="&#x3f;" u2="&#x2d;" k="41" />
    <hkern u1="&#x3f;" u2="&#x2c;" k="195" />
    <hkern u1="&#x3f;" u2="&#x29;" k="27" />
    <hkern u1="&#x3f;" u2="&#xae;" k="-16" />
    <hkern u1="&#x3f;" u2="&#xa3;" k="23" />
    <hkern u1="&#x3f;" u2="v" k="-37" />
    <hkern u1="&#x3f;" u2="\" k="41" />
    <hkern u1="&#x3f;" u2="X" k="51" />
    <hkern u1="&#x3f;" u2="&#x40;" k="20" />
    <hkern u1="&#x3f;" u2="&#x38;" k="20" />
    <hkern u1="&#x3f;" u2="&#x36;" k="41" />
    <hkern u1="&#x3f;" u2="&#x35;" k="20" />
    <hkern u1="&#x3f;" u2="&#x34;" k="51" />
    <hkern u1="&#x3f;" u2="&#x33;" k="20" />
    <hkern u1="&#x3f;" u2="&#x2f;" k="205" />
    <hkern u1="&#x3f;" u2="&#x26;" k="39" />
    <hkern u1="&#x40;" g2="Jcircumflex.salt" k="10" />
    <hkern u1="&#x40;" g2="J.salt" k="10" />
    <hkern u1="&#x40;" u2="&#x203a;" k="20" />
    <hkern u1="&#x40;" u2="&#x2026;" k="41" />
    <hkern u1="&#x40;" u2="&#x201e;" k="82" />
    <hkern u1="&#x40;" u2="&#x201a;" k="82" />
    <hkern u1="&#x40;" u2="&#x1ef2;" k="113" />
    <hkern u1="&#x40;" u2="&#x1e84;" k="41" />
    <hkern u1="&#x40;" u2="&#x1e82;" k="41" />
    <hkern u1="&#x40;" u2="&#x1e80;" k="41" />
    <hkern u1="&#x40;" u2="&#x1fd;" k="10" />
    <hkern u1="&#x40;" u2="&#x1fb;" k="10" />
    <hkern u1="&#x40;" u2="&#x1fa;" k="86" />
    <hkern u1="&#x40;" u2="&#x178;" k="113" />
    <hkern u1="&#x40;" u2="&#x176;" k="113" />
    <hkern u1="&#x40;" u2="&#x174;" k="41" />
    <hkern u1="&#x40;" u2="&#x164;" k="31" />
    <hkern u1="&#x40;" u2="&#x162;" k="31" />
    <hkern u1="&#x40;" u2="&#x134;" k="25" />
    <hkern u1="&#x40;" u2="&#x105;" k="10" />
    <hkern u1="&#x40;" u2="&#x104;" k="86" />
    <hkern u1="&#x40;" u2="&#x103;" k="10" />
    <hkern u1="&#x40;" u2="&#x102;" k="86" />
    <hkern u1="&#x40;" u2="&#x101;" k="10" />
    <hkern u1="&#x40;" u2="&#x100;" k="86" />
    <hkern u1="&#x40;" u2="&#xe6;" k="10" />
    <hkern u1="&#x40;" u2="&#xe5;" k="10" />
    <hkern u1="&#x40;" u2="&#xe4;" k="10" />
    <hkern u1="&#x40;" u2="&#xe3;" k="10" />
    <hkern u1="&#x40;" u2="&#xe2;" k="10" />
    <hkern u1="&#x40;" u2="&#xe1;" k="10" />
    <hkern u1="&#x40;" u2="&#xe0;" k="10" />
    <hkern u1="&#x40;" u2="&#xdd;" k="113" />
    <hkern u1="&#x40;" u2="&#xc5;" k="86" />
    <hkern u1="&#x40;" u2="&#xc4;" k="86" />
    <hkern u1="&#x40;" u2="&#xc3;" k="86" />
    <hkern u1="&#x40;" u2="&#xc2;" k="86" />
    <hkern u1="&#x40;" u2="&#xc1;" k="86" />
    <hkern u1="&#x40;" u2="&#xc0;" k="86" />
    <hkern u1="&#x40;" u2="&#xbb;" k="20" />
    <hkern u1="&#x40;" u2="&#x7d;" k="57" />
    <hkern u1="&#x40;" u2="a" k="10" />
    <hkern u1="&#x40;" u2="]" k="57" />
    <hkern u1="&#x40;" u2="Y" k="113" />
    <hkern u1="&#x40;" u2="W" k="41" />
    <hkern u1="&#x40;" u2="T" k="31" />
    <hkern u1="&#x40;" u2="J" k="25" />
    <hkern u1="&#x40;" u2="A" k="86" />
    <hkern u1="&#x40;" u2="&#x3b;" k="20" />
    <hkern u1="&#x40;" u2="&#x3a;" k="20" />
    <hkern u1="&#x40;" u2="&#x2e;" k="41" />
    <hkern u1="&#x40;" u2="&#x2c;" k="82" />
    <hkern u1="&#x40;" u2="&#x29;" k="57" />
    <hkern u1="&#x40;" u2="&#x2122;" k="10" />
    <hkern u1="&#x40;" u2="x" k="10" />
    <hkern u1="&#x40;" u2="\" k="82" />
    <hkern u1="&#x40;" u2="X" k="102" />
    <hkern u1="&#x40;" u2="V" k="61" />
    <hkern u1="&#x40;" u2="&#x37;" k="31" />
    <hkern u1="&#x40;" u2="&#x34;" k="4" />
    <hkern u1="&#x40;" u2="&#x2f;" k="154" />
    <hkern u1="&#x40;" u2="&#x26;" k="12" />
    <hkern u1="A" u2="&#x2122;" k="233" />
    <hkern u1="A" u2="&#xbf;" k="18" />
    <hkern u1="A" u2="&#xae;" k="195" />
    <hkern u1="A" u2="x" k="-16" />
    <hkern u1="A" u2="v" k="113" />
    <hkern u1="A" u2="\" k="154" />
    <hkern u1="A" u2="X" k="-10" />
    <hkern u1="A" u2="V" k="211" />
    <hkern u1="A" u2="&#x40;" k="10" />
    <hkern u1="A" u2="&#x3f;" k="143" />
    <hkern u1="A" u2="&#x2a;" k="205" />
    <hkern u1="B" u2="&#xfb02;" k="31" />
    <hkern u1="B" u2="&#xfb01;" k="31" />
    <hkern u1="B" u2="&#x203a;" k="41" />
    <hkern u1="B" u2="&#x2026;" k="20" />
    <hkern u1="B" u2="&#x201e;" k="31" />
    <hkern u1="B" u2="&#x201d;" k="72" />
    <hkern u1="B" u2="&#x201c;" k="72" />
    <hkern u1="B" u2="&#x201a;" k="31" />
    <hkern u1="B" u2="&#x2019;" k="72" />
    <hkern u1="B" u2="&#x2018;" k="72" />
    <hkern u1="B" u2="&#x2014;" k="16" />
    <hkern u1="B" u2="&#x2013;" k="16" />
    <hkern u1="B" u2="&#x1ef3;" k="41" />
    <hkern u1="B" u2="&#x1ef2;" k="102" />
    <hkern u1="B" u2="&#x1e85;" k="31" />
    <hkern u1="B" u2="&#x1e84;" k="55" />
    <hkern u1="B" u2="&#x1e83;" k="31" />
    <hkern u1="B" u2="&#x1e82;" k="55" />
    <hkern u1="B" u2="&#x1e81;" k="31" />
    <hkern u1="B" u2="&#x1e80;" k="55" />
    <hkern u1="B" u2="&#x1fd;" k="16" />
    <hkern u1="B" u2="&#x1fb;" k="16" />
    <hkern u1="B" u2="&#x1fa;" k="41" />
    <hkern u1="B" u2="&#x17d;" k="10" />
    <hkern u1="B" u2="&#x17b;" k="10" />
    <hkern u1="B" u2="&#x179;" k="10" />
    <hkern u1="B" u2="&#x178;" k="102" />
    <hkern u1="B" u2="&#x177;" k="41" />
    <hkern u1="B" u2="&#x176;" k="102" />
    <hkern u1="B" u2="&#x175;" k="31" />
    <hkern u1="B" u2="&#x174;" k="55" />
    <hkern u1="B" u2="&#x172;" k="10" />
    <hkern u1="B" u2="&#x170;" k="10" />
    <hkern u1="B" u2="&#x16e;" k="10" />
    <hkern u1="B" u2="&#x16c;" k="10" />
    <hkern u1="B" u2="&#x16a;" k="10" />
    <hkern u1="B" u2="&#x168;" k="10" />
    <hkern u1="B" u2="&#x164;" k="72" />
    <hkern u1="B" u2="&#x163;" k="27" />
    <hkern u1="B" u2="&#x162;" k="72" />
    <hkern u1="B" u2="&#x149;" k="72" />
    <hkern u1="B" u2="&#x105;" k="16" />
    <hkern u1="B" u2="&#x104;" k="41" />
    <hkern u1="B" u2="&#x103;" k="16" />
    <hkern u1="B" u2="&#x102;" k="41" />
    <hkern u1="B" u2="&#x101;" k="16" />
    <hkern u1="B" u2="&#x100;" k="41" />
    <hkern u1="B" u2="&#xff;" k="41" />
    <hkern u1="B" u2="&#xfd;" k="41" />
    <hkern u1="B" u2="&#xe6;" k="16" />
    <hkern u1="B" u2="&#xe5;" k="16" />
    <hkern u1="B" u2="&#xe4;" k="16" />
    <hkern u1="B" u2="&#xe3;" k="16" />
    <hkern u1="B" u2="&#xe2;" k="16" />
    <hkern u1="B" u2="&#xe1;" k="16" />
    <hkern u1="B" u2="&#xe0;" k="16" />
    <hkern u1="B" u2="&#xdd;" k="102" />
    <hkern u1="B" u2="&#xdc;" k="10" />
    <hkern u1="B" u2="&#xdb;" k="10" />
    <hkern u1="B" u2="&#xda;" k="10" />
    <hkern u1="B" u2="&#xd9;" k="10" />
    <hkern u1="B" u2="&#xc5;" k="41" />
    <hkern u1="B" u2="&#xc4;" k="41" />
    <hkern u1="B" u2="&#xc3;" k="41" />
    <hkern u1="B" u2="&#xc2;" k="41" />
    <hkern u1="B" u2="&#xc1;" k="41" />
    <hkern u1="B" u2="&#xc0;" k="41" />
    <hkern u1="B" u2="&#xbb;" k="41" />
    <hkern u1="B" u2="&#x7d;" k="41" />
    <hkern u1="B" u2="y" k="41" />
    <hkern u1="B" u2="w" k="31" />
    <hkern u1="B" u2="t" k="27" />
    <hkern u1="B" u2="f" k="31" />
    <hkern u1="B" u2="a" k="16" />
    <hkern u1="B" u2="]" k="41" />
    <hkern u1="B" u2="Z" k="10" />
    <hkern u1="B" u2="Y" k="102" />
    <hkern u1="B" u2="W" k="55" />
    <hkern u1="B" u2="U" k="10" />
    <hkern u1="B" u2="T" k="72" />
    <hkern u1="B" u2="A" k="41" />
    <hkern u1="B" u2="&#x3b;" k="12" />
    <hkern u1="B" u2="&#x3a;" k="12" />
    <hkern u1="B" u2="&#x2e;" k="20" />
    <hkern u1="B" u2="&#x2d;" k="16" />
    <hkern u1="B" u2="&#x2c;" k="31" />
    <hkern u1="B" u2="&#x29;" k="41" />
    <hkern u1="B" u2="&#x2122;" k="61" />
    <hkern u1="B" u2="&#xae;" k="41" />
    <hkern u1="B" u2="x" k="51" />
    <hkern u1="B" u2="v" k="41" />
    <hkern u1="B" u2="\" k="92" />
    <hkern u1="B" u2="X" k="51" />
    <hkern u1="B" u2="V" k="86" />
    <hkern u1="B" u2="&#x3f;" k="20" />
    <hkern u1="B" u2="&#x2f;" k="61" />
    <hkern u1="B" u2="&#x2a;" k="53" />
    <hkern u1="B" u2="&#x21;" k="10" />
    <hkern u1="C" u2="x" k="10" />
    <hkern u1="C" u2="\" k="51" />
    <hkern u1="C" u2="X" k="61" />
    <hkern u1="C" u2="V" k="41" />
    <hkern u1="C" u2="&#x2f;" k="72" />
    <hkern u1="D" u2="&#x2122;" k="31" />
    <hkern u1="D" u2="x" k="29" />
    <hkern u1="D" u2="\" k="51" />
    <hkern u1="D" u2="X" k="82" />
    <hkern u1="D" u2="V" k="57" />
    <hkern u1="D" u2="&#x2f;" k="72" />
    <hkern u1="D" u2="&#x2a;" k="10" />
    <hkern u1="E" u2="&#x2122;" k="-16" />
    <hkern u1="E" u2="X" k="-10" />
    <hkern u1="E" u2="&#x2f;" k="-10" />
    <hkern u1="F" g2="Jcircumflex.salt" k="41" />
    <hkern u1="F" g2="J.salt" k="41" />
    <hkern u1="F" u2="&#xfb02;" k="4" />
    <hkern u1="F" u2="&#xfb01;" k="4" />
    <hkern u1="F" u2="&#x203a;" k="10" />
    <hkern u1="F" u2="&#x2039;" k="8" />
    <hkern u1="F" u2="&#x2026;" k="164" />
    <hkern u1="F" u2="&#x201e;" k="184" />
    <hkern u1="F" u2="&#x201a;" k="184" />
    <hkern u1="F" u2="&#x1ef2;" k="-10" />
    <hkern u1="F" u2="&#x1e84;" k="-20" />
    <hkern u1="F" u2="&#x1e82;" k="-20" />
    <hkern u1="F" u2="&#x1e80;" k="-20" />
    <hkern u1="F" u2="&#x219;" k="10" />
    <hkern u1="F" u2="&#x1ff;" k="16" />
    <hkern u1="F" u2="&#x1fe;" k="10" />
    <hkern u1="F" u2="&#x1fd;" k="61" />
    <hkern u1="F" u2="&#x1fb;" k="61" />
    <hkern u1="F" u2="&#x1fa;" k="119" />
    <hkern u1="F" u2="&#x17e;" k="10" />
    <hkern u1="F" u2="&#x17d;" k="10" />
    <hkern u1="F" u2="&#x17c;" k="10" />
    <hkern u1="F" u2="&#x17b;" k="10" />
    <hkern u1="F" u2="&#x17a;" k="10" />
    <hkern u1="F" u2="&#x179;" k="10" />
    <hkern u1="F" u2="&#x178;" k="-10" />
    <hkern u1="F" u2="&#x176;" k="-10" />
    <hkern u1="F" u2="&#x174;" k="-20" />
    <hkern u1="F" u2="&#x173;" k="10" />
    <hkern u1="F" u2="&#x171;" k="10" />
    <hkern u1="F" u2="&#x16f;" k="10" />
    <hkern u1="F" u2="&#x16d;" k="10" />
    <hkern u1="F" u2="&#x16b;" k="10" />
    <hkern u1="F" u2="&#x169;" k="10" />
    <hkern u1="F" u2="&#x161;" k="10" />
    <hkern u1="F" u2="&#x160;" k="10" />
    <hkern u1="F" u2="&#x15f;" k="10" />
    <hkern u1="F" u2="&#x15e;" k="10" />
    <hkern u1="F" u2="&#x15d;" k="10" />
    <hkern u1="F" u2="&#x15c;" k="10" />
    <hkern u1="F" u2="&#x15b;" k="10" />
    <hkern u1="F" u2="&#x15a;" k="10" />
    <hkern u1="F" u2="&#x159;" k="10" />
    <hkern u1="F" u2="&#x157;" k="10" />
    <hkern u1="F" u2="&#x155;" k="10" />
    <hkern u1="F" u2="&#x152;" k="10" />
    <hkern u1="F" u2="&#x151;" k="16" />
    <hkern u1="F" u2="&#x150;" k="10" />
    <hkern u1="F" u2="&#x14f;" k="16" />
    <hkern u1="F" u2="&#x14e;" k="10" />
    <hkern u1="F" u2="&#x14d;" k="16" />
    <hkern u1="F" u2="&#x14c;" k="10" />
    <hkern u1="F" u2="&#x14b;" k="10" />
    <hkern u1="F" u2="&#x148;" k="10" />
    <hkern u1="F" u2="&#x146;" k="10" />
    <hkern u1="F" u2="&#x144;" k="10" />
    <hkern u1="F" u2="&#x134;" k="82" />
    <hkern u1="F" u2="&#x123;" k="16" />
    <hkern u1="F" u2="&#x122;" k="10" />
    <hkern u1="F" u2="&#x121;" k="16" />
    <hkern u1="F" u2="&#x120;" k="10" />
    <hkern u1="F" u2="&#x11f;" k="16" />
    <hkern u1="F" u2="&#x11e;" k="10" />
    <hkern u1="F" u2="&#x11d;" k="16" />
    <hkern u1="F" u2="&#x11c;" k="10" />
    <hkern u1="F" u2="&#x11b;" k="16" />
    <hkern u1="F" u2="&#x119;" k="16" />
    <hkern u1="F" u2="&#x117;" k="16" />
    <hkern u1="F" u2="&#x115;" k="16" />
    <hkern u1="F" u2="&#x113;" k="16" />
    <hkern u1="F" u2="&#x111;" k="16" />
    <hkern u1="F" u2="&#x10f;" k="16" />
    <hkern u1="F" u2="&#x10d;" k="16" />
    <hkern u1="F" u2="&#x10c;" k="10" />
    <hkern u1="F" u2="&#x10b;" k="16" />
    <hkern u1="F" u2="&#x10a;" k="10" />
    <hkern u1="F" u2="&#x109;" k="16" />
    <hkern u1="F" u2="&#x108;" k="10" />
    <hkern u1="F" u2="&#x107;" k="16" />
    <hkern u1="F" u2="&#x106;" k="10" />
    <hkern u1="F" u2="&#x105;" k="61" />
    <hkern u1="F" u2="&#x104;" k="119" />
    <hkern u1="F" u2="&#x103;" k="61" />
    <hkern u1="F" u2="&#x102;" k="119" />
    <hkern u1="F" u2="&#x101;" k="61" />
    <hkern u1="F" u2="&#x100;" k="119" />
    <hkern u1="F" u2="&#xfc;" k="10" />
    <hkern u1="F" u2="&#xfb;" k="10" />
    <hkern u1="F" u2="&#xfa;" k="10" />
    <hkern u1="F" u2="&#xf9;" k="10" />
    <hkern u1="F" u2="&#xf8;" k="16" />
    <hkern u1="F" u2="&#xf6;" k="16" />
    <hkern u1="F" u2="&#xf5;" k="16" />
    <hkern u1="F" u2="&#xf4;" k="16" />
    <hkern u1="F" u2="&#xf3;" k="16" />
    <hkern u1="F" u2="&#xf2;" k="16" />
    <hkern u1="F" u2="&#xeb;" k="16" />
    <hkern u1="F" u2="&#xea;" k="16" />
    <hkern u1="F" u2="&#xe9;" k="16" />
    <hkern u1="F" u2="&#xe8;" k="16" />
    <hkern u1="F" u2="&#xe7;" k="16" />
    <hkern u1="F" u2="&#xe6;" k="61" />
    <hkern u1="F" u2="&#xe5;" k="61" />
    <hkern u1="F" u2="&#xe4;" k="61" />
    <hkern u1="F" u2="&#xe3;" k="61" />
    <hkern u1="F" u2="&#xe2;" k="61" />
    <hkern u1="F" u2="&#xe1;" k="61" />
    <hkern u1="F" u2="&#xe0;" k="61" />
    <hkern u1="F" u2="&#xdd;" k="-10" />
    <hkern u1="F" u2="&#xd8;" k="10" />
    <hkern u1="F" u2="&#xd6;" k="10" />
    <hkern u1="F" u2="&#xd5;" k="10" />
    <hkern u1="F" u2="&#xd4;" k="10" />
    <hkern u1="F" u2="&#xd3;" k="10" />
    <hkern u1="F" u2="&#xd2;" k="10" />
    <hkern u1="F" u2="&#xc7;" k="10" />
    <hkern u1="F" u2="&#xc5;" k="119" />
    <hkern u1="F" u2="&#xc4;" k="119" />
    <hkern u1="F" u2="&#xc3;" k="119" />
    <hkern u1="F" u2="&#xc2;" k="119" />
    <hkern u1="F" u2="&#xc1;" k="119" />
    <hkern u1="F" u2="&#xc0;" k="119" />
    <hkern u1="F" u2="&#xbb;" k="10" />
    <hkern u1="F" u2="&#xab;" k="8" />
    <hkern u1="F" u2="&#x7d;" k="-10" />
    <hkern u1="F" u2="z" k="10" />
    <hkern u1="F" u2="u" k="10" />
    <hkern u1="F" u2="s" k="10" />
    <hkern u1="F" u2="r" k="10" />
    <hkern u1="F" u2="q" k="16" />
    <hkern u1="F" u2="p" k="10" />
    <hkern u1="F" u2="o" k="16" />
    <hkern u1="F" u2="n" k="10" />
    <hkern u1="F" u2="m" k="10" />
    <hkern u1="F" u2="g" k="16" />
    <hkern u1="F" u2="f" k="4" />
    <hkern u1="F" u2="e" k="16" />
    <hkern u1="F" u2="d" k="16" />
    <hkern u1="F" u2="c" k="16" />
    <hkern u1="F" u2="a" k="61" />
    <hkern u1="F" u2="]" k="-10" />
    <hkern u1="F" u2="Z" k="10" />
    <hkern u1="F" u2="Y" k="-10" />
    <hkern u1="F" u2="W" k="-20" />
    <hkern u1="F" u2="S" k="10" />
    <hkern u1="F" u2="Q" k="10" />
    <hkern u1="F" u2="O" k="10" />
    <hkern u1="F" u2="J" k="82" />
    <hkern u1="F" u2="G" k="10" />
    <hkern u1="F" u2="C" k="10" />
    <hkern u1="F" u2="A" k="119" />
    <hkern u1="F" u2="&#x3b;" k="31" />
    <hkern u1="F" u2="&#x3a;" k="31" />
    <hkern u1="F" u2="&#x2e;" k="164" />
    <hkern u1="F" u2="&#x2c;" k="184" />
    <hkern u1="F" u2="&#x29;" k="-10" />
    <hkern u1="F" u2="&#x2122;" k="-20" />
    <hkern u1="F" u2="&#xbf;" k="20" />
    <hkern u1="F" u2="&#xae;" k="-20" />
    <hkern u1="F" u2="x" k="10" />
    <hkern u1="F" u2="\" k="-20" />
    <hkern u1="F" u2="X" k="10" />
    <hkern u1="F" u2="V" k="-20" />
    <hkern u1="F" u2="&#x40;" k="10" />
    <hkern u1="F" u2="&#x2f;" k="92" />
    <hkern u1="G" u2="x" k="20" />
    <hkern u1="G" u2="\" k="82" />
    <hkern u1="G" u2="X" k="41" />
    <hkern u1="G" u2="V" k="41" />
    <hkern u1="G" u2="&#x2f;" k="72" />
    <hkern u1="J" u2="&#x2f;" k="41" />
    <hkern u1="K" u2="&#x2122;" k="-16" />
    <hkern u1="K" u2="&#xbf;" k="20" />
    <hkern u1="K" u2="&#xae;" k="72" />
    <hkern u1="K" u2="x" k="-20" />
    <hkern u1="K" u2="v" k="92" />
    <hkern u1="K" u2="X" k="-20" />
    <hkern u1="K" u2="&#x40;" k="31" />
    <hkern u1="K" u2="&#x3f;" k="31" />
    <hkern u1="K" u2="&#x2a;" k="39" />
    <hkern u1="K" u2="&#x26;" k="16" />
    <hkern u1="L" u2="&#x2122;" k="414" />
    <hkern u1="L" u2="&#xae;" k="362" />
    <hkern u1="L" u2="v" k="113" />
    <hkern u1="L" u2="\" k="184" />
    <hkern u1="L" u2="V" k="195" />
    <hkern u1="L" u2="&#x40;" k="10" />
    <hkern u1="L" u2="&#x3f;" k="133" />
    <hkern u1="L" u2="&#x2a;" k="451" />
    <hkern u1="L" u2="&#x26;" k="4" />
    <hkern u1="O" u2="&#x2122;" k="31" />
    <hkern u1="O" u2="x" k="29" />
    <hkern u1="O" u2="\" k="51" />
    <hkern u1="O" u2="X" k="82" />
    <hkern u1="O" u2="V" k="57" />
    <hkern u1="O" u2="&#x2f;" k="72" />
    <hkern u1="O" u2="&#x2a;" k="10" />
    <hkern u1="P" g2="Jcircumflex.salt" k="51" />
    <hkern u1="P" g2="J.salt" k="51" />
    <hkern u1="P" u2="&#xfb02;" k="-10" />
    <hkern u1="P" u2="&#xfb01;" k="-10" />
    <hkern u1="P" u2="&#x2117;" k="-16" />
    <hkern u1="P" u2="&#x2026;" k="276" />
    <hkern u1="P" u2="&#x201e;" k="266" />
    <hkern u1="P" u2="&#x201d;" k="-31" />
    <hkern u1="P" u2="&#x201c;" k="-31" />
    <hkern u1="P" u2="&#x201a;" k="266" />
    <hkern u1="P" u2="&#x2019;" k="-31" />
    <hkern u1="P" u2="&#x2018;" k="-31" />
    <hkern u1="P" u2="&#x1ef3;" k="-31" />
    <hkern u1="P" u2="&#x1ef2;" k="41" />
    <hkern u1="P" u2="&#x1e85;" k="-37" />
    <hkern u1="P" u2="&#x1e84;" k="10" />
    <hkern u1="P" u2="&#x1e83;" k="-37" />
    <hkern u1="P" u2="&#x1e82;" k="10" />
    <hkern u1="P" u2="&#x1e81;" k="-37" />
    <hkern u1="P" u2="&#x1e80;" k="10" />
    <hkern u1="P" u2="&#x1ff;" k="6" />
    <hkern u1="P" u2="&#x1fe;" k="-10" />
    <hkern u1="P" u2="&#x1fd;" k="51" />
    <hkern u1="P" u2="&#x1fb;" k="51" />
    <hkern u1="P" u2="&#x1fa;" k="139" />
    <hkern u1="P" u2="&#x17d;" k="20" />
    <hkern u1="P" u2="&#x17b;" k="20" />
    <hkern u1="P" u2="&#x179;" k="20" />
    <hkern u1="P" u2="&#x178;" k="41" />
    <hkern u1="P" u2="&#x177;" k="-31" />
    <hkern u1="P" u2="&#x176;" k="41" />
    <hkern u1="P" u2="&#x175;" k="-37" />
    <hkern u1="P" u2="&#x174;" k="10" />
    <hkern u1="P" u2="&#x173;" k="-4" />
    <hkern u1="P" u2="&#x171;" k="-4" />
    <hkern u1="P" u2="&#x16f;" k="-4" />
    <hkern u1="P" u2="&#x16d;" k="-4" />
    <hkern u1="P" u2="&#x16b;" k="-4" />
    <hkern u1="P" u2="&#x169;" k="-4" />
    <hkern u1="P" u2="&#x163;" k="-10" />
    <hkern u1="P" u2="&#x152;" k="-10" />
    <hkern u1="P" u2="&#x151;" k="6" />
    <hkern u1="P" u2="&#x150;" k="-10" />
    <hkern u1="P" u2="&#x14f;" k="6" />
    <hkern u1="P" u2="&#x14e;" k="-10" />
    <hkern u1="P" u2="&#x14d;" k="6" />
    <hkern u1="P" u2="&#x14c;" k="-10" />
    <hkern u1="P" u2="&#x149;" k="-31" />
    <hkern u1="P" u2="&#x134;" k="102" />
    <hkern u1="P" u2="&#x123;" k="6" />
    <hkern u1="P" u2="&#x122;" k="-10" />
    <hkern u1="P" u2="&#x121;" k="6" />
    <hkern u1="P" u2="&#x120;" k="-10" />
    <hkern u1="P" u2="&#x11f;" k="6" />
    <hkern u1="P" u2="&#x11e;" k="-10" />
    <hkern u1="P" u2="&#x11d;" k="6" />
    <hkern u1="P" u2="&#x11c;" k="-10" />
    <hkern u1="P" u2="&#x11b;" k="6" />
    <hkern u1="P" u2="&#x119;" k="6" />
    <hkern u1="P" u2="&#x117;" k="6" />
    <hkern u1="P" u2="&#x115;" k="6" />
    <hkern u1="P" u2="&#x113;" k="6" />
    <hkern u1="P" u2="&#x111;" k="6" />
    <hkern u1="P" u2="&#x10f;" k="6" />
    <hkern u1="P" u2="&#x10d;" k="6" />
    <hkern u1="P" u2="&#x10c;" k="-10" />
    <hkern u1="P" u2="&#x10b;" k="6" />
    <hkern u1="P" u2="&#x10a;" k="-10" />
    <hkern u1="P" u2="&#x109;" k="6" />
    <hkern u1="P" u2="&#x108;" k="-10" />
    <hkern u1="P" u2="&#x107;" k="6" />
    <hkern u1="P" u2="&#x106;" k="-10" />
    <hkern u1="P" u2="&#x105;" k="51" />
    <hkern u1="P" u2="&#x104;" k="139" />
    <hkern u1="P" u2="&#x103;" k="51" />
    <hkern u1="P" u2="&#x102;" k="139" />
    <hkern u1="P" u2="&#x101;" k="51" />
    <hkern u1="P" u2="&#x100;" k="139" />
    <hkern u1="P" u2="&#xff;" k="-31" />
    <hkern u1="P" u2="&#xfd;" k="-31" />
    <hkern u1="P" u2="&#xfc;" k="-4" />
    <hkern u1="P" u2="&#xfb;" k="-4" />
    <hkern u1="P" u2="&#xfa;" k="-4" />
    <hkern u1="P" u2="&#xf9;" k="-4" />
    <hkern u1="P" u2="&#xf8;" k="6" />
    <hkern u1="P" u2="&#xf6;" k="6" />
    <hkern u1="P" u2="&#xf5;" k="6" />
    <hkern u1="P" u2="&#xf4;" k="6" />
    <hkern u1="P" u2="&#xf3;" k="6" />
    <hkern u1="P" u2="&#xf2;" k="6" />
    <hkern u1="P" u2="&#xeb;" k="6" />
    <hkern u1="P" u2="&#xea;" k="6" />
    <hkern u1="P" u2="&#xe9;" k="6" />
    <hkern u1="P" u2="&#xe8;" k="6" />
    <hkern u1="P" u2="&#xe7;" k="6" />
    <hkern u1="P" u2="&#xe6;" k="51" />
    <hkern u1="P" u2="&#xe5;" k="51" />
    <hkern u1="P" u2="&#xe4;" k="51" />
    <hkern u1="P" u2="&#xe3;" k="51" />
    <hkern u1="P" u2="&#xe2;" k="51" />
    <hkern u1="P" u2="&#xe1;" k="51" />
    <hkern u1="P" u2="&#xe0;" k="51" />
    <hkern u1="P" u2="&#xdd;" k="41" />
    <hkern u1="P" u2="&#xd8;" k="-10" />
    <hkern u1="P" u2="&#xd6;" k="-10" />
    <hkern u1="P" u2="&#xd5;" k="-10" />
    <hkern u1="P" u2="&#xd4;" k="-10" />
    <hkern u1="P" u2="&#xd3;" k="-10" />
    <hkern u1="P" u2="&#xd2;" k="-10" />
    <hkern u1="P" u2="&#xc7;" k="-10" />
    <hkern u1="P" u2="&#xc5;" k="139" />
    <hkern u1="P" u2="&#xc4;" k="139" />
    <hkern u1="P" u2="&#xc3;" k="139" />
    <hkern u1="P" u2="&#xc2;" k="139" />
    <hkern u1="P" u2="&#xc1;" k="139" />
    <hkern u1="P" u2="&#xc0;" k="139" />
    <hkern u1="P" u2="&#xba;" k="-20" />
    <hkern u1="P" u2="&#xaa;" k="-20" />
    <hkern u1="P" u2="&#xa9;" k="-16" />
    <hkern u1="P" u2="&#x7d;" k="31" />
    <hkern u1="P" u2="y" k="-31" />
    <hkern u1="P" u2="w" k="-37" />
    <hkern u1="P" u2="u" k="-4" />
    <hkern u1="P" u2="t" k="-10" />
    <hkern u1="P" u2="q" k="6" />
    <hkern u1="P" u2="o" k="6" />
    <hkern u1="P" u2="g" k="6" />
    <hkern u1="P" u2="f" k="-10" />
    <hkern u1="P" u2="e" k="6" />
    <hkern u1="P" u2="d" k="6" />
    <hkern u1="P" u2="c" k="6" />
    <hkern u1="P" u2="a" k="51" />
    <hkern u1="P" u2="]" k="31" />
    <hkern u1="P" u2="Z" k="20" />
    <hkern u1="P" u2="Y" k="41" />
    <hkern u1="P" u2="W" k="10" />
    <hkern u1="P" u2="Q" k="-10" />
    <hkern u1="P" u2="O" k="-10" />
    <hkern u1="P" u2="J" k="102" />
    <hkern u1="P" u2="G" k="-10" />
    <hkern u1="P" u2="C" k="-10" />
    <hkern u1="P" u2="A" k="139" />
    <hkern u1="P" u2="&#x3b;" k="12" />
    <hkern u1="P" u2="&#x3a;" k="12" />
    <hkern u1="P" u2="&#x2e;" k="276" />
    <hkern u1="P" u2="&#x2c;" k="266" />
    <hkern u1="P" u2="&#x29;" k="31" />
    <hkern u1="P" u2="&#xbf;" k="41" />
    <hkern u1="P" u2="&#xae;" k="-20" />
    <hkern u1="P" u2="v" k="-37" />
    <hkern u1="P" u2="\" k="20" />
    <hkern u1="P" u2="X" k="72" />
    <hkern u1="P" u2="V" k="20" />
    <hkern u1="P" u2="&#x40;" k="10" />
    <hkern u1="P" u2="&#x3f;" k="-31" />
    <hkern u1="P" u2="&#x2f;" k="164" />
    <hkern u1="P" u2="&#x21;" k="-10" />
    <hkern u1="Q" u2="&#x2122;" k="31" />
    <hkern u1="Q" u2="x" k="29" />
    <hkern u1="Q" u2="\" k="51" />
    <hkern u1="Q" u2="X" k="82" />
    <hkern u1="Q" u2="V" k="57" />
    <hkern u1="Q" u2="&#x2f;" k="72" />
    <hkern u1="Q" u2="&#x2a;" k="10" />
    <hkern u1="R" u2="&#x2122;" k="20" />
    <hkern u1="R" u2="&#xbf;" k="10" />
    <hkern u1="R" u2="&#xae;" k="10" />
    <hkern u1="R" u2="\" k="51" />
    <hkern u1="R" u2="V" k="51" />
    <hkern u1="R" u2="&#x40;" k="10" />
    <hkern u1="R" u2="&#x2a;" k="20" />
    <hkern u1="R" u2="&#x26;" k="10" />
    <hkern u1="S" u2="&#x2122;" k="49" />
    <hkern u1="S" u2="x" k="37" />
    <hkern u1="S" u2="v" k="6" />
    <hkern u1="S" u2="\" k="92" />
    <hkern u1="S" u2="X" k="41" />
    <hkern u1="S" u2="V" k="78" />
    <hkern u1="S" u2="&#x2f;" k="72" />
    <hkern u1="S" u2="&#x2a;" k="20" />
    <hkern u1="T" u2="&#x2122;" k="-18" />
    <hkern u1="T" u2="&#xbf;" k="102" />
    <hkern u1="T" u2="&#xae;" k="-10" />
    <hkern u1="T" u2="x" k="72" />
    <hkern u1="T" u2="v" k="51" />
    <hkern u1="T" u2="i" k="10" />
    <hkern u1="T" u2="V" k="-16" />
    <hkern u1="T" u2="&#x40;" k="72" />
    <hkern u1="T" u2="&#x3f;" k="-16" />
    <hkern u1="T" u2="&#x2f;" k="174" />
    <hkern u1="T" u2="&#x26;" k="18" />
    <hkern u1="T" u2="&#x21;" k="-10" />
    <hkern u1="U" u2="x" k="6" />
    <hkern u1="U" u2="X" k="20" />
    <hkern u1="U" u2="&#x2f;" k="61" />
    <hkern u1="V" g2="Jcircumflex.salt" k="72" />
    <hkern u1="V" g2="J.salt" k="72" />
    <hkern u1="V" u2="&#xfb02;" k="51" />
    <hkern u1="V" u2="&#xfb01;" k="51" />
    <hkern u1="V" u2="&#x2117;" k="72" />
    <hkern u1="V" u2="&#x203a;" k="96" />
    <hkern u1="V" u2="&#x2039;" k="113" />
    <hkern u1="V" u2="&#x2026;" k="287" />
    <hkern u1="V" u2="&#x201e;" k="246" />
    <hkern u1="V" u2="&#x201c;" k="31" />
    <hkern u1="V" u2="&#x201a;" k="246" />
    <hkern u1="V" u2="&#x2018;" k="31" />
    <hkern u1="V" u2="&#x2014;" k="113" />
    <hkern u1="V" u2="&#x2013;" k="113" />
    <hkern u1="V" u2="&#x1ef3;" k="27" />
    <hkern u1="V" u2="&#x1e85;" k="25" />
    <hkern u1="V" u2="&#x1e84;" k="-10" />
    <hkern u1="V" u2="&#x1e83;" k="25" />
    <hkern u1="V" u2="&#x1e82;" k="-10" />
    <hkern u1="V" u2="&#x1e81;" k="25" />
    <hkern u1="V" u2="&#x1e80;" k="-10" />
    <hkern u1="V" u2="&#x219;" k="113" />
    <hkern u1="V" u2="&#x1ff;" k="133" />
    <hkern u1="V" u2="&#x1fe;" k="57" />
    <hkern u1="V" u2="&#x1fd;" k="170" />
    <hkern u1="V" u2="&#x1fb;" k="170" />
    <hkern u1="V" u2="&#x1fa;" k="211" />
    <hkern u1="V" u2="&#x17e;" k="66" />
    <hkern u1="V" u2="&#x17c;" k="66" />
    <hkern u1="V" u2="&#x17a;" k="66" />
    <hkern u1="V" u2="&#x177;" k="27" />
    <hkern u1="V" u2="&#x175;" k="25" />
    <hkern u1="V" u2="&#x174;" k="-10" />
    <hkern u1="V" u2="&#x173;" k="72" />
    <hkern u1="V" u2="&#x171;" k="72" />
    <hkern u1="V" u2="&#x16f;" k="72" />
    <hkern u1="V" u2="&#x16d;" k="72" />
    <hkern u1="V" u2="&#x16b;" k="72" />
    <hkern u1="V" u2="&#x169;" k="72" />
    <hkern u1="V" u2="&#x164;" k="-16" />
    <hkern u1="V" u2="&#x163;" k="35" />
    <hkern u1="V" u2="&#x162;" k="-16" />
    <hkern u1="V" u2="&#x161;" k="113" />
    <hkern u1="V" u2="&#x160;" k="47" />
    <hkern u1="V" u2="&#x15f;" k="113" />
    <hkern u1="V" u2="&#x15e;" k="47" />
    <hkern u1="V" u2="&#x15d;" k="113" />
    <hkern u1="V" u2="&#x15c;" k="47" />
    <hkern u1="V" u2="&#x15b;" k="113" />
    <hkern u1="V" u2="&#x15a;" k="47" />
    <hkern u1="V" u2="&#x159;" k="76" />
    <hkern u1="V" u2="&#x157;" k="76" />
    <hkern u1="V" u2="&#x155;" k="76" />
    <hkern u1="V" u2="&#x152;" k="57" />
    <hkern u1="V" u2="&#x151;" k="133" />
    <hkern u1="V" u2="&#x150;" k="57" />
    <hkern u1="V" u2="&#x14f;" k="133" />
    <hkern u1="V" u2="&#x14e;" k="57" />
    <hkern u1="V" u2="&#x14d;" k="133" />
    <hkern u1="V" u2="&#x14c;" k="57" />
    <hkern u1="V" u2="&#x14b;" k="76" />
    <hkern u1="V" u2="&#x148;" k="76" />
    <hkern u1="V" u2="&#x146;" k="76" />
    <hkern u1="V" u2="&#x144;" k="76" />
    <hkern u1="V" u2="&#x134;" k="154" />
    <hkern u1="V" u2="&#x123;" k="133" />
    <hkern u1="V" u2="&#x122;" k="57" />
    <hkern u1="V" u2="&#x121;" k="133" />
    <hkern u1="V" u2="&#x120;" k="57" />
    <hkern u1="V" u2="&#x11f;" k="133" />
    <hkern u1="V" u2="&#x11e;" k="57" />
    <hkern u1="V" u2="&#x11d;" k="133" />
    <hkern u1="V" u2="&#x11c;" k="57" />
    <hkern u1="V" u2="&#x11b;" k="133" />
    <hkern u1="V" u2="&#x119;" k="133" />
    <hkern u1="V" u2="&#x117;" k="133" />
    <hkern u1="V" u2="&#x115;" k="133" />
    <hkern u1="V" u2="&#x113;" k="133" />
    <hkern u1="V" u2="&#x111;" k="133" />
    <hkern u1="V" u2="&#x10f;" k="133" />
    <hkern u1="V" u2="&#x10d;" k="133" />
    <hkern u1="V" u2="&#x10c;" k="57" />
    <hkern u1="V" u2="&#x10b;" k="133" />
    <hkern u1="V" u2="&#x10a;" k="57" />
    <hkern u1="V" u2="&#x109;" k="133" />
    <hkern u1="V" u2="&#x108;" k="57" />
    <hkern u1="V" u2="&#x107;" k="133" />
    <hkern u1="V" u2="&#x106;" k="57" />
    <hkern u1="V" u2="&#x105;" k="170" />
    <hkern u1="V" u2="&#x104;" k="211" />
    <hkern u1="V" u2="&#x103;" k="170" />
    <hkern u1="V" u2="&#x102;" k="211" />
    <hkern u1="V" u2="&#x101;" k="170" />
    <hkern u1="V" u2="&#x100;" k="211" />
    <hkern u1="V" u2="&#xff;" k="27" />
    <hkern u1="V" u2="&#xfd;" k="27" />
    <hkern u1="V" u2="&#xfc;" k="72" />
    <hkern u1="V" u2="&#xfb;" k="72" />
    <hkern u1="V" u2="&#xfa;" k="72" />
    <hkern u1="V" u2="&#xf9;" k="72" />
    <hkern u1="V" u2="&#xf8;" k="133" />
    <hkern u1="V" u2="&#xf6;" k="133" />
    <hkern u1="V" u2="&#xf5;" k="133" />
    <hkern u1="V" u2="&#xf4;" k="133" />
    <hkern u1="V" u2="&#xf3;" k="133" />
    <hkern u1="V" u2="&#xf2;" k="133" />
    <hkern u1="V" u2="&#xeb;" k="133" />
    <hkern u1="V" u2="&#xea;" k="133" />
    <hkern u1="V" u2="&#xe9;" k="133" />
    <hkern u1="V" u2="&#xe8;" k="133" />
    <hkern u1="V" u2="&#xe7;" k="133" />
    <hkern u1="V" u2="&#xe6;" k="170" />
    <hkern u1="V" u2="&#xe5;" k="170" />
    <hkern u1="V" u2="&#xe4;" k="170" />
    <hkern u1="V" u2="&#xe3;" k="170" />
    <hkern u1="V" u2="&#xe2;" k="170" />
    <hkern u1="V" u2="&#xe1;" k="170" />
    <hkern u1="V" u2="&#xe0;" k="170" />
    <hkern u1="V" u2="&#xd8;" k="57" />
    <hkern u1="V" u2="&#xd6;" k="57" />
    <hkern u1="V" u2="&#xd5;" k="57" />
    <hkern u1="V" u2="&#xd4;" k="57" />
    <hkern u1="V" u2="&#xd3;" k="57" />
    <hkern u1="V" u2="&#xd2;" k="57" />
    <hkern u1="V" u2="&#xc7;" k="57" />
    <hkern u1="V" u2="&#xc5;" k="211" />
    <hkern u1="V" u2="&#xc4;" k="211" />
    <hkern u1="V" u2="&#xc3;" k="211" />
    <hkern u1="V" u2="&#xc2;" k="211" />
    <hkern u1="V" u2="&#xc1;" k="211" />
    <hkern u1="V" u2="&#xc0;" k="211" />
    <hkern u1="V" u2="&#xbb;" k="96" />
    <hkern u1="V" u2="&#xab;" k="113" />
    <hkern u1="V" u2="&#xa9;" k="72" />
    <hkern u1="V" u2="&#x7d;" k="-10" />
    <hkern u1="V" u2="z" k="66" />
    <hkern u1="V" u2="y" k="27" />
    <hkern u1="V" u2="w" k="25" />
    <hkern u1="V" u2="u" k="72" />
    <hkern u1="V" u2="t" k="35" />
    <hkern u1="V" u2="s" k="113" />
    <hkern u1="V" u2="r" k="76" />
    <hkern u1="V" u2="q" k="133" />
    <hkern u1="V" u2="p" k="76" />
    <hkern u1="V" u2="o" k="133" />
    <hkern u1="V" u2="n" k="76" />
    <hkern u1="V" u2="m" k="76" />
    <hkern u1="V" u2="g" k="133" />
    <hkern u1="V" u2="f" k="51" />
    <hkern u1="V" u2="e" k="133" />
    <hkern u1="V" u2="d" k="133" />
    <hkern u1="V" u2="c" k="133" />
    <hkern u1="V" u2="a" k="170" />
    <hkern u1="V" u2="]" k="-10" />
    <hkern u1="V" u2="W" k="-10" />
    <hkern u1="V" u2="T" k="-16" />
    <hkern u1="V" u2="S" k="47" />
    <hkern u1="V" u2="Q" k="57" />
    <hkern u1="V" u2="O" k="57" />
    <hkern u1="V" u2="J" k="154" />
    <hkern u1="V" u2="G" k="57" />
    <hkern u1="V" u2="C" k="57" />
    <hkern u1="V" u2="A" k="211" />
    <hkern u1="V" u2="&#x3b;" k="143" />
    <hkern u1="V" u2="&#x3a;" k="143" />
    <hkern u1="V" u2="&#x2e;" k="287" />
    <hkern u1="V" u2="&#x2d;" k="113" />
    <hkern u1="V" u2="&#x2c;" k="246" />
    <hkern u1="V" u2="&#x29;" k="-10" />
    <hkern u1="V" u2="&#x2122;" k="-37" />
    <hkern u1="V" u2="&#xbf;" k="111" />
    <hkern u1="V" u2="&#xae;" k="20" />
    <hkern u1="V" u2="&#xa1;" k="37" />
    <hkern u1="V" u2="x" k="41" />
    <hkern u1="V" u2="v" k="27" />
    <hkern u1="V" u2="i" k="10" />
    <hkern u1="V" u2="h" k="10" />
    <hkern u1="V" u2="&#x40;" k="82" />
    <hkern u1="V" u2="&#x2f;" k="174" />
    <hkern u1="V" u2="&#x2a;" k="16" />
    <hkern u1="V" u2="&#x26;" k="57" />
    <hkern u1="W" u2="&#x2122;" k="-37" />
    <hkern u1="W" u2="&#xbf;" k="66" />
    <hkern u1="W" u2="&#xa1;" k="20" />
    <hkern u1="W" u2="x" k="31" />
    <hkern u1="W" u2="v" k="6" />
    <hkern u1="W" u2="i" k="10" />
    <hkern u1="W" u2="\" k="-10" />
    <hkern u1="W" u2="V" k="-10" />
    <hkern u1="W" u2="&#x40;" k="51" />
    <hkern u1="W" u2="&#x2f;" k="133" />
    <hkern u1="W" u2="&#x2a;" k="18" />
    <hkern u1="W" u2="&#x26;" k="27" />
    <hkern u1="X" g2="Jcircumflex.salt" k="39" />
    <hkern u1="X" g2="J.salt" k="39" />
    <hkern u1="X" u2="&#xfb02;" k="20" />
    <hkern u1="X" u2="&#xfb01;" k="20" />
    <hkern u1="X" u2="&#x2117;" k="72" />
    <hkern u1="X" u2="&#x203a;" k="37" />
    <hkern u1="X" u2="&#x2039;" k="106" />
    <hkern u1="X" u2="&#x2026;" k="31" />
    <hkern u1="X" u2="&#x201d;" k="82" />
    <hkern u1="X" u2="&#x201c;" k="113" />
    <hkern u1="X" u2="&#x2019;" k="82" />
    <hkern u1="X" u2="&#x2018;" k="113" />
    <hkern u1="X" u2="&#x2014;" k="174" />
    <hkern u1="X" u2="&#x2013;" k="174" />
    <hkern u1="X" u2="&#x1ef3;" k="102" />
    <hkern u1="X" u2="&#x1e85;" k="51" />
    <hkern u1="X" u2="&#x1e83;" k="51" />
    <hkern u1="X" u2="&#x1e81;" k="51" />
    <hkern u1="X" u2="&#x219;" k="37" />
    <hkern u1="X" u2="&#x1ff;" k="61" />
    <hkern u1="X" u2="&#x1fe;" k="82" />
    <hkern u1="X" u2="&#x1fd;" k="51" />
    <hkern u1="X" u2="&#x1fb;" k="51" />
    <hkern u1="X" u2="&#x1fa;" k="-10" />
    <hkern u1="X" u2="&#x177;" k="102" />
    <hkern u1="X" u2="&#x175;" k="51" />
    <hkern u1="X" u2="&#x173;" k="43" />
    <hkern u1="X" u2="&#x172;" k="20" />
    <hkern u1="X" u2="&#x171;" k="43" />
    <hkern u1="X" u2="&#x170;" k="20" />
    <hkern u1="X" u2="&#x16f;" k="43" />
    <hkern u1="X" u2="&#x16e;" k="20" />
    <hkern u1="X" u2="&#x16d;" k="43" />
    <hkern u1="X" u2="&#x16c;" k="20" />
    <hkern u1="X" u2="&#x16b;" k="43" />
    <hkern u1="X" u2="&#x16a;" k="20" />
    <hkern u1="X" u2="&#x169;" k="43" />
    <hkern u1="X" u2="&#x168;" k="20" />
    <hkern u1="X" u2="&#x163;" k="61" />
    <hkern u1="X" u2="&#x161;" k="37" />
    <hkern u1="X" u2="&#x160;" k="45" />
    <hkern u1="X" u2="&#x15f;" k="37" />
    <hkern u1="X" u2="&#x15e;" k="45" />
    <hkern u1="X" u2="&#x15d;" k="37" />
    <hkern u1="X" u2="&#x15c;" k="45" />
    <hkern u1="X" u2="&#x15b;" k="37" />
    <hkern u1="X" u2="&#x15a;" k="45" />
    <hkern u1="X" u2="&#x152;" k="82" />
    <hkern u1="X" u2="&#x151;" k="61" />
    <hkern u1="X" u2="&#x150;" k="82" />
    <hkern u1="X" u2="&#x14f;" k="61" />
    <hkern u1="X" u2="&#x14e;" k="82" />
    <hkern u1="X" u2="&#x14d;" k="61" />
    <hkern u1="X" u2="&#x14c;" k="82" />
    <hkern u1="X" u2="&#x149;" k="82" />
    <hkern u1="X" u2="&#x134;" k="51" />
    <hkern u1="X" u2="&#x123;" k="61" />
    <hkern u1="X" u2="&#x122;" k="82" />
    <hkern u1="X" u2="&#x121;" k="61" />
    <hkern u1="X" u2="&#x120;" k="82" />
    <hkern u1="X" u2="&#x11f;" k="61" />
    <hkern u1="X" u2="&#x11e;" k="82" />
    <hkern u1="X" u2="&#x11d;" k="61" />
    <hkern u1="X" u2="&#x11c;" k="82" />
    <hkern u1="X" u2="&#x11b;" k="61" />
    <hkern u1="X" u2="&#x119;" k="61" />
    <hkern u1="X" u2="&#x117;" k="61" />
    <hkern u1="X" u2="&#x115;" k="61" />
    <hkern u1="X" u2="&#x113;" k="61" />
    <hkern u1="X" u2="&#x111;" k="61" />
    <hkern u1="X" u2="&#x10f;" k="61" />
    <hkern u1="X" u2="&#x10d;" k="61" />
    <hkern u1="X" u2="&#x10c;" k="82" />
    <hkern u1="X" u2="&#x10b;" k="61" />
    <hkern u1="X" u2="&#x10a;" k="82" />
    <hkern u1="X" u2="&#x109;" k="61" />
    <hkern u1="X" u2="&#x108;" k="82" />
    <hkern u1="X" u2="&#x107;" k="61" />
    <hkern u1="X" u2="&#x106;" k="82" />
    <hkern u1="X" u2="&#x105;" k="51" />
    <hkern u1="X" u2="&#x104;" k="-10" />
    <hkern u1="X" u2="&#x103;" k="51" />
    <hkern u1="X" u2="&#x102;" k="-10" />
    <hkern u1="X" u2="&#x101;" k="51" />
    <hkern u1="X" u2="&#x100;" k="-10" />
    <hkern u1="X" u2="&#xff;" k="102" />
    <hkern u1="X" u2="&#xfd;" k="102" />
    <hkern u1="X" u2="&#xfc;" k="43" />
    <hkern u1="X" u2="&#xfb;" k="43" />
    <hkern u1="X" u2="&#xfa;" k="43" />
    <hkern u1="X" u2="&#xf9;" k="43" />
    <hkern u1="X" u2="&#xf8;" k="61" />
    <hkern u1="X" u2="&#xf6;" k="61" />
    <hkern u1="X" u2="&#xf5;" k="61" />
    <hkern u1="X" u2="&#xf4;" k="61" />
    <hkern u1="X" u2="&#xf3;" k="61" />
    <hkern u1="X" u2="&#xf2;" k="61" />
    <hkern u1="X" u2="&#xeb;" k="61" />
    <hkern u1="X" u2="&#xea;" k="61" />
    <hkern u1="X" u2="&#xe9;" k="61" />
    <hkern u1="X" u2="&#xe8;" k="61" />
    <hkern u1="X" u2="&#xe7;" k="61" />
    <hkern u1="X" u2="&#xe6;" k="51" />
    <hkern u1="X" u2="&#xe5;" k="51" />
    <hkern u1="X" u2="&#xe4;" k="51" />
    <hkern u1="X" u2="&#xe3;" k="51" />
    <hkern u1="X" u2="&#xe2;" k="51" />
    <hkern u1="X" u2="&#xe1;" k="51" />
    <hkern u1="X" u2="&#xe0;" k="51" />
    <hkern u1="X" u2="&#xdc;" k="20" />
    <hkern u1="X" u2="&#xdb;" k="20" />
    <hkern u1="X" u2="&#xda;" k="20" />
    <hkern u1="X" u2="&#xd9;" k="20" />
    <hkern u1="X" u2="&#xd8;" k="82" />
    <hkern u1="X" u2="&#xd6;" k="82" />
    <hkern u1="X" u2="&#xd5;" k="82" />
    <hkern u1="X" u2="&#xd4;" k="82" />
    <hkern u1="X" u2="&#xd3;" k="82" />
    <hkern u1="X" u2="&#xd2;" k="82" />
    <hkern u1="X" u2="&#xc7;" k="82" />
    <hkern u1="X" u2="&#xc5;" k="-10" />
    <hkern u1="X" u2="&#xc4;" k="-10" />
    <hkern u1="X" u2="&#xc3;" k="-10" />
    <hkern u1="X" u2="&#xc2;" k="-10" />
    <hkern u1="X" u2="&#xc1;" k="-10" />
    <hkern u1="X" u2="&#xc0;" k="-10" />
    <hkern u1="X" u2="&#xbb;" k="37" />
    <hkern u1="X" u2="&#xba;" k="31" />
    <hkern u1="X" u2="&#xab;" k="106" />
    <hkern u1="X" u2="&#xaa;" k="31" />
    <hkern u1="X" u2="&#xa9;" k="72" />
    <hkern u1="X" u2="y" k="102" />
    <hkern u1="X" u2="w" k="51" />
    <hkern u1="X" u2="u" k="43" />
    <hkern u1="X" u2="t" k="61" />
    <hkern u1="X" u2="s" k="37" />
    <hkern u1="X" u2="q" k="61" />
    <hkern u1="X" u2="o" k="61" />
    <hkern u1="X" u2="g" k="61" />
    <hkern u1="X" u2="f" k="20" />
    <hkern u1="X" u2="e" k="61" />
    <hkern u1="X" u2="d" k="61" />
    <hkern u1="X" u2="c" k="61" />
    <hkern u1="X" u2="a" k="51" />
    <hkern u1="X" u2="U" k="20" />
    <hkern u1="X" u2="S" k="45" />
    <hkern u1="X" u2="Q" k="82" />
    <hkern u1="X" u2="O" k="82" />
    <hkern u1="X" u2="J" k="51" />
    <hkern u1="X" u2="G" k="82" />
    <hkern u1="X" u2="C" k="82" />
    <hkern u1="X" u2="A" k="-10" />
    <hkern u1="X" u2="&#x3b;" k="41" />
    <hkern u1="X" u2="&#x3a;" k="41" />
    <hkern u1="X" u2="&#x2e;" k="31" />
    <hkern u1="X" u2="&#x2d;" k="174" />
    <hkern u1="X" u2="&#xbf;" k="25" />
    <hkern u1="X" u2="&#xae;" k="82" />
    <hkern u1="X" u2="x" k="-10" />
    <hkern u1="X" u2="v" k="102" />
    <hkern u1="X" u2="&#x40;" k="70" />
    <hkern u1="X" u2="&#x3f;" k="41" />
    <hkern u1="X" u2="&#x2a;" k="86" />
    <hkern u1="X" u2="&#x26;" k="31" />
    <hkern u1="Y" u2="&#x2122;" k="-47" />
    <hkern u1="Y" u2="&#xbf;" k="162" />
    <hkern u1="Y" u2="&#xae;" k="39" />
    <hkern u1="Y" u2="&#xa1;" k="41" />
    <hkern u1="Y" u2="x" k="92" />
    <hkern u1="Y" u2="v" k="66" />
    <hkern u1="Y" u2="i" k="16" />
    <hkern u1="Y" u2="h" k="10" />
    <hkern u1="Y" u2="&#x40;" k="141" />
    <hkern u1="Y" u2="&#x3f;" k="16" />
    <hkern u1="Y" u2="&#x2f;" k="225" />
    <hkern u1="Y" u2="&#x2a;" k="37" />
    <hkern u1="Y" u2="&#x26;" k="106" />
    <hkern u1="Z" u2="&#x2122;" k="2" />
    <hkern u1="Z" u2="&#xbf;" k="4" />
    <hkern u1="Z" u2="&#xae;" k="33" />
    <hkern u1="Z" u2="v" k="27" />
    <hkern u1="Z" u2="&#x3f;" k="18" />
    <hkern u1="Z" u2="&#x2a;" k="20" />
    <hkern u1="[" u2="&#x20ac;" k="86" />
    <hkern u1="[" u2="&#xbf;" k="39" />
    <hkern u1="[" u2="&#xae;" k="20" />
    <hkern u1="[" u2="&#xa5;" k="20" />
    <hkern u1="[" u2="&#xa2;" k="51" />
    <hkern u1="[" u2="x" k="10" />
    <hkern u1="[" u2="v" k="31" />
    <hkern u1="[" u2="j" k="-41" />
    <hkern u1="[" u2="i" k="10" />
    <hkern u1="[" u2="V" k="-10" />
    <hkern u1="[" u2="&#x40;" k="41" />
    <hkern u1="[" u2="&#x3f;" k="20" />
    <hkern u1="[" u2="&#x39;" k="51" />
    <hkern u1="[" u2="&#x38;" k="51" />
    <hkern u1="[" u2="&#x37;" k="10" />
    <hkern u1="[" u2="&#x36;" k="61" />
    <hkern u1="[" u2="&#x35;" k="41" />
    <hkern u1="[" u2="&#x34;" k="51" />
    <hkern u1="[" u2="&#x33;" k="10" />
    <hkern u1="[" u2="&#x31;" k="10" />
    <hkern u1="[" u2="&#x30;" k="31" />
    <hkern u1="[" u2="&#x2a;" k="72" />
    <hkern u1="[" u2="&#x26;" k="51" />
    <hkern u1="[" u2="&#x24;" k="20" />
    <hkern u1="[" u2="&#x23;" k="51" />
    <hkern u1="[" u2="&#x21;" k="10" />
    <hkern u1="\" g2="Jcircumflex.salt" k="41" />
    <hkern u1="\" g2="J.salt" k="41" />
    <hkern u1="\" u2="&#xfb02;" k="20" />
    <hkern u1="\" u2="&#xfb01;" k="20" />
    <hkern u1="\" u2="&#x2117;" k="78" />
    <hkern u1="\" u2="&#x203a;" k="41" />
    <hkern u1="\" u2="&#x2039;" k="49" />
    <hkern u1="\" u2="&#x2030;" k="205" />
    <hkern u1="\" u2="&#x2026;" k="31" />
    <hkern u1="\" u2="&#x201e;" k="-41" />
    <hkern u1="\" u2="&#x201d;" k="256" />
    <hkern u1="\" u2="&#x201c;" k="242" />
    <hkern u1="\" u2="&#x201a;" k="-41" />
    <hkern u1="\" u2="&#x2019;" k="256" />
    <hkern u1="\" u2="&#x2018;" k="242" />
    <hkern u1="\" u2="&#x2014;" k="143" />
    <hkern u1="\" u2="&#x2013;" k="143" />
    <hkern u1="\" u2="&#x1ef3;" k="51" />
    <hkern u1="\" u2="&#x1ef2;" k="236" />
    <hkern u1="\" u2="&#x1e85;" k="51" />
    <hkern u1="\" u2="&#x1e84;" k="123" />
    <hkern u1="\" u2="&#x1e83;" k="51" />
    <hkern u1="\" u2="&#x1e82;" k="123" />
    <hkern u1="\" u2="&#x1e81;" k="51" />
    <hkern u1="\" u2="&#x1e80;" k="123" />
    <hkern u1="\" u2="&#x219;" k="20" />
    <hkern u1="\" u2="&#x1ff;" k="37" />
    <hkern u1="\" u2="&#x1fe;" k="72" />
    <hkern u1="\" u2="&#x1fd;" k="31" />
    <hkern u1="\" u2="&#x1fb;" k="31" />
    <hkern u1="\" u2="&#x178;" k="236" />
    <hkern u1="\" u2="&#x177;" k="51" />
    <hkern u1="\" u2="&#x176;" k="236" />
    <hkern u1="\" u2="&#x175;" k="51" />
    <hkern u1="\" u2="&#x174;" k="123" />
    <hkern u1="\" u2="&#x173;" k="31" />
    <hkern u1="\" u2="&#x172;" k="72" />
    <hkern u1="\" u2="&#x171;" k="31" />
    <hkern u1="\" u2="&#x170;" k="72" />
    <hkern u1="\" u2="&#x16f;" k="31" />
    <hkern u1="\" u2="&#x16e;" k="72" />
    <hkern u1="\" u2="&#x16d;" k="31" />
    <hkern u1="\" u2="&#x16c;" k="72" />
    <hkern u1="\" u2="&#x16b;" k="31" />
    <hkern u1="\" u2="&#x16a;" k="72" />
    <hkern u1="\" u2="&#x169;" k="31" />
    <hkern u1="\" u2="&#x168;" k="72" />
    <hkern u1="\" u2="&#x164;" k="143" />
    <hkern u1="\" u2="&#x163;" k="82" />
    <hkern u1="\" u2="&#x162;" k="143" />
    <hkern u1="\" u2="&#x161;" k="20" />
    <hkern u1="\" u2="&#x160;" k="41" />
    <hkern u1="\" u2="&#x15f;" k="20" />
    <hkern u1="\" u2="&#x15e;" k="41" />
    <hkern u1="\" u2="&#x15d;" k="20" />
    <hkern u1="\" u2="&#x15c;" k="41" />
    <hkern u1="\" u2="&#x15b;" k="20" />
    <hkern u1="\" u2="&#x15a;" k="41" />
    <hkern u1="\" u2="&#x152;" k="72" />
    <hkern u1="\" u2="&#x151;" k="37" />
    <hkern u1="\" u2="&#x150;" k="72" />
    <hkern u1="\" u2="&#x14f;" k="37" />
    <hkern u1="\" u2="&#x14e;" k="72" />
    <hkern u1="\" u2="&#x14d;" k="37" />
    <hkern u1="\" u2="&#x14c;" k="72" />
    <hkern u1="\" u2="&#x149;" k="256" />
    <hkern u1="\" u2="&#x134;" k="41" />
    <hkern u1="\" u2="&#x123;" k="37" />
    <hkern u1="\" u2="&#x122;" k="72" />
    <hkern u1="\" u2="&#x121;" k="37" />
    <hkern u1="\" u2="&#x120;" k="72" />
    <hkern u1="\" u2="&#x11f;" k="37" />
    <hkern u1="\" u2="&#x11e;" k="72" />
    <hkern u1="\" u2="&#x11d;" k="37" />
    <hkern u1="\" u2="&#x11c;" k="72" />
    <hkern u1="\" u2="&#x11b;" k="37" />
    <hkern u1="\" u2="&#x119;" k="37" />
    <hkern u1="\" u2="&#x117;" k="37" />
    <hkern u1="\" u2="&#x115;" k="37" />
    <hkern u1="\" u2="&#x113;" k="37" />
    <hkern u1="\" u2="&#x111;" k="37" />
    <hkern u1="\" u2="&#x10f;" k="37" />
    <hkern u1="\" u2="&#x10d;" k="37" />
    <hkern u1="\" u2="&#x10c;" k="72" />
    <hkern u1="\" u2="&#x10b;" k="37" />
    <hkern u1="\" u2="&#x10a;" k="72" />
    <hkern u1="\" u2="&#x109;" k="37" />
    <hkern u1="\" u2="&#x108;" k="72" />
    <hkern u1="\" u2="&#x107;" k="37" />
    <hkern u1="\" u2="&#x106;" k="72" />
    <hkern u1="\" u2="&#x105;" k="31" />
    <hkern u1="\" u2="&#x103;" k="31" />
    <hkern u1="\" u2="&#x101;" k="31" />
    <hkern u1="\" u2="&#xff;" k="51" />
    <hkern u1="\" u2="&#xfd;" k="51" />
    <hkern u1="\" u2="&#xfc;" k="31" />
    <hkern u1="\" u2="&#xfb;" k="31" />
    <hkern u1="\" u2="&#xfa;" k="31" />
    <hkern u1="\" u2="&#xf9;" k="31" />
    <hkern u1="\" u2="&#xf8;" k="37" />
    <hkern u1="\" u2="&#xf6;" k="37" />
    <hkern u1="\" u2="&#xf5;" k="37" />
    <hkern u1="\" u2="&#xf4;" k="37" />
    <hkern u1="\" u2="&#xf3;" k="37" />
    <hkern u1="\" u2="&#xf2;" k="37" />
    <hkern u1="\" u2="&#xeb;" k="37" />
    <hkern u1="\" u2="&#xea;" k="37" />
    <hkern u1="\" u2="&#xe9;" k="37" />
    <hkern u1="\" u2="&#xe8;" k="37" />
    <hkern u1="\" u2="&#xe7;" k="37" />
    <hkern u1="\" u2="&#xe6;" k="31" />
    <hkern u1="\" u2="&#xe5;" k="31" />
    <hkern u1="\" u2="&#xe4;" k="31" />
    <hkern u1="\" u2="&#xe3;" k="31" />
    <hkern u1="\" u2="&#xe2;" k="31" />
    <hkern u1="\" u2="&#xe1;" k="31" />
    <hkern u1="\" u2="&#xe0;" k="31" />
    <hkern u1="\" u2="&#xdd;" k="236" />
    <hkern u1="\" u2="&#xdc;" k="72" />
    <hkern u1="\" u2="&#xdb;" k="72" />
    <hkern u1="\" u2="&#xda;" k="72" />
    <hkern u1="\" u2="&#xd9;" k="72" />
    <hkern u1="\" u2="&#xd8;" k="72" />
    <hkern u1="\" u2="&#xd6;" k="72" />
    <hkern u1="\" u2="&#xd5;" k="72" />
    <hkern u1="\" u2="&#xd4;" k="72" />
    <hkern u1="\" u2="&#xd3;" k="72" />
    <hkern u1="\" u2="&#xd2;" k="72" />
    <hkern u1="\" u2="&#xc7;" k="72" />
    <hkern u1="\" u2="&#xbb;" k="41" />
    <hkern u1="\" u2="&#xba;" k="102" />
    <hkern u1="\" u2="&#xab;" k="49" />
    <hkern u1="\" u2="&#xaa;" k="102" />
    <hkern u1="\" u2="&#xa9;" k="78" />
    <hkern u1="\" u2="&#x7d;" k="-20" />
    <hkern u1="\" u2="&#x7b;" k="20" />
    <hkern u1="\" u2="y" k="51" />
    <hkern u1="\" u2="w" k="51" />
    <hkern u1="\" u2="u" k="31" />
    <hkern u1="\" u2="t" k="82" />
    <hkern u1="\" u2="s" k="20" />
    <hkern u1="\" u2="q" k="37" />
    <hkern u1="\" u2="o" k="37" />
    <hkern u1="\" u2="g" k="37" />
    <hkern u1="\" u2="f" k="20" />
    <hkern u1="\" u2="e" k="37" />
    <hkern u1="\" u2="d" k="37" />
    <hkern u1="\" u2="c" k="37" />
    <hkern u1="\" u2="a" k="31" />
    <hkern u1="\" u2="]" k="-20" />
    <hkern u1="\" u2="[" k="20" />
    <hkern u1="\" u2="Y" k="236" />
    <hkern u1="\" u2="W" k="123" />
    <hkern u1="\" u2="U" k="72" />
    <hkern u1="\" u2="T" k="143" />
    <hkern u1="\" u2="S" k="41" />
    <hkern u1="\" u2="Q" k="72" />
    <hkern u1="\" u2="O" k="72" />
    <hkern u1="\" u2="J" k="41" />
    <hkern u1="\" u2="G" k="72" />
    <hkern u1="\" u2="C" k="72" />
    <hkern u1="\" u2="&#x3b;" k="41" />
    <hkern u1="\" u2="&#x3a;" k="41" />
    <hkern u1="\" u2="&#x2e;" k="31" />
    <hkern u1="\" u2="&#x2d;" k="143" />
    <hkern u1="\" u2="&#x2c;" k="-41" />
    <hkern u1="\" u2="&#x29;" k="-20" />
    <hkern u1="\" u2="&#x28;" k="20" />
    <hkern u1="\" u2="&#x25;" k="205" />
    <hkern u1="\" u2="&#x2122;" k="256" />
    <hkern u1="\" u2="&#x20ac;" k="92" />
    <hkern u1="\" u2="&#xbf;" k="25" />
    <hkern u1="\" u2="&#xae;" k="256" />
    <hkern u1="\" u2="&#xa5;" k="61" />
    <hkern u1="\" u2="&#xa3;" k="10" />
    <hkern u1="\" u2="&#xa2;" k="113" />
    <hkern u1="\" u2="v" k="92" />
    <hkern u1="\" u2="j" k="-31" />
    <hkern u1="\" u2="V" k="215" />
    <hkern u1="\" u2="&#x40;" k="51" />
    <hkern u1="\" u2="&#x3f;" k="184" />
    <hkern u1="\" u2="&#x39;" k="123" />
    <hkern u1="\" u2="&#x38;" k="31" />
    <hkern u1="\" u2="&#x37;" k="16" />
    <hkern u1="\" u2="&#x36;" k="31" />
    <hkern u1="\" u2="&#x35;" k="41" />
    <hkern u1="\" u2="&#x34;" k="31" />
    <hkern u1="\" u2="&#x33;" k="31" />
    <hkern u1="\" u2="&#x32;" k="-10" />
    <hkern u1="\" u2="&#x31;" k="92" />
    <hkern u1="\" u2="&#x30;" k="51" />
    <hkern u1="\" u2="&#x2a;" k="266" />
    <hkern u1="\" u2="&#x26;" k="61" />
    <hkern u1="\" u2="&#x24;" k="41" />
    <hkern u1="\" u2="&#x23;" k="31" />
    <hkern u1="\" u2="&#x21;" k="16" />
    <hkern u1="]" u2="\" k="72" />
    <hkern u1="]" u2="&#x2f;" k="41" />
    <hkern u1="]" u2="&#x2a;" k="10" />
    <hkern u1="a" u2="&#x2122;" k="119" />
    <hkern u1="a" u2="&#xae;" k="20" />
    <hkern u1="a" u2="v" k="20" />
    <hkern u1="a" u2="\" k="123" />
    <hkern u1="a" u2="&#x3f;" k="78" />
    <hkern u1="a" u2="&#x2a;" k="20" />
    <hkern u1="b" u2="&#x2122;" k="61" />
    <hkern u1="b" u2="&#xae;" k="20" />
    <hkern u1="b" u2="x" k="53" />
    <hkern u1="b" u2="v" k="23" />
    <hkern u1="b" u2="\" k="123" />
    <hkern u1="b" u2="&#x3f;" k="31" />
    <hkern u1="b" u2="&#x2f;" k="61" />
    <hkern u1="b" u2="&#x2a;" k="31" />
    <hkern u1="c" u2="&#x2122;" k="72" />
    <hkern u1="c" u2="&#xae;" k="10" />
    <hkern u1="c" u2="x" k="20" />
    <hkern u1="c" u2="v" k="6" />
    <hkern u1="c" u2="\" k="123" />
    <hkern u1="c" u2="&#x3f;" k="31" />
    <hkern u1="c" u2="&#x2f;" k="20" />
    <hkern u1="c" u2="&#x2a;" k="31" />
    <hkern u1="e" u2="&#x2122;" k="78" />
    <hkern u1="e" u2="&#xae;" k="18" />
    <hkern u1="e" u2="x" k="31" />
    <hkern u1="e" u2="v" k="20" />
    <hkern u1="e" u2="\" k="123" />
    <hkern u1="e" u2="&#x3f;" k="31" />
    <hkern u1="e" u2="&#x2f;" k="31" />
    <hkern u1="e" u2="&#x2a;" k="31" />
    <hkern u1="f" u2="&#xfb02;" k="2" />
    <hkern u1="f" u2="&#xfb01;" k="2" />
    <hkern u1="f" u2="&#x203a;" k="10" />
    <hkern u1="f" u2="&#x2039;" k="25" />
    <hkern u1="f" u2="&#x2026;" k="102" />
    <hkern u1="f" u2="&#x201e;" k="102" />
    <hkern u1="f" u2="&#x201d;" k="-51" />
    <hkern u1="f" u2="&#x201c;" k="-31" />
    <hkern u1="f" u2="&#x201a;" k="102" />
    <hkern u1="f" u2="&#x2019;" k="-51" />
    <hkern u1="f" u2="&#x2018;" k="-31" />
    <hkern u1="f" u2="&#x2014;" k="10" />
    <hkern u1="f" u2="&#x2013;" k="10" />
    <hkern u1="f" u2="&#x1ef3;" k="-35" />
    <hkern u1="f" u2="&#x1e85;" k="-29" />
    <hkern u1="f" u2="&#x1e83;" k="-29" />
    <hkern u1="f" u2="&#x1e81;" k="-29" />
    <hkern u1="f" u2="&#x219;" k="6" />
    <hkern u1="f" u2="&#x1ff;" k="20" />
    <hkern u1="f" u2="&#x1fd;" k="51" />
    <hkern u1="f" u2="&#x1fb;" k="51" />
    <hkern u1="f" u2="&#x17e;" k="4" />
    <hkern u1="f" u2="&#x17c;" k="4" />
    <hkern u1="f" u2="&#x17a;" k="4" />
    <hkern u1="f" u2="&#x177;" k="-35" />
    <hkern u1="f" u2="&#x175;" k="-29" />
    <hkern u1="f" u2="&#x173;" k="2" />
    <hkern u1="f" u2="&#x171;" k="2" />
    <hkern u1="f" u2="&#x16f;" k="2" />
    <hkern u1="f" u2="&#x16d;" k="2" />
    <hkern u1="f" u2="&#x16b;" k="2" />
    <hkern u1="f" u2="&#x169;" k="2" />
    <hkern u1="f" u2="&#x163;" k="2" />
    <hkern u1="f" u2="&#x161;" k="6" />
    <hkern u1="f" u2="&#x15f;" k="6" />
    <hkern u1="f" u2="&#x15d;" k="6" />
    <hkern u1="f" u2="&#x15b;" k="6" />
    <hkern u1="f" u2="&#x159;" k="4" />
    <hkern u1="f" u2="&#x157;" k="4" />
    <hkern u1="f" u2="&#x155;" k="4" />
    <hkern u1="f" u2="&#x151;" k="20" />
    <hkern u1="f" u2="&#x14f;" k="20" />
    <hkern u1="f" u2="&#x14d;" k="20" />
    <hkern u1="f" u2="&#x14b;" k="4" />
    <hkern u1="f" u2="&#x149;" k="-51" />
    <hkern u1="f" u2="&#x148;" k="4" />
    <hkern u1="f" u2="&#x146;" k="4" />
    <hkern u1="f" u2="&#x144;" k="4" />
    <hkern u1="f" u2="&#x123;" k="20" />
    <hkern u1="f" u2="&#x121;" k="20" />
    <hkern u1="f" u2="&#x11f;" k="20" />
    <hkern u1="f" u2="&#x11d;" k="20" />
    <hkern u1="f" u2="&#x11b;" k="20" />
    <hkern u1="f" u2="&#x119;" k="20" />
    <hkern u1="f" u2="&#x117;" k="20" />
    <hkern u1="f" u2="&#x115;" k="20" />
    <hkern u1="f" u2="&#x113;" k="20" />
    <hkern u1="f" u2="&#x111;" k="20" />
    <hkern u1="f" u2="&#x10f;" k="20" />
    <hkern u1="f" u2="&#x10d;" k="20" />
    <hkern u1="f" u2="&#x10b;" k="20" />
    <hkern u1="f" u2="&#x109;" k="20" />
    <hkern u1="f" u2="&#x107;" k="20" />
    <hkern u1="f" u2="&#x105;" k="51" />
    <hkern u1="f" u2="&#x103;" k="51" />
    <hkern u1="f" u2="&#x101;" k="51" />
    <hkern u1="f" u2="&#xff;" k="-35" />
    <hkern u1="f" u2="&#xfd;" k="-35" />
    <hkern u1="f" u2="&#xfc;" k="2" />
    <hkern u1="f" u2="&#xfb;" k="2" />
    <hkern u1="f" u2="&#xfa;" k="2" />
    <hkern u1="f" u2="&#xf9;" k="2" />
    <hkern u1="f" u2="&#xf8;" k="20" />
    <hkern u1="f" u2="&#xf6;" k="20" />
    <hkern u1="f" u2="&#xf5;" k="20" />
    <hkern u1="f" u2="&#xf4;" k="20" />
    <hkern u1="f" u2="&#xf3;" k="20" />
    <hkern u1="f" u2="&#xf2;" k="20" />
    <hkern u1="f" u2="&#xeb;" k="20" />
    <hkern u1="f" u2="&#xea;" k="20" />
    <hkern u1="f" u2="&#xe9;" k="20" />
    <hkern u1="f" u2="&#xe8;" k="20" />
    <hkern u1="f" u2="&#xe7;" k="20" />
    <hkern u1="f" u2="&#xe6;" k="51" />
    <hkern u1="f" u2="&#xe5;" k="51" />
    <hkern u1="f" u2="&#xe4;" k="51" />
    <hkern u1="f" u2="&#xe3;" k="51" />
    <hkern u1="f" u2="&#xe2;" k="51" />
    <hkern u1="f" u2="&#xe1;" k="51" />
    <hkern u1="f" u2="&#xe0;" k="51" />
    <hkern u1="f" u2="&#xbb;" k="10" />
    <hkern u1="f" u2="&#xba;" k="-18" />
    <hkern u1="f" u2="&#xab;" k="25" />
    <hkern u1="f" u2="&#xaa;" k="-18" />
    <hkern u1="f" u2="&#x7d;" k="-10" />
    <hkern u1="f" u2="z" k="4" />
    <hkern u1="f" u2="y" k="-35" />
    <hkern u1="f" u2="w" k="-29" />
    <hkern u1="f" u2="u" k="2" />
    <hkern u1="f" u2="t" k="2" />
    <hkern u1="f" u2="s" k="6" />
    <hkern u1="f" u2="r" k="4" />
    <hkern u1="f" u2="q" k="20" />
    <hkern u1="f" u2="p" k="4" />
    <hkern u1="f" u2="o" k="20" />
    <hkern u1="f" u2="n" k="4" />
    <hkern u1="f" u2="m" k="4" />
    <hkern u1="f" u2="g" k="20" />
    <hkern u1="f" u2="f" k="2" />
    <hkern u1="f" u2="e" k="20" />
    <hkern u1="f" u2="d" k="20" />
    <hkern u1="f" u2="c" k="20" />
    <hkern u1="f" u2="a" k="51" />
    <hkern u1="f" u2="]" k="-10" />
    <hkern u1="f" u2="&#x3b;" k="31" />
    <hkern u1="f" u2="&#x3a;" k="31" />
    <hkern u1="f" u2="&#x2e;" k="102" />
    <hkern u1="f" u2="&#x2d;" k="10" />
    <hkern u1="f" u2="&#x2c;" k="102" />
    <hkern u1="f" u2="&#x29;" k="-10" />
    <hkern u1="f" u2="&#x2122;" k="-78" />
    <hkern u1="f" u2="&#xbf;" k="35" />
    <hkern u1="f" u2="&#xae;" k="-39" />
    <hkern u1="f" u2="x" k="-12" />
    <hkern u1="f" u2="v" k="-37" />
    <hkern u1="f" u2="&#x40;" k="10" />
    <hkern u1="f" u2="&#x3f;" k="-20" />
    <hkern u1="f" u2="&#x2f;" k="82" />
    <hkern u1="f" u2="&#x2a;" k="-31" />
    <hkern u1="f" u2="&#x26;" k="10" />
    <hkern u1="g" u2="&#x2122;" k="18" />
    <hkern u1="g" u2="\" k="20" />
    <hkern u1="h" u2="&#x2122;" k="100" />
    <hkern u1="h" u2="&#xae;" k="29" />
    <hkern u1="h" u2="v" k="20" />
    <hkern u1="h" u2="\" k="113" />
    <hkern u1="h" u2="&#x2a;" k="31" />
    <hkern u1="i" u2="&#x7d;" k="10" />
    <hkern u1="i" u2="]" k="10" />
    <hkern u1="i" u2="&#x29;" k="10" />
    <hkern u1="j" u2="&#x201e;" k="10" />
    <hkern u1="j" u2="&#x201a;" k="10" />
    <hkern u1="j" u2="&#x7d;" k="10" />
    <hkern u1="j" u2="]" k="10" />
    <hkern u1="j" u2="&#x2c;" k="10" />
    <hkern u1="j" u2="&#x29;" k="10" />
    <hkern u1="j" u2="\" k="20" />
    <hkern u1="j" u2="&#x2f;" k="10" />
    <hkern u1="k" u2="&#xbf;" k="20" />
    <hkern u1="k" u2="&#xae;" k="-16" />
    <hkern u1="k" u2="x" k="-31" />
    <hkern u1="k" u2="v" k="-10" />
    <hkern u1="k" u2="\" k="51" />
    <hkern u1="k" u2="&#x40;" k="16" />
    <hkern u1="k" u2="&#x3f;" k="-20" />
    <hkern u1="k" u2="&#x26;" k="10" />
    <hkern u1="k" u2="&#x21;" k="10" />
    <hkern u1="m" u2="&#x2122;" k="100" />
    <hkern u1="m" u2="&#xae;" k="29" />
    <hkern u1="m" u2="v" k="20" />
    <hkern u1="m" u2="\" k="113" />
    <hkern u1="m" u2="&#x2a;" k="31" />
    <hkern u1="n" u2="&#x2122;" k="100" />
    <hkern u1="n" u2="&#xae;" k="29" />
    <hkern u1="n" u2="v" k="20" />
    <hkern u1="n" u2="\" k="113" />
    <hkern u1="n" u2="&#x2a;" k="31" />
    <hkern u1="o" u2="&#x2122;" k="61" />
    <hkern u1="o" u2="&#xae;" k="20" />
    <hkern u1="o" u2="x" k="53" />
    <hkern u1="o" u2="v" k="23" />
    <hkern u1="o" u2="\" k="123" />
    <hkern u1="o" u2="&#x3f;" k="31" />
    <hkern u1="o" u2="&#x2f;" k="61" />
    <hkern u1="o" u2="&#x2a;" k="31" />
    <hkern u1="p" u2="&#x2122;" k="61" />
    <hkern u1="p" u2="&#xae;" k="20" />
    <hkern u1="p" u2="x" k="53" />
    <hkern u1="p" u2="v" k="23" />
    <hkern u1="p" u2="\" k="123" />
    <hkern u1="p" u2="&#x3f;" k="31" />
    <hkern u1="p" u2="&#x2f;" k="61" />
    <hkern u1="p" u2="&#x2a;" k="31" />
    <hkern u1="q" u2="&#x2122;" k="18" />
    <hkern u1="q" u2="\" k="20" />
    <hkern u1="r" u2="&#xbf;" k="20" />
    <hkern u1="r" u2="&#xae;" k="-31" />
    <hkern u1="r" u2="x" k="-20" />
    <hkern u1="r" u2="v" k="-41" />
    <hkern u1="r" u2="\" k="41" />
    <hkern u1="r" u2="&#x3f;" k="-20" />
    <hkern u1="r" u2="&#x2f;" k="113" />
    <hkern u1="r" u2="&#x2a;" k="-20" />
    <hkern u1="s" u2="&#x2122;" k="63" />
    <hkern u1="s" u2="&#xae;" k="10" />
    <hkern u1="s" u2="x" k="25" />
    <hkern u1="s" u2="v" k="31" />
    <hkern u1="s" u2="\" k="137" />
    <hkern u1="s" u2="&#x3f;" k="31" />
    <hkern u1="s" u2="&#x2f;" k="51" />
    <hkern u1="s" u2="&#x2a;" k="31" />
    <hkern u1="t" u2="&#x2122;" k="16" />
    <hkern u1="t" u2="&#xae;" k="-18" />
    <hkern u1="t" u2="x" k="-31" />
    <hkern u1="t" u2="v" k="-20" />
    <hkern u1="t" u2="\" k="72" />
    <hkern u1="t" u2="&#x2f;" k="20" />
    <hkern u1="u" u2="&#x2122;" k="18" />
    <hkern u1="u" u2="\" k="20" />
    <hkern u1="v" u2="&#xfb02;" k="-25" />
    <hkern u1="v" u2="&#xfb01;" k="-25" />
    <hkern u1="v" u2="&#x203a;" k="-10" />
    <hkern u1="v" u2="&#x2039;" k="20" />
    <hkern u1="v" u2="&#x2026;" k="164" />
    <hkern u1="v" u2="&#x201e;" k="113" />
    <hkern u1="v" u2="&#x201d;" k="-61" />
    <hkern u1="v" u2="&#x201c;" k="-61" />
    <hkern u1="v" u2="&#x201a;" k="113" />
    <hkern u1="v" u2="&#x2019;" k="-61" />
    <hkern u1="v" u2="&#x2018;" k="-61" />
    <hkern u1="v" u2="&#x2014;" k="20" />
    <hkern u1="v" u2="&#x2013;" k="20" />
    <hkern u1="v" u2="&#x1ef3;" k="-20" />
    <hkern u1="v" u2="&#x1e85;" k="-20" />
    <hkern u1="v" u2="&#x1e83;" k="-20" />
    <hkern u1="v" u2="&#x1e81;" k="-20" />
    <hkern u1="v" u2="&#x219;" k="10" />
    <hkern u1="v" u2="&#x1ff;" k="23" />
    <hkern u1="v" u2="&#x1fd;" k="61" />
    <hkern u1="v" u2="&#x1fb;" k="61" />
    <hkern u1="v" u2="&#x177;" k="-20" />
    <hkern u1="v" u2="&#x175;" k="-20" />
    <hkern u1="v" u2="&#x163;" k="-16" />
    <hkern u1="v" u2="&#x161;" k="10" />
    <hkern u1="v" u2="&#x15f;" k="10" />
    <hkern u1="v" u2="&#x15d;" k="10" />
    <hkern u1="v" u2="&#x15b;" k="10" />
    <hkern u1="v" u2="&#x151;" k="23" />
    <hkern u1="v" u2="&#x14f;" k="23" />
    <hkern u1="v" u2="&#x14d;" k="23" />
    <hkern u1="v" u2="&#x149;" k="-61" />
    <hkern u1="v" u2="&#x123;" k="23" />
    <hkern u1="v" u2="&#x121;" k="23" />
    <hkern u1="v" u2="&#x11f;" k="23" />
    <hkern u1="v" u2="&#x11d;" k="23" />
    <hkern u1="v" u2="&#x11b;" k="23" />
    <hkern u1="v" u2="&#x119;" k="23" />
    <hkern u1="v" u2="&#x117;" k="23" />
    <hkern u1="v" u2="&#x115;" k="23" />
    <hkern u1="v" u2="&#x113;" k="23" />
    <hkern u1="v" u2="&#x111;" k="23" />
    <hkern u1="v" u2="&#x10f;" k="23" />
    <hkern u1="v" u2="&#x10d;" k="23" />
    <hkern u1="v" u2="&#x10b;" k="23" />
    <hkern u1="v" u2="&#x109;" k="23" />
    <hkern u1="v" u2="&#x107;" k="23" />
    <hkern u1="v" u2="&#x105;" k="61" />
    <hkern u1="v" u2="&#x103;" k="61" />
    <hkern u1="v" u2="&#x101;" k="61" />
    <hkern u1="v" u2="&#xff;" k="-20" />
    <hkern u1="v" u2="&#xfd;" k="-20" />
    <hkern u1="v" u2="&#xf8;" k="23" />
    <hkern u1="v" u2="&#xf6;" k="23" />
    <hkern u1="v" u2="&#xf5;" k="23" />
    <hkern u1="v" u2="&#xf4;" k="23" />
    <hkern u1="v" u2="&#xf3;" k="23" />
    <hkern u1="v" u2="&#xf2;" k="23" />
    <hkern u1="v" u2="&#xeb;" k="23" />
    <hkern u1="v" u2="&#xea;" k="23" />
    <hkern u1="v" u2="&#xe9;" k="23" />
    <hkern u1="v" u2="&#xe8;" k="23" />
    <hkern u1="v" u2="&#xe7;" k="23" />
    <hkern u1="v" u2="&#xe6;" k="61" />
    <hkern u1="v" u2="&#xe5;" k="61" />
    <hkern u1="v" u2="&#xe4;" k="61" />
    <hkern u1="v" u2="&#xe3;" k="61" />
    <hkern u1="v" u2="&#xe2;" k="61" />
    <hkern u1="v" u2="&#xe1;" k="61" />
    <hkern u1="v" u2="&#xe0;" k="61" />
    <hkern u1="v" u2="&#xbb;" k="-10" />
    <hkern u1="v" u2="&#xba;" k="-39" />
    <hkern u1="v" u2="&#xab;" k="20" />
    <hkern u1="v" u2="&#xaa;" k="-39" />
    <hkern u1="v" u2="&#x7d;" k="31" />
    <hkern u1="v" u2="y" k="-20" />
    <hkern u1="v" u2="w" k="-20" />
    <hkern u1="v" u2="t" k="-16" />
    <hkern u1="v" u2="s" k="10" />
    <hkern u1="v" u2="q" k="23" />
    <hkern u1="v" u2="o" k="23" />
    <hkern u1="v" u2="g" k="23" />
    <hkern u1="v" u2="f" k="-25" />
    <hkern u1="v" u2="e" k="23" />
    <hkern u1="v" u2="d" k="23" />
    <hkern u1="v" u2="c" k="23" />
    <hkern u1="v" u2="a" k="61" />
    <hkern u1="v" u2="]" k="31" />
    <hkern u1="v" u2="&#x3b;" k="35" />
    <hkern u1="v" u2="&#x3a;" k="35" />
    <hkern u1="v" u2="&#x2e;" k="164" />
    <hkern u1="v" u2="&#x2d;" k="20" />
    <hkern u1="v" u2="&#x2c;" k="113" />
    <hkern u1="v" u2="&#x29;" k="31" />
    <hkern u1="v" u2="&#x2122;" k="-20" />
    <hkern u1="v" u2="&#xbf;" k="41" />
    <hkern u1="v" u2="&#xae;" k="-41" />
    <hkern u1="v" u2="v" k="-20" />
    <hkern u1="v" u2="\" k="10" />
    <hkern u1="v" u2="&#x3f;" k="-45" />
    <hkern u1="v" u2="&#x2f;" k="92" />
    <hkern u1="v" u2="&#x2a;" k="-31" />
    <hkern u1="v" u2="&#x26;" k="20" />
    <hkern u1="w" u2="&#xbf;" k="39" />
    <hkern u1="w" u2="&#xae;" k="-41" />
    <hkern u1="w" u2="v" k="-20" />
    <hkern u1="w" u2="\" k="10" />
    <hkern u1="w" u2="&#x3f;" k="-37" />
    <hkern u1="w" u2="&#x2f;" k="72" />
    <hkern u1="w" u2="&#x2a;" k="-10" />
    <hkern u1="w" u2="&#x26;" k="18" />
    <hkern u1="x" u2="&#x2117;" k="20" />
    <hkern u1="x" u2="&#x2039;" k="82" />
    <hkern u1="x" u2="&#x2026;" k="20" />
    <hkern u1="x" u2="&#x201c;" k="20" />
    <hkern u1="x" u2="&#x2018;" k="20" />
    <hkern u1="x" u2="&#x2014;" k="82" />
    <hkern u1="x" u2="&#x2013;" k="82" />
    <hkern u1="x" u2="&#x219;" k="18" />
    <hkern u1="x" u2="&#x1ff;" k="53" />
    <hkern u1="x" u2="&#x1fd;" k="45" />
    <hkern u1="x" u2="&#x1fb;" k="45" />
    <hkern u1="x" u2="&#x173;" k="10" />
    <hkern u1="x" u2="&#x171;" k="10" />
    <hkern u1="x" u2="&#x16f;" k="10" />
    <hkern u1="x" u2="&#x16d;" k="10" />
    <hkern u1="x" u2="&#x16b;" k="10" />
    <hkern u1="x" u2="&#x169;" k="10" />
    <hkern u1="x" u2="&#x163;" k="10" />
    <hkern u1="x" u2="&#x161;" k="18" />
    <hkern u1="x" u2="&#x15f;" k="18" />
    <hkern u1="x" u2="&#x15d;" k="18" />
    <hkern u1="x" u2="&#x15b;" k="18" />
    <hkern u1="x" u2="&#x151;" k="53" />
    <hkern u1="x" u2="&#x14f;" k="53" />
    <hkern u1="x" u2="&#x14d;" k="53" />
    <hkern u1="x" u2="&#x123;" k="53" />
    <hkern u1="x" u2="&#x121;" k="53" />
    <hkern u1="x" u2="&#x11f;" k="53" />
    <hkern u1="x" u2="&#x11d;" k="53" />
    <hkern u1="x" u2="&#x11b;" k="53" />
    <hkern u1="x" u2="&#x119;" k="53" />
    <hkern u1="x" u2="&#x117;" k="53" />
    <hkern u1="x" u2="&#x115;" k="53" />
    <hkern u1="x" u2="&#x113;" k="53" />
    <hkern u1="x" u2="&#x111;" k="53" />
    <hkern u1="x" u2="&#x10f;" k="53" />
    <hkern u1="x" u2="&#x10d;" k="53" />
    <hkern u1="x" u2="&#x10b;" k="53" />
    <hkern u1="x" u2="&#x109;" k="53" />
    <hkern u1="x" u2="&#x107;" k="53" />
    <hkern u1="x" u2="&#x105;" k="45" />
    <hkern u1="x" u2="&#x103;" k="45" />
    <hkern u1="x" u2="&#x101;" k="45" />
    <hkern u1="x" u2="&#xfc;" k="10" />
    <hkern u1="x" u2="&#xfb;" k="10" />
    <hkern u1="x" u2="&#xfa;" k="10" />
    <hkern u1="x" u2="&#xf9;" k="10" />
    <hkern u1="x" u2="&#xf8;" k="53" />
    <hkern u1="x" u2="&#xf6;" k="53" />
    <hkern u1="x" u2="&#xf5;" k="53" />
    <hkern u1="x" u2="&#xf4;" k="53" />
    <hkern u1="x" u2="&#xf3;" k="53" />
    <hkern u1="x" u2="&#xf2;" k="53" />
    <hkern u1="x" u2="&#xeb;" k="53" />
    <hkern u1="x" u2="&#xea;" k="53" />
    <hkern u1="x" u2="&#xe9;" k="53" />
    <hkern u1="x" u2="&#xe8;" k="53" />
    <hkern u1="x" u2="&#xe7;" k="53" />
    <hkern u1="x" u2="&#xe6;" k="45" />
    <hkern u1="x" u2="&#xe5;" k="45" />
    <hkern u1="x" u2="&#xe4;" k="45" />
    <hkern u1="x" u2="&#xe3;" k="45" />
    <hkern u1="x" u2="&#xe2;" k="45" />
    <hkern u1="x" u2="&#xe1;" k="45" />
    <hkern u1="x" u2="&#xe0;" k="45" />
    <hkern u1="x" u2="&#xab;" k="82" />
    <hkern u1="x" u2="&#xa9;" k="20" />
    <hkern u1="x" u2="&#x7d;" k="10" />
    <hkern u1="x" u2="u" k="10" />
    <hkern u1="x" u2="t" k="10" />
    <hkern u1="x" u2="s" k="18" />
    <hkern u1="x" u2="q" k="53" />
    <hkern u1="x" u2="o" k="53" />
    <hkern u1="x" u2="g" k="53" />
    <hkern u1="x" u2="e" k="53" />
    <hkern u1="x" u2="d" k="53" />
    <hkern u1="x" u2="c" k="53" />
    <hkern u1="x" u2="a" k="45" />
    <hkern u1="x" u2="]" k="10" />
    <hkern u1="x" u2="&#x3b;" k="10" />
    <hkern u1="x" u2="&#x3a;" k="10" />
    <hkern u1="x" u2="&#x2e;" k="20" />
    <hkern u1="x" u2="&#x2d;" k="82" />
    <hkern u1="x" u2="&#x29;" k="10" />
    <hkern u1="x" u2="&#x2122;" k="2" />
    <hkern u1="x" u2="&#xbf;" k="29" />
    <hkern u1="x" u2="x" k="-10" />
    <hkern u1="x" u2="v" k="-2" />
    <hkern u1="x" u2="\" k="51" />
    <hkern u1="x" u2="&#x40;" k="16" />
    <hkern u1="x" u2="&#x3f;" k="-16" />
    <hkern u1="x" u2="&#x26;" k="39" />
    <hkern u1="y" u2="&#xbf;" k="27" />
    <hkern u1="y" u2="&#xae;" k="-39" />
    <hkern u1="y" u2="x" k="-10" />
    <hkern u1="y" u2="v" k="-20" />
    <hkern u1="y" u2="\" k="20" />
    <hkern u1="y" u2="&#x40;" k="14" />
    <hkern u1="y" u2="&#x3f;" k="-51" />
    <hkern u1="y" u2="&#x2f;" k="61" />
    <hkern u1="y" u2="&#x2a;" k="-20" />
    <hkern u1="z" u2="\" k="51" />
    <hkern u1="&#x7b;" u2="&#x20ac;" k="86" />
    <hkern u1="&#x7b;" u2="&#xbf;" k="39" />
    <hkern u1="&#x7b;" u2="&#xae;" k="20" />
    <hkern u1="&#x7b;" u2="&#xa5;" k="20" />
    <hkern u1="&#x7b;" u2="&#xa2;" k="51" />
    <hkern u1="&#x7b;" u2="x" k="10" />
    <hkern u1="&#x7b;" u2="v" k="31" />
    <hkern u1="&#x7b;" u2="j" k="-41" />
    <hkern u1="&#x7b;" u2="i" k="10" />
    <hkern u1="&#x7b;" u2="V" k="-10" />
    <hkern u1="&#x7b;" u2="&#x40;" k="41" />
    <hkern u1="&#x7b;" u2="&#x3f;" k="20" />
    <hkern u1="&#x7b;" u2="&#x39;" k="51" />
    <hkern u1="&#x7b;" u2="&#x38;" k="51" />
    <hkern u1="&#x7b;" u2="&#x37;" k="10" />
    <hkern u1="&#x7b;" u2="&#x36;" k="61" />
    <hkern u1="&#x7b;" u2="&#x35;" k="41" />
    <hkern u1="&#x7b;" u2="&#x34;" k="51" />
    <hkern u1="&#x7b;" u2="&#x33;" k="10" />
    <hkern u1="&#x7b;" u2="&#x31;" k="10" />
    <hkern u1="&#x7b;" u2="&#x30;" k="31" />
    <hkern u1="&#x7b;" u2="&#x2a;" k="72" />
    <hkern u1="&#x7b;" u2="&#x26;" k="51" />
    <hkern u1="&#x7b;" u2="&#x24;" k="20" />
    <hkern u1="&#x7b;" u2="&#x23;" k="51" />
    <hkern u1="&#x7b;" u2="&#x21;" k="10" />
    <hkern u1="&#x7d;" u2="\" k="72" />
    <hkern u1="&#x7d;" u2="&#x2f;" k="41" />
    <hkern u1="&#x7d;" u2="&#x2a;" k="10" />
    <hkern u1="&#xa1;" u2="&#x201d;" k="31" />
    <hkern u1="&#xa1;" u2="&#x201c;" k="31" />
    <hkern u1="&#xa1;" u2="&#x2019;" k="31" />
    <hkern u1="&#xa1;" u2="&#x2018;" k="31" />
    <hkern u1="&#xa1;" u2="&#x1ef3;" k="16" />
    <hkern u1="&#xa1;" u2="&#x1ef2;" k="41" />
    <hkern u1="&#xa1;" u2="&#x1e84;" k="20" />
    <hkern u1="&#xa1;" u2="&#x1e82;" k="20" />
    <hkern u1="&#xa1;" u2="&#x1e80;" k="20" />
    <hkern u1="&#xa1;" u2="&#x178;" k="41" />
    <hkern u1="&#xa1;" u2="&#x177;" k="16" />
    <hkern u1="&#xa1;" u2="&#x176;" k="41" />
    <hkern u1="&#xa1;" u2="&#x174;" k="20" />
    <hkern u1="&#xa1;" u2="&#x149;" k="31" />
    <hkern u1="&#xa1;" u2="&#xff;" k="16" />
    <hkern u1="&#xa1;" u2="&#xfd;" k="16" />
    <hkern u1="&#xa1;" u2="&#xdd;" k="41" />
    <hkern u1="&#xa1;" u2="y" k="16" />
    <hkern u1="&#xa1;" u2="Y" k="41" />
    <hkern u1="&#xa1;" u2="W" k="20" />
    <hkern u1="&#xa1;" u2="\" k="35" />
    <hkern u1="&#xa1;" u2="V" k="37" />
    <hkern u1="&#xa1;" u2="&#x39;" k="8" />
    <hkern u1="&#xa1;" u2="&#x2a;" k="39" />
    <hkern u1="&#xa2;" u2="&#x203a;" k="8" />
    <hkern u1="&#xa2;" u2="&#x2026;" k="41" />
    <hkern u1="&#xa2;" u2="&#x201e;" k="51" />
    <hkern u1="&#xa2;" u2="&#x201a;" k="51" />
    <hkern u1="&#xa2;" u2="&#xbb;" k="8" />
    <hkern u1="&#xa2;" u2="&#x7d;" k="41" />
    <hkern u1="&#xa2;" u2="]" k="41" />
    <hkern u1="&#xa2;" u2="&#x3b;" k="10" />
    <hkern u1="&#xa2;" u2="&#x3a;" k="10" />
    <hkern u1="&#xa2;" u2="&#x2e;" k="41" />
    <hkern u1="&#xa2;" u2="&#x2c;" k="51" />
    <hkern u1="&#xa2;" u2="&#x29;" k="41" />
    <hkern u1="&#xa2;" u2="&#x2122;" k="39" />
    <hkern u1="&#xa2;" u2="&#xae;" k="10" />
    <hkern u1="&#xa2;" u2="\" k="102" />
    <hkern u1="&#xa2;" u2="&#x3f;" k="20" />
    <hkern u1="&#xa2;" u2="&#x37;" k="20" />
    <hkern u1="&#xa2;" u2="&#x2f;" k="82" />
    <hkern u1="&#xa3;" u2="&#x2039;" k="39" />
    <hkern u1="&#xa3;" u2="&#x2026;" k="41" />
    <hkern u1="&#xa3;" u2="&#x201e;" k="31" />
    <hkern u1="&#xa3;" u2="&#x201a;" k="31" />
    <hkern u1="&#xa3;" u2="&#x2014;" k="41" />
    <hkern u1="&#xa3;" u2="&#x2013;" k="41" />
    <hkern u1="&#xa3;" u2="&#xab;" k="39" />
    <hkern u1="&#xa3;" u2="&#x7d;" k="20" />
    <hkern u1="&#xa3;" u2="]" k="20" />
    <hkern u1="&#xa3;" u2="&#x3b;" k="10" />
    <hkern u1="&#xa3;" u2="&#x3a;" k="10" />
    <hkern u1="&#xa3;" u2="&#x2e;" k="41" />
    <hkern u1="&#xa3;" u2="&#x2d;" k="41" />
    <hkern u1="&#xa3;" u2="&#x2c;" k="31" />
    <hkern u1="&#xa3;" u2="&#x29;" k="20" />
    <hkern u1="&#xa3;" u2="&#xbf;" k="25" />
    <hkern u1="&#xa3;" u2="&#xae;" k="-16" />
    <hkern u1="&#xa3;" u2="\" k="31" />
    <hkern u1="&#xa3;" u2="&#x40;" k="20" />
    <hkern u1="&#xa3;" u2="&#x36;" k="20" />
    <hkern u1="&#xa3;" u2="&#x35;" k="10" />
    <hkern u1="&#xa3;" u2="&#x34;" k="31" />
    <hkern u1="&#xa3;" u2="&#x31;" k="-20" />
    <hkern u1="&#xa3;" u2="&#x2f;" k="82" />
    <hkern u1="&#xa3;" u2="&#x26;" k="20" />
    <hkern u1="&#xa5;" u2="&#x2117;" k="20" />
    <hkern u1="&#xa5;" u2="&#x203a;" k="41" />
    <hkern u1="&#xa5;" u2="&#x2039;" k="29" />
    <hkern u1="&#xa5;" u2="&#x2026;" k="51" />
    <hkern u1="&#xa5;" u2="&#x201e;" k="61" />
    <hkern u1="&#xa5;" u2="&#x201d;" k="31" />
    <hkern u1="&#xa5;" u2="&#x201c;" k="31" />
    <hkern u1="&#xa5;" u2="&#x201a;" k="61" />
    <hkern u1="&#xa5;" u2="&#x2019;" k="31" />
    <hkern u1="&#xa5;" u2="&#x2018;" k="31" />
    <hkern u1="&#xa5;" u2="&#x2014;" k="20" />
    <hkern u1="&#xa5;" u2="&#x2013;" k="20" />
    <hkern u1="&#xa5;" u2="&#x149;" k="31" />
    <hkern u1="&#xa5;" u2="&#xbb;" k="41" />
    <hkern u1="&#xa5;" u2="&#xba;" k="31" />
    <hkern u1="&#xa5;" u2="&#xab;" k="29" />
    <hkern u1="&#xa5;" u2="&#xaa;" k="31" />
    <hkern u1="&#xa5;" u2="&#xa9;" k="20" />
    <hkern u1="&#xa5;" u2="&#x7d;" k="20" />
    <hkern u1="&#xa5;" u2="]" k="20" />
    <hkern u1="&#xa5;" u2="&#x3b;" k="51" />
    <hkern u1="&#xa5;" u2="&#x3a;" k="51" />
    <hkern u1="&#xa5;" u2="&#x2e;" k="51" />
    <hkern u1="&#xa5;" u2="&#x2d;" k="20" />
    <hkern u1="&#xa5;" u2="&#x2c;" k="61" />
    <hkern u1="&#xa5;" u2="&#x29;" k="20" />
    <hkern u1="&#xa5;" u2="&#xbf;" k="35" />
    <hkern u1="&#xa5;" u2="&#xae;" k="31" />
    <hkern u1="&#xa5;" u2="\" k="10" />
    <hkern u1="&#xa5;" u2="&#x40;" k="20" />
    <hkern u1="&#xa5;" u2="&#x3f;" k="23" />
    <hkern u1="&#xa5;" u2="&#x38;" k="10" />
    <hkern u1="&#xa5;" u2="&#x32;" k="10" />
    <hkern u1="&#xa5;" u2="&#x2f;" k="123" />
    <hkern u1="&#xa5;" u2="&#x2a;" k="20" />
    <hkern u1="&#xa5;" u2="&#x26;" k="31" />
    <hkern u1="&#xa9;" u2="&#xa5;" k="20" />
    <hkern u1="&#xa9;" u2="x" k="20" />
    <hkern u1="&#xa9;" u2="\" k="92" />
    <hkern u1="&#xa9;" u2="X" k="72" />
    <hkern u1="&#xa9;" u2="V" k="72" />
    <hkern u1="&#xa9;" u2="&#x37;" k="20" />
    <hkern u1="&#xa9;" u2="&#x2f;" k="96" />
    <hkern u1="&#xaa;" u2="v" k="-39" />
    <hkern u1="&#xaa;" u2="\" k="45" />
    <hkern u1="&#xaa;" u2="X" k="29" />
    <hkern u1="&#xaa;" u2="&#x34;" k="4" />
    <hkern u1="&#xaa;" u2="&#x2f;" k="92" />
    <hkern u1="&#xab;" u2="&#x20ac;" k="20" />
    <hkern u1="&#xab;" u2="&#xa5;" k="41" />
    <hkern u1="&#xab;" u2="&#xa2;" k="8" />
    <hkern u1="&#xab;" u2="v" k="-10" />
    <hkern u1="&#xab;" u2="j" k="10" />
    <hkern u1="&#xab;" u2="\" k="102" />
    <hkern u1="&#xab;" u2="X" k="37" />
    <hkern u1="&#xab;" u2="V" k="96" />
    <hkern u1="&#xab;" u2="&#x39;" k="8" />
    <hkern u1="&#xab;" u2="&#x38;" k="27" />
    <hkern u1="&#xab;" u2="&#x37;" k="18" />
    <hkern u1="&#xab;" u2="&#x36;" k="27" />
    <hkern u1="&#xab;" u2="&#x35;" k="35" />
    <hkern u1="&#xab;" u2="&#x34;" k="18" />
    <hkern u1="&#xab;" u2="&#x33;" k="10" />
    <hkern u1="&#xab;" u2="&#x2f;" k="41" />
    <hkern u1="&#xab;" u2="&#x2a;" k="18" />
    <hkern u1="&#xab;" u2="&#x26;" k="16" />
    <hkern u1="&#xab;" u2="&#x23;" k="18" />
    <hkern u1="&#xae;" g2="Jcircumflex.salt" k="92" />
    <hkern u1="&#xae;" g2="J.salt" k="92" />
    <hkern u1="&#xae;" u2="&#xfb02;" k="-29" />
    <hkern u1="&#xae;" u2="&#xfb01;" k="-29" />
    <hkern u1="&#xae;" u2="&#x2026;" k="106" />
    <hkern u1="&#xae;" u2="&#x1ef3;" k="-39" />
    <hkern u1="&#xae;" u2="&#x1ef2;" k="39" />
    <hkern u1="&#xae;" u2="&#x1e85;" k="-41" />
    <hkern u1="&#xae;" u2="&#x1e83;" k="-41" />
    <hkern u1="&#xae;" u2="&#x1e81;" k="-41" />
    <hkern u1="&#xae;" u2="&#x219;" k="10" />
    <hkern u1="&#xae;" u2="&#x1ff;" k="20" />
    <hkern u1="&#xae;" u2="&#x1fd;" k="61" />
    <hkern u1="&#xae;" u2="&#x1fb;" k="61" />
    <hkern u1="&#xae;" u2="&#x1fa;" k="195" />
    <hkern u1="&#xae;" u2="&#x17d;" k="20" />
    <hkern u1="&#xae;" u2="&#x17b;" k="20" />
    <hkern u1="&#xae;" u2="&#x179;" k="20" />
    <hkern u1="&#xae;" u2="&#x178;" k="39" />
    <hkern u1="&#xae;" u2="&#x177;" k="-39" />
    <hkern u1="&#xae;" u2="&#x176;" k="39" />
    <hkern u1="&#xae;" u2="&#x175;" k="-41" />
    <hkern u1="&#xae;" u2="&#x164;" k="-10" />
    <hkern u1="&#xae;" u2="&#x163;" k="-18" />
    <hkern u1="&#xae;" u2="&#x162;" k="-10" />
    <hkern u1="&#xae;" u2="&#x161;" k="10" />
    <hkern u1="&#xae;" u2="&#x15f;" k="10" />
    <hkern u1="&#xae;" u2="&#x15d;" k="10" />
    <hkern u1="&#xae;" u2="&#x15b;" k="10" />
    <hkern u1="&#xae;" u2="&#x151;" k="20" />
    <hkern u1="&#xae;" u2="&#x14f;" k="20" />
    <hkern u1="&#xae;" u2="&#x14d;" k="20" />
    <hkern u1="&#xae;" u2="&#x134;" k="328" />
    <hkern u1="&#xae;" u2="&#x123;" k="20" />
    <hkern u1="&#xae;" u2="&#x121;" k="20" />
    <hkern u1="&#xae;" u2="&#x11f;" k="20" />
    <hkern u1="&#xae;" u2="&#x11d;" k="20" />
    <hkern u1="&#xae;" u2="&#x11b;" k="20" />
    <hkern u1="&#xae;" u2="&#x119;" k="20" />
    <hkern u1="&#xae;" u2="&#x117;" k="20" />
    <hkern u1="&#xae;" u2="&#x115;" k="20" />
    <hkern u1="&#xae;" u2="&#x113;" k="20" />
    <hkern u1="&#xae;" u2="&#x111;" k="20" />
    <hkern u1="&#xae;" u2="&#x10f;" k="20" />
    <hkern u1="&#xae;" u2="&#x10d;" k="20" />
    <hkern u1="&#xae;" u2="&#x10b;" k="20" />
    <hkern u1="&#xae;" u2="&#x109;" k="20" />
    <hkern u1="&#xae;" u2="&#x107;" k="20" />
    <hkern u1="&#xae;" u2="&#x105;" k="61" />
    <hkern u1="&#xae;" u2="&#x104;" k="195" />
    <hkern u1="&#xae;" u2="&#x103;" k="61" />
    <hkern u1="&#xae;" u2="&#x102;" k="195" />
    <hkern u1="&#xae;" u2="&#x101;" k="61" />
    <hkern u1="&#xae;" u2="&#x100;" k="195" />
    <hkern u1="&#xae;" u2="&#xff;" k="-39" />
    <hkern u1="&#xae;" u2="&#xfd;" k="-39" />
    <hkern u1="&#xae;" u2="&#xf8;" k="20" />
    <hkern u1="&#xae;" u2="&#xf6;" k="20" />
    <hkern u1="&#xae;" u2="&#xf5;" k="20" />
    <hkern u1="&#xae;" u2="&#xf4;" k="20" />
    <hkern u1="&#xae;" u2="&#xf3;" k="20" />
    <hkern u1="&#xae;" u2="&#xf2;" k="20" />
    <hkern u1="&#xae;" u2="&#xeb;" k="20" />
    <hkern u1="&#xae;" u2="&#xea;" k="20" />
    <hkern u1="&#xae;" u2="&#xe9;" k="20" />
    <hkern u1="&#xae;" u2="&#xe8;" k="20" />
    <hkern u1="&#xae;" u2="&#xe7;" k="20" />
    <hkern u1="&#xae;" u2="&#xe6;" k="61" />
    <hkern u1="&#xae;" u2="&#xe5;" k="61" />
    <hkern u1="&#xae;" u2="&#xe4;" k="61" />
    <hkern u1="&#xae;" u2="&#xe3;" k="61" />
    <hkern u1="&#xae;" u2="&#xe2;" k="61" />
    <hkern u1="&#xae;" u2="&#xe1;" k="61" />
    <hkern u1="&#xae;" u2="&#xe0;" k="61" />
    <hkern u1="&#xae;" u2="&#xdd;" k="39" />
    <hkern u1="&#xae;" u2="&#xc5;" k="195" />
    <hkern u1="&#xae;" u2="&#xc4;" k="195" />
    <hkern u1="&#xae;" u2="&#xc3;" k="195" />
    <hkern u1="&#xae;" u2="&#xc2;" k="195" />
    <hkern u1="&#xae;" u2="&#xc1;" k="195" />
    <hkern u1="&#xae;" u2="&#xc0;" k="195" />
    <hkern u1="&#xae;" u2="&#x7d;" k="20" />
    <hkern u1="&#xae;" u2="y" k="-39" />
    <hkern u1="&#xae;" u2="w" k="-41" />
    <hkern u1="&#xae;" u2="t" k="-18" />
    <hkern u1="&#xae;" u2="s" k="10" />
    <hkern u1="&#xae;" u2="q" k="20" />
    <hkern u1="&#xae;" u2="o" k="20" />
    <hkern u1="&#xae;" u2="g" k="20" />
    <hkern u1="&#xae;" u2="f" k="-29" />
    <hkern u1="&#xae;" u2="e" k="20" />
    <hkern u1="&#xae;" u2="d" k="20" />
    <hkern u1="&#xae;" u2="c" k="20" />
    <hkern u1="&#xae;" u2="a" k="61" />
    <hkern u1="&#xae;" u2="]" k="20" />
    <hkern u1="&#xae;" u2="Z" k="20" />
    <hkern u1="&#xae;" u2="Y" k="39" />
    <hkern u1="&#xae;" u2="T" k="-10" />
    <hkern u1="&#xae;" u2="J" k="328" />
    <hkern u1="&#xae;" u2="A" k="195" />
    <hkern u1="&#xae;" u2="&#x2e;" k="106" />
    <hkern u1="&#xae;" u2="&#x29;" k="20" />
    <hkern u1="&#xae;" u2="&#xa5;" k="31" />
    <hkern u1="&#xae;" u2="&#xa3;" k="41" />
    <hkern u1="&#xae;" u2="v" k="-41" />
    <hkern u1="&#xae;" u2="\" k="49" />
    <hkern u1="&#xae;" u2="X" k="82" />
    <hkern u1="&#xae;" u2="V" k="20" />
    <hkern u1="&#xae;" u2="&#x40;" k="20" />
    <hkern u1="&#xae;" u2="&#x3f;" k="-16" />
    <hkern u1="&#xae;" u2="&#x38;" k="23" />
    <hkern u1="&#xae;" u2="&#x37;" k="20" />
    <hkern u1="&#xae;" u2="&#x36;" k="49" />
    <hkern u1="&#xae;" u2="&#x35;" k="43" />
    <hkern u1="&#xae;" u2="&#x34;" k="53" />
    <hkern u1="&#xae;" u2="&#x33;" k="20" />
    <hkern u1="&#xae;" u2="&#x31;" k="-10" />
    <hkern u1="&#xae;" u2="&#x2f;" k="240" />
    <hkern u1="&#xae;" u2="&#x24;" k="10" />
    <hkern u1="&#xba;" u2="v" k="-39" />
    <hkern u1="&#xba;" u2="\" k="45" />
    <hkern u1="&#xba;" u2="X" k="29" />
    <hkern u1="&#xba;" u2="&#x34;" k="4" />
    <hkern u1="&#xba;" u2="&#x2f;" k="92" />
    <hkern u1="&#xbb;" u2="&#xa5;" k="29" />
    <hkern u1="&#xbb;" u2="x" k="82" />
    <hkern u1="&#xbb;" u2="v" k="20" />
    <hkern u1="&#xbb;" u2="\" k="119" />
    <hkern u1="&#xbb;" u2="X" k="106" />
    <hkern u1="&#xbb;" u2="V" k="113" />
    <hkern u1="&#xbb;" u2="&#x3f;" k="78" />
    <hkern u1="&#xbb;" u2="&#x39;" k="41" />
    <hkern u1="&#xbb;" u2="&#x37;" k="72" />
    <hkern u1="&#xbb;" u2="&#x36;" k="-10" />
    <hkern u1="&#xbb;" u2="&#x34;" k="-31" />
    <hkern u1="&#xbb;" u2="&#x31;" k="20" />
    <hkern u1="&#xbb;" u2="&#x2f;" k="41" />
    <hkern u1="&#xbb;" u2="&#x2a;" k="41" />
    <hkern u1="&#xbb;" u2="&#x23;" k="-37" />
    <hkern u1="&#xbb;" u2="&#x21;" k="16" />
    <hkern u1="&#xbf;" u2="&#xfb02;" k="10" />
    <hkern u1="&#xbf;" u2="&#xfb01;" k="10" />
    <hkern u1="&#xbf;" u2="&#x2030;" k="129" />
    <hkern u1="&#xbf;" u2="&#x201d;" k="184" />
    <hkern u1="&#xbf;" u2="&#x201c;" k="205" />
    <hkern u1="&#xbf;" u2="&#x2019;" k="184" />
    <hkern u1="&#xbf;" u2="&#x2018;" k="205" />
    <hkern u1="&#xbf;" u2="&#x2014;" k="39" />
    <hkern u1="&#xbf;" u2="&#x2013;" k="39" />
    <hkern u1="&#xbf;" u2="&#x1ef3;" k="78" />
    <hkern u1="&#xbf;" u2="&#x1ef2;" k="180" />
    <hkern u1="&#xbf;" u2="&#x1e85;" k="39" />
    <hkern u1="&#xbf;" u2="&#x1e84;" k="96" />
    <hkern u1="&#xbf;" u2="&#x1e83;" k="39" />
    <hkern u1="&#xbf;" u2="&#x1e82;" k="96" />
    <hkern u1="&#xbf;" u2="&#x1e81;" k="39" />
    <hkern u1="&#xbf;" u2="&#x1e80;" k="96" />
    <hkern u1="&#xbf;" u2="&#x1fa;" k="18" />
    <hkern u1="&#xbf;" u2="&#x178;" k="180" />
    <hkern u1="&#xbf;" u2="&#x177;" k="78" />
    <hkern u1="&#xbf;" u2="&#x176;" k="180" />
    <hkern u1="&#xbf;" u2="&#x175;" k="39" />
    <hkern u1="&#xbf;" u2="&#x174;" k="96" />
    <hkern u1="&#xbf;" u2="&#x164;" k="147" />
    <hkern u1="&#xbf;" u2="&#x163;" k="20" />
    <hkern u1="&#xbf;" u2="&#x162;" k="147" />
    <hkern u1="&#xbf;" u2="&#x149;" k="184" />
    <hkern u1="&#xbf;" u2="&#x104;" k="18" />
    <hkern u1="&#xbf;" u2="&#x102;" k="18" />
    <hkern u1="&#xbf;" u2="&#x100;" k="18" />
    <hkern u1="&#xbf;" u2="&#xff;" k="78" />
    <hkern u1="&#xbf;" u2="&#xfd;" k="78" />
    <hkern u1="&#xbf;" u2="&#xdd;" k="180" />
    <hkern u1="&#xbf;" u2="&#xc5;" k="18" />
    <hkern u1="&#xbf;" u2="&#xc4;" k="18" />
    <hkern u1="&#xbf;" u2="&#xc3;" k="18" />
    <hkern u1="&#xbf;" u2="&#xc2;" k="18" />
    <hkern u1="&#xbf;" u2="&#xc1;" k="18" />
    <hkern u1="&#xbf;" u2="&#xc0;" k="18" />
    <hkern u1="&#xbf;" u2="&#x7d;" k="29" />
    <hkern u1="&#xbf;" u2="y" k="78" />
    <hkern u1="&#xbf;" u2="w" k="39" />
    <hkern u1="&#xbf;" u2="t" k="20" />
    <hkern u1="&#xbf;" u2="f" k="10" />
    <hkern u1="&#xbf;" u2="]" k="29" />
    <hkern u1="&#xbf;" u2="Y" k="180" />
    <hkern u1="&#xbf;" u2="W" k="96" />
    <hkern u1="&#xbf;" u2="T" k="147" />
    <hkern u1="&#xbf;" u2="A" k="18" />
    <hkern u1="&#xbf;" u2="&#x2d;" k="39" />
    <hkern u1="&#xbf;" u2="&#x29;" k="29" />
    <hkern u1="&#xbf;" u2="&#x25;" k="129" />
    <hkern u1="&#xbf;" u2="&#xbf;" k="-16" />
    <hkern u1="&#xbf;" u2="&#xa5;" k="20" />
    <hkern u1="&#xbf;" u2="x" k="16" />
    <hkern u1="&#xbf;" u2="v" k="57" />
    <hkern u1="&#xbf;" u2="\" k="160" />
    <hkern u1="&#xbf;" u2="X" k="25" />
    <hkern u1="&#xbf;" u2="V" k="127" />
    <hkern u1="&#xbf;" u2="&#x39;" k="61" />
    <hkern u1="&#xbf;" u2="&#x37;" k="23" />
    <hkern u1="&#xbf;" u2="&#x31;" k="70" />
    <hkern u1="&#xbf;" u2="&#x2f;" k="18" />
    <hkern u1="&#xbf;" u2="&#x2a;" k="164" />
    <hkern u1="&#xc0;" u2="&#x2122;" k="233" />
    <hkern u1="&#xc0;" u2="&#xbf;" k="18" />
    <hkern u1="&#xc0;" u2="&#xae;" k="195" />
    <hkern u1="&#xc0;" u2="x" k="-16" />
    <hkern u1="&#xc0;" u2="v" k="113" />
    <hkern u1="&#xc0;" u2="\" k="154" />
    <hkern u1="&#xc0;" u2="X" k="-10" />
    <hkern u1="&#xc0;" u2="V" k="211" />
    <hkern u1="&#xc0;" u2="&#x40;" k="10" />
    <hkern u1="&#xc0;" u2="&#x3f;" k="143" />
    <hkern u1="&#xc0;" u2="&#x2a;" k="205" />
    <hkern u1="&#xc1;" u2="&#x2122;" k="233" />
    <hkern u1="&#xc1;" u2="&#xbf;" k="18" />
    <hkern u1="&#xc1;" u2="&#xae;" k="195" />
    <hkern u1="&#xc1;" u2="x" k="-16" />
    <hkern u1="&#xc1;" u2="v" k="113" />
    <hkern u1="&#xc1;" u2="\" k="154" />
    <hkern u1="&#xc1;" u2="X" k="-10" />
    <hkern u1="&#xc1;" u2="V" k="211" />
    <hkern u1="&#xc1;" u2="&#x40;" k="10" />
    <hkern u1="&#xc1;" u2="&#x3f;" k="143" />
    <hkern u1="&#xc1;" u2="&#x2a;" k="205" />
    <hkern u1="&#xc2;" u2="&#x2122;" k="233" />
    <hkern u1="&#xc2;" u2="&#xbf;" k="18" />
    <hkern u1="&#xc2;" u2="&#xae;" k="195" />
    <hkern u1="&#xc2;" u2="x" k="-16" />
    <hkern u1="&#xc2;" u2="v" k="113" />
    <hkern u1="&#xc2;" u2="\" k="154" />
    <hkern u1="&#xc2;" u2="X" k="-10" />
    <hkern u1="&#xc2;" u2="V" k="211" />
    <hkern u1="&#xc2;" u2="&#x40;" k="10" />
    <hkern u1="&#xc2;" u2="&#x3f;" k="143" />
    <hkern u1="&#xc2;" u2="&#x2a;" k="205" />
    <hkern u1="&#xc3;" u2="&#x2122;" k="233" />
    <hkern u1="&#xc3;" u2="&#xbf;" k="18" />
    <hkern u1="&#xc3;" u2="&#xae;" k="195" />
    <hkern u1="&#xc3;" u2="x" k="-16" />
    <hkern u1="&#xc3;" u2="v" k="113" />
    <hkern u1="&#xc3;" u2="\" k="154" />
    <hkern u1="&#xc3;" u2="X" k="-10" />
    <hkern u1="&#xc3;" u2="V" k="211" />
    <hkern u1="&#xc3;" u2="&#x40;" k="10" />
    <hkern u1="&#xc3;" u2="&#x3f;" k="143" />
    <hkern u1="&#xc3;" u2="&#x2a;" k="205" />
    <hkern u1="&#xc4;" u2="&#x2122;" k="233" />
    <hkern u1="&#xc4;" u2="&#xbf;" k="18" />
    <hkern u1="&#xc4;" u2="&#xae;" k="195" />
    <hkern u1="&#xc4;" u2="x" k="-16" />
    <hkern u1="&#xc4;" u2="v" k="113" />
    <hkern u1="&#xc4;" u2="\" k="154" />
    <hkern u1="&#xc4;" u2="X" k="-10" />
    <hkern u1="&#xc4;" u2="V" k="211" />
    <hkern u1="&#xc4;" u2="&#x40;" k="10" />
    <hkern u1="&#xc4;" u2="&#x3f;" k="143" />
    <hkern u1="&#xc4;" u2="&#x2a;" k="205" />
    <hkern u1="&#xc5;" u2="&#x2122;" k="233" />
    <hkern u1="&#xc5;" u2="&#xbf;" k="18" />
    <hkern u1="&#xc5;" u2="&#xae;" k="195" />
    <hkern u1="&#xc5;" u2="x" k="-16" />
    <hkern u1="&#xc5;" u2="v" k="113" />
    <hkern u1="&#xc5;" u2="\" k="154" />
    <hkern u1="&#xc5;" u2="X" k="-10" />
    <hkern u1="&#xc5;" u2="V" k="211" />
    <hkern u1="&#xc5;" u2="&#x40;" k="10" />
    <hkern u1="&#xc5;" u2="&#x3f;" k="143" />
    <hkern u1="&#xc5;" u2="&#x2a;" k="205" />
    <hkern u1="&#xc6;" u2="&#x2122;" k="-16" />
    <hkern u1="&#xc6;" u2="X" k="-10" />
    <hkern u1="&#xc6;" u2="&#x2f;" k="-10" />
    <hkern u1="&#xc7;" u2="x" k="10" />
    <hkern u1="&#xc7;" u2="\" k="51" />
    <hkern u1="&#xc7;" u2="X" k="61" />
    <hkern u1="&#xc7;" u2="V" k="41" />
    <hkern u1="&#xc7;" u2="&#x2f;" k="72" />
    <hkern u1="&#xc8;" u2="&#x2122;" k="-16" />
    <hkern u1="&#xc8;" u2="X" k="-10" />
    <hkern u1="&#xc8;" u2="&#x2f;" k="-10" />
    <hkern u1="&#xc9;" u2="&#x2122;" k="-16" />
    <hkern u1="&#xc9;" u2="X" k="-10" />
    <hkern u1="&#xc9;" u2="&#x2f;" k="-10" />
    <hkern u1="&#xca;" u2="&#x2122;" k="-16" />
    <hkern u1="&#xca;" u2="X" k="-10" />
    <hkern u1="&#xca;" u2="&#x2f;" k="-10" />
    <hkern u1="&#xcb;" u2="&#x2122;" k="-16" />
    <hkern u1="&#xcb;" u2="X" k="-10" />
    <hkern u1="&#xcb;" u2="&#x2f;" k="-10" />
    <hkern u1="&#xd0;" u2="&#x2122;" k="31" />
    <hkern u1="&#xd0;" u2="x" k="29" />
    <hkern u1="&#xd0;" u2="\" k="51" />
    <hkern u1="&#xd0;" u2="X" k="82" />
    <hkern u1="&#xd0;" u2="V" k="57" />
    <hkern u1="&#xd0;" u2="&#x2f;" k="72" />
    <hkern u1="&#xd0;" u2="&#x2a;" k="10" />
    <hkern u1="&#xd2;" u2="&#x2122;" k="31" />
    <hkern u1="&#xd2;" u2="x" k="29" />
    <hkern u1="&#xd2;" u2="\" k="51" />
    <hkern u1="&#xd2;" u2="X" k="82" />
    <hkern u1="&#xd2;" u2="V" k="57" />
    <hkern u1="&#xd2;" u2="&#x2f;" k="72" />
    <hkern u1="&#xd2;" u2="&#x2a;" k="10" />
    <hkern u1="&#xd3;" u2="&#x2122;" k="31" />
    <hkern u1="&#xd3;" u2="x" k="29" />
    <hkern u1="&#xd3;" u2="\" k="51" />
    <hkern u1="&#xd3;" u2="X" k="82" />
    <hkern u1="&#xd3;" u2="V" k="57" />
    <hkern u1="&#xd3;" u2="&#x2f;" k="72" />
    <hkern u1="&#xd3;" u2="&#x2a;" k="10" />
    <hkern u1="&#xd4;" u2="&#x2122;" k="31" />
    <hkern u1="&#xd4;" u2="x" k="29" />
    <hkern u1="&#xd4;" u2="\" k="51" />
    <hkern u1="&#xd4;" u2="X" k="82" />
    <hkern u1="&#xd4;" u2="V" k="57" />
    <hkern u1="&#xd4;" u2="&#x2f;" k="72" />
    <hkern u1="&#xd4;" u2="&#x2a;" k="10" />
    <hkern u1="&#xd5;" u2="&#x2122;" k="31" />
    <hkern u1="&#xd5;" u2="x" k="29" />
    <hkern u1="&#xd5;" u2="\" k="51" />
    <hkern u1="&#xd5;" u2="X" k="82" />
    <hkern u1="&#xd5;" u2="V" k="57" />
    <hkern u1="&#xd5;" u2="&#x2f;" k="72" />
    <hkern u1="&#xd5;" u2="&#x2a;" k="10" />
    <hkern u1="&#xd6;" u2="&#x2122;" k="31" />
    <hkern u1="&#xd6;" u2="x" k="29" />
    <hkern u1="&#xd6;" u2="\" k="51" />
    <hkern u1="&#xd6;" u2="X" k="82" />
    <hkern u1="&#xd6;" u2="V" k="57" />
    <hkern u1="&#xd6;" u2="&#x2f;" k="72" />
    <hkern u1="&#xd6;" u2="&#x2a;" k="10" />
    <hkern u1="&#xd8;" u2="&#x2122;" k="31" />
    <hkern u1="&#xd8;" u2="x" k="29" />
    <hkern u1="&#xd8;" u2="\" k="51" />
    <hkern u1="&#xd8;" u2="X" k="82" />
    <hkern u1="&#xd8;" u2="V" k="57" />
    <hkern u1="&#xd8;" u2="&#x2f;" k="72" />
    <hkern u1="&#xd8;" u2="&#x2a;" k="10" />
    <hkern u1="&#xd9;" u2="x" k="6" />
    <hkern u1="&#xd9;" u2="X" k="20" />
    <hkern u1="&#xd9;" u2="&#x2f;" k="61" />
    <hkern u1="&#xda;" u2="x" k="6" />
    <hkern u1="&#xda;" u2="X" k="20" />
    <hkern u1="&#xda;" u2="&#x2f;" k="61" />
    <hkern u1="&#xdb;" u2="x" k="6" />
    <hkern u1="&#xdb;" u2="X" k="20" />
    <hkern u1="&#xdb;" u2="&#x2f;" k="61" />
    <hkern u1="&#xdc;" u2="x" k="6" />
    <hkern u1="&#xdc;" u2="X" k="20" />
    <hkern u1="&#xdc;" u2="&#x2f;" k="61" />
    <hkern u1="&#xdd;" u2="&#x2122;" k="-47" />
    <hkern u1="&#xdd;" u2="&#xbf;" k="162" />
    <hkern u1="&#xdd;" u2="&#xae;" k="39" />
    <hkern u1="&#xdd;" u2="&#xa1;" k="41" />
    <hkern u1="&#xdd;" u2="x" k="92" />
    <hkern u1="&#xdd;" u2="v" k="66" />
    <hkern u1="&#xdd;" u2="i" k="16" />
    <hkern u1="&#xdd;" u2="h" k="10" />
    <hkern u1="&#xdd;" u2="&#x40;" k="141" />
    <hkern u1="&#xdd;" u2="&#x3f;" k="16" />
    <hkern u1="&#xdd;" u2="&#x2f;" k="225" />
    <hkern u1="&#xdd;" u2="&#x2a;" k="37" />
    <hkern u1="&#xdd;" u2="&#x26;" k="106" />
    <hkern u1="&#xdf;" u2="&#x2122;" k="63" />
    <hkern u1="&#xdf;" u2="&#xae;" k="10" />
    <hkern u1="&#xdf;" u2="x" k="25" />
    <hkern u1="&#xdf;" u2="v" k="31" />
    <hkern u1="&#xdf;" u2="\" k="137" />
    <hkern u1="&#xdf;" u2="&#x3f;" k="31" />
    <hkern u1="&#xdf;" u2="&#x2f;" k="51" />
    <hkern u1="&#xdf;" u2="&#x2a;" k="31" />
    <hkern u1="&#xe0;" u2="&#x2122;" k="119" />
    <hkern u1="&#xe0;" u2="&#xae;" k="20" />
    <hkern u1="&#xe0;" u2="v" k="20" />
    <hkern u1="&#xe0;" u2="\" k="123" />
    <hkern u1="&#xe0;" u2="&#x3f;" k="78" />
    <hkern u1="&#xe0;" u2="&#x2a;" k="20" />
    <hkern u1="&#xe1;" u2="&#x2122;" k="119" />
    <hkern u1="&#xe1;" u2="&#xae;" k="20" />
    <hkern u1="&#xe1;" u2="v" k="20" />
    <hkern u1="&#xe1;" u2="\" k="123" />
    <hkern u1="&#xe1;" u2="&#x3f;" k="78" />
    <hkern u1="&#xe1;" u2="&#x2a;" k="20" />
    <hkern u1="&#xe2;" u2="&#x2122;" k="119" />
    <hkern u1="&#xe2;" u2="&#xae;" k="20" />
    <hkern u1="&#xe2;" u2="v" k="20" />
    <hkern u1="&#xe2;" u2="\" k="123" />
    <hkern u1="&#xe2;" u2="&#x3f;" k="78" />
    <hkern u1="&#xe2;" u2="&#x2a;" k="20" />
    <hkern u1="&#xe3;" u2="&#x2122;" k="119" />
    <hkern u1="&#xe3;" u2="&#xae;" k="20" />
    <hkern u1="&#xe3;" u2="v" k="20" />
    <hkern u1="&#xe3;" u2="\" k="123" />
    <hkern u1="&#xe3;" u2="&#x3f;" k="78" />
    <hkern u1="&#xe3;" u2="&#x2a;" k="20" />
    <hkern u1="&#xe4;" u2="&#x2122;" k="119" />
    <hkern u1="&#xe4;" u2="&#xae;" k="20" />
    <hkern u1="&#xe4;" u2="v" k="20" />
    <hkern u1="&#xe4;" u2="\" k="123" />
    <hkern u1="&#xe4;" u2="&#x3f;" k="78" />
    <hkern u1="&#xe4;" u2="&#x2a;" k="20" />
    <hkern u1="&#xe5;" u2="&#x2122;" k="119" />
    <hkern u1="&#xe5;" u2="&#xae;" k="20" />
    <hkern u1="&#xe5;" u2="v" k="20" />
    <hkern u1="&#xe5;" u2="\" k="123" />
    <hkern u1="&#xe5;" u2="&#x3f;" k="78" />
    <hkern u1="&#xe5;" u2="&#x2a;" k="20" />
    <hkern u1="&#xe6;" u2="&#x2122;" k="78" />
    <hkern u1="&#xe6;" u2="&#xae;" k="18" />
    <hkern u1="&#xe6;" u2="x" k="31" />
    <hkern u1="&#xe6;" u2="v" k="20" />
    <hkern u1="&#xe6;" u2="\" k="123" />
    <hkern u1="&#xe6;" u2="&#x3f;" k="31" />
    <hkern u1="&#xe6;" u2="&#x2f;" k="31" />
    <hkern u1="&#xe6;" u2="&#x2a;" k="31" />
    <hkern u1="&#xe7;" u2="&#x2122;" k="72" />
    <hkern u1="&#xe7;" u2="&#xae;" k="10" />
    <hkern u1="&#xe7;" u2="x" k="20" />
    <hkern u1="&#xe7;" u2="v" k="6" />
    <hkern u1="&#xe7;" u2="\" k="123" />
    <hkern u1="&#xe7;" u2="&#x3f;" k="31" />
    <hkern u1="&#xe7;" u2="&#x2f;" k="20" />
    <hkern u1="&#xe7;" u2="&#x2a;" k="31" />
    <hkern u1="&#xe8;" u2="&#x2122;" k="78" />
    <hkern u1="&#xe8;" u2="&#xae;" k="18" />
    <hkern u1="&#xe8;" u2="x" k="31" />
    <hkern u1="&#xe8;" u2="v" k="20" />
    <hkern u1="&#xe8;" u2="\" k="123" />
    <hkern u1="&#xe8;" u2="&#x3f;" k="31" />
    <hkern u1="&#xe8;" u2="&#x2f;" k="31" />
    <hkern u1="&#xe8;" u2="&#x2a;" k="31" />
    <hkern u1="&#xe9;" u2="&#x2122;" k="78" />
    <hkern u1="&#xe9;" u2="&#xae;" k="18" />
    <hkern u1="&#xe9;" u2="x" k="31" />
    <hkern u1="&#xe9;" u2="v" k="20" />
    <hkern u1="&#xe9;" u2="\" k="123" />
    <hkern u1="&#xe9;" u2="&#x3f;" k="31" />
    <hkern u1="&#xe9;" u2="&#x2f;" k="31" />
    <hkern u1="&#xe9;" u2="&#x2a;" k="31" />
    <hkern u1="&#xea;" u2="&#x2122;" k="78" />
    <hkern u1="&#xea;" u2="&#xae;" k="18" />
    <hkern u1="&#xea;" u2="x" k="31" />
    <hkern u1="&#xea;" u2="v" k="20" />
    <hkern u1="&#xea;" u2="\" k="123" />
    <hkern u1="&#xea;" u2="&#x3f;" k="31" />
    <hkern u1="&#xea;" u2="&#x2f;" k="31" />
    <hkern u1="&#xea;" u2="&#x2a;" k="31" />
    <hkern u1="&#xeb;" u2="&#x2122;" k="78" />
    <hkern u1="&#xeb;" u2="&#xae;" k="18" />
    <hkern u1="&#xeb;" u2="x" k="31" />
    <hkern u1="&#xeb;" u2="v" k="20" />
    <hkern u1="&#xeb;" u2="\" k="123" />
    <hkern u1="&#xeb;" u2="&#x3f;" k="31" />
    <hkern u1="&#xeb;" u2="&#x2f;" k="31" />
    <hkern u1="&#xeb;" u2="&#x2a;" k="31" />
    <hkern u1="&#xf1;" u2="&#x2122;" k="100" />
    <hkern u1="&#xf1;" u2="&#xae;" k="29" />
    <hkern u1="&#xf1;" u2="v" k="20" />
    <hkern u1="&#xf1;" u2="\" k="113" />
    <hkern u1="&#xf1;" u2="&#x2a;" k="31" />
    <hkern u1="&#xf2;" u2="&#x2122;" k="61" />
    <hkern u1="&#xf2;" u2="&#xae;" k="20" />
    <hkern u1="&#xf2;" u2="x" k="53" />
    <hkern u1="&#xf2;" u2="v" k="23" />
    <hkern u1="&#xf2;" u2="\" k="123" />
    <hkern u1="&#xf2;" u2="&#x3f;" k="31" />
    <hkern u1="&#xf2;" u2="&#x2f;" k="61" />
    <hkern u1="&#xf2;" u2="&#x2a;" k="31" />
    <hkern u1="&#xf3;" u2="&#x2122;" k="61" />
    <hkern u1="&#xf3;" u2="&#xae;" k="20" />
    <hkern u1="&#xf3;" u2="x" k="53" />
    <hkern u1="&#xf3;" u2="v" k="23" />
    <hkern u1="&#xf3;" u2="\" k="123" />
    <hkern u1="&#xf3;" u2="&#x3f;" k="31" />
    <hkern u1="&#xf3;" u2="&#x2f;" k="61" />
    <hkern u1="&#xf3;" u2="&#x2a;" k="31" />
    <hkern u1="&#xf4;" u2="&#x2122;" k="61" />
    <hkern u1="&#xf4;" u2="&#xae;" k="20" />
    <hkern u1="&#xf4;" u2="x" k="53" />
    <hkern u1="&#xf4;" u2="v" k="23" />
    <hkern u1="&#xf4;" u2="\" k="123" />
    <hkern u1="&#xf4;" u2="&#x3f;" k="31" />
    <hkern u1="&#xf4;" u2="&#x2f;" k="61" />
    <hkern u1="&#xf4;" u2="&#x2a;" k="31" />
    <hkern u1="&#xf5;" u2="&#x2122;" k="61" />
    <hkern u1="&#xf5;" u2="&#xae;" k="20" />
    <hkern u1="&#xf5;" u2="x" k="53" />
    <hkern u1="&#xf5;" u2="v" k="23" />
    <hkern u1="&#xf5;" u2="\" k="123" />
    <hkern u1="&#xf5;" u2="&#x3f;" k="31" />
    <hkern u1="&#xf5;" u2="&#x2f;" k="61" />
    <hkern u1="&#xf5;" u2="&#x2a;" k="31" />
    <hkern u1="&#xf6;" u2="&#x2122;" k="61" />
    <hkern u1="&#xf6;" u2="&#xae;" k="20" />
    <hkern u1="&#xf6;" u2="x" k="53" />
    <hkern u1="&#xf6;" u2="v" k="23" />
    <hkern u1="&#xf6;" u2="\" k="123" />
    <hkern u1="&#xf6;" u2="&#x3f;" k="31" />
    <hkern u1="&#xf6;" u2="&#x2f;" k="61" />
    <hkern u1="&#xf6;" u2="&#x2a;" k="31" />
    <hkern u1="&#xf8;" u2="&#x2122;" k="61" />
    <hkern u1="&#xf8;" u2="&#xae;" k="20" />
    <hkern u1="&#xf8;" u2="x" k="53" />
    <hkern u1="&#xf8;" u2="v" k="23" />
    <hkern u1="&#xf8;" u2="\" k="123" />
    <hkern u1="&#xf8;" u2="&#x3f;" k="31" />
    <hkern u1="&#xf8;" u2="&#x2f;" k="61" />
    <hkern u1="&#xf8;" u2="&#x2a;" k="31" />
    <hkern u1="&#xf9;" u2="&#x2122;" k="18" />
    <hkern u1="&#xf9;" u2="\" k="20" />
    <hkern u1="&#xfa;" u2="&#x2122;" k="18" />
    <hkern u1="&#xfa;" u2="\" k="20" />
    <hkern u1="&#xfb;" u2="&#x2122;" k="18" />
    <hkern u1="&#xfb;" u2="\" k="20" />
    <hkern u1="&#xfc;" u2="&#x2122;" k="18" />
    <hkern u1="&#xfc;" u2="\" k="20" />
    <hkern u1="&#xfd;" u2="&#xbf;" k="27" />
    <hkern u1="&#xfd;" u2="&#xae;" k="-39" />
    <hkern u1="&#xfd;" u2="x" k="-10" />
    <hkern u1="&#xfd;" u2="v" k="-20" />
    <hkern u1="&#xfd;" u2="\" k="20" />
    <hkern u1="&#xfd;" u2="&#x40;" k="14" />
    <hkern u1="&#xfd;" u2="&#x3f;" k="-51" />
    <hkern u1="&#xfd;" u2="&#x2f;" k="61" />
    <hkern u1="&#xfd;" u2="&#x2a;" k="-20" />
    <hkern u1="&#xfe;" u2="&#x2122;" k="61" />
    <hkern u1="&#xfe;" u2="&#xae;" k="20" />
    <hkern u1="&#xfe;" u2="x" k="53" />
    <hkern u1="&#xfe;" u2="v" k="23" />
    <hkern u1="&#xfe;" u2="\" k="123" />
    <hkern u1="&#xfe;" u2="&#x3f;" k="31" />
    <hkern u1="&#xfe;" u2="&#x2f;" k="61" />
    <hkern u1="&#xfe;" u2="&#x2a;" k="31" />
    <hkern u1="&#xff;" u2="&#xbf;" k="27" />
    <hkern u1="&#xff;" u2="&#xae;" k="-39" />
    <hkern u1="&#xff;" u2="x" k="-10" />
    <hkern u1="&#xff;" u2="v" k="-20" />
    <hkern u1="&#xff;" u2="\" k="20" />
    <hkern u1="&#xff;" u2="&#x40;" k="14" />
    <hkern u1="&#xff;" u2="&#x3f;" k="-51" />
    <hkern u1="&#xff;" u2="&#x2f;" k="61" />
    <hkern u1="&#xff;" u2="&#x2a;" k="-20" />
    <hkern u1="&#x100;" u2="&#x2122;" k="233" />
    <hkern u1="&#x100;" u2="&#xbf;" k="18" />
    <hkern u1="&#x100;" u2="&#xae;" k="195" />
    <hkern u1="&#x100;" u2="x" k="-16" />
    <hkern u1="&#x100;" u2="v" k="113" />
    <hkern u1="&#x100;" u2="\" k="154" />
    <hkern u1="&#x100;" u2="X" k="-10" />
    <hkern u1="&#x100;" u2="V" k="211" />
    <hkern u1="&#x100;" u2="&#x40;" k="10" />
    <hkern u1="&#x100;" u2="&#x3f;" k="143" />
    <hkern u1="&#x100;" u2="&#x2a;" k="205" />
    <hkern u1="&#x101;" u2="&#x2122;" k="119" />
    <hkern u1="&#x101;" u2="&#xae;" k="20" />
    <hkern u1="&#x101;" u2="v" k="20" />
    <hkern u1="&#x101;" u2="\" k="123" />
    <hkern u1="&#x101;" u2="&#x3f;" k="78" />
    <hkern u1="&#x101;" u2="&#x2a;" k="20" />
    <hkern u1="&#x102;" u2="&#x2122;" k="233" />
    <hkern u1="&#x102;" u2="&#xbf;" k="18" />
    <hkern u1="&#x102;" u2="&#xae;" k="195" />
    <hkern u1="&#x102;" u2="x" k="-16" />
    <hkern u1="&#x102;" u2="v" k="113" />
    <hkern u1="&#x102;" u2="\" k="154" />
    <hkern u1="&#x102;" u2="X" k="-10" />
    <hkern u1="&#x102;" u2="V" k="211" />
    <hkern u1="&#x102;" u2="&#x40;" k="10" />
    <hkern u1="&#x102;" u2="&#x3f;" k="143" />
    <hkern u1="&#x102;" u2="&#x2a;" k="205" />
    <hkern u1="&#x103;" u2="&#x2122;" k="119" />
    <hkern u1="&#x103;" u2="&#xae;" k="20" />
    <hkern u1="&#x103;" u2="v" k="20" />
    <hkern u1="&#x103;" u2="\" k="123" />
    <hkern u1="&#x103;" u2="&#x3f;" k="78" />
    <hkern u1="&#x103;" u2="&#x2a;" k="20" />
    <hkern u1="&#x104;" u2="&#x2122;" k="233" />
    <hkern u1="&#x104;" u2="&#xbf;" k="18" />
    <hkern u1="&#x104;" u2="&#xae;" k="195" />
    <hkern u1="&#x104;" u2="x" k="-16" />
    <hkern u1="&#x104;" u2="v" k="113" />
    <hkern u1="&#x104;" u2="\" k="154" />
    <hkern u1="&#x104;" u2="X" k="-10" />
    <hkern u1="&#x104;" u2="V" k="211" />
    <hkern u1="&#x104;" u2="&#x40;" k="10" />
    <hkern u1="&#x104;" u2="&#x3f;" k="143" />
    <hkern u1="&#x104;" u2="&#x2a;" k="205" />
    <hkern u1="&#x105;" u2="&#x2122;" k="119" />
    <hkern u1="&#x105;" u2="&#xae;" k="20" />
    <hkern u1="&#x105;" u2="v" k="20" />
    <hkern u1="&#x105;" u2="\" k="123" />
    <hkern u1="&#x105;" u2="&#x3f;" k="78" />
    <hkern u1="&#x105;" u2="&#x2a;" k="20" />
    <hkern u1="&#x106;" u2="x" k="10" />
    <hkern u1="&#x106;" u2="\" k="51" />
    <hkern u1="&#x106;" u2="X" k="61" />
    <hkern u1="&#x106;" u2="V" k="41" />
    <hkern u1="&#x106;" u2="&#x2f;" k="72" />
    <hkern u1="&#x107;" u2="&#x2122;" k="72" />
    <hkern u1="&#x107;" u2="&#xae;" k="10" />
    <hkern u1="&#x107;" u2="x" k="20" />
    <hkern u1="&#x107;" u2="v" k="6" />
    <hkern u1="&#x107;" u2="\" k="123" />
    <hkern u1="&#x107;" u2="&#x3f;" k="31" />
    <hkern u1="&#x107;" u2="&#x2f;" k="20" />
    <hkern u1="&#x107;" u2="&#x2a;" k="31" />
    <hkern u1="&#x108;" u2="x" k="10" />
    <hkern u1="&#x108;" u2="\" k="51" />
    <hkern u1="&#x108;" u2="X" k="61" />
    <hkern u1="&#x108;" u2="V" k="41" />
    <hkern u1="&#x108;" u2="&#x2f;" k="72" />
    <hkern u1="&#x109;" u2="&#x2122;" k="72" />
    <hkern u1="&#x109;" u2="&#xae;" k="10" />
    <hkern u1="&#x109;" u2="x" k="20" />
    <hkern u1="&#x109;" u2="v" k="6" />
    <hkern u1="&#x109;" u2="\" k="123" />
    <hkern u1="&#x109;" u2="&#x3f;" k="31" />
    <hkern u1="&#x109;" u2="&#x2f;" k="20" />
    <hkern u1="&#x109;" u2="&#x2a;" k="31" />
    <hkern u1="&#x10a;" u2="x" k="10" />
    <hkern u1="&#x10a;" u2="\" k="51" />
    <hkern u1="&#x10a;" u2="X" k="61" />
    <hkern u1="&#x10a;" u2="V" k="41" />
    <hkern u1="&#x10a;" u2="&#x2f;" k="72" />
    <hkern u1="&#x10b;" u2="&#x2122;" k="72" />
    <hkern u1="&#x10b;" u2="&#xae;" k="10" />
    <hkern u1="&#x10b;" u2="x" k="20" />
    <hkern u1="&#x10b;" u2="v" k="6" />
    <hkern u1="&#x10b;" u2="\" k="123" />
    <hkern u1="&#x10b;" u2="&#x3f;" k="31" />
    <hkern u1="&#x10b;" u2="&#x2f;" k="20" />
    <hkern u1="&#x10b;" u2="&#x2a;" k="31" />
    <hkern u1="&#x10c;" u2="x" k="10" />
    <hkern u1="&#x10c;" u2="\" k="51" />
    <hkern u1="&#x10c;" u2="X" k="61" />
    <hkern u1="&#x10c;" u2="V" k="41" />
    <hkern u1="&#x10c;" u2="&#x2f;" k="72" />
    <hkern u1="&#x10d;" u2="&#x2122;" k="72" />
    <hkern u1="&#x10d;" u2="&#xae;" k="10" />
    <hkern u1="&#x10d;" u2="x" k="20" />
    <hkern u1="&#x10d;" u2="v" k="6" />
    <hkern u1="&#x10d;" u2="\" k="123" />
    <hkern u1="&#x10d;" u2="&#x3f;" k="31" />
    <hkern u1="&#x10d;" u2="&#x2f;" k="20" />
    <hkern u1="&#x10d;" u2="&#x2a;" k="31" />
    <hkern u1="&#x10e;" u2="&#x2122;" k="31" />
    <hkern u1="&#x10e;" u2="x" k="29" />
    <hkern u1="&#x10e;" u2="\" k="51" />
    <hkern u1="&#x10e;" u2="X" k="82" />
    <hkern u1="&#x10e;" u2="V" k="57" />
    <hkern u1="&#x10e;" u2="&#x2f;" k="72" />
    <hkern u1="&#x10e;" u2="&#x2a;" k="10" />
    <hkern u1="&#x10f;" u2="&#x2122;" k="-20" />
    <hkern u1="&#x10f;" u2="&#x20ac;" k="61" />
    <hkern u1="&#x10f;" u2="&#xbf;" k="184" />
    <hkern u1="&#x10f;" u2="&#xae;" k="10" />
    <hkern u1="&#x10f;" u2="&#xa3;" k="123" />
    <hkern u1="&#x10f;" u2="&#xa2;" k="82" />
    <hkern u1="&#x10f;" u2="x" k="51" />
    <hkern u1="&#x10f;" u2="v" k="10" />
    <hkern u1="&#x10f;" u2="j" k="10" />
    <hkern u1="&#x10f;" u2="i" k="10" />
    <hkern u1="&#x10f;" u2="X" k="20" />
    <hkern u1="&#x10f;" u2="V" k="-31" />
    <hkern u1="&#x10f;" u2="&#x40;" k="102" />
    <hkern u1="&#x10f;" u2="&#x39;" k="20" />
    <hkern u1="&#x10f;" u2="&#x38;" k="72" />
    <hkern u1="&#x10f;" u2="&#x36;" k="164" />
    <hkern u1="&#x10f;" u2="&#x35;" k="92" />
    <hkern u1="&#x10f;" u2="&#x34;" k="174" />
    <hkern u1="&#x10f;" u2="&#x33;" k="41" />
    <hkern u1="&#x10f;" u2="&#x32;" k="61" />
    <hkern u1="&#x10f;" u2="&#x30;" k="72" />
    <hkern u1="&#x10f;" u2="&#x2f;" k="256" />
    <hkern u1="&#x10f;" u2="&#x2a;" k="25" />
    <hkern u1="&#x10f;" u2="&#x26;" k="123" />
    <hkern u1="&#x10f;" u2="&#x24;" k="31" />
    <hkern u1="&#x10f;" u2="&#x23;" k="61" />
    <hkern u1="&#x110;" u2="&#x2122;" k="31" />
    <hkern u1="&#x110;" u2="x" k="29" />
    <hkern u1="&#x110;" u2="\" k="51" />
    <hkern u1="&#x110;" u2="X" k="82" />
    <hkern u1="&#x110;" u2="V" k="57" />
    <hkern u1="&#x110;" u2="&#x2f;" k="72" />
    <hkern u1="&#x110;" u2="&#x2a;" k="10" />
    <hkern u1="&#x112;" u2="&#x2122;" k="-16" />
    <hkern u1="&#x112;" u2="X" k="-10" />
    <hkern u1="&#x112;" u2="&#x2f;" k="-10" />
    <hkern u1="&#x113;" u2="&#x2122;" k="78" />
    <hkern u1="&#x113;" u2="&#xae;" k="18" />
    <hkern u1="&#x113;" u2="x" k="31" />
    <hkern u1="&#x113;" u2="v" k="20" />
    <hkern u1="&#x113;" u2="\" k="123" />
    <hkern u1="&#x113;" u2="&#x3f;" k="31" />
    <hkern u1="&#x113;" u2="&#x2f;" k="31" />
    <hkern u1="&#x113;" u2="&#x2a;" k="31" />
    <hkern u1="&#x114;" u2="&#x2122;" k="-16" />
    <hkern u1="&#x114;" u2="X" k="-10" />
    <hkern u1="&#x114;" u2="&#x2f;" k="-10" />
    <hkern u1="&#x115;" u2="&#x2122;" k="78" />
    <hkern u1="&#x115;" u2="&#xae;" k="18" />
    <hkern u1="&#x115;" u2="x" k="31" />
    <hkern u1="&#x115;" u2="v" k="20" />
    <hkern u1="&#x115;" u2="\" k="123" />
    <hkern u1="&#x115;" u2="&#x3f;" k="31" />
    <hkern u1="&#x115;" u2="&#x2f;" k="31" />
    <hkern u1="&#x115;" u2="&#x2a;" k="31" />
    <hkern u1="&#x116;" u2="&#x2122;" k="-16" />
    <hkern u1="&#x116;" u2="X" k="-10" />
    <hkern u1="&#x116;" u2="&#x2f;" k="-10" />
    <hkern u1="&#x117;" u2="&#x2122;" k="78" />
    <hkern u1="&#x117;" u2="&#xae;" k="18" />
    <hkern u1="&#x117;" u2="x" k="31" />
    <hkern u1="&#x117;" u2="v" k="20" />
    <hkern u1="&#x117;" u2="\" k="123" />
    <hkern u1="&#x117;" u2="&#x3f;" k="31" />
    <hkern u1="&#x117;" u2="&#x2f;" k="31" />
    <hkern u1="&#x117;" u2="&#x2a;" k="31" />
    <hkern u1="&#x118;" u2="&#x2122;" k="-16" />
    <hkern u1="&#x118;" u2="X" k="-10" />
    <hkern u1="&#x118;" u2="&#x2f;" k="-10" />
    <hkern u1="&#x119;" u2="&#x2122;" k="78" />
    <hkern u1="&#x119;" u2="&#xae;" k="18" />
    <hkern u1="&#x119;" u2="x" k="31" />
    <hkern u1="&#x119;" u2="v" k="20" />
    <hkern u1="&#x119;" u2="\" k="123" />
    <hkern u1="&#x119;" u2="&#x3f;" k="31" />
    <hkern u1="&#x119;" u2="&#x2f;" k="31" />
    <hkern u1="&#x119;" u2="&#x2a;" k="31" />
    <hkern u1="&#x11a;" u2="&#x2122;" k="-16" />
    <hkern u1="&#x11a;" u2="X" k="-10" />
    <hkern u1="&#x11a;" u2="&#x2f;" k="-10" />
    <hkern u1="&#x11b;" u2="&#x2122;" k="78" />
    <hkern u1="&#x11b;" u2="&#xae;" k="18" />
    <hkern u1="&#x11b;" u2="x" k="31" />
    <hkern u1="&#x11b;" u2="v" k="20" />
    <hkern u1="&#x11b;" u2="\" k="123" />
    <hkern u1="&#x11b;" u2="&#x3f;" k="31" />
    <hkern u1="&#x11b;" u2="&#x2f;" k="31" />
    <hkern u1="&#x11b;" u2="&#x2a;" k="31" />
    <hkern u1="&#x11c;" u2="x" k="20" />
    <hkern u1="&#x11c;" u2="\" k="82" />
    <hkern u1="&#x11c;" u2="X" k="41" />
    <hkern u1="&#x11c;" u2="V" k="41" />
    <hkern u1="&#x11c;" u2="&#x2f;" k="72" />
    <hkern u1="&#x11d;" u2="&#x2122;" k="18" />
    <hkern u1="&#x11d;" u2="\" k="20" />
    <hkern u1="&#x11e;" u2="x" k="20" />
    <hkern u1="&#x11e;" u2="\" k="82" />
    <hkern u1="&#x11e;" u2="X" k="41" />
    <hkern u1="&#x11e;" u2="V" k="41" />
    <hkern u1="&#x11e;" u2="&#x2f;" k="72" />
    <hkern u1="&#x11f;" u2="&#x2122;" k="18" />
    <hkern u1="&#x11f;" u2="\" k="20" />
    <hkern u1="&#x120;" u2="x" k="20" />
    <hkern u1="&#x120;" u2="\" k="82" />
    <hkern u1="&#x120;" u2="X" k="41" />
    <hkern u1="&#x120;" u2="V" k="41" />
    <hkern u1="&#x120;" u2="&#x2f;" k="72" />
    <hkern u1="&#x121;" u2="&#x2122;" k="18" />
    <hkern u1="&#x121;" u2="\" k="20" />
    <hkern u1="&#x122;" u2="x" k="20" />
    <hkern u1="&#x122;" u2="\" k="82" />
    <hkern u1="&#x122;" u2="X" k="41" />
    <hkern u1="&#x122;" u2="V" k="41" />
    <hkern u1="&#x122;" u2="&#x2f;" k="72" />
    <hkern u1="&#x123;" u2="&#x2122;" k="18" />
    <hkern u1="&#x123;" u2="\" k="20" />
    <hkern u1="&#x125;" u2="&#x2122;" k="100" />
    <hkern u1="&#x125;" u2="&#xae;" k="29" />
    <hkern u1="&#x125;" u2="v" k="20" />
    <hkern u1="&#x125;" u2="\" k="113" />
    <hkern u1="&#x125;" u2="&#x2a;" k="31" />
    <hkern u1="&#x127;" u2="&#x2122;" k="100" />
    <hkern u1="&#x127;" u2="&#xae;" k="29" />
    <hkern u1="&#x127;" u2="v" k="20" />
    <hkern u1="&#x127;" u2="\" k="113" />
    <hkern u1="&#x127;" u2="&#x2a;" k="31" />
    <hkern u1="&#x132;" u2="&#x2f;" k="41" />
    <hkern u1="&#x134;" u2="&#x2f;" k="41" />
    <hkern u1="&#x136;" u2="&#x2122;" k="-16" />
    <hkern u1="&#x136;" u2="&#xbf;" k="20" />
    <hkern u1="&#x136;" u2="&#xae;" k="72" />
    <hkern u1="&#x136;" u2="x" k="-20" />
    <hkern u1="&#x136;" u2="v" k="92" />
    <hkern u1="&#x136;" u2="X" k="-20" />
    <hkern u1="&#x136;" u2="&#x40;" k="31" />
    <hkern u1="&#x136;" u2="&#x3f;" k="31" />
    <hkern u1="&#x136;" u2="&#x2a;" k="39" />
    <hkern u1="&#x136;" u2="&#x26;" k="16" />
    <hkern u1="&#x137;" u2="&#xbf;" k="20" />
    <hkern u1="&#x137;" u2="&#xae;" k="-16" />
    <hkern u1="&#x137;" u2="x" k="-31" />
    <hkern u1="&#x137;" u2="v" k="-10" />
    <hkern u1="&#x137;" u2="\" k="51" />
    <hkern u1="&#x137;" u2="&#x40;" k="16" />
    <hkern u1="&#x137;" u2="&#x3f;" k="-20" />
    <hkern u1="&#x137;" u2="&#x26;" k="10" />
    <hkern u1="&#x137;" u2="&#x21;" k="10" />
    <hkern u1="&#x138;" u2="&#xbf;" k="20" />
    <hkern u1="&#x138;" u2="&#xae;" k="-16" />
    <hkern u1="&#x138;" u2="x" k="-31" />
    <hkern u1="&#x138;" u2="v" k="-10" />
    <hkern u1="&#x138;" u2="\" k="51" />
    <hkern u1="&#x138;" u2="&#x40;" k="16" />
    <hkern u1="&#x138;" u2="&#x3f;" k="-20" />
    <hkern u1="&#x138;" u2="&#x26;" k="10" />
    <hkern u1="&#x138;" u2="&#x21;" k="10" />
    <hkern u1="&#x139;" u2="&#x2122;" k="414" />
    <hkern u1="&#x139;" u2="&#xae;" k="362" />
    <hkern u1="&#x139;" u2="v" k="113" />
    <hkern u1="&#x139;" u2="\" k="184" />
    <hkern u1="&#x139;" u2="V" k="195" />
    <hkern u1="&#x139;" u2="&#x40;" k="10" />
    <hkern u1="&#x139;" u2="&#x3f;" k="133" />
    <hkern u1="&#x139;" u2="&#x2a;" k="451" />
    <hkern u1="&#x139;" u2="&#x26;" k="4" />
    <hkern u1="&#x13b;" u2="&#x2122;" k="414" />
    <hkern u1="&#x13b;" u2="&#xae;" k="362" />
    <hkern u1="&#x13b;" u2="v" k="113" />
    <hkern u1="&#x13b;" u2="\" k="184" />
    <hkern u1="&#x13b;" u2="V" k="195" />
    <hkern u1="&#x13b;" u2="&#x40;" k="10" />
    <hkern u1="&#x13b;" u2="&#x3f;" k="133" />
    <hkern u1="&#x13b;" u2="&#x2a;" k="451" />
    <hkern u1="&#x13b;" u2="&#x26;" k="4" />
    <hkern u1="&#x13e;" u2="&#x2122;" k="-20" />
    <hkern u1="&#x13e;" u2="&#x20ac;" k="61" />
    <hkern u1="&#x13e;" u2="&#xbf;" k="184" />
    <hkern u1="&#x13e;" u2="&#xae;" k="10" />
    <hkern u1="&#x13e;" u2="&#xa3;" k="123" />
    <hkern u1="&#x13e;" u2="&#xa2;" k="82" />
    <hkern u1="&#x13e;" u2="x" k="51" />
    <hkern u1="&#x13e;" u2="v" k="10" />
    <hkern u1="&#x13e;" u2="j" k="10" />
    <hkern u1="&#x13e;" u2="i" k="10" />
    <hkern u1="&#x13e;" u2="X" k="20" />
    <hkern u1="&#x13e;" u2="V" k="-31" />
    <hkern u1="&#x13e;" u2="&#x40;" k="102" />
    <hkern u1="&#x13e;" u2="&#x39;" k="20" />
    <hkern u1="&#x13e;" u2="&#x38;" k="72" />
    <hkern u1="&#x13e;" u2="&#x36;" k="164" />
    <hkern u1="&#x13e;" u2="&#x35;" k="92" />
    <hkern u1="&#x13e;" u2="&#x34;" k="174" />
    <hkern u1="&#x13e;" u2="&#x33;" k="41" />
    <hkern u1="&#x13e;" u2="&#x32;" k="61" />
    <hkern u1="&#x13e;" u2="&#x30;" k="72" />
    <hkern u1="&#x13e;" u2="&#x2f;" k="256" />
    <hkern u1="&#x13e;" u2="&#x2a;" k="25" />
    <hkern u1="&#x13e;" u2="&#x26;" k="123" />
    <hkern u1="&#x13e;" u2="&#x24;" k="31" />
    <hkern u1="&#x13e;" u2="&#x23;" k="61" />
    <hkern u1="&#x144;" u2="&#x2122;" k="100" />
    <hkern u1="&#x144;" u2="&#xae;" k="29" />
    <hkern u1="&#x144;" u2="v" k="20" />
    <hkern u1="&#x144;" u2="\" k="113" />
    <hkern u1="&#x144;" u2="&#x2a;" k="31" />
    <hkern u1="&#x146;" u2="&#x2122;" k="100" />
    <hkern u1="&#x146;" u2="&#xae;" k="29" />
    <hkern u1="&#x146;" u2="v" k="20" />
    <hkern u1="&#x146;" u2="\" k="113" />
    <hkern u1="&#x146;" u2="&#x2a;" k="31" />
    <hkern u1="&#x148;" u2="&#x2122;" k="100" />
    <hkern u1="&#x148;" u2="&#xae;" k="29" />
    <hkern u1="&#x148;" u2="v" k="20" />
    <hkern u1="&#x148;" u2="\" k="113" />
    <hkern u1="&#x148;" u2="&#x2a;" k="31" />
    <hkern u1="&#x14b;" u2="&#x2122;" k="100" />
    <hkern u1="&#x14b;" u2="&#xae;" k="29" />
    <hkern u1="&#x14b;" u2="v" k="20" />
    <hkern u1="&#x14b;" u2="\" k="113" />
    <hkern u1="&#x14b;" u2="&#x2a;" k="31" />
    <hkern u1="&#x14c;" u2="&#x2122;" k="31" />
    <hkern u1="&#x14c;" u2="x" k="29" />
    <hkern u1="&#x14c;" u2="\" k="51" />
    <hkern u1="&#x14c;" u2="X" k="82" />
    <hkern u1="&#x14c;" u2="V" k="57" />
    <hkern u1="&#x14c;" u2="&#x2f;" k="72" />
    <hkern u1="&#x14c;" u2="&#x2a;" k="10" />
    <hkern u1="&#x14d;" u2="&#x2122;" k="61" />
    <hkern u1="&#x14d;" u2="&#xae;" k="20" />
    <hkern u1="&#x14d;" u2="x" k="53" />
    <hkern u1="&#x14d;" u2="v" k="23" />
    <hkern u1="&#x14d;" u2="\" k="123" />
    <hkern u1="&#x14d;" u2="&#x3f;" k="31" />
    <hkern u1="&#x14d;" u2="&#x2f;" k="61" />
    <hkern u1="&#x14d;" u2="&#x2a;" k="31" />
    <hkern u1="&#x14e;" u2="&#x2122;" k="31" />
    <hkern u1="&#x14e;" u2="x" k="29" />
    <hkern u1="&#x14e;" u2="\" k="51" />
    <hkern u1="&#x14e;" u2="X" k="82" />
    <hkern u1="&#x14e;" u2="V" k="57" />
    <hkern u1="&#x14e;" u2="&#x2f;" k="72" />
    <hkern u1="&#x14e;" u2="&#x2a;" k="10" />
    <hkern u1="&#x14f;" u2="&#x2122;" k="61" />
    <hkern u1="&#x14f;" u2="&#xae;" k="20" />
    <hkern u1="&#x14f;" u2="x" k="53" />
    <hkern u1="&#x14f;" u2="v" k="23" />
    <hkern u1="&#x14f;" u2="\" k="123" />
    <hkern u1="&#x14f;" u2="&#x3f;" k="31" />
    <hkern u1="&#x14f;" u2="&#x2f;" k="61" />
    <hkern u1="&#x14f;" u2="&#x2a;" k="31" />
    <hkern u1="&#x150;" u2="&#x2122;" k="31" />
    <hkern u1="&#x150;" u2="x" k="29" />
    <hkern u1="&#x150;" u2="\" k="51" />
    <hkern u1="&#x150;" u2="X" k="82" />
    <hkern u1="&#x150;" u2="V" k="57" />
    <hkern u1="&#x150;" u2="&#x2f;" k="72" />
    <hkern u1="&#x150;" u2="&#x2a;" k="10" />
    <hkern u1="&#x151;" u2="&#x2122;" k="61" />
    <hkern u1="&#x151;" u2="&#xae;" k="20" />
    <hkern u1="&#x151;" u2="x" k="53" />
    <hkern u1="&#x151;" u2="v" k="23" />
    <hkern u1="&#x151;" u2="\" k="123" />
    <hkern u1="&#x151;" u2="&#x3f;" k="31" />
    <hkern u1="&#x151;" u2="&#x2f;" k="61" />
    <hkern u1="&#x151;" u2="&#x2a;" k="31" />
    <hkern u1="&#x153;" u2="&#x2122;" k="78" />
    <hkern u1="&#x153;" u2="&#xae;" k="18" />
    <hkern u1="&#x153;" u2="x" k="31" />
    <hkern u1="&#x153;" u2="v" k="20" />
    <hkern u1="&#x153;" u2="\" k="123" />
    <hkern u1="&#x153;" u2="&#x3f;" k="31" />
    <hkern u1="&#x153;" u2="&#x2f;" k="31" />
    <hkern u1="&#x153;" u2="&#x2a;" k="31" />
    <hkern u1="&#x154;" u2="&#x2122;" k="20" />
    <hkern u1="&#x154;" u2="&#xbf;" k="10" />
    <hkern u1="&#x154;" u2="&#xae;" k="10" />
    <hkern u1="&#x154;" u2="\" k="51" />
    <hkern u1="&#x154;" u2="V" k="51" />
    <hkern u1="&#x154;" u2="&#x40;" k="10" />
    <hkern u1="&#x154;" u2="&#x2a;" k="20" />
    <hkern u1="&#x154;" u2="&#x26;" k="10" />
    <hkern u1="&#x155;" u2="&#xbf;" k="20" />
    <hkern u1="&#x155;" u2="&#xae;" k="-31" />
    <hkern u1="&#x155;" u2="x" k="-20" />
    <hkern u1="&#x155;" u2="v" k="-41" />
    <hkern u1="&#x155;" u2="\" k="41" />
    <hkern u1="&#x155;" u2="&#x3f;" k="-20" />
    <hkern u1="&#x155;" u2="&#x2f;" k="113" />
    <hkern u1="&#x155;" u2="&#x2a;" k="-20" />
    <hkern u1="&#x156;" u2="&#x2122;" k="20" />
    <hkern u1="&#x156;" u2="&#xbf;" k="10" />
    <hkern u1="&#x156;" u2="&#xae;" k="10" />
    <hkern u1="&#x156;" u2="\" k="51" />
    <hkern u1="&#x156;" u2="V" k="51" />
    <hkern u1="&#x156;" u2="&#x40;" k="10" />
    <hkern u1="&#x156;" u2="&#x2a;" k="20" />
    <hkern u1="&#x156;" u2="&#x26;" k="10" />
    <hkern u1="&#x157;" u2="&#xbf;" k="20" />
    <hkern u1="&#x157;" u2="&#xae;" k="-31" />
    <hkern u1="&#x157;" u2="x" k="-20" />
    <hkern u1="&#x157;" u2="v" k="-41" />
    <hkern u1="&#x157;" u2="\" k="41" />
    <hkern u1="&#x157;" u2="&#x3f;" k="-20" />
    <hkern u1="&#x157;" u2="&#x2f;" k="113" />
    <hkern u1="&#x157;" u2="&#x2a;" k="-20" />
    <hkern u1="&#x158;" u2="&#x2122;" k="20" />
    <hkern u1="&#x158;" u2="&#xbf;" k="10" />
    <hkern u1="&#x158;" u2="&#xae;" k="10" />
    <hkern u1="&#x158;" u2="\" k="51" />
    <hkern u1="&#x158;" u2="V" k="51" />
    <hkern u1="&#x158;" u2="&#x40;" k="10" />
    <hkern u1="&#x158;" u2="&#x2a;" k="20" />
    <hkern u1="&#x158;" u2="&#x26;" k="10" />
    <hkern u1="&#x159;" u2="&#xbf;" k="20" />
    <hkern u1="&#x159;" u2="&#xae;" k="-31" />
    <hkern u1="&#x159;" u2="x" k="-20" />
    <hkern u1="&#x159;" u2="v" k="-41" />
    <hkern u1="&#x159;" u2="\" k="41" />
    <hkern u1="&#x159;" u2="&#x3f;" k="-20" />
    <hkern u1="&#x159;" u2="&#x2f;" k="113" />
    <hkern u1="&#x159;" u2="&#x2a;" k="-20" />
    <hkern u1="&#x15a;" u2="&#x2122;" k="49" />
    <hkern u1="&#x15a;" u2="x" k="37" />
    <hkern u1="&#x15a;" u2="v" k="6" />
    <hkern u1="&#x15a;" u2="\" k="92" />
    <hkern u1="&#x15a;" u2="X" k="41" />
    <hkern u1="&#x15a;" u2="V" k="78" />
    <hkern u1="&#x15a;" u2="&#x2f;" k="72" />
    <hkern u1="&#x15a;" u2="&#x2a;" k="20" />
    <hkern u1="&#x15b;" u2="&#x2122;" k="63" />
    <hkern u1="&#x15b;" u2="&#xae;" k="10" />
    <hkern u1="&#x15b;" u2="x" k="25" />
    <hkern u1="&#x15b;" u2="v" k="31" />
    <hkern u1="&#x15b;" u2="\" k="137" />
    <hkern u1="&#x15b;" u2="&#x3f;" k="31" />
    <hkern u1="&#x15b;" u2="&#x2f;" k="51" />
    <hkern u1="&#x15b;" u2="&#x2a;" k="31" />
    <hkern u1="&#x15c;" u2="&#x2122;" k="49" />
    <hkern u1="&#x15c;" u2="x" k="37" />
    <hkern u1="&#x15c;" u2="v" k="6" />
    <hkern u1="&#x15c;" u2="\" k="92" />
    <hkern u1="&#x15c;" u2="X" k="41" />
    <hkern u1="&#x15c;" u2="V" k="78" />
    <hkern u1="&#x15c;" u2="&#x2f;" k="72" />
    <hkern u1="&#x15c;" u2="&#x2a;" k="20" />
    <hkern u1="&#x15d;" u2="&#x2122;" k="63" />
    <hkern u1="&#x15d;" u2="&#xae;" k="10" />
    <hkern u1="&#x15d;" u2="x" k="25" />
    <hkern u1="&#x15d;" u2="v" k="31" />
    <hkern u1="&#x15d;" u2="\" k="137" />
    <hkern u1="&#x15d;" u2="&#x3f;" k="31" />
    <hkern u1="&#x15d;" u2="&#x2f;" k="51" />
    <hkern u1="&#x15d;" u2="&#x2a;" k="31" />
    <hkern u1="&#x15e;" u2="&#x2122;" k="49" />
    <hkern u1="&#x15e;" u2="x" k="37" />
    <hkern u1="&#x15e;" u2="v" k="6" />
    <hkern u1="&#x15e;" u2="\" k="92" />
    <hkern u1="&#x15e;" u2="X" k="41" />
    <hkern u1="&#x15e;" u2="V" k="78" />
    <hkern u1="&#x15e;" u2="&#x2f;" k="72" />
    <hkern u1="&#x15e;" u2="&#x2a;" k="20" />
    <hkern u1="&#x15f;" u2="&#x2122;" k="63" />
    <hkern u1="&#x15f;" u2="&#xae;" k="10" />
    <hkern u1="&#x15f;" u2="x" k="25" />
    <hkern u1="&#x15f;" u2="v" k="31" />
    <hkern u1="&#x15f;" u2="\" k="137" />
    <hkern u1="&#x15f;" u2="&#x3f;" k="31" />
    <hkern u1="&#x15f;" u2="&#x2f;" k="51" />
    <hkern u1="&#x15f;" u2="&#x2a;" k="31" />
    <hkern u1="&#x160;" u2="&#x2122;" k="49" />
    <hkern u1="&#x160;" u2="x" k="37" />
    <hkern u1="&#x160;" u2="v" k="6" />
    <hkern u1="&#x160;" u2="\" k="92" />
    <hkern u1="&#x160;" u2="X" k="41" />
    <hkern u1="&#x160;" u2="V" k="78" />
    <hkern u1="&#x160;" u2="&#x2f;" k="72" />
    <hkern u1="&#x160;" u2="&#x2a;" k="20" />
    <hkern u1="&#x161;" u2="&#x2122;" k="63" />
    <hkern u1="&#x161;" u2="&#xae;" k="10" />
    <hkern u1="&#x161;" u2="x" k="25" />
    <hkern u1="&#x161;" u2="v" k="31" />
    <hkern u1="&#x161;" u2="\" k="137" />
    <hkern u1="&#x161;" u2="&#x3f;" k="31" />
    <hkern u1="&#x161;" u2="&#x2f;" k="51" />
    <hkern u1="&#x161;" u2="&#x2a;" k="31" />
    <hkern u1="&#x162;" u2="&#x2122;" k="-18" />
    <hkern u1="&#x162;" u2="&#xbf;" k="102" />
    <hkern u1="&#x162;" u2="&#xae;" k="-10" />
    <hkern u1="&#x162;" u2="x" k="72" />
    <hkern u1="&#x162;" u2="v" k="51" />
    <hkern u1="&#x162;" u2="i" k="10" />
    <hkern u1="&#x162;" u2="V" k="-16" />
    <hkern u1="&#x162;" u2="&#x40;" k="72" />
    <hkern u1="&#x162;" u2="&#x3f;" k="-16" />
    <hkern u1="&#x162;" u2="&#x2f;" k="174" />
    <hkern u1="&#x162;" u2="&#x26;" k="18" />
    <hkern u1="&#x162;" u2="&#x21;" k="-10" />
    <hkern u1="&#x163;" u2="&#x2122;" k="16" />
    <hkern u1="&#x163;" u2="&#xae;" k="-18" />
    <hkern u1="&#x163;" u2="x" k="-31" />
    <hkern u1="&#x163;" u2="v" k="-20" />
    <hkern u1="&#x163;" u2="\" k="72" />
    <hkern u1="&#x163;" u2="&#x2f;" k="20" />
    <hkern u1="&#x164;" u2="&#x2122;" k="-18" />
    <hkern u1="&#x164;" u2="&#xbf;" k="102" />
    <hkern u1="&#x164;" u2="&#xae;" k="-10" />
    <hkern u1="&#x164;" u2="x" k="72" />
    <hkern u1="&#x164;" u2="v" k="51" />
    <hkern u1="&#x164;" u2="i" k="10" />
    <hkern u1="&#x164;" u2="V" k="-16" />
    <hkern u1="&#x164;" u2="&#x40;" k="72" />
    <hkern u1="&#x164;" u2="&#x3f;" k="-16" />
    <hkern u1="&#x164;" u2="&#x2f;" k="174" />
    <hkern u1="&#x164;" u2="&#x26;" k="18" />
    <hkern u1="&#x164;" u2="&#x21;" k="-10" />
    <hkern u1="&#x168;" u2="x" k="6" />
    <hkern u1="&#x168;" u2="X" k="20" />
    <hkern u1="&#x168;" u2="&#x2f;" k="61" />
    <hkern u1="&#x169;" u2="&#x2122;" k="18" />
    <hkern u1="&#x169;" u2="\" k="20" />
    <hkern u1="&#x16a;" u2="x" k="6" />
    <hkern u1="&#x16a;" u2="X" k="20" />
    <hkern u1="&#x16a;" u2="&#x2f;" k="61" />
    <hkern u1="&#x16b;" u2="&#x2122;" k="18" />
    <hkern u1="&#x16b;" u2="\" k="20" />
    <hkern u1="&#x16c;" u2="x" k="6" />
    <hkern u1="&#x16c;" u2="X" k="20" />
    <hkern u1="&#x16c;" u2="&#x2f;" k="61" />
    <hkern u1="&#x16d;" u2="&#x2122;" k="18" />
    <hkern u1="&#x16d;" u2="\" k="20" />
    <hkern u1="&#x16e;" u2="x" k="6" />
    <hkern u1="&#x16e;" u2="X" k="20" />
    <hkern u1="&#x16e;" u2="&#x2f;" k="61" />
    <hkern u1="&#x16f;" u2="&#x2122;" k="18" />
    <hkern u1="&#x16f;" u2="\" k="20" />
    <hkern u1="&#x170;" u2="x" k="6" />
    <hkern u1="&#x170;" u2="X" k="20" />
    <hkern u1="&#x170;" u2="&#x2f;" k="61" />
    <hkern u1="&#x171;" u2="&#x2122;" k="18" />
    <hkern u1="&#x171;" u2="\" k="20" />
    <hkern u1="&#x172;" u2="x" k="6" />
    <hkern u1="&#x172;" u2="X" k="20" />
    <hkern u1="&#x172;" u2="&#x2f;" k="61" />
    <hkern u1="&#x173;" u2="&#x2122;" k="18" />
    <hkern u1="&#x173;" u2="\" k="20" />
    <hkern u1="&#x174;" u2="&#x2122;" k="-37" />
    <hkern u1="&#x174;" u2="&#xbf;" k="66" />
    <hkern u1="&#x174;" u2="&#xa1;" k="20" />
    <hkern u1="&#x174;" u2="x" k="31" />
    <hkern u1="&#x174;" u2="v" k="6" />
    <hkern u1="&#x174;" u2="i" k="10" />
    <hkern u1="&#x174;" u2="\" k="-10" />
    <hkern u1="&#x174;" u2="V" k="-10" />
    <hkern u1="&#x174;" u2="&#x40;" k="51" />
    <hkern u1="&#x174;" u2="&#x2f;" k="133" />
    <hkern u1="&#x174;" u2="&#x2a;" k="18" />
    <hkern u1="&#x174;" u2="&#x26;" k="27" />
    <hkern u1="&#x175;" u2="&#xbf;" k="39" />
    <hkern u1="&#x175;" u2="&#xae;" k="-41" />
    <hkern u1="&#x175;" u2="v" k="-20" />
    <hkern u1="&#x175;" u2="\" k="10" />
    <hkern u1="&#x175;" u2="&#x3f;" k="-37" />
    <hkern u1="&#x175;" u2="&#x2f;" k="72" />
    <hkern u1="&#x175;" u2="&#x2a;" k="-10" />
    <hkern u1="&#x175;" u2="&#x26;" k="18" />
    <hkern u1="&#x176;" u2="&#x2122;" k="-47" />
    <hkern u1="&#x176;" u2="&#xbf;" k="162" />
    <hkern u1="&#x176;" u2="&#xae;" k="39" />
    <hkern u1="&#x176;" u2="&#xa1;" k="41" />
    <hkern u1="&#x176;" u2="x" k="92" />
    <hkern u1="&#x176;" u2="v" k="66" />
    <hkern u1="&#x176;" u2="i" k="16" />
    <hkern u1="&#x176;" u2="h" k="10" />
    <hkern u1="&#x176;" u2="&#x40;" k="141" />
    <hkern u1="&#x176;" u2="&#x3f;" k="16" />
    <hkern u1="&#x176;" u2="&#x2f;" k="225" />
    <hkern u1="&#x176;" u2="&#x2a;" k="37" />
    <hkern u1="&#x176;" u2="&#x26;" k="106" />
    <hkern u1="&#x177;" u2="&#xbf;" k="27" />
    <hkern u1="&#x177;" u2="&#xae;" k="-39" />
    <hkern u1="&#x177;" u2="x" k="-10" />
    <hkern u1="&#x177;" u2="v" k="-20" />
    <hkern u1="&#x177;" u2="\" k="20" />
    <hkern u1="&#x177;" u2="&#x40;" k="14" />
    <hkern u1="&#x177;" u2="&#x3f;" k="-51" />
    <hkern u1="&#x177;" u2="&#x2f;" k="61" />
    <hkern u1="&#x177;" u2="&#x2a;" k="-20" />
    <hkern u1="&#x178;" u2="&#x2122;" k="-47" />
    <hkern u1="&#x178;" u2="&#xbf;" k="162" />
    <hkern u1="&#x178;" u2="&#xae;" k="39" />
    <hkern u1="&#x178;" u2="&#xa1;" k="41" />
    <hkern u1="&#x178;" u2="x" k="92" />
    <hkern u1="&#x178;" u2="v" k="66" />
    <hkern u1="&#x178;" u2="i" k="16" />
    <hkern u1="&#x178;" u2="h" k="10" />
    <hkern u1="&#x178;" u2="&#x40;" k="141" />
    <hkern u1="&#x178;" u2="&#x3f;" k="16" />
    <hkern u1="&#x178;" u2="&#x2f;" k="225" />
    <hkern u1="&#x178;" u2="&#x2a;" k="37" />
    <hkern u1="&#x178;" u2="&#x26;" k="106" />
    <hkern u1="&#x179;" u2="&#x2122;" k="2" />
    <hkern u1="&#x179;" u2="&#xbf;" k="4" />
    <hkern u1="&#x179;" u2="&#xae;" k="33" />
    <hkern u1="&#x179;" u2="v" k="27" />
    <hkern u1="&#x179;" u2="&#x3f;" k="18" />
    <hkern u1="&#x179;" u2="&#x2a;" k="20" />
    <hkern u1="&#x17a;" u2="\" k="51" />
    <hkern u1="&#x17b;" u2="&#x2122;" k="2" />
    <hkern u1="&#x17b;" u2="&#xbf;" k="4" />
    <hkern u1="&#x17b;" u2="&#xae;" k="33" />
    <hkern u1="&#x17b;" u2="v" k="27" />
    <hkern u1="&#x17b;" u2="&#x3f;" k="18" />
    <hkern u1="&#x17b;" u2="&#x2a;" k="20" />
    <hkern u1="&#x17c;" u2="\" k="51" />
    <hkern u1="&#x17d;" u2="&#x2122;" k="2" />
    <hkern u1="&#x17d;" u2="&#xbf;" k="4" />
    <hkern u1="&#x17d;" u2="&#xae;" k="33" />
    <hkern u1="&#x17d;" u2="v" k="27" />
    <hkern u1="&#x17d;" u2="&#x3f;" k="18" />
    <hkern u1="&#x17d;" u2="&#x2a;" k="20" />
    <hkern u1="&#x17e;" u2="\" k="51" />
    <hkern u1="&#x1fa;" u2="&#x2122;" k="233" />
    <hkern u1="&#x1fa;" u2="&#xbf;" k="18" />
    <hkern u1="&#x1fa;" u2="&#xae;" k="195" />
    <hkern u1="&#x1fa;" u2="x" k="-16" />
    <hkern u1="&#x1fa;" u2="v" k="113" />
    <hkern u1="&#x1fa;" u2="\" k="154" />
    <hkern u1="&#x1fa;" u2="X" k="-10" />
    <hkern u1="&#x1fa;" u2="V" k="211" />
    <hkern u1="&#x1fa;" u2="&#x40;" k="10" />
    <hkern u1="&#x1fa;" u2="&#x3f;" k="143" />
    <hkern u1="&#x1fa;" u2="&#x2a;" k="205" />
    <hkern u1="&#x1fb;" u2="&#x2122;" k="119" />
    <hkern u1="&#x1fb;" u2="&#xae;" k="20" />
    <hkern u1="&#x1fb;" u2="v" k="20" />
    <hkern u1="&#x1fb;" u2="\" k="123" />
    <hkern u1="&#x1fb;" u2="&#x3f;" k="78" />
    <hkern u1="&#x1fb;" u2="&#x2a;" k="20" />
    <hkern u1="&#x1fc;" u2="&#x2122;" k="-16" />
    <hkern u1="&#x1fc;" u2="X" k="-10" />
    <hkern u1="&#x1fc;" u2="&#x2f;" k="-10" />
    <hkern u1="&#x1fd;" u2="&#x2122;" k="78" />
    <hkern u1="&#x1fd;" u2="&#xae;" k="18" />
    <hkern u1="&#x1fd;" u2="x" k="31" />
    <hkern u1="&#x1fd;" u2="v" k="20" />
    <hkern u1="&#x1fd;" u2="\" k="123" />
    <hkern u1="&#x1fd;" u2="&#x3f;" k="31" />
    <hkern u1="&#x1fd;" u2="&#x2f;" k="31" />
    <hkern u1="&#x1fd;" u2="&#x2a;" k="31" />
    <hkern u1="&#x1fe;" u2="&#x2122;" k="31" />
    <hkern u1="&#x1fe;" u2="x" k="29" />
    <hkern u1="&#x1fe;" u2="\" k="51" />
    <hkern u1="&#x1fe;" u2="X" k="82" />
    <hkern u1="&#x1fe;" u2="V" k="57" />
    <hkern u1="&#x1fe;" u2="&#x2f;" k="72" />
    <hkern u1="&#x1fe;" u2="&#x2a;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x2122;" k="61" />
    <hkern u1="&#x1ff;" u2="&#xae;" k="20" />
    <hkern u1="&#x1ff;" u2="x" k="53" />
    <hkern u1="&#x1ff;" u2="v" k="23" />
    <hkern u1="&#x1ff;" u2="\" k="123" />
    <hkern u1="&#x1ff;" u2="&#x3f;" k="31" />
    <hkern u1="&#x1ff;" u2="&#x2f;" k="61" />
    <hkern u1="&#x1ff;" u2="&#x2a;" k="31" />
    <hkern u1="&#x219;" u2="&#x2122;" k="63" />
    <hkern u1="&#x219;" u2="&#xae;" k="10" />
    <hkern u1="&#x219;" u2="x" k="25" />
    <hkern u1="&#x219;" u2="v" k="31" />
    <hkern u1="&#x219;" u2="\" k="137" />
    <hkern u1="&#x219;" u2="&#x3f;" k="31" />
    <hkern u1="&#x219;" u2="&#x2f;" k="51" />
    <hkern u1="&#x219;" u2="&#x2a;" k="31" />
    <hkern u1="&#x1e80;" u2="&#x2122;" k="-37" />
    <hkern u1="&#x1e80;" u2="&#xbf;" k="66" />
    <hkern u1="&#x1e80;" u2="&#xa1;" k="20" />
    <hkern u1="&#x1e80;" u2="x" k="31" />
    <hkern u1="&#x1e80;" u2="v" k="6" />
    <hkern u1="&#x1e80;" u2="i" k="10" />
    <hkern u1="&#x1e80;" u2="\" k="-10" />
    <hkern u1="&#x1e80;" u2="V" k="-10" />
    <hkern u1="&#x1e80;" u2="&#x40;" k="51" />
    <hkern u1="&#x1e80;" u2="&#x2f;" k="133" />
    <hkern u1="&#x1e80;" u2="&#x2a;" k="18" />
    <hkern u1="&#x1e80;" u2="&#x26;" k="27" />
    <hkern u1="&#x1e81;" u2="&#xbf;" k="39" />
    <hkern u1="&#x1e81;" u2="&#xae;" k="-41" />
    <hkern u1="&#x1e81;" u2="v" k="-20" />
    <hkern u1="&#x1e81;" u2="\" k="10" />
    <hkern u1="&#x1e81;" u2="&#x3f;" k="-37" />
    <hkern u1="&#x1e81;" u2="&#x2f;" k="72" />
    <hkern u1="&#x1e81;" u2="&#x2a;" k="-10" />
    <hkern u1="&#x1e81;" u2="&#x26;" k="18" />
    <hkern u1="&#x1e82;" u2="&#x2122;" k="-37" />
    <hkern u1="&#x1e82;" u2="&#xbf;" k="66" />
    <hkern u1="&#x1e82;" u2="&#xa1;" k="20" />
    <hkern u1="&#x1e82;" u2="x" k="31" />
    <hkern u1="&#x1e82;" u2="v" k="6" />
    <hkern u1="&#x1e82;" u2="i" k="10" />
    <hkern u1="&#x1e82;" u2="\" k="-10" />
    <hkern u1="&#x1e82;" u2="V" k="-10" />
    <hkern u1="&#x1e82;" u2="&#x40;" k="51" />
    <hkern u1="&#x1e82;" u2="&#x2f;" k="133" />
    <hkern u1="&#x1e82;" u2="&#x2a;" k="18" />
    <hkern u1="&#x1e82;" u2="&#x26;" k="27" />
    <hkern u1="&#x1e83;" u2="&#xbf;" k="39" />
    <hkern u1="&#x1e83;" u2="&#xae;" k="-41" />
    <hkern u1="&#x1e83;" u2="v" k="-20" />
    <hkern u1="&#x1e83;" u2="\" k="10" />
    <hkern u1="&#x1e83;" u2="&#x3f;" k="-37" />
    <hkern u1="&#x1e83;" u2="&#x2f;" k="72" />
    <hkern u1="&#x1e83;" u2="&#x2a;" k="-10" />
    <hkern u1="&#x1e83;" u2="&#x26;" k="18" />
    <hkern u1="&#x1e84;" u2="&#x2122;" k="-37" />
    <hkern u1="&#x1e84;" u2="&#xbf;" k="66" />
    <hkern u1="&#x1e84;" u2="&#xa1;" k="20" />
    <hkern u1="&#x1e84;" u2="x" k="31" />
    <hkern u1="&#x1e84;" u2="v" k="6" />
    <hkern u1="&#x1e84;" u2="i" k="10" />
    <hkern u1="&#x1e84;" u2="\" k="-10" />
    <hkern u1="&#x1e84;" u2="V" k="-10" />
    <hkern u1="&#x1e84;" u2="&#x40;" k="51" />
    <hkern u1="&#x1e84;" u2="&#x2f;" k="133" />
    <hkern u1="&#x1e84;" u2="&#x2a;" k="18" />
    <hkern u1="&#x1e84;" u2="&#x26;" k="27" />
    <hkern u1="&#x1e85;" u2="&#xbf;" k="39" />
    <hkern u1="&#x1e85;" u2="&#xae;" k="-41" />
    <hkern u1="&#x1e85;" u2="v" k="-20" />
    <hkern u1="&#x1e85;" u2="\" k="10" />
    <hkern u1="&#x1e85;" u2="&#x3f;" k="-37" />
    <hkern u1="&#x1e85;" u2="&#x2f;" k="72" />
    <hkern u1="&#x1e85;" u2="&#x2a;" k="-10" />
    <hkern u1="&#x1e85;" u2="&#x26;" k="18" />
    <hkern u1="&#x1ef2;" u2="&#x2122;" k="-47" />
    <hkern u1="&#x1ef2;" u2="&#xbf;" k="162" />
    <hkern u1="&#x1ef2;" u2="&#xae;" k="39" />
    <hkern u1="&#x1ef2;" u2="&#xa1;" k="41" />
    <hkern u1="&#x1ef2;" u2="x" k="92" />
    <hkern u1="&#x1ef2;" u2="v" k="66" />
    <hkern u1="&#x1ef2;" u2="i" k="16" />
    <hkern u1="&#x1ef2;" u2="h" k="10" />
    <hkern u1="&#x1ef2;" u2="&#x40;" k="141" />
    <hkern u1="&#x1ef2;" u2="&#x3f;" k="16" />
    <hkern u1="&#x1ef2;" u2="&#x2f;" k="225" />
    <hkern u1="&#x1ef2;" u2="&#x2a;" k="37" />
    <hkern u1="&#x1ef2;" u2="&#x26;" k="106" />
    <hkern u1="&#x1ef3;" u2="&#xbf;" k="27" />
    <hkern u1="&#x1ef3;" u2="&#xae;" k="-39" />
    <hkern u1="&#x1ef3;" u2="x" k="-10" />
    <hkern u1="&#x1ef3;" u2="v" k="-20" />
    <hkern u1="&#x1ef3;" u2="\" k="20" />
    <hkern u1="&#x1ef3;" u2="&#x40;" k="14" />
    <hkern u1="&#x1ef3;" u2="&#x3f;" k="-51" />
    <hkern u1="&#x1ef3;" u2="&#x2f;" k="61" />
    <hkern u1="&#x1ef3;" u2="&#x2a;" k="-20" />
    <hkern u1="&#x2013;" u2="&#x2122;" k="39" />
    <hkern u1="&#x2013;" u2="&#xa5;" k="20" />
    <hkern u1="&#x2013;" u2="x" k="82" />
    <hkern u1="&#x2013;" u2="v" k="20" />
    <hkern u1="&#x2013;" u2="\" k="113" />
    <hkern u1="&#x2013;" u2="X" k="174" />
    <hkern u1="&#x2013;" u2="V" k="113" />
    <hkern u1="&#x2013;" u2="&#x3f;" k="20" />
    <hkern u1="&#x2013;" u2="&#x39;" k="14" />
    <hkern u1="&#x2013;" u2="&#x37;" k="72" />
    <hkern u1="&#x2013;" u2="&#x33;" k="10" />
    <hkern u1="&#x2013;" u2="&#x32;" k="20" />
    <hkern u1="&#x2013;" u2="&#x31;" k="31" />
    <hkern u1="&#x2013;" u2="&#x2f;" k="133" />
    <hkern u1="&#x2013;" u2="&#x2a;" k="20" />
    <hkern u1="&#x2014;" u2="&#x2122;" k="39" />
    <hkern u1="&#x2014;" u2="&#xa5;" k="20" />
    <hkern u1="&#x2014;" u2="x" k="82" />
    <hkern u1="&#x2014;" u2="v" k="20" />
    <hkern u1="&#x2014;" u2="\" k="113" />
    <hkern u1="&#x2014;" u2="X" k="174" />
    <hkern u1="&#x2014;" u2="V" k="113" />
    <hkern u1="&#x2014;" u2="&#x3f;" k="20" />
    <hkern u1="&#x2014;" u2="&#x39;" k="14" />
    <hkern u1="&#x2014;" u2="&#x37;" k="72" />
    <hkern u1="&#x2014;" u2="&#x33;" k="10" />
    <hkern u1="&#x2014;" u2="&#x32;" k="20" />
    <hkern u1="&#x2014;" u2="&#x31;" k="31" />
    <hkern u1="&#x2014;" u2="&#x2f;" k="133" />
    <hkern u1="&#x2014;" u2="&#x2a;" k="20" />
    <hkern u1="&#x2018;" u2="&#x2122;" k="-20" />
    <hkern u1="&#x2018;" u2="&#x20ac;" k="31" />
    <hkern u1="&#x2018;" u2="&#xbf;" k="162" />
    <hkern u1="&#x2018;" u2="&#xae;" k="10" />
    <hkern u1="&#x2018;" u2="&#xa3;" k="92" />
    <hkern u1="&#x2018;" u2="&#xa2;" k="41" />
    <hkern u1="&#x2018;" u2="x" k="10" />
    <hkern u1="&#x2018;" u2="v" k="-10" />
    <hkern u1="&#x2018;" u2="i" k="10" />
    <hkern u1="&#x2018;" u2="\" k="-20" />
    <hkern u1="&#x2018;" u2="X" k="20" />
    <hkern u1="&#x2018;" u2="V" k="-31" />
    <hkern u1="&#x2018;" u2="&#x40;" k="72" />
    <hkern u1="&#x2018;" u2="&#x39;" k="10" />
    <hkern u1="&#x2018;" u2="&#x38;" k="51" />
    <hkern u1="&#x2018;" u2="&#x37;" k="-10" />
    <hkern u1="&#x2018;" u2="&#x36;" k="123" />
    <hkern u1="&#x2018;" u2="&#x35;" k="82" />
    <hkern u1="&#x2018;" u2="&#x34;" k="154" />
    <hkern u1="&#x2018;" u2="&#x33;" k="31" />
    <hkern u1="&#x2018;" u2="&#x32;" k="31" />
    <hkern u1="&#x2018;" u2="&#x30;" k="41" />
    <hkern u1="&#x2018;" u2="&#x2f;" k="225" />
    <hkern u1="&#x2018;" u2="&#x26;" k="82" />
    <hkern u1="&#x2018;" u2="&#x24;" k="20" />
    <hkern u1="&#x2018;" u2="&#x23;" k="20" />
    <hkern u1="&#x2019;" u2="&#x2122;" k="-20" />
    <hkern u1="&#x2019;" u2="&#x20ac;" k="61" />
    <hkern u1="&#x2019;" u2="&#xbf;" k="184" />
    <hkern u1="&#x2019;" u2="&#xae;" k="10" />
    <hkern u1="&#x2019;" u2="&#xa3;" k="123" />
    <hkern u1="&#x2019;" u2="&#xa2;" k="82" />
    <hkern u1="&#x2019;" u2="x" k="51" />
    <hkern u1="&#x2019;" u2="v" k="10" />
    <hkern u1="&#x2019;" u2="j" k="10" />
    <hkern u1="&#x2019;" u2="i" k="10" />
    <hkern u1="&#x2019;" u2="X" k="20" />
    <hkern u1="&#x2019;" u2="V" k="-31" />
    <hkern u1="&#x2019;" u2="&#x40;" k="102" />
    <hkern u1="&#x2019;" u2="&#x39;" k="20" />
    <hkern u1="&#x2019;" u2="&#x38;" k="72" />
    <hkern u1="&#x2019;" u2="&#x36;" k="164" />
    <hkern u1="&#x2019;" u2="&#x35;" k="92" />
    <hkern u1="&#x2019;" u2="&#x34;" k="174" />
    <hkern u1="&#x2019;" u2="&#x33;" k="41" />
    <hkern u1="&#x2019;" u2="&#x32;" k="61" />
    <hkern u1="&#x2019;" u2="&#x30;" k="72" />
    <hkern u1="&#x2019;" u2="&#x2f;" k="256" />
    <hkern u1="&#x2019;" u2="&#x2a;" k="25" />
    <hkern u1="&#x2019;" u2="&#x26;" k="123" />
    <hkern u1="&#x2019;" u2="&#x24;" k="31" />
    <hkern u1="&#x2019;" u2="&#x23;" k="61" />
    <hkern u1="&#x201a;" u2="&#x20ac;" k="20" />
    <hkern u1="&#x201a;" u2="&#xbf;" k="4" />
    <hkern u1="&#x201a;" u2="&#xa5;" k="41" />
    <hkern u1="&#x201a;" u2="&#xa2;" k="31" />
    <hkern u1="&#x201a;" u2="x" k="20" />
    <hkern u1="&#x201a;" u2="v" k="102" />
    <hkern u1="&#x201a;" u2="j" k="10" />
    <hkern u1="&#x201a;" u2="\" k="174" />
    <hkern u1="&#x201a;" u2="X" k="41" />
    <hkern u1="&#x201a;" u2="V" k="215" />
    <hkern u1="&#x201a;" u2="&#x3f;" k="113" />
    <hkern u1="&#x201a;" u2="&#x39;" k="135" />
    <hkern u1="&#x201a;" u2="&#x38;" k="10" />
    <hkern u1="&#x201a;" u2="&#x37;" k="31" />
    <hkern u1="&#x201a;" u2="&#x31;" k="113" />
    <hkern u1="&#x201a;" u2="&#x30;" k="10" />
    <hkern u1="&#x201a;" u2="&#x2a;" k="205" />
    <hkern u1="&#x201a;" u2="&#x23;" k="4" />
    <hkern u1="&#x201a;" u2="&#x21;" k="16" />
    <hkern u1="&#x201c;" u2="&#x2122;" k="-20" />
    <hkern u1="&#x201c;" u2="&#x20ac;" k="31" />
    <hkern u1="&#x201c;" u2="&#xbf;" k="162" />
    <hkern u1="&#x201c;" u2="&#xae;" k="10" />
    <hkern u1="&#x201c;" u2="&#xa3;" k="92" />
    <hkern u1="&#x201c;" u2="&#xa2;" k="41" />
    <hkern u1="&#x201c;" u2="x" k="10" />
    <hkern u1="&#x201c;" u2="v" k="-10" />
    <hkern u1="&#x201c;" u2="i" k="10" />
    <hkern u1="&#x201c;" u2="\" k="-20" />
    <hkern u1="&#x201c;" u2="X" k="20" />
    <hkern u1="&#x201c;" u2="V" k="-31" />
    <hkern u1="&#x201c;" u2="&#x40;" k="72" />
    <hkern u1="&#x201c;" u2="&#x39;" k="10" />
    <hkern u1="&#x201c;" u2="&#x38;" k="51" />
    <hkern u1="&#x201c;" u2="&#x37;" k="-10" />
    <hkern u1="&#x201c;" u2="&#x36;" k="123" />
    <hkern u1="&#x201c;" u2="&#x35;" k="82" />
    <hkern u1="&#x201c;" u2="&#x34;" k="154" />
    <hkern u1="&#x201c;" u2="&#x33;" k="31" />
    <hkern u1="&#x201c;" u2="&#x32;" k="31" />
    <hkern u1="&#x201c;" u2="&#x30;" k="41" />
    <hkern u1="&#x201c;" u2="&#x2f;" k="225" />
    <hkern u1="&#x201c;" u2="&#x26;" k="82" />
    <hkern u1="&#x201c;" u2="&#x24;" k="20" />
    <hkern u1="&#x201c;" u2="&#x23;" k="20" />
    <hkern u1="&#x201d;" u2="&#x2122;" k="-20" />
    <hkern u1="&#x201d;" u2="&#x20ac;" k="61" />
    <hkern u1="&#x201d;" u2="&#xbf;" k="184" />
    <hkern u1="&#x201d;" u2="&#xae;" k="10" />
    <hkern u1="&#x201d;" u2="&#xa3;" k="123" />
    <hkern u1="&#x201d;" u2="&#xa2;" k="82" />
    <hkern u1="&#x201d;" u2="x" k="51" />
    <hkern u1="&#x201d;" u2="v" k="10" />
    <hkern u1="&#x201d;" u2="j" k="10" />
    <hkern u1="&#x201d;" u2="i" k="10" />
    <hkern u1="&#x201d;" u2="X" k="20" />
    <hkern u1="&#x201d;" u2="V" k="-31" />
    <hkern u1="&#x201d;" u2="&#x40;" k="102" />
    <hkern u1="&#x201d;" u2="&#x39;" k="20" />
    <hkern u1="&#x201d;" u2="&#x38;" k="72" />
    <hkern u1="&#x201d;" u2="&#x36;" k="164" />
    <hkern u1="&#x201d;" u2="&#x35;" k="92" />
    <hkern u1="&#x201d;" u2="&#x34;" k="174" />
    <hkern u1="&#x201d;" u2="&#x33;" k="41" />
    <hkern u1="&#x201d;" u2="&#x32;" k="61" />
    <hkern u1="&#x201d;" u2="&#x30;" k="72" />
    <hkern u1="&#x201d;" u2="&#x2f;" k="256" />
    <hkern u1="&#x201d;" u2="&#x2a;" k="25" />
    <hkern u1="&#x201d;" u2="&#x26;" k="123" />
    <hkern u1="&#x201d;" u2="&#x24;" k="31" />
    <hkern u1="&#x201d;" u2="&#x23;" k="61" />
    <hkern u1="&#x201e;" u2="&#x20ac;" k="20" />
    <hkern u1="&#x201e;" u2="&#xbf;" k="4" />
    <hkern u1="&#x201e;" u2="&#xa5;" k="41" />
    <hkern u1="&#x201e;" u2="&#xa2;" k="31" />
    <hkern u1="&#x201e;" u2="x" k="20" />
    <hkern u1="&#x201e;" u2="v" k="102" />
    <hkern u1="&#x201e;" u2="j" k="10" />
    <hkern u1="&#x201e;" u2="\" k="174" />
    <hkern u1="&#x201e;" u2="X" k="41" />
    <hkern u1="&#x201e;" u2="V" k="215" />
    <hkern u1="&#x201e;" u2="&#x3f;" k="113" />
    <hkern u1="&#x201e;" u2="&#x39;" k="135" />
    <hkern u1="&#x201e;" u2="&#x38;" k="10" />
    <hkern u1="&#x201e;" u2="&#x37;" k="31" />
    <hkern u1="&#x201e;" u2="&#x31;" k="113" />
    <hkern u1="&#x201e;" u2="&#x30;" k="10" />
    <hkern u1="&#x201e;" u2="&#x2a;" k="205" />
    <hkern u1="&#x201e;" u2="&#x23;" k="4" />
    <hkern u1="&#x201e;" u2="&#x21;" k="16" />
    <hkern u1="&#x2026;" u2="&#x2122;" k="213" />
    <hkern u1="&#x2026;" u2="&#x20ac;" k="57" />
    <hkern u1="&#x2026;" u2="&#xae;" k="152" />
    <hkern u1="&#x2026;" u2="&#xa5;" k="51" />
    <hkern u1="&#x2026;" u2="&#xa2;" k="61" />
    <hkern u1="&#x2026;" u2="x" k="20" />
    <hkern u1="&#x2026;" u2="v" k="164" />
    <hkern u1="&#x2026;" u2="j" k="10" />
    <hkern u1="&#x2026;" u2="\" k="225" />
    <hkern u1="&#x2026;" u2="X" k="31" />
    <hkern u1="&#x2026;" u2="V" k="287" />
    <hkern u1="&#x2026;" u2="&#x40;" k="10" />
    <hkern u1="&#x2026;" u2="&#x3f;" k="92" />
    <hkern u1="&#x2026;" u2="&#x39;" k="152" />
    <hkern u1="&#x2026;" u2="&#x37;" k="41" />
    <hkern u1="&#x2026;" u2="&#x36;" k="10" />
    <hkern u1="&#x2026;" u2="&#x35;" k="10" />
    <hkern u1="&#x2026;" u2="&#x34;" k="10" />
    <hkern u1="&#x2026;" u2="&#x31;" k="123" />
    <hkern u1="&#x2026;" u2="&#x30;" k="10" />
    <hkern u1="&#x2026;" u2="&#x2f;" k="20" />
    <hkern u1="&#x2026;" u2="&#x2a;" k="164" />
    <hkern u1="&#x2026;" u2="&#x26;" k="10" />
    <hkern u1="&#x2026;" u2="&#x23;" k="20" />
    <hkern u1="&#x2030;" u2="&#x2122;" k="143" />
    <hkern u1="&#x2030;" u2="&#xbf;" k="-23" />
    <hkern u1="&#x2030;" u2="&#xae;" k="57" />
    <hkern u1="&#x2030;" u2="\" k="154" />
    <hkern u1="&#x2030;" u2="&#x3f;" k="37" />
    <hkern u1="&#x2030;" u2="&#x39;" k="4" />
    <hkern u1="&#x2030;" u2="&#x38;" k="-16" />
    <hkern u1="&#x2030;" u2="&#x37;" k="-16" />
    <hkern u1="&#x2030;" u2="&#x36;" k="-31" />
    <hkern u1="&#x2030;" u2="&#x35;" k="-31" />
    <hkern u1="&#x2030;" u2="&#x34;" k="-31" />
    <hkern u1="&#x2030;" u2="&#x33;" k="-31" />
    <hkern u1="&#x2030;" u2="&#x32;" k="-31" />
    <hkern u1="&#x2030;" u2="&#x31;" k="20" />
    <hkern u1="&#x2030;" u2="&#x30;" k="-20" />
    <hkern u1="&#x2030;" u2="&#x2f;" k="20" />
    <hkern u1="&#x2030;" u2="&#x2a;" k="70" />
    <hkern u1="&#x2039;" u2="&#x20ac;" k="20" />
    <hkern u1="&#x2039;" u2="&#xa5;" k="41" />
    <hkern u1="&#x2039;" u2="&#xa2;" k="8" />
    <hkern u1="&#x2039;" u2="v" k="-10" />
    <hkern u1="&#x2039;" u2="j" k="10" />
    <hkern u1="&#x2039;" u2="\" k="102" />
    <hkern u1="&#x2039;" u2="X" k="37" />
    <hkern u1="&#x2039;" u2="V" k="96" />
    <hkern u1="&#x2039;" u2="&#x39;" k="8" />
    <hkern u1="&#x2039;" u2="&#x38;" k="27" />
    <hkern u1="&#x2039;" u2="&#x37;" k="18" />
    <hkern u1="&#x2039;" u2="&#x36;" k="27" />
    <hkern u1="&#x2039;" u2="&#x35;" k="35" />
    <hkern u1="&#x2039;" u2="&#x34;" k="18" />
    <hkern u1="&#x2039;" u2="&#x33;" k="10" />
    <hkern u1="&#x2039;" u2="&#x2f;" k="41" />
    <hkern u1="&#x2039;" u2="&#x2a;" k="18" />
    <hkern u1="&#x2039;" u2="&#x26;" k="16" />
    <hkern u1="&#x2039;" u2="&#x23;" k="18" />
    <hkern u1="&#x203a;" u2="&#xa5;" k="29" />
    <hkern u1="&#x203a;" u2="x" k="82" />
    <hkern u1="&#x203a;" u2="v" k="20" />
    <hkern u1="&#x203a;" u2="\" k="119" />
    <hkern u1="&#x203a;" u2="X" k="106" />
    <hkern u1="&#x203a;" u2="V" k="113" />
    <hkern u1="&#x203a;" u2="&#x3f;" k="78" />
    <hkern u1="&#x203a;" u2="&#x39;" k="41" />
    <hkern u1="&#x203a;" u2="&#x37;" k="72" />
    <hkern u1="&#x203a;" u2="&#x36;" k="-10" />
    <hkern u1="&#x203a;" u2="&#x34;" k="-31" />
    <hkern u1="&#x203a;" u2="&#x31;" k="20" />
    <hkern u1="&#x203a;" u2="&#x2f;" k="41" />
    <hkern u1="&#x203a;" u2="&#x2a;" k="41" />
    <hkern u1="&#x203a;" u2="&#x23;" k="-37" />
    <hkern u1="&#x203a;" u2="&#x21;" k="16" />
    <hkern u1="&#x20ac;" u2="&#x203a;" k="16" />
    <hkern u1="&#x20ac;" u2="&#x201e;" k="10" />
    <hkern u1="&#x20ac;" u2="&#x201a;" k="10" />
    <hkern u1="&#x20ac;" u2="&#x2014;" k="10" />
    <hkern u1="&#x20ac;" u2="&#x2013;" k="10" />
    <hkern u1="&#x20ac;" u2="&#xbb;" k="16" />
    <hkern u1="&#x20ac;" u2="&#x7d;" k="27" />
    <hkern u1="&#x20ac;" u2="]" k="27" />
    <hkern u1="&#x20ac;" u2="&#x2d;" k="10" />
    <hkern u1="&#x20ac;" u2="&#x2c;" k="10" />
    <hkern u1="&#x20ac;" u2="&#x29;" k="27" />
    <hkern u1="&#x20ac;" u2="\" k="51" />
    <hkern u1="&#x20ac;" u2="&#x31;" k="-20" />
    <hkern u1="&#x20ac;" u2="&#x2f;" k="72" />
    <hkern u1="&#x2117;" u2="&#xa5;" k="20" />
    <hkern u1="&#x2117;" u2="x" k="20" />
    <hkern u1="&#x2117;" u2="\" k="92" />
    <hkern u1="&#x2117;" u2="X" k="72" />
    <hkern u1="&#x2117;" u2="V" k="72" />
    <hkern u1="&#x2117;" u2="&#x37;" k="20" />
    <hkern u1="&#x2117;" u2="&#x2f;" k="96" />
    <hkern u1="&#x2122;" g2="Jcircumflex.salt" k="41" />
    <hkern u1="&#x2122;" g2="J.salt" k="41" />
    <hkern u1="&#x2122;" u2="&#xfb02;" k="-39" />
    <hkern u1="&#x2122;" u2="&#xfb01;" k="-39" />
    <hkern u1="&#x2122;" u2="&#x2030;" k="-39" />
    <hkern u1="&#x2122;" u2="&#x2026;" k="57" />
    <hkern u1="&#x2122;" u2="&#x1ef3;" k="-39" />
    <hkern u1="&#x2122;" u2="&#x1ef2;" k="-18" />
    <hkern u1="&#x2122;" u2="&#x1e85;" k="-39" />
    <hkern u1="&#x2122;" u2="&#x1e84;" k="-20" />
    <hkern u1="&#x2122;" u2="&#x1e83;" k="-39" />
    <hkern u1="&#x2122;" u2="&#x1e82;" k="-20" />
    <hkern u1="&#x2122;" u2="&#x1e81;" k="-39" />
    <hkern u1="&#x2122;" u2="&#x1e80;" k="-20" />
    <hkern u1="&#x2122;" u2="&#x1fd;" k="39" />
    <hkern u1="&#x2122;" u2="&#x1fb;" k="39" />
    <hkern u1="&#x2122;" u2="&#x1fa;" k="158" />
    <hkern u1="&#x2122;" u2="&#x17d;" k="4" />
    <hkern u1="&#x2122;" u2="&#x17b;" k="4" />
    <hkern u1="&#x2122;" u2="&#x179;" k="4" />
    <hkern u1="&#x2122;" u2="&#x178;" k="-18" />
    <hkern u1="&#x2122;" u2="&#x177;" k="-39" />
    <hkern u1="&#x2122;" u2="&#x176;" k="-18" />
    <hkern u1="&#x2122;" u2="&#x175;" k="-39" />
    <hkern u1="&#x2122;" u2="&#x174;" k="-20" />
    <hkern u1="&#x2122;" u2="&#x164;" k="-18" />
    <hkern u1="&#x2122;" u2="&#x162;" k="-18" />
    <hkern u1="&#x2122;" u2="&#x134;" k="334" />
    <hkern u1="&#x2122;" u2="&#x105;" k="39" />
    <hkern u1="&#x2122;" u2="&#x104;" k="158" />
    <hkern u1="&#x2122;" u2="&#x103;" k="39" />
    <hkern u1="&#x2122;" u2="&#x102;" k="158" />
    <hkern u1="&#x2122;" u2="&#x101;" k="39" />
    <hkern u1="&#x2122;" u2="&#x100;" k="158" />
    <hkern u1="&#x2122;" u2="&#xff;" k="-39" />
    <hkern u1="&#x2122;" u2="&#xfd;" k="-39" />
    <hkern u1="&#x2122;" u2="&#xe6;" k="39" />
    <hkern u1="&#x2122;" u2="&#xe5;" k="39" />
    <hkern u1="&#x2122;" u2="&#xe4;" k="39" />
    <hkern u1="&#x2122;" u2="&#xe3;" k="39" />
    <hkern u1="&#x2122;" u2="&#xe2;" k="39" />
    <hkern u1="&#x2122;" u2="&#xe1;" k="39" />
    <hkern u1="&#x2122;" u2="&#xe0;" k="39" />
    <hkern u1="&#x2122;" u2="&#xdd;" k="-18" />
    <hkern u1="&#x2122;" u2="&#xc5;" k="158" />
    <hkern u1="&#x2122;" u2="&#xc4;" k="158" />
    <hkern u1="&#x2122;" u2="&#xc3;" k="158" />
    <hkern u1="&#x2122;" u2="&#xc2;" k="158" />
    <hkern u1="&#x2122;" u2="&#xc1;" k="158" />
    <hkern u1="&#x2122;" u2="&#xc0;" k="158" />
    <hkern u1="&#x2122;" u2="y" k="-39" />
    <hkern u1="&#x2122;" u2="w" k="-39" />
    <hkern u1="&#x2122;" u2="f" k="-39" />
    <hkern u1="&#x2122;" u2="a" k="39" />
    <hkern u1="&#x2122;" u2="Z" k="4" />
    <hkern u1="&#x2122;" u2="Y" k="-18" />
    <hkern u1="&#x2122;" u2="W" k="-20" />
    <hkern u1="&#x2122;" u2="T" k="-18" />
    <hkern u1="&#x2122;" u2="J" k="334" />
    <hkern u1="&#x2122;" u2="A" k="158" />
    <hkern u1="&#x2122;" u2="&#x2e;" k="57" />
    <hkern u1="&#x2122;" u2="&#x25;" k="-39" />
    <hkern u1="&#x2122;" u2="x" k="-39" />
    <hkern u1="&#x2122;" u2="v" k="-59" />
    <hkern u1="&#x2122;" u2="X" k="20" />
    <hkern u1="&#x2122;" u2="V" k="-20" />
    <hkern u1="&#x2122;" u2="&#x36;" k="39" />
    <hkern u1="&#x2122;" u2="&#x35;" k="20" />
    <hkern u1="&#x2122;" u2="&#x34;" k="27" />
    <hkern u1="&#x2122;" u2="&#x31;" k="-39" />
    <hkern u1="&#x2122;" u2="&#x2f;" k="219" />
    <hkern g1="J.salt" u2="&#x2f;" k="41" />
    <hkern g1="IJ.salt" u2="&#x2f;" k="41" />
    <hkern g1="Jcircumflex.salt" u2="&#x2f;" k="41" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="parenright,bracketright,braceright"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="ordfeminine,ordmasculine"
	k="72" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="hyphen,endash,emdash"
	k="92" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="J.salt,Jcircumflex.salt"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="napostrophe,quoteright,quotedblright"
	k="246" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="copyright,published"
	k="49" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="quoteleft,quotedblleft"
	k="225" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="guillemotright,guilsinglright"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="J,Jcircumflex"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="68" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="33" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="78" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="T,Tcommaaccent,Tcaron"
	k="154" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="143" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="215" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="31" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="f,fi,fl"
	k="43" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="guillemotleft,guilsinglleft"
	k="51" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="41" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="41" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="t,tcommaaccent"
	k="86" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="72" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="113" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="parenright,bracketright,braceright"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="ordfeminine,ordmasculine"
	k="-20" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="61" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="quoteleft,quotedblleft"
	k="-10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="T,Tcommaaccent,Tcaron"
	k="31" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="25" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="66" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="f,fi,fl"
	k="-8" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="period,ellipsis"
	k="49" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="comma,quotesinglbase,quotedblbase"
	k="51" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,AEacute"
	g2="parenright,bracketright,braceright"
	k="-10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,AEacute"
	g2="napostrophe,quoteright,quotedblright"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,AEacute"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,AEacute"
	g2="quoteleft,quotedblleft"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,AEacute"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,AEacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="14" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,AEacute"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,AEacute"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,AEacute"
	g2="comma,quotesinglbase,quotedblbase"
	k="-10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="parenright,bracketright,braceright"
	k="31" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="hyphen,endash,emdash"
	k="-16" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="napostrophe,quoteright,quotedblright"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="66" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="quoteleft,quotedblleft"
	k="2" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="-10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="T,Tcommaaccent,Tcaron"
	k="31" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="82" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="6" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="period,ellipsis"
	k="51" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="comma,quotesinglbase,quotedblbase"
	k="72" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="J,IJ,Jcircumflex,J.salt,IJ.salt,Jcircumflex.salt"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="61" />
    <hkern g1="J,IJ,Jcircumflex,J.salt,IJ.salt,Jcircumflex.salt"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="J,IJ,Jcircumflex,J.salt,IJ.salt,Jcircumflex.salt"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="J,IJ,Jcircumflex,J.salt,IJ.salt,Jcircumflex.salt"
	g2="period,ellipsis"
	k="41" />
    <hkern g1="J,IJ,Jcircumflex,J.salt,IJ.salt,Jcircumflex.salt"
	g2="comma,quotesinglbase,quotedblbase"
	k="82" />
    <hkern g1="K,Kcommaaccent"
	g2="ordfeminine,ordmasculine"
	k="51" />
    <hkern g1="K,Kcommaaccent"
	g2="hyphen,endash,emdash"
	k="164" />
    <hkern g1="K,Kcommaaccent"
	g2="J.salt,Jcircumflex.salt"
	k="41" />
    <hkern g1="K,Kcommaaccent"
	g2="napostrophe,quoteright,quotedblright"
	k="61" />
    <hkern g1="K,Kcommaaccent"
	g2="copyright,published"
	k="72" />
    <hkern g1="K,Kcommaaccent"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-31" />
    <hkern g1="K,Kcommaaccent"
	g2="quoteleft,quotedblleft"
	k="92" />
    <hkern g1="K,Kcommaaccent"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="K,Kcommaaccent"
	g2="J,Jcircumflex"
	k="51" />
    <hkern g1="K,Kcommaaccent"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="61" />
    <hkern g1="K,Kcommaaccent"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="37" />
    <hkern g1="K,Kcommaaccent"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="K,Kcommaaccent"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="41" />
    <hkern g1="K,Kcommaaccent"
	g2="f,fi,fl"
	k="20" />
    <hkern g1="K,Kcommaaccent"
	g2="guillemotleft,guilsinglleft"
	k="82" />
    <hkern g1="K,Kcommaaccent"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="57" />
    <hkern g1="K,Kcommaaccent"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="31" />
    <hkern g1="K,Kcommaaccent"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="49" />
    <hkern g1="K,Kcommaaccent"
	g2="t,tcommaaccent"
	k="51" />
    <hkern g1="K,Kcommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="68" />
    <hkern g1="K,Kcommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="82" />
    <hkern g1="K,Kcommaaccent"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="K,Kcommaaccent"
	g2="period,ellipsis"
	k="10" />
    <hkern g1="K,Kcommaaccent"
	g2="comma,quotesinglbase,quotedblbase"
	k="-10" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="parenright,bracketright,braceright"
	k="10" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="ordfeminine,ordmasculine"
	k="92" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="hyphen,endash,emdash"
	k="276" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="J.salt,Jcircumflex.salt"
	k="6" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="napostrophe,quoteright,quotedblright"
	k="451" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="copyright,published"
	k="33" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-20" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="quoteleft,quotedblleft"
	k="481" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="J,Jcircumflex"
	k="6" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="51" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="20" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="61" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="T,Tcommaaccent,Tcaron"
	k="184" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="123" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="195" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="f,fi,fl"
	k="20" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="guillemotleft,guilsinglleft"
	k="31" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="10" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="t,tcommaaccent"
	k="61" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="61" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="113" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="L,Lacute,Lcommaaccent"
	g2="comma,quotesinglbase,quotedblbase"
	k="-16" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="parenright,bracketright,braceright"
	k="31" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="68" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="guillemotright,guilsinglright"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="T,Tcommaaccent,Tcaron"
	k="41" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="31" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="86" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="14" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="guillemotleft,guilsinglleft"
	k="-10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="period,ellipsis"
	k="51" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="comma,quotesinglbase,quotedblbase"
	k="72" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="parenright,bracketright,braceright"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="hyphen,endash,emdash"
	k="35" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="J.salt,Jcircumflex.salt"
	k="20" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="napostrophe,quoteright,quotedblright"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-6" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="quoteleft,quotedblleft"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="J,Jcircumflex"
	k="31" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="T,Tcommaaccent,Tcaron"
	k="45" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="25" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="61" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="31" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="guillemotleft,guilsinglleft"
	k="20" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="20" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="period,ellipsis"
	k="41" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="parenright,bracketright,braceright"
	k="41" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="napostrophe,quoteright,quotedblright"
	k="51" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="41" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="quoteleft,quotedblleft"
	k="41" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="guillemotright,guilsinglright"
	k="18" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="T,Tcommaaccent,Tcaron"
	k="55" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="41" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="82" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="f,fi,fl"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="t,tcommaaccent"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="6" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="14" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron"
	g2="comma,quotesinglbase,quotedblbase"
	k="41" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="hyphen,endash,emdash"
	k="205" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="J.salt,Jcircumflex.salt"
	k="72" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="copyright,published"
	k="41" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="154" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="quoteleft,quotedblleft"
	k="20" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="guillemotright,guilsinglright"
	k="129" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="J,Jcircumflex"
	k="205" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="41" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="14" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-25" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-20" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="195" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="f,fi,fl"
	k="31" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="guillemotleft,guilsinglleft"
	k="154" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="174" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="143" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="82" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="t,tcommaaccent"
	k="31" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="51" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="51" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="colon,semicolon"
	k="225" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="period,ellipsis"
	k="287" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="comma,quotesinglbase,quotedblbase"
	k="246" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="m,n,p,r,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="92" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="72" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="parenright,bracketright,braceright"
	k="10" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="78" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="period,ellipsis"
	k="61" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="comma,quotesinglbase,quotedblbase"
	k="72" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="parenright,bracketright,braceright"
	k="-10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="ordfeminine,ordmasculine"
	k="-10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen,endash,emdash"
	k="61" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="J.salt,Jcircumflex.salt"
	k="61" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="copyright,published"
	k="31" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="143" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="quoteleft,quotedblleft"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guillemotright,guilsinglright"
	k="66" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="J,Jcircumflex"
	k="102" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="31" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="T,Tcommaaccent,Tcaron"
	k="-25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="113" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="f,fi,fl"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guillemotleft,guilsinglleft"
	k="51" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="78" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="78" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="41" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="t,tcommaaccent"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="colon,semicolon"
	k="92" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="period,ellipsis"
	k="205" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="comma,quotesinglbase,quotedblbase"
	k="164" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="m,n,p,r,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="41" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="z,zacute,zdotaccent,zcaron"
	k="31" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="parenright,bracketright,braceright"
	k="-10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="ordfeminine,ordmasculine"
	k="35" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="hyphen,endash,emdash"
	k="184" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="J.salt,Jcircumflex.salt"
	k="82" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="napostrophe,quoteright,quotedblright"
	k="31" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="copyright,published"
	k="61" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="215" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="quoteleft,quotedblleft"
	k="51" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guillemotright,guilsinglright"
	k="147" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="J,Jcircumflex"
	k="205" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="86" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="61" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="T,Tcommaaccent,Tcaron"
	k="-20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="221" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="f,fi,fl"
	k="61" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guillemotleft,guilsinglleft"
	k="182" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="184" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="174" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="113" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="t,tcommaaccent"
	k="61" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="68" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="72" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="colon,semicolon"
	k="205" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="period,ellipsis"
	k="328" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="comma,quotesinglbase,quotedblbase"
	k="317" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="m,n,p,r,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="113" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="z,zacute,zdotaccent,zcaron"
	k="113" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="hyphen,endash,emdash"
	k="51" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="napostrophe,quoteright,quotedblright"
	k="31" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="quoteleft,quotedblleft"
	k="41" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="J,Jcircumflex"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="39" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="f,fi,fl"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="41" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="31" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="t,tcommaaccent"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="31" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute"
	g2="parenright,bracketright,braceright"
	k="31" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute"
	g2="napostrophe,quoteright,quotedblright"
	k="31" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute"
	g2="quoteleft,quotedblleft"
	k="31" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute"
	g2="f,fi,fl"
	k="6" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute"
	g2="t,tcommaaccent"
	k="25" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="14" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="parenright,bracketright,braceright"
	k="31" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="hyphen,endash,emdash"
	k="-20" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="napostrophe,quoteright,quotedblright"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="quoteleft,quotedblleft"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="guillemotleft,guilsinglleft"
	k="-16" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="-10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="-6" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="t,tcommaaccent"
	k="14" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="comma,quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="parenright,bracketright,braceright"
	k="31" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="ordfeminine,ordmasculine"
	k="-18" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="hyphen,endash,emdash"
	k="-10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="napostrophe,quoteright,quotedblright"
	k="31" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="quoteleft,quotedblleft"
	k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="guillemotright,guilsinglright"
	k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="f,fi,fl"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="guillemotleft,guilsinglleft"
	k="-16" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="4" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="t,tcommaaccent"
	k="16" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="12" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="29" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="comma,quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="parenright,bracketright,braceright"
	k="20" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="hyphen,endash,emdash"
	k="41" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="copyright,published"
	k="2" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="33" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="f,fi,fl"
	k="10" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="guillemotleft,guilsinglleft"
	k="41" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="41" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="20" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="t,tcommaaccent"
	k="10" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-10" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-10" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="parenright,bracketright,braceright"
	k="10" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="napostrophe,quoteright,quotedblright"
	k="20" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="quoteleft,quotedblleft"
	k="10" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="T,Tcommaaccent,Tcaron"
	k="125" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="t,tcommaaccent"
	k="10" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="4" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="parenright,bracketright,braceright"
	k="41" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="napostrophe,quoteright,quotedblright"
	k="20" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="quoteleft,quotedblleft"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="6" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="f,fi,fl"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="guillemotleft,guilsinglleft"
	k="-16" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="t,tcommaaccent"
	k="14" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="25" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="period,ellipsis"
	k="31" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
	g2="comma,quotesinglbase,quotedblbase"
	k="20" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="parenright,bracketright,braceright"
	k="31" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="ordfeminine,ordmasculine"
	k="-20" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="napostrophe,quoteright,quotedblright"
	k="-31" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="copyright,published"
	k="-20" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="quoteleft,quotedblleft"
	k="-31" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="47" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="guillemotleft,guilsinglleft"
	k="4" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="6" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="-6" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-41" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-41" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="colon,semicolon"
	k="14" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="period,ellipsis"
	k="154" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="comma,quotesinglbase,quotedblbase"
	k="154" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="parenright,bracketright,braceright"
	k="41" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="napostrophe,quoteright,quotedblright"
	k="31" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="quoteleft,quotedblleft"
	k="20" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="f,fi,fl"
	k="14" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="6" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="t,tcommaaccent"
	k="29" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="25" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="31" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="comma,quotesinglbase,quotedblbase"
	k="20" />
    <hkern g1="t,tcommaaccent"
	g2="parenright,bracketright,braceright"
	k="10" />
    <hkern g1="t,tcommaaccent"
	g2="ordfeminine,ordmasculine"
	k="-20" />
    <hkern g1="t,tcommaaccent"
	g2="hyphen,endash,emdash"
	k="10" />
    <hkern g1="t,tcommaaccent"
	g2="quoteleft,quotedblleft"
	k="-10" />
    <hkern g1="t,tcommaaccent"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="4" />
    <hkern g1="t,tcommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-31" />
    <hkern g1="t,tcommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-20" />
    <hkern g1="t,tcommaaccent"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	g2="parenright,bracketright,braceright"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="parenright,bracketright,braceright"
	k="20" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="ordfeminine,ordmasculine"
	k="-31" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="hyphen,endash,emdash"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="napostrophe,quoteright,quotedblright"
	k="-51" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="quoteleft,quotedblleft"
	k="-41" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="45" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="f,fi,fl"
	k="-16" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="6" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="t,tcommaaccent"
	k="-10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-20" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-20" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="period,ellipsis"
	k="113" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="comma,quotesinglbase,quotedblbase"
	k="82" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="parenright,bracketright,braceright"
	k="10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="ordfeminine,ordmasculine"
	k="-20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="hyphen,endash,emdash"
	k="16" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="napostrophe,quoteright,quotedblright"
	k="-72" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="quoteleft,quotedblleft"
	k="-51" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="guillemotright,guilsinglright"
	k="-8" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="55" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="guillemotleft,guilsinglleft"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="25" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="6" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="t,tcommaaccent"
	k="-16" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-31" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="colon,semicolon"
	k="25" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="period,ellipsis"
	k="154" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="comma,quotesinglbase,quotedblbase"
	k="123" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="hyphen,endash,emdash"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="16" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="4" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="6" />
    <hkern g1="period,ellipsis"
	g2="parenright,bracketright,braceright"
	k="51" />
    <hkern g1="period,ellipsis"
	g2="ordfeminine,ordmasculine"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="hyphen,endash,emdash"
	k="61" />
    <hkern g1="period,ellipsis"
	g2="J.salt,Jcircumflex.salt"
	k="14" />
    <hkern g1="period,ellipsis"
	g2="napostrophe,quoteright,quotedblright"
	k="205" />
    <hkern g1="period,ellipsis"
	g2="quoteleft,quotedblleft"
	k="205" />
    <hkern g1="period,ellipsis"
	g2="J,Jcircumflex"
	k="10" />
    <hkern g1="period,ellipsis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="51" />
    <hkern g1="period,ellipsis"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="61" />
    <hkern g1="period,ellipsis"
	g2="T,Tcommaaccent,Tcaron"
	k="287" />
    <hkern g1="period,ellipsis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="205" />
    <hkern g1="period,ellipsis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="328" />
    <hkern g1="period,ellipsis"
	g2="f,fi,fl"
	k="61" />
    <hkern g1="period,ellipsis"
	g2="guillemotleft,guilsinglleft"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="31" />
    <hkern g1="period,ellipsis"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="t,tcommaaccent"
	k="72" />
    <hkern g1="period,ellipsis"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="113" />
    <hkern g1="period,ellipsis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="164" />
    <hkern g1="period,ellipsis"
	g2="parenleft,bracketleft,braceleft"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="percent,perthousand"
	k="164" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="parenright,bracketright,braceright"
	k="31" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="hyphen,endash,emdash"
	k="25" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="napostrophe,quoteright,quotedblright"
	k="170" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="quoteleft,quotedblleft"
	k="186" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="guillemotright,guilsinglright"
	k="8" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="31" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="41" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="T,Tcommaaccent,Tcaron"
	k="246" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="154" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="287" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="f,fi,fl"
	k="47" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="guillemotleft,guilsinglleft"
	k="6" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="16" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="t,tcommaaccent"
	k="61" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="82" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="123" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="parenleft,bracketleft,braceleft"
	k="18" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="percent,perthousand"
	k="154" />
    <hkern g1="colon,semicolon"
	g2="parenright,bracketright,braceright"
	k="51" />
    <hkern g1="colon,semicolon"
	g2="J.salt,Jcircumflex.salt"
	k="10" />
    <hkern g1="colon,semicolon"
	g2="J,Jcircumflex"
	k="10" />
    <hkern g1="colon,semicolon"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="10" />
    <hkern g1="colon,semicolon"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="10" />
    <hkern g1="colon,semicolon"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="colon,semicolon"
	g2="T,Tcommaaccent,Tcaron"
	k="225" />
    <hkern g1="colon,semicolon"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="92" />
    <hkern g1="colon,semicolon"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="205" />
    <hkern g1="colon,semicolon"
	g2="f,fi,fl"
	k="10" />
    <hkern g1="colon,semicolon"
	g2="t,tcommaaccent"
	k="10" />
    <hkern g1="colon,semicolon"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="20" />
    <hkern g1="colon,semicolon"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="51" />
    <hkern g1="hyphen,endash,emdash"
	g2="parenright,bracketright,braceright"
	k="113" />
    <hkern g1="hyphen,endash,emdash"
	g2="J.salt,Jcircumflex.salt"
	k="10" />
    <hkern g1="hyphen,endash,emdash"
	g2="napostrophe,quoteright,quotedblright"
	k="4" />
    <hkern g1="hyphen,endash,emdash"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="92" />
    <hkern g1="hyphen,endash,emdash"
	g2="quoteleft,quotedblleft"
	k="4" />
    <hkern g1="hyphen,endash,emdash"
	g2="guillemotright,guilsinglright"
	k="57" />
    <hkern g1="hyphen,endash,emdash"
	g2="J,Jcircumflex"
	k="20" />
    <hkern g1="hyphen,endash,emdash"
	g2="T,Tcommaaccent,Tcaron"
	k="205" />
    <hkern g1="hyphen,endash,emdash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="61" />
    <hkern g1="hyphen,endash,emdash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="184" />
    <hkern g1="hyphen,endash,emdash"
	g2="f,fi,fl"
	k="10" />
    <hkern g1="hyphen,endash,emdash"
	g2="guillemotleft,guilsinglleft"
	k="-20" />
    <hkern g1="hyphen,endash,emdash"
	g2="t,tcommaaccent"
	k="10" />
    <hkern g1="hyphen,endash,emdash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="hyphen,endash,emdash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="hyphen,endash,emdash"
	g2="period,ellipsis"
	k="61" />
    <hkern g1="hyphen,endash,emdash"
	g2="comma,quotesinglbase,quotedblbase"
	k="18" />
    <hkern g1="hyphen,endash,emdash"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="hyphen,endash,emdash"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="parenright,bracketright,braceright"
	k="39" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="hyphen,endash,emdash"
	k="57" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="J.salt,Jcircumflex.salt"
	k="20" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="20" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="J,Jcircumflex"
	k="31" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="20" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="T,Tcommaaccent,Tcaron"
	k="129" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="66" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="147" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="20" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-8" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="comma,quotesinglbase,quotedblbase"
	k="8" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="parenleft,bracketleft,braceleft"
	k="25" />
    <hkern g1="guillemotright,guilsinglright"
	g2="parenright,bracketright,braceright"
	k="51" />
    <hkern g1="guillemotright,guilsinglright"
	g2="hyphen,endash,emdash"
	k="-20" />
    <hkern g1="guillemotright,guilsinglright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="51" />
    <hkern g1="guillemotright,guilsinglright"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="-10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="T,Tcommaaccent,Tcaron"
	k="154" />
    <hkern g1="guillemotright,guilsinglright"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="51" />
    <hkern g1="guillemotright,guilsinglright"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="182" />
    <hkern g1="guillemotright,guilsinglright"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="-16" />
    <hkern g1="guillemotright,guilsinglright"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="guillemotright,guilsinglright"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="guillemotright,guilsinglright"
	g2="comma,quotesinglbase,quotedblbase"
	k="20" />
    <hkern g1="guillemotright,guilsinglright"
	g2="percent,perthousand"
	k="18" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="ordfeminine,ordmasculine"
	k="20" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="hyphen,endash,emdash"
	k="113" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="J.salt,Jcircumflex.salt"
	k="61" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="napostrophe,quoteright,quotedblright"
	k="20" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="copyright,published"
	k="39" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="quoteleft,quotedblleft"
	k="37" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="guillemotright,guilsinglright"
	k="37" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="J,Jcircumflex"
	k="61" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="31" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="41" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-10" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-10" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="61" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="f,fi,fl"
	k="20" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="guillemotleft,guilsinglleft"
	k="51" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="41" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="41" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="31" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="t,tcommaaccent"
	k="20" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="20" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="colon,semicolon"
	k="51" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="period,ellipsis"
	k="51" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="comma,quotesinglbase,quotedblbase"
	k="-20" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="m,n,p,r,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="20" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="percent,perthousand"
	k="20" />
    <hkern g1="parenright,bracketright,braceright"
	g2="napostrophe,quoteright,quotedblright"
	k="20" />
    <hkern g1="parenright,bracketright,braceright"
	g2="quoteleft,quotedblleft"
	k="10" />
    <hkern g1="parenright,bracketright,braceright"
	g2="guillemotright,guilsinglright"
	k="39" />
    <hkern g1="parenright,bracketright,braceright"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="parenright,bracketright,braceright"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="parenright,bracketright,braceright"
	g2="comma,quotesinglbase,quotedblbase"
	k="31" />
    <hkern g1="percent,perthousand"
	g2="parenright,bracketright,braceright"
	k="31" />
    <hkern g1="percent,perthousand"
	g2="napostrophe,quoteright,quotedblright"
	k="102" />
    <hkern g1="percent,perthousand"
	g2="quoteleft,quotedblleft"
	k="98" />
    <hkern g1="copyright,published"
	g2="parenright,bracketright,braceright"
	k="39" />
    <hkern g1="copyright,published"
	g2="napostrophe,quoteright,quotedblright"
	k="10" />
    <hkern g1="copyright,published"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="49" />
    <hkern g1="copyright,published"
	g2="T,Tcommaaccent,Tcaron"
	k="41" />
    <hkern g1="copyright,published"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="31" />
    <hkern g1="copyright,published"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="61" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="parenright,bracketright,braceright"
	k="20" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="J.salt,Jcircumflex.salt"
	k="4" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="51" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="J,Jcircumflex"
	k="14" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-10" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="31" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-31" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-20" />
    <hkern g1="quoteleft,quotedblleft"
	g2="hyphen,endash,emdash"
	k="14" />
    <hkern g1="quoteleft,quotedblleft"
	g2="J.salt,Jcircumflex.salt"
	k="113" />
    <hkern g1="quoteleft,quotedblleft"
	g2="copyright,published"
	k="41" />
    <hkern g1="quoteleft,quotedblleft"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="266" />
    <hkern g1="quoteleft,quotedblleft"
	g2="J,Jcircumflex"
	k="461" />
    <hkern g1="quoteleft,quotedblleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="37" />
    <hkern g1="quoteleft,quotedblleft"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="20" />
    <hkern g1="quoteleft,quotedblleft"
	g2="T,Tcommaaccent,Tcaron"
	k="-20" />
    <hkern g1="quoteleft,quotedblleft"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-31" />
    <hkern g1="quoteleft,quotedblleft"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-31" />
    <hkern g1="quoteleft,quotedblleft"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="143" />
    <hkern g1="quoteleft,quotedblleft"
	g2="f,fi,fl"
	k="20" />
    <hkern g1="quoteleft,quotedblleft"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="82" />
    <hkern g1="quoteleft,quotedblleft"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="72" />
    <hkern g1="quoteleft,quotedblleft"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="31" />
    <hkern g1="quoteleft,quotedblleft"
	g2="t,tcommaaccent"
	k="10" />
    <hkern g1="quoteleft,quotedblleft"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-10" />
    <hkern g1="quoteleft,quotedblleft"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-10" />
    <hkern g1="quoteleft,quotedblleft"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="quoteleft,quotedblleft"
	g2="period,ellipsis"
	k="215" />
    <hkern g1="quoteleft,quotedblleft"
	g2="comma,quotesinglbase,quotedblbase"
	k="205" />
    <hkern g1="quoteleft,quotedblleft"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="quoteleft,quotedblleft"
	g2="m,n,p,r,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="51" />
    <hkern g1="quoteleft,quotedblleft"
	g2="z,zacute,zdotaccent,zcaron"
	k="20" />
    <hkern g1="quoteleft,quotedblleft"
	g2="parenleft,bracketleft,braceleft"
	k="20" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="hyphen,endash,emdash"
	k="61" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="J.salt,Jcircumflex.salt"
	k="113" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="copyright,published"
	k="72" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="287" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="J,Jcircumflex"
	k="492" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="55" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron"
	k="61" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="T,Tcommaaccent,Tcaron"
	k="-20" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-31" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-31" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="184" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="f,fi,fl"
	k="41" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oslashacute"
	k="133" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="133" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="61" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="t,tcommaaccent"
	k="31" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="20" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="colon,semicolon"
	k="72" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="period,ellipsis"
	k="270" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="comma,quotesinglbase,quotedblbase"
	k="174" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="31" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="m,n,p,r,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="78" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="z,zacute,zdotaccent,zcaron"
	k="61" />
    <hkern g1="dcaron,lcaron,quoteright,quotedblright"
	g2="parenleft,bracketleft,braceleft"
	k="31" />
  </font>
</defs></svg>
