<?php
use App\Components\Helper;
?>
@extends(\App\Components\Helper::getLayoutForUser())
@section('content')
    <style>
        .select2-results__options {
            max-height: 300px !important;
            overflow: auto !important;
        }
    </style>
    <main>
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <h1>Dashboard</h1>
                    <nav class="breadcrumb-container d-none d-sm-block d-lg-inline-block" aria-label="breadcrumb">
                        <ol class="breadcrumb pt-0">
                            <li class="breadcrumb-item">
                                <a href="{{\App\Components\Helper::dashboardLink()}}">Home</a>
                            </li>
                            <!--<li class="breadcrumb-item">
                                <a href="#">Library</a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">Data</li>-->
                        </ol>
                    </nav>
                    <div class="separator mb-5"></div>
                </div>

                <div class="col-lg-12 col-xl-12">
                    <div class="icon-cards-row">
                        <div class="glide dashboard-numbers">
                            <div class="glide__track" data-glide-el="track">
                                <ul class="glide__slides">
                                    <li class="glide__slide">
                                        <a href="https://goimeicheck.com/user/add-money" class="card">
                                            <div class="card-body text-center">
                                                <i class="iconsminds-money-bag"></i>
                                                <p class="card-text mb-0">Available Balance</p>
                                                <p class="lead text-center"><?=Helper::printAmount(Helper::userBalance(auth()->user()))?></p>
                                            </div>
                                        </a>
                                    </li>
                                    <li class="glide__slide">
                                        <a href="https://goimeicheck.com/user/requests?type=12" class="card">
                                            <div class="card-body text-center">
                                                <i class="iconsminds-clock"></i>
                                                <p class="card-text mb-0">In Process Orders</p>
                                                <p class="lead text-center">0</p>
                                            </div>
                                        </a>
                                    </li>
                                    <li class="glide__slide">
                                        <a href="https://goimeicheck.com/user/requests?type=14" class="card">
                                            <div class="card-body text-center">
                                                <i class="iconsminds-close"></i>
                                                <p class="card-text mb-0">Rejected Orders (Within last 1 month)</p>
                                                <p class="lead text-center">0</p>
                                            </div>
                                        </a>
                                    </li>
                                    <li class="glide__slide">
                                        <a href="https://goimeicheck.com/user/requests?type=13" class="card">
                                            <div class="card-body text-center">
                                                <i class="iconsminds-basket-coins"></i>
                                                <p class="card-text mb-0">Completed Orders (Within last 1 month)</p>
                                                <p class="lead text-center">0</p>
                                            </div>
                                        </a>
                                    </li>
                                    <li class="glide__slide">
                                        <a href="https://goimeicheck.com/user/add-money" class="card">
                                            <div class="card-body text-center">
                                                <i class="iconsminds-money-bag"></i>
                                                <p class="card-text mb-0">Receipt Amount</p>
                                                <p class="lead text-center">$0</p>
                                            </div>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-12 col-xl-6">
                    <div class="row">
                        <div class="col-md-12 mb-4">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">IMEI Services</h5>
                                    <div class="dashboard-quick-post">
                                        <form action="{{ route('dashboard', ['type'=>'single']) }}" method="POST"
                                              class="normal-form">
                                            @csrf
                                            <div class="row">
                                                <label class="col-sm-3 col-form-label">
                                                    <label class="control-label" for="servicerequests-service_id">Service</label>
                                                </label>

                                                <div class="col-sm-9">
                                                    <select id="servicerequests-service_id" class="form-control select2-single" name="ServiceRequests[service_id]" required="" aria-required="true">
                                                        <?=\App\Models\Service::printOptionsHTML()?>
                                                    </select>

                                                    <p class="help-block help-block-error"></p>
                                                </div>
                                            </div>
                                        </form>

                                        <form id="w0" class="form" action="/user/index" method="post">
                                            <input type="hidden" name="_csrf" value="xbqva4XafoGMnTCSZFLE76UaCmZEc9QvE6shU6XC4UG0wsNd0J86y_jZBsRRILTdxmNpEwA54nl6hns1wbGgDg==">                                    <div class="form-group field-servicerequests-service_id required">
                                                <div class="row">
                                                    <label class="col-sm-3 col-form-label">
                                                        <label class="control-label" for="servicerequests-service_id">Service</label>
                                                    </label>

                                                    <div class="col-sm-9">
                                                        <select id="servicerequests-service_id" class="form-control select2-single" name="ServiceRequests[service_id]" required="" aria-required="true">
                                                            <option value="">Select Service</option>
                                                            <optgroup label="APPLE CARRIER CHECK SERVICES">
                                                                <option value="1">ICS000# Apple iPhone Carrier Checker (IMEI/SN) - $0.1</option>
                                                                <option value="84">ICS001# Apple iPhone Carrier + FMI On/Off Checker (IMEI/SN) - $0.15</option>
                                                                <option value="23">ICS002# Apple iPhone Warranty + Carrier + FMI Checker (IMEI/SN) - $0.35</option>
                                                                <option value="150">ICS003# Apple iPhone Warranty + Carrier + GSMA Checker (IMEI/SN) - $0.2</option>
                                                                <option value="70">ICS004# Apple iPhone Warranty + Carrier + FMI + GSMA Checker (IMEI/SN) - $0.4</option>
                                                                <option value="127">ICS005# Apple iPhone Carrier + FMI + EID + Warranty + GSMA Checker (IMEI/SN) - $1</option>
                                                                <option value="3">ICS009# Exclusive All In One Carrier Checker (IMEI/SN) - $2</option>
                                                            </optgroup>
                                                            <optgroup label="APPLE SIMLOCK CHECK SERVICES">
                                                                <option value="121">ICS009# Apple iPhone SimLock Checker (IMEI/SN) - $0.08</option>
                                                                <option value="73">ICS010# Apple iPhone SimLock + FMI On/Off Checker (IMEI/SN) - $0.1</option>
                                                            </optgroup>
                                                            <optgroup label="Apple IMEI-SN &amp; SN-IMEI INSTANT CHECK SERVICES">
                                                                <option value="5">ICS011# Apple iPhone IMEI ⇄ IMEI2 ⇄ Serial Convert ( ✔️ Color ✔️ Storage ) - $0.04</option>
                                                                <option value="98">ICS012# Apple iPhone IMEI ⇄ IMEI2 ⇄ Serial Convert (✔️ Apple Care &amp; ✔️ Warranty ❌ Color &amp; ❌ Storage) - $0.04</option>
                                                                <option value="132">ICS013# Apple iPhone IMEI ⇄ IMEI2 ⇄ Serial Convert  (✔️ Apple Care  ✔️ Replaced Device &amp; ✔️ Warranty)- Exclusive Service - $0.04</option>
                                                            </optgroup>
                                                            <optgroup label="APPLE INSTANT CHECK SERVICES">
                                                                <option value="86">ICS009# Apple All Devices Part Number / MPN + DOP (IMEI/SN) - $0.2</option>
                                                                <option value="33">ICS013# Apple All Devices Warranty Check Service (IMEI/SN) - $0.05</option>
                                                                <option value="28">ICS014# Apple All Devices Warranty Check Service (SN Only) - $0.05</option>
                                                                <option value="149">ICS015# Apple All Devices Warranty PRO Check Service (IMEI/SN) - $0.05</option>
                                                                <option value="89">ICS089# Apple iPhone  Purchase Country Checker (IMEI/SN) - $0.12</option>
                                                                <option value="90">ICS090# Apple All Devices Replacement Status Checker (IMEI/SN) - $0.1</option>
                                                                <option value="136">ICS091# Apple All Devices Activation Status Checker (IMEI/SN) - $0.1</option>
                                                                <option value="63">ICS092# Apple All Devices Activation &amp; Replacement Status Checker (IMEI/SN) - $0.1</option>
                                                            </optgroup>
                                                            <optgroup label="APPLE MDM CHECK SERVICES">
                                                                <option value="131">ICS010# Apple All Devices  MPN + MDM On/OFF Checker - (IMEI/SN) - $0.7</option>
                                                                <option value="39">ICS011# Apple iPhone/iPad/iMac/Macbook MDM Checker -(IMEI/SN) - Source B - $0.5</option>
                                                            </optgroup>
                                                            <optgroup label="APPLE GSX SERVICES">
                                                                <option value="120">ICS#181 Apple GSX Next-Tether Check Service --&gt; IMEI/SN - $0.8</option>
                                                                <option value="91">ICS160# Apple iPhone/iPad GSX Full - Replace/Case/Sold/Mac Checker (IMEI/SN) - $3</option>
                                                                <option value="93">ICS163# Apple iPhone/iPad/Mac GSX Policies &amp; WIFI MAC Address Checker (IMEI/SN) - $2.75</option>
                                                                <option value="76">ICS164# Apple All Devices Sold By Info - $2.25</option>
                                                                <option value="130">ICS991# Apple All Devices GSX Case History Only - $1.5</option>
                                                                <option value="85">ICS992# Apple All Devices Case ID Info Only - $1.5</option>
                                                                <option value="68">ICS995# Apple All Devices GSX Cases &amp; Repairs - $1.5</option>
                                                            </optgroup>
                                                            <optgroup label="BLACKLIST CHECK SERVICES">
                                                                <option value="8">ICS0001# Worldwide GSMA Blacklist Checker By IMEI - $0.08</option>
                                                                <option value="64">ICS0002# Worldwide GSMA Blacklist Pro Checker - By IMEI - $0.15</option>
                                                                <option value="139">ICS0003# Japan NTT Docomo Network checker By IMEI - $0.1</option>
                                                                <option value="61">ICS0004# Japan AU KDDI Network checker By IMEI - $0.15</option>
                                                                <option value="60">ICS0005# Japan Softbank Network Checker By IMEI - $0.15</option>
                                                                <option value="146">ICS0006# Korea Blacklist Checker By IMEI - $0.15</option>
                                                                <option value="142">ICS0007# US ATT Network Status Checker By IMEI - $0.15</option>
                                                                <option value="55">ICS0008# US Verizon Network Checker By IMEI - $0.15</option>
                                                                <option value="141">ICS0009# US Verizon Network PRO Checker By IMEI - $0.15</option>
                                                                <option value="143">ICS0010# US Spectrum Network Checker By IMEI - $0.15</option>
                                                                <option value="62">ICS0011# USA T-Mobile Clean/Blocked/Unpaid checker By IMEI - $0.1</option>
                                                                <option value="124">ICS0012# USA T-Mobile Generic Simlock &amp; Unlock Eligibility Status Checker Pro - IMEI - $0.1</option>
                                                            </optgroup>
                                                            <optgroup label="GENERIC CHECK SERVICES">
                                                                <option value="87">ICS000# Tac Model Checker By IMEI - $0.05</option>
                                                                <option value="113">ICS001# LG Full Info Check By IMEI - $0.15</option>
                                                                <option value="140">ICS002# Alcatel Info Checker By IMEI - $0.15</option>
                                                                <option value="144">ICS003# OPPO Full Info Check By IMEI - $0.15</option>
                                                                <option value="145">ICS004# VIVO Full Info Check By IMEI - $0.15</option>
                                                                <option value="137">ICS005# ZTE Model + Warranty Checker By IMEI - $0.15</option>
                                                                <option value="97">ICS006# Google Pixel Model + Warranty Checker By IMEI - $0.15</option>
                                                                <option value="138">ICS008# One Plus Model +Warranty Checker By IMEI - $0.15</option>
                                                                <option value="80">ICS009# Samsung Carrier + Warranty Checker - $0.15</option>
                                                                <option value="148">ICS010# Samsung KG (KNOX Guard) On/Off Checker - (IMEI/SN) - $0.5</option>
                                                                <option value="34">ICS011# Samsung Carrier + Warranty + KNOX On/Off Checker - $0.49</option>
                                                                <option value="110">ICS012# Nokia Full Info Country + Warranty Check By IMEI - $0.25</option>
                                                                <option value="111">ICS013# Motorola Full Info Country + Warranty Check By IMEI - $0.1</option>
                                                                <option value="112">ICS014# Lenovo Full Info Country + Warranty Check By IMEI - $0.1</option>
                                                                <option value="99">ICS015# Huawei Warranty Checker Full Info By IMEI - $0.1</option>
                                                                <option value="42">ICS016# Honor Warranty Checker Full Info By IMEI - $0.49</option>
                                                            </optgroup>
                                                            <optgroup label="APPLE FMI &amp; ICLOUD CHECK SERVICES">
                                                                <option value="20">ICS012# Apple iPhone FMI ON/OFF Check Service (IMEI/SN)- Source A - $0.04</option>
                                                                <option value="133">ICS013# Apple iPhone/iPad FMI ON/OFF Checker Service (IMEI/SN) - Source B - $0.5</option>
                                                                <option value="26">ICS014# Apple iPhone FMI ON/OFF &amp; iCloud CLEAN/LOST Check Service - (IMEI/SN) - $0.07</option>
                                                                <option value="77">ICS019# Apple Macbook/iMac FMI ON/OFF Checker - BY SN - $0.6</option>
                                                                <option value="129">ICS021# Apple Macbook/iMac FMI ON/OFF &amp; iCloud Clean/Lost Checker - BY SN - Exclusive Service - $0.5</option>
                                                            </optgroup>
                                                            <optgroup label="XIAOMI MI INSTANT CHECK SERVICES">
                                                                <option value="122">ICS0110# Xiaomi Mi Lock Status Check ON/OFF Only - IMEI/CODE - $0.1</option>
                                                                <option value="88">ICS0111# Xiaomi MI Warranty + Activation Lock + Blacklist Checker By IMEI - $0.15</option>
                                                                <option value="123">ICS0112# Xiaomi Full Check Info Sales/Activation/Used Region &amp; Lock Status On/OFF Checker Pro - IMEI/Code - $0.2</option>
                                                            </optgroup>
                                                            <optgroup label="Services Not In Use">
                                                                <option value="115">ICS007# Testing Service - $0.02</option>
                                                            </optgroup>
                                                            <optgroup label="Private Tool For Testing Services">
                                                                <option value="69">ICS5555# Private Tool 1 - $100000</option>
                                                                <option value="38">ICS999# Private Tool 2 - $100000</option>
                                                            </optgroup>
                                                        </select><p class="help-block help-block-error"></p></div></div>
                                            </div>
                                            <div class="row">
                                                <label class="col-sm-3 col-form-label"></label>
                                                <div class="col-sm-9">
                                                    <div class="service-details"></div>
                                                </div>
                                            </div>

                                            <div class="form-group field-servicerequests-imei required">
                                                <div class="row"><label class="col-sm-3 col-form-label"><label class="" for="servicerequests-imei">IMEI/SN</label></label><div class="col-sm-9"><input type="text" id="servicerequests-imei" class="form-control" name="ServiceRequests[imei]" placeholder="IMEI/SN" required="" autocomplete="off" aria-required="true"><p class="help-block help-block-error"></p></div></div>
                                            </div>
                                            <div class="form-group row mb-0">
                                                <div class="col-sm-12">
                                                    <button type="submit" class="btn btn-primary">Send Request</button>
                                                    <a href="https://goimeicheck.com/user/index?bulk=bulk" class="btn btn-info float-right">Bulk IMEI Order</a>
                                                </div>
                                            </div>
                                        </form>                                </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-12 col-xl-6">
                    <div class="row">
                        <div class="col-md-12 mb-4">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Recent Bulk Batches</h5>
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                            <tr>
                                                <th>#</th>
                                                <th>Date</th>
                                                <th>Service</th>
                                                <th>Orders</th>
                                                <th>Pending</th>
                                                <th></th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <tr>
                                                <td>1</td>
                                                <td>2025-05-27 19:53:13</td>
                                                <td>ICS009# Apple All Devices Part Number / MPN + DOP (IMEI/SN)</td>
                                                <td>1</td>
                                                <td>
                                                    Completed                                                        </td>
                                                <td>
                                                    <a href="https://goimeicheck.com/user/requests?batch=49220250527&download=csv" target="_blank" title="Download Report">
                                                        <img src="img/export-excel.png" style="height:16px;">

                                                    </a>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>2</td>
                                                <td>2025-01-18 14:37:27</td>
                                                <td>ICS013# Apple iPhone/iPad FMI ON/OFF Checker Service (IMEI/SN) - Source B</td>
                                                <td>1</td>
                                                <td>
                                                    Completed                                                        </td>
                                                <td>
                                                    <a href="https://goimeicheck.com/user/requests?batch=20250118809&download=csv" target="_blank" title="Download Report">
                                                        <img src="img/export-excel.png" style="height:16px;">

                                                    </a>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>3</td>
                                                <td>2024-12-21 23:21:12</td>
                                                <td>ICS003# Apple iPhone Warranty + Carrier + GSMA Checker (IMEI/SN)</td>
                                                <td>8</td>
                                                <td>
                                                    Completed                                                        </td>
                                                <td>
                                                    <a href="https://goimeicheck.com/user/requests?batch=2024127656&download=csv" target="_blank" title="Download Report">
                                                        <img src="img/export-excel.png" style="height:16px;">

                                                    </a>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>4</td>
                                                <td>2024-12-19 18:40:35</td>
                                                <td>ICS003# Apple iPhone Warranty + Carrier + GSMA Checker (IMEI/SN)</td>
                                                <td>1</td>
                                                <td>
                                                    Completed                                                        </td>
                                                <td>
                                                    <a href="https://goimeicheck.com/user/requests?batch=20241219822&download=csv" target="_blank" title="Download Report">
                                                        <img src="img/export-excel.png" style="height:16px;">

                                                    </a>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>5</td>
                                                <td>2024-12-19 18:32:48</td>
                                                <td>ICS003# Apple iPhone Warranty + Carrier + GSMA Checker (IMEI/SN)</td>
                                                <td>1</td>
                                                <td>
                                                    Completed                                                        </td>
                                                <td>
                                                    <a href="https://goimeicheck.com/user/requests?batch=56720241219&download=csv" target="_blank" title="Download Report">
                                                        <img src="img/export-excel.png" style="height:16px;">

                                                    </a>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>6</td>
                                                <td>2024-11-18 00:17:07</td>
                                                <td>ICS013# Apple iPhone IMEI ⇄ IMEI2 ⇄ Serial Convert  (✔️ Apple Care  ✔️ Replaced Device & ✔️ Warranty)- Exclusive Service</td>
                                                <td>8</td>
                                                <td>
                                                    Completed                                                        </td>
                                                <td>
                                                    <a href="https://goimeicheck.com/user/requests?batch=20241118275&download=csv" target="_blank" title="Download Report">
                                                        <img src="img/export-excel.png" style="height:16px;">

                                                    </a>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>7</td>
                                                <td>2024-11-17 23:53:19</td>
                                                <td>ICS013# Apple iPhone IMEI ⇄ IMEI2 ⇄ Serial Convert  (✔️ Apple Care  ✔️ Replaced Device & ✔️ Warranty)- Exclusive Service</td>
                                                <td>8</td>
                                                <td>
                                                    Completed                                                        </td>
                                                <td>
                                                    <a href="https://goimeicheck.com/user/requests?batch=20241117642&download=csv" target="_blank" title="Download Report">
                                                        <img src="img/export-excel.png" style="height:16px;">

                                                    </a>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12 mb-4">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Queue Orders</h5>
                                    <div class="dashboard-quick-post">
                                        <table class="data-table data-table-standard responsive nowrap" data-order="[[ 1, &quot;desc&quot; ]]">
                                            <thead>
                                            <tr>
                                                <th>Service</th>
                                                <th>Orders</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <tr>
                                                <td>No Pending Queue</td>
                                                <td>0</td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
@endsection
@section('pageJs')
    <script src="js/vendor/Chart.bundle.min.js"></script>
    <script src="js/vendor/chartjs-plugin-datalabels.js"></script>

    <script>
        let rootStyle = getComputedStyle(document.body);
        let primaryColor = rootStyle.getPropertyValue("--primary-color").trim();
        let themeColor1 = rootStyle.getPropertyValue("--theme-color-1").trim();
        let themeColor2 = rootStyle.getPropertyValue("--theme-color-2").trim();
        let themeColor3 = rootStyle.getPropertyValue("--theme-color-3").trim();
        let themeColor4 = rootStyle.getPropertyValue("--theme-color-4").trim();
        let themeColor5 = rootStyle.getPropertyValue("--theme-color-5").trim();
        let themeColor6 = rootStyle.getPropertyValue("--theme-color-6").trim();
        let themeColor1_10 = rootStyle
            .getPropertyValue("--theme-color-1-10")
            .trim();
        let themeColor2_10 = rootStyle
            .getPropertyValue("--theme-color-2-10")
            .trim();
        let themeColor3_10 = rootStyle
            .getPropertyValue("--theme-color-3-10")
            .trim();
        let themeColor4_10 = rootStyle
            .getPropertyValue("--theme-color-4-10")
            .trim();

        let themeColor5_10 = rootStyle
            .getPropertyValue("--theme-color-5-10")
            .trim();
        let themeColor6_10 = rootStyle
            .getPropertyValue("--theme-color-6-10")
            .trim();
        let foregroundColor = rootStyle
            .getPropertyValue("--foreground-color")
            .trim();
        let separatorColor = rootStyle.getPropertyValue("--separator-color").trim();
        let chartTooltip = {
            backgroundColor: foregroundColor,
            titleFontColor: primaryColor,
            borderColor: separatorColor,
            borderWidth: 0.5,
            bodyFontColor: primaryColor,
            bodySpacing: 10,
            xPadding: 15,
            yPadding: 15,
            cornerRadius: 0.15,
            displayColors: false
        };

        if (document.getElementById("ticketsChart")) {
            var polarChartNoShadow = document
                .getElementById("ticketsChart")
                .getContext("2d");
            var myChart = new Chart(polarChartNoShadow, {
                type: "polarArea",
                options: {
                    plugins: {
                        datalabels: {
                            display: false
                        }
                    },
                    responsive: true,
                    maintainAspectRatio: false,
                    scale: {
                        ticks: {
                            display: false
                        }
                    },
                    legend: {
                        position: "bottom",
                        labels: {
                            padding: 30,
                            usePointStyle: true,
                            fontSize: 12
                        }
                    },
                    tooltips: chartTooltip
                },
                data: {
                    datasets: [
                        {
                            label: "Stock",
                            borderWidth: 2,
                            pointBackgroundColor: themeColor1,
                            borderColor: [themeColor1, themeColor2, themeColor3],
                            backgroundColor: [
                                themeColor1_10,
                                themeColor2_10,
                                themeColor3_10
                            ],
                            data: [80, 90, 70]
                        }
                    ],
                    labels: ["Cakes", "Desserts", "Cupcakes"]
                }
            });
        }




    </script>


@endsection
