<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use App\Casts\AmountCast;
use App\Components\Helper;
use App\Constants\CommonConstants;
use Illuminate\Database\Eloquent\Model;

class ProviderService extends BaseModel
{
    protected $fillable = [
        'provider_id',
        'name',
        'sid',
        'price',
        'type',
        'is_chinese_result',
        'is_active',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'price' => AmountCast::class,
    ];

    public function getDisplayNameAttribute() {
        return $this->name.' - #'.$this->sid.' ('.Helper::printAmount($this->price).')';
    }

    public function onSaved()
    {
        Provider::generateCacheFile();
        parent::onSaved();
    }

    public function provider()
    {
        return $this->hasOne(Provider::class, 'id', 'provider_id');
    }

    public function getTypeNameAttribute() {
        switch ($this->type) {
            case CommonConstants::TYPE_BOTH:
                return 'Both (IMEI/SN)';
                break;
            case CommonConstants::TYPE_IMEI:
                return 'IMEI';
                break;
            case CommonConstants::TYPE_SN:
                return 'SN';
                break;
        }
    }

    public static function defaultQuery() {
        return self::query()->where('is_active','=',CommonConstants::YES);
    }
}
