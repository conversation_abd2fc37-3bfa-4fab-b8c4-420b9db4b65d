@extends('layouts.guest')
@section('content')
    <div class="loginPage">
        <div class="container">

            <div class="elementWrapper">
                <div class="row justify-content-center">
                    <div class="col-sm-10 col-lg-6 col-md-8">
                        <p class="txt__18 mb-2 text-center">GO IMEI CHECK</p>
                        <h2 class="signinheader">Welcome Back</h2>
                        <div class="googleSignIn">
                            <p class="orSignin">
                                <span class="mx-2 my-4">Fill below credentials to access your account</span>
                            </p>
                            <form method="POST" action="{{ route('login') }}">
                                @csrf
                                <div class="form-group">
                                    <label class="form-label">Email</label>
                                    <input
                                        type="email" autocomplete="off"
                                        class="inputField"
                                        placeholder="Enter Your Email"
                                        name="email" value="{{old('email')}}"
                                    />
                                    @error('email')
                                    <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                    @enderror
                                </div>
                                <div class="form-group mb-0">
                                    <label class="form-label">Password</label>
                                    <input
                                        type="password"
                                        class="inputField"
                                        placeholder="Enter Your Password"
                                        name="password"
                                    />
                                    @error('password')
                                    <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                    @enderror
                                </div>

                                <div class="keepSignedin">
                                    <div
                                        class="custom-control custom-checkbox custom-checkbox"
                                    >
                                        <input
                                            type="checkbox"
                                            class="custom-control-input"
                                            id="customCheck1"
                                            name="remember"
                                        />
                                        <label
                                            class="custom-control-label keepSignedin__label"
                                            for="customCheck1"
                                        >Keep me signed in</label
                                        >
                                    </div>
                                    <a class="pass__reset" href="javasript:void(0);"
                                    >Forgot Password?</a
                                    >
                                </div>
                                <button type="submit" class="button button--dark w-100">
                                    Sign in Now
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="login__element1">
                    <img src="./images/login_element1.png" alt="Image" />
                </div>
                <div class="login__element2">
                    <img src="./images/login_element2.png" alt="Image" />
                </div>
                <div class="login__element3">
                    <img src="./images/login_element3.png" alt="Image" />
                </div>
                <div class="login__element4">
                    <img src="./images/login_element4.png" alt="Image" />
                </div>
            </div>
        </div>
    </div>
@endsection
