@extends(\App\Components\Helper::getLayoutForUser())
@section('content')
    <main>
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <h1>{{__($providerService->name)}}</h1>
                    <div class="text-zero top-right-button-container">
                        <a href="{{route('provider-service.edit',$providerService->id)}}" class="btn btn-secondary btn-lg top-right-button mr-1"> <i class="glyph-icon simple-icon-pencil"></i> UPDATE</a>
                    </div>

                    <nav class="breadcrumb-container d-none d-sm-block d-lg-inline-block" aria-label="breadcrumb">
                        <ol class="breadcrumb pt-0">
                            <li class="breadcrumb-item">
                                <a href="{{\App\Components\Helper::dashboardLink()}}">{{__('Dashboard')}}</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{route('provider-service.index')}}">{{__('Provider Services')}}</a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">{{__($providerService->name)}}</li>
                        </ol>
                    </nav>
                    <div class="separator mb-5"></div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-lg-12 col-md-12 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <table class="table">
                                <tbody>
                                    <tr>
                                        <th>Name</th>
                                        <td>{{__($providerService->name)}}</td>
                                    </tr>
                                    <tr>
                                        <th>Provider</th>
                                        <td>{{__($providerService->provider->displayName)}}</td>
                                    </tr>
                                    <tr>
                                        <th>Service ID</th>
                                        <td>{{__($providerService->sid)}}</td>
                                    </tr>
                                    <tr>
                                        <th>Type</th>
                                        <td>{{__($providerService->typeName)}}</td>
                                    </tr>
                                    <tr>
                                        <th>Price</th>
                                        <td>{{__($providerService->price)}}</td>
                                    </tr>
                                    <tr>
                                        <th>Active</th>
                                        <td><?=\App\Components\Helper::onOffButton($providerService,'is_active')?></td>
                                    </tr>
                                    <tr>
                                        <th>Chinese Result</th>
                                        <td><?=\App\Components\Helper::onOffButton($providerService,'is_chinese_result')?></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

@endsection
