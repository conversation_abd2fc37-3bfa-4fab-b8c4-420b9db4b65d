<?php

namespace Database\Seeders;

use App\Constants\CommonConstants;
use App\Models\Plan;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $rows=[
            ['name'=>'Retail Web Plan','is_default'=>1],
            ['name'=>'Silver Plan','price'=>50,'is_visible_to_guest'=>1],
            ['name'=>'Platinum Plan','price'=>100,'is_visible_to_guest'=>1,'is_best_seller'=>1],
            ['name'=>'Gold Plan','price'=>300,'is_visible_to_guest'=>1],
            ['name'=>'Bulk VIP Group'],
        ];

        if(count($rows)>0) {
            foreach ($rows as $row) {
                $row['created_at']=date(CommonConstants::PHP_DATE_FORMAT);

                Plan::create($row);
            }
        }
    }
}
