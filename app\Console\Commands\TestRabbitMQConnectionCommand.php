<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;

class TestRabbitMQConnectionCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'rabbitmq:test-connection';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test basic RabbitMQ connection and message sending';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing basic RabbitMQ connection...');

        $config = config('queue.connections.rabbitmq');
        
        $this->info("Connecting to: {$config['host']}:{$config['port']}");
        $this->info("VHost: {$config['vhost']}");
        $this->info("User: {$config['user']}");

        try {
            // Create connection with timeout
            $connection = new AMQPStreamConnection(
                $config['host'],
                $config['port'],
                $config['user'],
                $config['password'],
                $config['vhost'],
                false, // insist
                'AMQPLAIN', // login_method
                null, // login_response
                'en_US', // locale
                5.0, // connection_timeout
                5.0, // read_write_timeout
                null, // context
                false, // keepalive
                0 // heartbeat
            );

            $this->info('✅ Connection established successfully!');

            $channel = $connection->channel();
            $this->info('✅ Channel created successfully!');

            // Declare queue
            $queueName = 'test_queue_' . time();
            $channel->queue_declare($queueName, false, true, false, false);
            $this->info("✅ Test queue '{$queueName}' declared successfully!");

            // Send test message
            $testMessage = new AMQPMessage(
                json_encode([
                    'test' => true,
                    'timestamp' => now()->toISOString(),
                    'message' => 'Test message from server'
                ]),
                ['delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT]
            );

            $channel->basic_publish($testMessage, '', $queueName);
            $this->info('✅ Test message sent successfully!');

            // Check message count
            list($queue, $messageCount, $consumerCount) = $channel->queue_declare($queueName, true);
            $this->info("✅ Queue status - Messages: {$messageCount}, Consumers: {$consumerCount}");

            // Consume the message back
            $receivedMessage = $channel->basic_get($queueName);
            if ($receivedMessage) {
                $this->info('✅ Test message received successfully!');
                $this->info('Message content: ' . $receivedMessage->getBody());
                $channel->basic_ack($receivedMessage->getDeliveryTag());
            } else {
                $this->error('❌ Failed to receive test message');
            }

            // Clean up
            $channel->queue_delete($queueName);
            $this->info("✅ Test queue '{$queueName}' deleted");

            $channel->close();
            $connection->close();
            $this->info('✅ Connection closed successfully!');

            $this->info('');
            $this->info('🎉 All tests passed! RabbitMQ connection is working properly.');

        } catch (\Exception $e) {
            $this->error('❌ Connection test failed: ' . $e->getMessage());
            $this->error('Error details: ' . $e->getTraceAsString());
            return 1;
        }

        return 0;
    }
}
