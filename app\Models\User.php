<?php

namespace App\Models;

use App\Base\Traits\BaseModelTrait;
use App\Constants\CommonConstants;
use App\Constants\UserConstants;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Base\Model\BaseModel;
use App\Components\Helper;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Http\Request;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;
    use BaseModelTrait {
        BaseModelTrait::onCreating as modelOnCreating;
        BaseModelTrait::onCreated as modelOnCreated;
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'username',
        'email',
        'password',
        'user_agent',
        'country_id',
        'created_ip',
    ];

    public static function defaultQuery() {
        return self::query()
            ->where('user_role_id','=',CommonConstants::DEFAULT_USER_ROLE)
            ->where('is_active','=',CommonConstants::YES);
    }

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    public function getUniqueColumns() {
        return ['username','email'];
    }

    public static function generateApiKey() {
        $apiKey=Helper::generateRandomString();
        $check=self::query()->where('api_key','=',$apiKey)
            ->limit(1)->first();
        if($check) {
            return self::generateApiKey();
        }
        return $apiKey;
    }

    public function onCreating()
    {
        $this->name=trim(ucwords(strtolower($this->name)));
        $this->username=trim(strtolower($this->username));
        if(!$this->plan_id) {
            $defaultPlan=Plan::fetchDefaultPlan();
            if($defaultPlan) {
                $this->plan_id=$defaultPlan->id;
            }
        }

        $this->api_key=self::generateApiKey();
        $this->email_verified_at=date(CommonConstants::PHP_DATE_FORMAT);

        $this->modelOnCreating();
    }

    public function onCreated()
    {
        updateUserCache();
        $this->modelOnCreated();
    }

    public function plan()
    {
        return $this->hasOne(Plan::class, 'id', 'plan_id');
    }

    public function country()
    {
        return $this->hasOne(Country::class, 'id', 'country_id');
    }

    public function getProfileNameAttribute() {
        return $this->name." (".$this->username.")";
    }

    public function getApiData() {
        return [
            'name'=>$this->name,
            'username'=>$this->username,
            'email'=>$this->email,
            //'is_api_enabled'=>(int)$this->is_api_enabled,
            //'api_key'=>((int)$this->is_api_enabled ? $this->api_key : null),
            'country'=>($this->country ? $this->country->iso : null),
            'plan'=>($this->plan ? $this->plan->name : null),
        ];
    }

    public function addLoginHistory(Request $request, $isApiCall=false) {
        $isApiCall=(int)$isApiCall;

        $model=new UserLoginHistory();
        $model->user_id=$this->id;
        $model->is_api=$isApiCall;
        try {
            $model->save();
        } catch (\Exception $e) {

        }
    }

    public function isAdmin() {
        if($this->user_role_id==UserConstants::ROLE_ADMIN) {
            return true;
        }
        return false;
    }

    public function isUser() {
        if($this->user_role_id==UserConstants::ROLE_USER) {
            return true;
        }
        return false;
    }

    public function getDisplayNameAttribute() {
        return ucwords($this->name);
    }

    public function getDisplayDetailsAttribute() {
        return $this->username." #".$this->id;
    }

    public function getBalanceAttribute() {
        $query=Transaction::query()
            ->select(['debit_amount','credit_amount'])
            ->where('user_id','=',$this->id)
            ->get();

        $debit=$query->sum("debit_amount");
        $credit=$query->sum("credit_amount");

        if(!$credit || $credit=="") {
            $credit=0;
        }
        if(!$debit || $debit=="") {
            $debit=0;
        }
        return round(($credit-$debit),CommonConstants::USD_PRECISION);
    }

    public function generateNewApiKey() {
        UserApiKeyLog::saveLog($this->id,$this->api_key);

        $this->api_key=self::generateApiKey();
        $this->updated_at=date(CommonConstants::PHP_DATE_FORMAT);
        if($this->update(['api_key','updated_at'])) {
            return true;
        }
        return false;
    }
}
