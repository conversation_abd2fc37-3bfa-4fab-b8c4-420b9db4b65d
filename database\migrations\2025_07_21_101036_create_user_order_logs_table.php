<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('user_order_logs', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->bigInteger('user_id')->index();
            $table->bigInteger('service_id')->index();
            $table->date('date')->index();
            $table->bigInteger('total_orders')->index();
            $table->bigInteger('price')->index();
            $table->bigInteger('cost_price')->index();
            $table->bigInteger('final_pl')->index();
            $table->timestamps();
            $table->unique(['user_id','service_id','date'],'unq_user_service');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_order_logs');
    }
};
