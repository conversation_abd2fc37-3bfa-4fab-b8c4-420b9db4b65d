<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;

class RabbitMQSetupCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'rabbitmq:setup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Setup RabbitMQ queues and test connection';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Setting up RabbitMQ connection...');

        try {
            // Get RabbitMQ configuration
            $config = config('queue.connections.rabbitmq');
            
            $this->info("Connecting to RabbitMQ server:");
            $this->info("Host: {$config['host']}:{$config['port']}");
            $this->info("User: {$config['user']}");
            $this->info("VHost: {$config['vhost']}");

            // Create connection
            $connection = new AMQPStreamConnection(
                $config['host'],
                $config['port'],
                $config['user'],
                $config['password'],
                $config['vhost']
            );

            $channel = $connection->channel();
            $this->info('✓ Connected to RabbitMQ successfully!');

            // Create exchange
            $exchange = $config['exchange'];
            $channel->exchange_declare(
                $exchange,
                $config['exchange_type'],
                false, // passive
                true,  // durable
                false  // auto_delete
            );
            $this->info("✓ Exchange '{$exchange}' created/verified");

            // Create queues
            $queues = ['default', 'imei_checks', 'notifications', 'queue'];
            
            foreach ($queues as $queueName) {
                $channel->queue_declare(
                    $queueName,
                    false, // passive
                    true,  // durable
                    false, // exclusive
                    false  // auto_delete
                );
                
                // Bind queue to exchange
                $channel->queue_bind($queueName, $exchange);
                
                $this->info("✓ Queue '{$queueName}' created/verified and bound to exchange");
            }

            // Test message
            $testMessage = new AMQPMessage(
                json_encode([
                    'test' => true,
                    'message' => 'RabbitMQ setup test',
                    'timestamp' => now()->toISOString()
                ]),
                ['delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT]
            );

            $channel->basic_publish($testMessage, $exchange, 'default');
            $this->info('✓ Test message sent to default queue');

            // Check queue sizes
            foreach ($queues as $queueName) {
                list($messageCount) = $channel->queue_declare($queueName, true);
                $this->info("Queue '{$queueName}': {$messageCount} messages");
            }

            $channel->close();
            $connection->close();

            $this->info('');
            $this->info('🎉 RabbitMQ setup completed successfully!');
            $this->info('You can now dispatch jobs using:');
            $this->info('  php artisan testing');
            $this->info('');
            $this->info('Start a worker with:');
            $this->info('  php artisan rabbitmq:work --queue=imei_checks');
            $this->info('  php artisan rabbitmq:work --queue=notifications');

        } catch (\Exception $e) {
            $this->error('Failed to setup RabbitMQ: ' . $e->getMessage());
            $this->error('');
            $this->error('Please check:');
            $this->error('1. RabbitMQ server is running');
            $this->error('2. Credentials are correct');
            $this->error('3. VHost exists and user has permissions');
            $this->error('4. Network connectivity to the server');
            
            return 1;
        }

        return 0;
    }
}
