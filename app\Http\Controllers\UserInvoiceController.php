<?php

namespace App\Http\Controllers;

use App\Base\Model\BaseModel;
use App\Components\Helper;
use App\Constants\CommonConstants;
use App\Constants\UserConstants;
use App\Models\UserInvoice;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Yajra\DataTables\Facades\DataTables;

class UserInvoiceController extends Controller
{
    public function index()
    {
        return view('user-invoice.index');
    }

    public function show(UserInvoice $userInvoice,Request $request)
    {
        if($request->input('type')) {
            switch (trim(strtolower($request->input('type')))) {
                case "re-api":
                    if($userInvoice->generateNewApiKey()) {
                        return redirect()->route('user-invoice.show',$userInvoice->id)->with('success', 'New API key generated successfully.');
                    }
                    break;
            }
            return redirect()->route('user-invoice.show',$userInvoice->id)->with('error', 'Invalid request.');
        }
        return view('user-invoice.show', compact('userInvoice'));
    }

    public function create()
    {
        $userInvoice=new UserInvoice();
        return view('user-invoice.create', compact('userInvoice'));
    }

    public function edit(UserInvoice $userInvoice)
    {
        return view('user-invoice.edit', compact('userInvoice'));
    }

    public function store(Request $request)
    {
        $userInvoice = new UserInvoice();
        return $this->save($request, $userInvoice);
    }

    public function update(Request $request, UserInvoice $userInvoice)
    {
        return $this->save($request, $userInvoice);
    }

    private function save(Request $request, UserInvoice $userInvoice)
    {
        $isNewRecord = true;
        if ($userInvoice->id != null) {
            $isNewRecord = false;
        }

        $rules = [
            'user_id' => ['required', 'integer'],
            'amount' => ['required', 'numeric',
                function ($attribute, $value, $fail) {
                    if (!empty($value) && $value<=0) {
                        $fail('Invalid amount entered');
                    }
                },
            ],
            'is_paid' => ['required', 'integer'],
            'particular' => ['required', 'string'],
        ];

        if ($isNewRecord) {

        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            if ($isNewRecord) {
                return redirect()->route('user-invoice.create')->withErrors($validator)->withInput();
            } else {
                return redirect()->route('user-invoice.edit', $userInvoice->id)->withErrors($validator)->withInput();
            }
        }

        $ip = request()->ip();
        $ipDetails = Helper::fetchIpDetails($ip, true);

        $userInvoice->user_id = $request->input('user_id');
        $userInvoice->amount = $request->input('amount');
        $userInvoice->is_paid = (int)$request->input('is_paid');
        $userInvoice->particular = trim($request->input('particular'));
        if ($isNewRecord) {
            $userInvoice->save();
        } else {
            $userInvoice->update();
        }
        return redirect()->route('user-invoice.index')->with('success', 'Invoice saved successfully.');
    }

    public function dataTable(Request $request)
    {
        try {
            if ($request->ajax()) {
                $query = UserInvoice::query();
                BaseModel::buildFilterQuery($query, [
                    'q' => ['ref_no', 'amount','generated_date','paid_date'],
                    'is_paid',
                ]);
                return Datatables::eloquent($query)
                    ->addColumn('checkboxes', function ($row) {
                        return '<input type="checkbox" name="pdr_checkbox[]" class="pdr_checkbox" value="' . $row->id . '" />';
                    })
                    ->addColumn('user', function ($row) {
                        return $row->user->displayName;
                    })
                    ->addColumn('generated_date', function ($row) {
                        if($row->generated_date) {
                            return $row->generated_date;
                        }
                        return '';
                    })
                    ->addColumn('paid_date', function ($row) {
                        if($row->paid_date) {
                            return $row->paid_date;
                        }
                        return '';
                    })
                    ->addColumn('adjustment', function ($row) {
                        if($row->adjustment) {
                            return "Yes #".$row->adjustment->ref_no;
                        }
                        return '';
                    })
                    ->addColumn('created_at', function ($row) {
                        return Helper::displayTime($row->created_at);
                    })
                    ->addColumn('updated_at', function ($row) {
                        return Helper::displayTime($row->updated_at);
                    })
                    ->addColumn('amount', function ($row) {
                        return Helper::printAmount($row->amount);
                    })
                    ->addColumn('display_order', function ($row) {
                        return Helper::editPopupStructure($row,'display_order');
                    })
                    ->addColumn('is_paid', function ($row) use ($query) {
                        $columnName = 'is_paid';
                        return Helper::onOffButton($row, $columnName, $query);
                    })
                    ->addColumn('actions', function ($row) {
                        $buttons = [];
                        //$buttons['Login']=['url' => route('user.loginAs', $row->id), 'icon' => 'las la-sign-in-alt'];
                        //$buttons['view'] = ['url' => route('category.show', $row->id)];
                        $buttons['edit'] = ['url' => route('user-invoice.edit', $row->id)];
                        return Helper::getActionButtons($buttons);
                    })
                    ->rawColumns(['checkboxes', 'particular', 'is_paid','actions'])
                    ->make(true);
            }
        } catch (\Exception $e) {
            //print_r(['message'=>$e->getMessage(),'trace'=>$e->getTraceAsString()]);
            die();
        }
    }
}
