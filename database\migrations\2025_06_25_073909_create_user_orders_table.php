<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('user_orders', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->bigInteger('order_ref')->nullable()->index();
            $table->foreignId('user_id')->nullable()->references('id')->on('users')->onDelete('cascade');
            $table->foreignId('service_id')->references('id')->on('services')->onDelete('cascade');
            $table->foreignId('provider_id')->references('id')->on('providers')->onDelete('cascade');
            $table->foreignId('provider_service_id')->references('id')->on('provider_services')->onDelete('cascade');
            $table->bigInteger('price')->index();
            $table->string('status')->default(\App\Constants\CommonConstants::ORDER_STATUS_PENDING)->index();
            $table->text('result')->nullable();
            $table->text('raw_result')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_orders');
    }
};
