@extends(\App\Components\Helper::getLayoutForUser())
@section('content')
    <main>
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <h1>{{__($category->name)}}</h1>
                    <div class="text-zero top-right-button-container">
                        <a href="{{route('category.edit',$category->id)}}" class="btn btn-secondary btn-lg top-right-button mr-1"> <i class="glyph-icon simple-icon-pencil"></i> UPDATE</a>
{{--                        <a href="{{route('category.destroy',$category->id)}}" class="btn btn-danger btn-lg top-right-button mr-1"> <i class="glyph-icon simple-icon-trash"></i> DELETE</a>--}}
                    </div>

                    <nav class="breadcrumb-container d-none d-sm-block d-lg-inline-block" aria-label="breadcrumb">
                        <ol class="breadcrumb pt-0">
                            <li class="breadcrumb-item">
                                <a href="{{\App\Components\Helper::dashboardLink()}}">{{__('Dashboard')}}</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{route('category.index')}}">{{__('Users')}}</a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">{{__($category->name)}}</li>
                        </ol>
                    </nav>
                    <div class="separator mb-5"></div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-lg-12 col-md-12 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <table class="table">
                                <tbody>
                                    <tr>
                                        <th>Name</th>
                                        <td>{{__($category->name)}}</td>
                                    </tr>
                                    <tr>
                                        <th>Display Order</th>
                                        <td>{{__($category->display_order)}}</td>
                                    </tr>
                                    <tr>
                                        <th>Active</th>
                                        <td><?=\App\Components\Helper::onOffButton($category,'is_active')?></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

@endsection
