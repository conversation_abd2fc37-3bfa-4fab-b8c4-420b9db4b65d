<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use App\Constants\CommonConstants;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Plan extends BaseModel
{
    protected $fillable = [
        'name',
        'price',
        'is_default',
        'is_best_seller',
        'is_visible_to_guest',
        'is_active',
        'created_at', 'updated_at'
    ];

    public static function fetchDefaultPlan() {
        return self::query()->where('is_active','=',CommonConstants::YES)
            ->where('is_default','=',CommonConstants::YES)
            ->limit(1)->first();
    }

    public static function defaultQuery() {
        return self::query()->orderBy("id");
    }

    public function onSaved()
    {
        if($this->is_default) {
            DB::statement("update `plans` set `is_default`=0 where `id`!='".$this->id."';");
        } else {
            $defaultPlan=self::fetchDefaultPlan();
            if(!$defaultPlan) {
                DB::statement("update `plans` set `is_default`=1 where `id`='1';");
            }
        }
        parent::onSaved();
    }
}
