<?php

namespace Database\Seeders;

use App\Constants\CommonConstants;
use App\Models\Category;
use App\Models\OperatingSystem;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class OperatingSystemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $names=[
            'IOS','Generic','Both'
        ];

        if(count($names)>0) {
            $counter=1;
            foreach ($names as $name) {
                $isExist=OperatingSystem::query()->where('name','=',$name)->first();
                if(!$isExist) {
                    $row = ['name' => $name];
                    OperatingSystem::create($row);
                }
                $counter++;
            }
        }
    }
}
