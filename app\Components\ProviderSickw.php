<?php

namespace App\Components;

use App\Base\Provider\BaseProvider;
use App\Constants\CommonConstants;
use App\Models\Log;
use App\Models\User;
use Illuminate\Support\Facades\Http;

class ProviderSickw extends BaseProvider
{
    public static $providerID = CommonConstants::PROVIDER_SICKW;
    public static function placeOrder($orderModel) {
        $validate=self::validateRequest($orderModel);
        if($validate!==true) {
            return $validate;
        }

        $url = self::$providerDetails['api_url'] . "?format=htmlinstant&key=".self::$providerDetails['api_key']."&imei=".$orderModel->imei."&service=".self::$providerServiceDetails['sid'];
        $response = Http::get($url);
        $result=$response->body();

        /* $curl = curl_init ($url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 40);
        curl_setopt($curl, CURLOPT_TIMEOUT, 60);
        $result = curl_exec($curl);
        curl_close($curl); */

        if($result && $result!="") {
            return self::parseResult($orderModel,$result);
        }

        return self::defaultResult();
    }

    public static function parseResult($orderModel,$result) {
        try {
            if ($result == "" || $result == false || stripos($result, "error code")!==false) {
                return self::failedResult(self::SERVICE_MAINTENANCE_MESSAGE,$result);
            }

            if (stripos($result, "Error A01") !== false
                || stripos($result, "Error A02") !== false
                || stripos($result, "Error A03") !== false
                || stripos($result, "Error E01") !== false
                || stripos($result, "Error E02") !== false
                || stripos($result, "Error R01") !== false
                || stripos($result, "Error B01") !== false
                || stripos($result, "Error S01") !== false
                || stripos($result, "Error S02") !== false
                || stripos($result, "Error S03") !== false) {
                return self::failedResult(self::DEFAULT_MESSAGE,$result);
            }

            $finalResult=$result;

            $sid=self::$providerServiceDetails['sid'];
            switch ($sid) {
                case "12":
                    $new_data = array(
                        'Model' => '', 'IMEI' => '', 'IMEI 2' => '', 'Serial Number' => '', 'Estimated Purchase Date' => '',
                    );

                    $new_csv_result = self::getCsvReport($finalResult, false, false, true);
                    if (is_array($new_csv_result) && count($new_csv_result) > 0) {
                        foreach ($new_csv_result as $key => $val) {
                            if (strtolower($key) == "model") {
                                $new_data['Model'] = $val;
                            }
                            if (strtolower($key) == "sn") {
                                $new_data['Serial Number'] = $val;
                            }
                            if (strtolower($key) == "imei") {
                                $new_data['IMEI'] = $val;
                            }
                            if (strtolower($key) == "imei2") {
                                $new_data['IMEI 2'] = $val;
                            }
                            if (strtolower($key) == "estimated purchase date") {
                                $new_data['Estimated Purchase Date'] = $val;
                            }
                        }

                        if (array_key_exists('Description', $new_csv_result)) {
                            $new_data['Model'] = $new_csv_result['Description'];
                        }
                    }

                    $newHtmlResult = "";
                    foreach ($new_data as $key => $val) {
                        if ($val == "") {
                            continue;
                        }
                        $newHtmlResult .= $key . " : " . $val . "<br />";
                    }
                    $finalResult = $newHtmlResult;

                    return self::successResult($finalResult,$result);
                    break;
            }

            return self::successResult($finalResult, $result);
        } catch (\Exception $e) {

        }
        return self::defaultResult();
    }
}
