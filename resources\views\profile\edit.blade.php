@extends(\App\Components\Helper::getLayoutForUser())
@section('content')
<main>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h1>Update Profile</h1>
                <nav class="breadcrumb-container d-none d-sm-block d-lg-inline-block" aria-label="breadcrumb">
                    <ol class="breadcrumb pt-0">
                        <li class="breadcrumb-item">
                            <a href="{{\App\Components\Helper::dashboardLink()}}">Home</a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="javascript:void(0);">Dashboard</a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">Update Profile</li>
                    </ol>
                </nav>
                <div class="separator mb-5"></div>
            </div>


            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Update Profile</h5>
                        <form class="form"  method="post" action="{{ route('account.update') }}">
                            @csrf
                            @method('patch')
                            <div class="row">

                                <div class="col-md-12">
                                    <div class="form-group  required">
                                        <div class="controls">
                                            <label class="" for="users-name">Name</label>
                                            <input type="text"  class="form-control" id="users-name" name="name" placeholder="Name" value="{{old('name', $user->name)}}">
                                            @error('name')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group required">
                                        <div class="controls">
                                            <label class="" for="users-username">Username</label>
                                            <input type="text"  class="form-control" id="users-username" name="username" placeholder="Username" value="{{old('username', $user->username)}}">
                                            @error('username')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group required">
                                        <div class="controls">
                                            <label class="" for="users-email">Email</label>
                                            <input type="email"  class="form-control" id="users-email" name="email" placeholder="Email" value="{{old('email', $user->email)}}">
                                            @error('email')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">
                                        Update
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>


            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Change Password</h5>
                        <form class="form" method="post" action="{{ route('account.update') }}">
                            @csrf
                            @method('put')

                            <div class="row">

                                <div class="col-md-12">
                                    <div class="form-group field-users-old_password required">
                                        <div class="controls"><label class="" for="users-old_password">Current Password</label>
                                            <input  class="form-control" id="users-old_password" name="current_password" type="password"
                                                                       placeholder="Old Password" autocomplete="off"
                                                                       aria-required="true">
                                            <?php
                                            if($errors->updatePassword->get('current_password')){
                                            ?>
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $errors->updatePassword->get('current_password')[0] }}</strong>
                                            </span>
                                            <?php
                                            }
                                            ?>
                                            </div>
                                    </div>
                                </div>



                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group field-users-password required">
                                        <div class="controls"><label class=""
                                                                     for="users-password">New Password</label><input
                                                type="password" id="users-password" class="form-control"
                                                name="password" placeholder="Password" >
                                            <?php
                                            if($errors->updatePassword->get('password')){
                                            ?>
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $errors->updatePassword->get('password')[0] }}</strong>
                                            </span>
                                            <?php
                                            }
                                            ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-group field-users-confirm_password required">
                                        <div class="controls"><label class="" for="users-confirm_password">Confirm Password</label>
                                            <input type="password" id="users-confirm_password"
                                                   class="form-control"
                                                   name="password_confirmation"
                                                   placeholder="Confirm Password" autocomplete="off"
                                                   aria-required="true">
                                            <?php
                                            if($errors->updatePassword->get('password_confirmation')){
                                                ?>
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $errors->updatePassword->get('password_confirmation')[0] }}</strong>
                                            </span>
                                            <?php
                                            }
                                            ?>


                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">
                                        Update
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
@endsection

{{--
<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
            {{ __('Profile') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            <div class="p-4 sm:p-8 bg-white dark:bg-gray-800 shadow sm:rounded-lg">
                <div class="max-w-xl">
                    @include('profile.partials.update-profile-information-form')
                </div>
            </div>

            <div class="p-4 sm:p-8 bg-white dark:bg-gray-800 shadow sm:rounded-lg">
                <div class="max-w-xl">
                    @include('profile.partials.update-password-form')
                </div>
            </div>

            <div class="p-4 sm:p-8 bg-white dark:bg-gray-800 shadow sm:rounded-lg">
                <div class="max-w-xl">
                    @include('profile.partials.delete-user-form')
                </div>
            </div>
        </div>
    </div>
</x-app-layout>--}}
