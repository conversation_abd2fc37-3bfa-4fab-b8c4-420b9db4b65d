<div class="row mb-4">
    <div class="col-lg-12 col-md-12 mb-4">
        <div class="card">
            <div class="card-body">

<div class="rounded-16 form-box bg-white -dark-bg-dark-1 shadow-4 h-100">
    <form action="{{ $category->id ==null ? route('category.store') : route('category.update', $category) }}" method="POST"
          class="normal-form">
        @csrf

        @if( $category->id != null )
            @method('PUT')
        @endif

        <div class="row">
            <div class="col-md-4 col-12">
                <div class="form-group required-field text-left @error('name') is-invalid @enderror">
                    <label for="name">{{ __('Name') }}</label>
                    <input required id="name" type="text" class="form-control @error('name') is-invalid @enderror"
                           name="name" placeholder="{{ __('Enter name') }}"
                           value="{{old('name', $category->name)}}">
                    @error('name')
                    <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                    @enderror
                </div>
            </div>
            <div class="col-md-4 col-12">
                <div class="form-group required-field text-left @error('display_order') is-invalid @enderror">
                    <label for="display_order">{{ __('Display Order') }}</label>
                    <input required id="display_order" type="number" min="0" class="form-control @error('display_order') is-invalid @enderror"
                           name="display_order" placeholder="{{ __('Enter display order') }}"
                           value="{{old('display_order', $category->display_order)}}">
                    @error('display_order')
                    <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                    @enderror
                </div>
            </div>

            <div class="col-md-4 col-12">
                <div class="form-group required-field text-left @error('is_active') is-invalid @enderror">
                    <label for="is_active">{{ __('Active') }}</label>
                    <select name="is_active" id="is_active" class="form-control @error('is_active') is-invalid @enderror">
                        <option <?=(!$category->is_active ? 'selected=""' : '')?> value="<?=\App\Constants\CommonConstants::NO?>">No</option>
                        <option <?=($category->is_active ? 'selected=""' : '')?> value="<?=\App\Constants\CommonConstants::YES?>">Yes</option>
                    </select>
                    @error('is_active')
                    <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                    @enderror
                </div>
            </div>
        </div>

        @if( $category->id == null )

        @endif

        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-12 formBtn">
                @if( $category->id == null )
                    <button type="submit" class="btn btn-primary">{{ __('Save') }}</button>
                @else
                    <button type="submit" class="btn btn-primary">{{ __('Update') }}</button>
                @endif
            </div>
        </div>

    </form>
</div>
            </div>
        </div>
    </div>
</div>

@section('pageJs')

    <script>

    </script>

@endsection
