<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use App\Constants\CommonConstants;
use Illuminate\Database\Eloquent\Model;

class Category extends BaseModel
{
    protected $fillable = [
        'name','is_active','display_order',
        'created_at',
        'updated_at',
    ];

    public function onCreating()
    {
        $this->name=trim(strtoupper($this->name));
        parent::onCreating(); // TODO: Change the autogenerated stub
    }

    public static function defaultQuery() {
        return self::query()
            ->where('is_active','=',CommonConstants::YES)
            ->orderBy("display_order");
    }

    public function onSaving()
    {
        if($this->display_order=="" || $this->display_order==null) {
            $this->display_order=self::generateDisplayOrder();
        }
        parent::onSaving();
    }

    public function getDisplayNameAttribute() {
        return $this->name;
    }

    public static function generateDisplayOrder() {
        $model=self::query()->orderByDesc('display_order')->first();
        if($model) {
            return $model->display_order+1;
        } else {
            return 1;
        }
    }
}
