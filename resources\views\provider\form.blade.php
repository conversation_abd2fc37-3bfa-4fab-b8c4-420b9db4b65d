<div class="row mb-4">
    <div class="col-lg-12 col-md-12 mb-4">
        <div class="card">
            <div class="card-body">

<div class="rounded-16 form-box bg-white -dark-bg-dark-1 shadow-4 h-100">
    <form action="{{ $provider->id ==null ? route('provider.store') : route('provider.update', $provider) }}" method="POST"
          class="normal-form">
        @csrf

        @if( $provider->id != null )
            @method('PUT')
        @endif

        <div class="row">
            <div class="col-md-4 col-12">
                <div class="form-group required-field text-left @error('name') is-invalid @enderror">
                    <label for="name">{{ __('Name') }}</label>
                    <input required id="name" type="text" class="form-control @error('name') is-invalid @enderror"
                           name="name" placeholder="{{ __('Enter name') }}"
                           value="{{old('name', $provider->name)}}">
                    @error('name')
                    <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                    @enderror
                </div>
            </div>

            <div class="col-md-4 col-12">
                <div class="form-group required-field text-left @error('api_username') is-invalid @enderror">
                    <label for="api_username">{{ __('API Username') }}</label>
                    <input id="api_username" type="text" class="form-control @error('api_username') is-invalid @enderror"
                           name="api_username" placeholder="{{ __('Enter API Username') }}"
                           value="{{old('api_username', $provider->api_username)}}">
                    @error('api_username')
                    <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                    @enderror
                </div>
            </div>
            <div class="col-md-4 col-12">
                <div class="form-group required-field text-left @error('is_active') is-invalid @enderror">
                    <label for="is_active">{{ __('Active') }}</label>
                    <select name="is_active" id="is_active" class="form-control @error('is_active') is-invalid @enderror">
                        <option <?=(!$provider->is_active ? 'selected=""' : '')?> value="<?=\App\Constants\CommonConstants::NO?>">No</option>
                        <option <?=($provider->is_active ? 'selected=""' : '')?> value="<?=\App\Constants\CommonConstants::YES?>">Yes</option>
                    </select>
                    @error('is_active')
                    <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                    @enderror
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6 col-12">
                <div class="form-group required-field text-left @error('api_url') is-invalid @enderror">
                    <label for="api_url">{{ __('API URL') }}</label>
                    <input id="api_url" type="text" class="form-control @error('api_url') is-invalid @enderror"
                           name="api_url" placeholder="{{ __('Enter API URL') }}"
                           value="{{old('api_url', $provider->api_url)}}">
                    @error('api_url')
                    <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                    @enderror
                </div>
            </div>
            <div class="col-md-6 col-12">
                <div class="form-group required-field text-left @error('api_key') is-invalid @enderror">
                    <label for="api_key">{{ __('API Key') }}</label>
                    <input id="api_key" type="text" class="form-control @error('api_key') is-invalid @enderror"
                           name="api_key" placeholder="{{ __('Enter API Key') }}"
                           value="{{old('api_key', $provider->api_key)}}">
                    @error('api_key')
                    <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                    @enderror
                </div>
            </div>
        </div>

        @if( $provider->id == null )

        @endif

        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-12 formBtn">
                @if( $provider->id == null )
                    <button type="submit" class="btn btn-primary">{{ __('Save') }}</button>
                @else
                    <button type="submit" class="btn btn-primary">{{ __('Update') }}</button>
                @endif
            </div>
        </div>

    </form>
</div>
            </div>
        </div>
    </div>
</div>

@section('pageJs')

    <script>

    </script>

@endsection
