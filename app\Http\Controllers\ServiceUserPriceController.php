<?php

namespace App\Http\Controllers;

use App\Base\Model\BaseModel;
use App\Components\Helper;
use App\Constants\CommonConstants;
use App\Constants\UserConstants;
use App\Models\ServiceUserPrice;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Yajra\DataTables\Facades\DataTables;

class ServiceUserPriceController extends Controller
{
    public function index()
    {
        return view('service-user-price.index');
    }

    public function show(ServiceUserPrice $serviceUserPrice,Request $request)
    {
        return redirect()->route('service-user-price.index');
        return view('service-user-price.show', compact('serviceUserPrice'));
    }

    public function create()
    {
        $serviceUserPrice=new ServiceUserPrice();
        $serviceUserPrice->is_active=CommonConstants::YES;
        return view('service-user-price.create', compact('serviceUserPrice'));
    }

    public function edit(ServiceUserPrice $serviceUserPrice)
    {
        return view('service-user-price.edit', compact('serviceUserPrice'));
    }

    public function store(Request $request)
    {
        $serviceUserPrice = new ServiceUserPrice();
        return $this->save($request, $serviceUserPrice);
    }

    public function update(Request $request, ServiceUserPrice $serviceUserPrice)
    {
        return $this->save($request, $serviceUserPrice);
    }

    private function save(Request $request, ServiceUserPrice $serviceUserPrice)
    {
        $isNewRecord = true;
        if ($serviceUserPrice->id != null) {
            $isNewRecord = false;
        }

        $rules = [
            'service_id' => ['required', 'integer'],
            'user_id' => ['required', 'integer'],
            'is_active' => ['required', 'integer'],
            'price' => ['required','numeric'],
        ];

        if ($isNewRecord) {

        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            if ($isNewRecord) {
                return redirect()->route('service-user-price.create')->withErrors($validator)->withInput();
            } else {
                return redirect()->route('service-user-price.edit', $serviceUserPrice->id)->withErrors($validator)->withInput();
            }
        }

        $serviceUserPrice->service_id = (int)$request->input('service_id');
        $serviceUserPrice->user_id = (int)$request->input('user_id');
        $isExist=ServiceUserPrice::query()
            ->where('service_id','=',$serviceUserPrice->service_id)
            ->where('user_id','=',$serviceUserPrice->user_id)
            ->first();
        if($isExist) {
            $serviceUserPrice=$isExist;
            $isNewRecord=false;
        }
        $serviceUserPrice->is_active = (int)$request->input('is_active');
        $serviceUserPrice->price = trim($request->input('price'));
        if ($isNewRecord) {
            $serviceUserPrice->save();
        } else {
            $serviceUserPrice->update();
        }

        \App\Models\Service::generateCacheFile();
        resetServicesCache();
        return redirect()->route('service-user-price.index')->with('success', 'Saved successfully.');
    }

    public function dataTable(Request $request)
    {
        try {
            if ($request->ajax()) {
                $query = ServiceUserPrice::query();
                BaseModel::buildFilterQuery($query, [
                    'q' => ['price'],
                    'user_id',
                    'service_id',
                    'is_active',
                ]);
                return Datatables::eloquent($query)
                    ->addColumn('checkboxes', function ($row) {
                        return '<input type="checkbox" name="pdr_checkbox[]" class="pdr_checkbox" value="' . $row->id . '" />';
                    })
                    ->addColumn('user_id', function ($row) {
                        return $row->user->displayDetails;
                    })
                    ->addColumn('service_id', function ($row) {
                        return $row->service->displayName;
                    })
                    ->addColumn('price', function ($row) {
                        return Helper::editPopupStructure($row,'price',null,'Price (in $)');
                    })
                    ->addColumn('created_at', function ($row) {
                        return Helper::displayTime($row->created_at);
                    })
                    ->addColumn('updated_at', function ($row) {
                        return Helper::displayTime($row->updated_at);
                    })
                    ->addColumn('is_active', function ($row) use ($query) {
                        $columnName = 'is_active';
                        return Helper::onOffButton($row, $columnName, $query);
                    })
                    ->addColumn('actions', function ($row) {
                        $buttons = [];
                        //$buttons['Login']=['url' => route('user.loginAs', $row->id), 'icon' => 'las la-sign-in-alt'];
                        //$buttons['view'] = ['url' => route('service-user-price.show', $row->id)];
                        $buttons['edit'] = ['url' => route('service-user-price.edit', $row->id)];
                        return Helper::getActionButtons($buttons);
                    })
                    ->rawColumns(['checkboxes', 'is_active','custom_id', 'price','sid', 'is_chinese_result','display_order', 'actions'])
                    ->make(true);
            }
        } catch (\Exception $e) {
            //print_r(['message'=>$e->getMessage(),'trace'=>$e->getTraceAsString()]);
            die();
        }
    }
}
