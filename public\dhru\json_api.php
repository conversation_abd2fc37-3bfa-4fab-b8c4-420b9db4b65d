<?php
if(array_key_exists('HTTP_CF_CONNECTING_IP', $_SERVER))
{
    $_SERVER['REMOTE_ADDR']=$_SERVER['HTTP_CF_CONNECTING_IP'];
}
if(array_key_exists('HTTP_X_FORWARDED_FOR', $_SERVER))
{
    $_SERVER['REMOTE_ADDR']=$_SERVER['HTTP_X_FORWARDED_FOR'];
    if(array_key_exists('HTTP_CF_CONNECTING_IP', $_SERVER))
    {
        $_SERVER['HTTP_CF_CONNECTING_IP']=$_SERVER['HTTP_X_FORWARDED_FOR'];
    }
}

if(count(explode(",", $_SERVER['REMOTE_ADDR']))>1) {
    $ex=explode(",", $_SERVER['REMOTE_ADDR']);
    $_SERVER['REMOTE_ADDR']=trim($ex[0]);
}
function sendRequest($url,$postparams=null)
{
	if($postparams==null)
	{
		$postparams=array();
	}

		if(is_array($postparams)) {
	    	if(!array_key_exists('user_ip',$postparams)) {
	        	$postparams['user_ip']=$_SERVER['REMOTE_ADDR'];
        }
	    	if(array_key_exists('HTTP_CF_CONNECTING_IP',$_SERVER)) {
            if(!array_key_exists('user_cf_ip',$postparams)) {
                $postparams['user_cf_ip']=$_SERVER['HTTP_CF_CONNECTING_IP'];
            }
        }
    }

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_VERBOSE, 0);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS,http_build_query($postparams));
    curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible;)");

    curl_setopt($ch, CURLOPT_URL, $url);

    $response = curl_exec($ch);
    curl_close($ch);
	$result=FALSE;
	$data=json_decode($response,true);
	if($response!=FALSE && $response!="" && $data)
	{
		$result=$data;
	}
	return $result;
}

function getOrderDetails($url,$api_key,$tran_id)
{
	try{
		$url=$url."api/get-order";
		$postparams = array('api_key'=>$api_key,'tran_id'=>$tran_id);
		$response=sendRequest($url,$postparams);
		if(is_array($response) && array_key_exists('status', $response) && array_key_exists('response', $response))
		{
			$html="";
			if(array_key_exists('result', $response['response']))
			{
				if(is_array($response['response']['result']))
				{
					foreach($response['response']['result'] as $k=>$v)
					{
						$html.=ucwords(str_replace("_", " ", $k))." : ".$v."<br />";
					}
				}
				else
				{
					$html=$response['response']['result'];
				}
			}

			if($response['status']=="success")
			{
				return array('status'=>4,'imei'=>$response['response']['imei'],'result'=>$html);
			}
			else if($response['status']!="pending"){
				return array('status'=>3,'imei'=>$response['response']['imei'],'result'=>$html);
			}
		}
	}
	catch(Exception $e)
	{
		//getting any error
	}
}

function getBulkOrderDetails($url,$api_key,$tran_id)
{
	try{
		$url=$url."api/get-bulk-order";
		$postparams = array('api_key'=>$api_key,'tran_id'=>$tran_id);
		$response=sendRequest($url,$postparams);
		if(is_array($response) && array_key_exists('status', $response) && array_key_exists('response', $response))
		{
			$resp=array();
			foreach ($response['response'] as $k_key => $v_value)
			{
				$html="";
				if(array_key_exists('result', $v_value['response']))
				{
					if(is_array($v_value['response']['result']))
					{
						foreach($v_value['response']['result'] as $k=>$v)
						{
							$html.=ucwords(str_replace("_", " ", $k))." : ".$v."<br />";
						}
					}
					else
					{
						$html=$v_value['response']['result'];
					}
				}

				if($v_value['status']=="success")
				{
					$resp[]= array('status'=>4,'imei'=>$v_value['response']['imei'],'result'=>$html);
				}
				else if($v_value['status']!="pending")
				{
					$resp[]= array('status'=>3,'imei'=>$v_value['response']['imei'],'result'=>$html);
				}
			}
			return $resp;

		}
	}
	catch(Exception $e)
	{
		//getting any error
	}
	return array();
}

function placeOrder($url,$api_key,$api_service,$imei)
{
	try{
		//$url=$url."api/place-order";
		$url=$url."api/place-bulk-order";
		$postparams = array('api_key'=>$api_key,'service'=>$api_service,'imei'=>$imei,'dhru_order'=>1);
		file_put_contents("sending.html", json_encode($postparams));
		$resp=sendRequest($url,$postparams);
		$response=array();
		if(is_array($resp) && array_key_exists('status', $resp) && array_key_exists('response', $resp))
		{
			if($resp['status']=="success")
			{
				if(array_key_exists(''.$imei, $resp['response']))
				{
					$response=$resp['response'][''.$imei];
				}
			}
		}
		if(is_array($response) && array_key_exists('status', $response) && array_key_exists('response', $response))
		{

			if($response['status']=="success")
			{
				//order success
				//$response['response']
				return $response;
			}
			else {
				//you got error message $response['response']['message']
				//any error
				if($response['status']=="error")
				{
					if(array_key_exists('response', $response))
					{
						if(array_key_exists('result', $response['response']))
						{
							return $response['response']['result'];
						}
						else if(array_key_exists('message', $response['response']))
						{
							return $response['response']['message'];
						}
					}
				}
			}
		}
	}
	catch(Exception $e)
	{
		//getting any error
	}
	return null;
}

function placeBulkOrder($url,$api_key,$api_service,$imei)
{
	try{
		$url=$url."api/place-bulk-order";
		$postparams = array('api_key'=>$api_key,'service'=>$api_service,'imei'=>$imei,'dhru_order'=>1);
		$response=sendRequest($url,$postparams);
		if(is_array($response) && array_key_exists('status', $response) && array_key_exists('response', $response))
		{
			if($response['status']=="success")
			{
				//order success
				//$response['response']
				return $response['response'];
			}
			else
			{
				return "Try Later.";
				//you got error message $response['response']['message']
				//any error
				if($response['status']=="error")
				{
					if(array_key_exists('response', $response))
					{
						if(array_key_exists('result', $response['response']))
						{
							return $response['response']['result'];
						}
						else if(array_key_exists('message', $response['response']))
						{
							return $response['response']['message'];
						}
					}
				}
			}
		}
	}
	catch(Exception $e)
	{
		//getting any error
	}
	return null;
}

function getBalance($url,$api_key)
{
	try{
		$url=$url."api/get-balance";
		$postparams = array('api_key'=>$api_key);
		$response=sendRequest($url,$postparams);
		//var_dump($response);
		if(is_array($response) && array_key_exists('status', $response) && array_key_exists('response', $response))
		{
			if($response['status']=="success")
			{
				//$response['response']['email']
				//$response['response']['balance']
				//$response['response']['currency']
				return $response;
			}
			else {
				//you got error message $response['response']['message']
				//any error
			}
		}
	}
	catch(Exception $e)
	{
		//getting any error
	}
}
function getServices($url,$api_key)
{
	try{
		$url=$url."api/get-services";
		$postparams = array('api_key'=>$api_key);
		$response=sendRequest($url,$postparams);

		if(is_array($response) && array_key_exists('status', $response) && array_key_exists('response', $response))
		{
			if($response['status']=="success")
			{
				//$response['response']
				return $response;
			}
			else {
				//you got error message $response['response']['message']
				//any error
			}
		}
	}
	catch(Exception $e)
	{
		//getting any error
	}
}
