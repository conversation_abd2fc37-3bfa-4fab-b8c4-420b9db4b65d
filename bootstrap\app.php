<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->validateCsrfTokens(except: [
            '/ajax/*',
            '/api/*',
            '/order-api/*',
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        $exceptions->renderable(function (Throwable $e, $request) {
            if ($e instanceof \ErrorException ||
                $e instanceof \Error ||
                $e instanceof \ReflectionException ||
                $e instanceof \Symfony\Component\HttpKernel\Exception\HttpException && $e->getStatusCode() === 500)
            {
                //return response()->json(['error'=>true,'status_code'=> $e->getCode() ?:503,'message'=>'Oops! Something went wrong, it\'s us, we will fix it soon','data'=>''], $e->getCode());
                return response()->json(json_decode($e->getMessage(),true), $e->getCode());
            }
        });
    })->create();
