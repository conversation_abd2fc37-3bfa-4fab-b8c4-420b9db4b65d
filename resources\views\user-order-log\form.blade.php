<div class="row mb-4">
    <div class="col-lg-12 col-md-12 mb-4">
        <div class="card">
            <div class="card-body">

<div class="rounded-16 form-box bg-white -dark-bg-dark-1 shadow-4 h-100">
    <form action="{{ $adminAdjustment->id ==null ? route('admin-adjustment.store') : route('admin-adjustment.update', $adminAdjustment) }}" method="POST"
          class="normal-form">
        @csrf

        @if( $adminAdjustment->id != null )
            @method('PUT')
        @endif

        @if( $adminAdjustment->id == null )
            <div class="row">
                <div class="col-md-8 col-12">
                    <div class="form-group required-field text-left @error('user_id') is-invalid @enderror">
                        <label for="user_id">{{ __('User') }}</label>
                        <select required name="user_id" id="user_id" class="form-control filterField search-user-ajax">
                            <option value="">Search Username</option>
                        </select>
                        @error('user_id')
                        <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                        @enderror
                    </div>
                </div>

                <div class="col-md-4 col-12">
                    <div class="form-group required-field text-left @error('amount') is-invalid @enderror">
                        <label for="amount">{{ __('Amount') }}</label>
                        <input required id="amount" type="text" class="form-control @error('amount') is-invalid @enderror"
                               name="amount" placeholder="{{ __('Enter amount') }}"
                               value="{{old('amount', $adminAdjustment->amount)}}">
                        @error('amount')
                        <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                        @enderror
                    </div>
                </div>

                <div class="col-md-4 col-12">
                    <div class="form-group required-field text-left @error('is_debit') is-invalid @enderror">
                        <label for="is_debit">{{ __('Type') }}</label>
                        <select name="is_debit" id="is_debit" class="form-control @error('is_debit') is-invalid @enderror">
                            <option value="<?=\App\Constants\CommonConstants::CREDIT?>">Credit</option>
                            <option value="<?=\App\Constants\CommonConstants::DEBIT?>">Debit</option>
                        </select>
                        @error('is_debit')
                        <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4 col-12">
                    <div class="form-group required-field text-left @error('is_invoice') is-invalid @enderror">
                        <label for="is_invoice">{{ __('Generate Invoice') }}</label>
                        <select name="is_invoice" id="is_invoice" class="form-control @error('is_invoice') is-invalid @enderror">
                            <option value="<?=\App\Constants\CommonConstants::NO?>">No</option>
                            <option value="<?=\App\Constants\CommonConstants::YES?>">Yes</option>
                        </select>
                        @error('is_invoice')
                        <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4 col-12">
                    <div class="form-group required-field text-left @error('is_paid') is-invalid @enderror">
                        <label for="is_paid">{{ __('Paid Invoice') }}</label>
                        <select name="is_paid" id="is_paid" class="form-control @error('is_paid') is-invalid @enderror">
                            <option value="<?=\App\Constants\CommonConstants::NO?>">No</option>
                            <option value="<?=\App\Constants\CommonConstants::YES?>">Yes</option>
                        </select>
                        @error('is_paid')
                        <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                        @enderror
                    </div>
                </div>

                <div class="col-md-12 col-12">
                    <div class="form-group required-field text-left @error('comments') is-invalid @enderror">
                        <label for="comments">{{ __('Comments') }}</label>
                        <input id="comments" type="text" class="form-control @error('comments') is-invalid @enderror"
                               name="comments" placeholder="{{ __('Enter comments') }}"
                               value="{{old('comments', $adminAdjustment->comments)}}">
                        @error('comments')
                        <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                        @enderror
                    </div>
                </div>
            </div>
        @endif

        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-12 formBtn">
                @if( $adminAdjustment->id == null )
                    <button type="submit" class="btn btn-primary">{{ __('Save') }}</button>
                @else
                    <button type="submit" class="btn btn-primary">{{ __('Update') }}</button>
                @endif
            </div>
        </div>

    </form>
</div>
            </div>
        </div>
    </div>
</div>

@section('pageJs')

    <script>

    </script>

@endsection
