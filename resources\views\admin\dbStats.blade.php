<?php
use App\Components\Helper;
?>
@extends(\App\Components\Helper::getLayoutForUser())
@section('content')

    <main>
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <h1>IMEI/SN Stats</h1>
                    <nav class="breadcrumb-container d-none d-sm-block d-lg-inline-block" aria-label="breadcrumb">
                        <ol class="breadcrumb pt-0">
                            <li class="breadcrumb-item">
                                <a href="{{\App\Components\Helper::dashboardLink()}}">Home</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="javascript:void(0);">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">IMEI/SN Stats</li>
                        </ol>
                    </nav>
                    <div class="separator mb-5"></div>
                </div>
                <div class="col-lg-12 col-xl-12 mx-auto">
                    <div class="row">
                        <div class="col-md-12 mb-4">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        IMEI/SN Stats                                </h5>
                                    <div class="row">
                                        <div class="col-xs-8 mx-auto">
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-10 mx-auto p-30">
                                            <form id="w0" class="form" action="https://goimeicheck.com/admin/db-stats" method="get">                                        <div class="row">
                                                    <div class="col-md-3">
                                                        <div class="form-group field-servicerequests-added_on required">
                                                            <div class="controls"><label class="control-label" for="servicerequests-added_on">Start</label><input type="date" id="servicerequests-added_on" class="form-control" name="start" value="2025-04-08" placeholder="Start Date" autocomplete="off" aria-required="true"><p class="help-block help-block-error"></p></div>
                                                        </div>                                            </div>
                                                    <div class="col-md-3">
                                                        <div class="form-group field-servicerequests-updated_on">
                                                            <div class="controls"><label class="control-label" for="servicerequests-updated_on">End</label><input type="date" id="servicerequests-updated_on" class="form-control" name="end" value="2025-04-08" placeholder="End Date" autocomplete="off"><p class="help-block help-block-error"></p></div>
                                                        </div>                                            </div>
                                                    <div class="col-md-3">
                                                        <div class="form-group field-servicerequests-service_id required">
                                                            <div class="controls"><label class="control-label" for="servicerequests-service_id">Service</label><select id="servicerequests-service_id" class="form-control select2-single" name="service" aria-required="true">
                                                                    <option value="">All Services</option>
                                                                    <optgroup label="APPLE CARRIER CHECK SERVICES">
                                                                        <option value="1">ICS000# Apple iPhone Carrier Checker (IMEI/SN) - $0.1</option>
                                                                        <option value="84">ICS001# Apple iPhone Carrier + FMI On/Off Checker (IMEI/SN) - $0.15</option>
                                                                        <option value="23">ICS002# Apple iPhone Warranty + Carrier + FMI Checker (IMEI/SN) - $0.35</option>
                                                                        <option value="150">ICS003# Apple iPhone Warranty + Carrier + GSMA Checker (IMEI/SN) - $0.2</option>
                                                                        <option value="70">ICS004# Apple iPhone Warranty + Carrier + FMI + GSMA Checker (IMEI/SN) - $0.4</option>
                                                                        <option value="127">ICS005# Apple iPhone Carrier + FMI + EID + Warranty + GSMA Checker (IMEI/SN) - $1</option>
                                                                        <option value="3">ICS009# Exclusive All In One Carrier Checker (IMEI/SN) - $2</option>
                                                                    </optgroup>
                                                                    <optgroup label="APPLE SIMLOCK CHECK SERVICES">
                                                                        <option value="121">ICS009# Apple iPhone SimLock Checker (IMEI/SN) - $0.08</option>
                                                                        <option value="73">ICS010# Apple iPhone SimLock + FMI On/Off Checker (IMEI/SN) - $0.1</option>
                                                                    </optgroup>
                                                                    <optgroup label="Apple IMEI-SN &amp; SN-IMEI INSTANT CHECK SERVICES">
                                                                        <option value="5">ICS011# Apple iPhone IMEI ⇄ IMEI2 ⇄ Serial Convert ( ✔️ Color ✔️ Storage ) - $0.04</option>
                                                                        <option value="98">ICS012# Apple iPhone IMEI ⇄ IMEI2 ⇄ Serial Convert (✔️ Apple Care &amp; ✔️ Warranty ❌ Color &amp; ❌ Storage) - $0.04</option>
                                                                        <option value="132">ICS013# Apple iPhone IMEI ⇄ IMEI2 ⇄ Serial Convert  (✔️ Apple Care  ✔️ Replaced Device &amp; ✔️ Warranty)- Exclusive Service - $0.04</option>
                                                                    </optgroup>
                                                                    <optgroup label="APPLE INSTANT CHECK SERVICES">
                                                                        <option value="86">ICS009# Apple All Devices Part Number / MPN + DOP (IMEI/SN) - $0.2</option>
                                                                        <option value="33">ICS013# Apple All Devices Warranty Check Service (IMEI/SN) - $0.05</option>
                                                                        <option value="28">ICS014# Apple All Devices Warranty Check Service (SN Only) - $0.05</option>
                                                                        <option value="149">ICS015# Apple All Devices Warranty PRO Check Service (IMEI/SN) - $0.05</option>
                                                                        <option value="89">ICS089# Apple iPhone  Purchase Country Checker (IMEI/SN) - $0.12</option>
                                                                        <option value="90">ICS090# Apple All Devices Replacement Status Checker (IMEI/SN) - $0.1</option>
                                                                        <option value="136">ICS091# Apple All Devices Activation Status Checker (IMEI/SN) - $0.1</option>
                                                                        <option value="63">ICS092# Apple All Devices Activation &amp; Replacement Status Checker (IMEI/SN) - $0.1</option>
                                                                    </optgroup>
                                                                    <optgroup label="APPLE MDM CHECK SERVICES">
                                                                        <option value="131">ICS010# Apple All Devices  MPN + MDM On/OFF Checker - (IMEI/SN) - $0.7</option>
                                                                        <option value="39">ICS011# Apple iPhone/iPad/iMac/Macbook MDM Checker -(IMEI/SN) - Source B - $0.5</option>
                                                                    </optgroup>
                                                                    <optgroup label="APPLE GSX SERVICES">
                                                                        <option value="120">ICS#181 Apple GSX Next-Tether Check Service --&gt; IMEI/SN - $0.8</option>
                                                                        <option value="91">ICS160# Apple iPhone/iPad GSX Full - Replace/Case/Sold/Mac Checker (IMEI/SN) - $3</option>
                                                                        <option value="93">ICS163# Apple iPhone/iPad/Mac GSX Policies &amp; WIFI MAC Address Checker (IMEI/SN) - $2.75</option>
                                                                        <option value="76">ICS164# Apple All Devices Sold By Info - $2.25</option>
                                                                        <option value="130">ICS991# Apple All Devices GSX Case History Only - $1.5</option>
                                                                        <option value="85">ICS992# Apple All Devices Case ID Info Only - $1.5</option>
                                                                        <option value="68">ICS995# Apple All Devices GSX Cases &amp; Repairs - $1.5</option>
                                                                    </optgroup>
                                                                    <optgroup label="BLACKLIST CHECK SERVICES">
                                                                        <option value="8">ICS0001# Worldwide GSMA Blacklist Checker By IMEI - $0.08</option>
                                                                        <option value="64">ICS0002# Worldwide GSMA Blacklist Pro Checker - By IMEI - $0.15</option>
                                                                        <option value="139">ICS0003# Japan NTT Docomo Network checker By IMEI - $0.1</option>
                                                                        <option value="61">ICS0004# Japan AU KDDI Network checker By IMEI - $0.15</option>
                                                                        <option value="60">ICS0005# Japan Softbank Network Checker By IMEI - $0.15</option>
                                                                        <option value="146">ICS0006# Korea Blacklist Checker By IMEI - $0.15</option>
                                                                        <option value="142">ICS0007# US ATT Network Status Checker By IMEI - $0.15</option>
                                                                        <option value="55">ICS0008# US Verizon Network Checker By IMEI - $0.15</option>
                                                                        <option value="141">ICS0009# US Verizon Network PRO Checker By IMEI - $0.15</option>
                                                                        <option value="143">ICS0010# US Spectrum Network Checker By IMEI - $0.15</option>
                                                                        <option value="62">ICS0011# USA T-Mobile Clean/Blocked/Unpaid checker By IMEI - $0.1</option>
                                                                        <option value="124">ICS0012# USA T-Mobile Generic Simlock &amp; Unlock Eligibility Status Checker Pro - IMEI - $0.1</option>
                                                                    </optgroup>
                                                                    <optgroup label="GENERIC CHECK SERVICES">
                                                                        <option value="87">ICS000# Tac Model Checker By IMEI - $0.05</option>
                                                                        <option value="113">ICS001# LG Full Info Check By IMEI - $0.15</option>
                                                                        <option value="140">ICS002# Alcatel Info Checker By IMEI - $0.15</option>
                                                                        <option value="144">ICS003# OPPO Full Info Check By IMEI - $0.15</option>
                                                                        <option value="145">ICS004# VIVO Full Info Check By IMEI - $0.15</option>
                                                                        <option value="137">ICS005# ZTE Model + Warranty Checker By IMEI - $0.15</option>
                                                                        <option value="97">ICS006# Google Pixel Model + Warranty Checker By IMEI - $0.15</option>
                                                                        <option value="99">ICS007# Huawei Warranty Checker Full Info By IMEI - $0.1</option>
                                                                        <option value="138">ICS008# One Plus Model +Warranty Checker By IMEI - $0.15</option>
                                                                        <option value="80">ICS009# Samsung Carrier + Warranty Checker Only IMEI - $0.15</option>
                                                                        <option value="148">ICS010# Samsung KG (KNOX Guard) On/Off Checker - (IMEI/SN) - $0.5</option>
                                                                        <option value="110">ICS012# Nokia Full Info Country + Warranty Check By IMEI - $0.25</option>
                                                                        <option value="111">ICS013# Motorola Full Info Country + Warranty Check By IMEI - $0.1</option>
                                                                        <option value="112">ICS014# Lenovo Full Info Country + Warranty Check By IMEI - $0.1</option>
                                                                        <option value="34">ICS014# Samsung Full Info - $0.05</option>
                                                                    </optgroup>
                                                                    <optgroup label="APPLE FMI &amp; ICLOUD CHECK SERVICES">
                                                                        <option value="20">ICS012# Apple iPhone FMI ON/OFF Check Service (IMEI/SN)- Source A - $0.04</option>
                                                                        <option value="133">ICS013# Apple iPhone/iPad FMI ON/OFF Checker Service (IMEI/SN) - Source B - $0.5</option>
                                                                        <option value="26">ICS014# Apple iPhone FMI ON/OFF &amp; iCloud CLEAN/LOST Check Service - (IMEI/SN) - $0.07</option>
                                                                        <option value="77">ICS019# Apple Macbook/iMac FMI ON/OFF Checker - BY SN - $0.6</option>
                                                                        <option value="129">ICS021# Apple Macbook/iMac FMI ON/OFF &amp; iCloud Clean/Lost Checker - BY SN - Exclusive Service - $0.5</option>
                                                                    </optgroup>
                                                                    <optgroup label="XIAOMI MI INSTANT CHECK SERVICES">
                                                                        <option value="122">ICS0110# Xiaomi Mi Lock Status Check ON/OFF Only - IMEI/CODE - $0.1</option>
                                                                        <option value="88">ICS0111# Xiaomi MI Warranty + Activation Lock + Blacklist Checker By IMEI - $0.15</option>
                                                                        <option value="123">ICS0112# Xiaomi Full Check Info Sales/Activation/Used Region &amp; Lock Status On/OFF Checker Pro - IMEI/Code - $0.2</option>
                                                                    </optgroup>
                                                                    <optgroup label="Services Not In Use">
                                                                        <option value="115">ICS007# Testing Service - $0.02</option>
                                                                    </optgroup>
                                                                </select><p class="help-block help-block-error"></p></div>
                                                        </div>                                            </div>
                                                    <div class="col-md-3">
                                                        <input type="submit" value="Search" class="btn btn-success btn-block mt-4" />
                                                    </div>
                                                </div>
                                            </form>                                    </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="table-responsive">
                                                <table class="table">
                                                    <thead>
                                                    <tr>
                                                        <th>Date</th>
                                                        <th>Newly Added</th>
                                                        <th>Total Orders</th>
                                                        <th>DB Orders</th>
                                                        <th>Total Charged</th>
                                                        <th>Total Cost</th>
                                                        <th>DB Charged</th>
                                                        <th>DB Cost</th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    <tr>
                                                        <td>08 Apr, 2025</td>
                                                        <td>46,934</td>
                                                        <td>60,834</td>
                                                        <td>0</td>
                                                        <td>635.8890</td>
                                                        <td>116.6966</td>
                                                        <td>0.0000</td>
                                                        <td>0.0000</td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

@endsection
@section('pageJs')



@endsection
