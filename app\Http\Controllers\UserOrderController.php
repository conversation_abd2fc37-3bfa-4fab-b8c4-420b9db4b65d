<?php

namespace App\Http\Controllers;

use App\Base\Model\BaseModel;
use App\Components\Helper;
use App\Constants\CommonConstants;
use App\Constants\UserConstants;
use App\Models\UserOrder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Yajra\DataTables\Facades\DataTables;

class UserOrderController extends Controller
{
    public function index()
    {
        return view('user-order.index');
    }

    public function show(UserOrder $userOrder,Request $request)
    {
        return redirect()->route('user-order.index');
        return view('user-order.show', compact('userOrder'));
    }

    public function create()
    {
        return redirect()->route('user-order.index');
        $userOrder=new UserOrder();
        return view('user-order.create', compact('userOrder'));
    }

    public function edit(UserOrder $userOrder)
    {
        return redirect()->route('user-order.index');
        return view('user-order.edit', compact('userOrder'));
    }

    public function store(Request $request)
    {
        $userOrder = new UserOrder();
        return $this->save($request, $userOrder);
    }

    public function update(Request $request, UserOrder $userOrder)
    {
        return $this->save($request, $userOrder);
    }

    private function save(Request $request, UserOrder $userOrder)
    {
        return redirect()->route('user-order.index');
        $isNewRecord = true;
        if ($userOrder->id != null) {
            $isNewRecord = false;
        }

        $rules = [
            'operating_system_id' => ['required', 'integer'],
            'category_id' => ['required', 'integer'],
            'provider_id' => ['required', 'integer'],
            'display_order' => ['required', 'integer'],
            'name' => ['required', 'string'],
            'order_format' => ['required', 'string'],
            'download_format' => ['required', 'string'],
            'price' => ['required','numeric'],
        ];

        if ($isNewRecord) {

        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            if ($isNewRecord) {
                return redirect()->route('user-order.create')->withErrors($validator)->withInput();
            } else {
                return redirect()->route('user-order.edit', $userOrder->id)->withErrors($validator)->withInput();
            }
        }

        $userOrder->operating_system_id = (int)$request->input('operating_system_id');
        $userOrder->category_id = (int)$request->input('category_id');
        $userOrder->custom_id = (int)$request->input('custom_id');
        $userOrder->provider_id = (int)$request->input('provider_id');
        $userOrder->display_order = (int)$request->input('display_order');
        $userOrder->name = $request->input('name');
        $userOrder->is_active = (int)$request->input('is_active');
        $userOrder->price = trim($request->input('price'));
        $userOrder->order_format = trim($request->input('order_format'));
        $userOrder->download_format = trim($request->input('download_format'));
        if ($isNewRecord) {
            $userOrder->save();
        } else {
            $userOrder->update();
        }
        return redirect()->route('user-order.index')->with('success', 'UserOrder saved successfully.');
    }

    public function dataTable(Request $request)
    {
        try {
            if ($request->ajax()) {
                $query = UserOrder::query();
                BaseModel::buildFilterQuery($query, [
                    'q' => ['order_ref','batch_ref','imei'],
                    'user_id',
                    'service_id',
                ]);
                return Datatables::eloquent($query)
                    ->addColumn('checkboxes', function ($row) {
                        return '<input type="checkbox" name="pdr_checkbox[]" class="pdr_checkbox" value="' . $row->id . '" />';
                    })
                    ->addColumn('user', function ($row) {
                        if($row->user) {
                            return $row->user->displayDetails;
                        }
                        return 'N/A';
                    })
                    ->addColumn('service', function ($row) {
                        if($row->service) {
                            return $row->service->displayDetails;
                        }
                        return 'N/A';
                    })
                    ->addColumn('price', function ($row) {
                        return Helper::printAmount($row->price);
                    })
                    ->addColumn('status', function ($row) {
                        return $row->printOrderStatus();
                    })
                    ->addColumn('date', function ($row) {
                        $html='<span title="Submited" class="badge badge-warning">'.$row->created_at.'</span>';
                        $html.='<br><span title="Replied" class="badge badge-success">'.$row->updated_at.'</span>';
                        $html.='<br><span title="Time Taken" class="badge badge-info">Time Taken : '.$row->time_taken.' (s)</span>';
                        return $html;
                    })
                    ->addColumn('actions', function ($row) {
                        $buttons = [];
                        //$buttons['Login']=['url' => route('user.loginAs', $row->id), 'icon' => 'las la-sign-in-alt'];
                        //$buttons['view'] = ['url' => route('user-order.show', $row->id)];
                        //$buttons['edit'] = ['url' => route('user-order.edit', $row->id)];
                        return Helper::getActionButtons($buttons);
                    })
                    ->rawColumns(['checkboxes', 'status', 'result','date', 'actions'])
                    ->make(true);
            }
        } catch (\Exception $e) {
            //print_r(['message'=>$e->getMessage(),'trace'=>$e->getTraceAsString()]);
            die();
        }
    }
}
