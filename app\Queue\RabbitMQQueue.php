<?php

namespace App\Queue;

use Illuminate\Contracts\Queue\Queue as QueueContract;
use Illuminate\Queue\Queue;
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;
use PhpAmqpLib\Channel\AMQPChannel;

class RabbitMQQueue extends Queue implements QueueContract
{
    protected $connection;
    protected $channel;
    protected $defaultQueue;
    protected $exchange;
    protected $exchangeType;
    protected $exchangeRoutingKey;
    protected $persistent;

    public function __construct(
        AMQPStreamConnection $connection,
        $defaultQueue,
        $exchange = 'default',
        $exchangeType = 'direct',
        $exchangeRoutingKey = '',
        $persistent = true
    ) {
        $this->connection = $connection;
        $this->channel = $connection->channel();
        $this->defaultQueue = $defaultQueue;
        $this->exchange = $exchange;
        $this->exchangeType = $exchangeType;
        $this->exchangeRoutingKey = $exchangeRoutingKey;
        $this->persistent = $persistent;

        $this->declareQueue($defaultQueue);
        $this->declareExchange();
    }

    /**
     * Get the size of the queue.
     *
     * @param  string|null  $queue
     * @return int
     */
    public function size($queue = null)
    {
        $queue = $queue ?: $this->defaultQueue;

        list($queueName, $messageCount, $consumerCount) = $this->channel->queue_declare($queue, true);

        return $messageCount;
    }

    /**
     * Push a new job onto the queue.
     *
     * @param  string  $job
     * @param  mixed  $data
     * @param  string|null  $queue
     * @return mixed
     */
    public function push($job, $data = '', $queue = null)
    {
        return $this->pushRaw($this->createPayload($job, $queue, $data), $queue);
    }

    /**
     * Push a raw payload onto the queue.
     *
     * @param  string  $payload
     * @param  string|null  $queue
     * @param  array  $options
     * @return mixed
     */
    public function pushRaw($payload, $queue = null, array $options = [])
    {
        $queue = $queue ?: $this->defaultQueue;

        $this->declareQueue($queue);

        $message = new AMQPMessage($payload, [
            'delivery_mode' => $this->persistent ? AMQPMessage::DELIVERY_MODE_PERSISTENT : AMQPMessage::DELIVERY_MODE_NON_PERSISTENT,
        ]);

        // Log the job being pushed for debugging
        \Log::info('RabbitMQ: Pushing job to queue', [
            'queue' => $queue,
            'exchange' => $this->exchange,
            'routing_key' => $queue, // Use queue name as routing key
            'payload_size' => strlen($payload),
            'payload_preview' => substr($payload, 0, 200) . '...',
            //'connection_host' => $this->connection->getHost(),
            //'connection_vhost' => $this->connection->getVhost()
        ]);

        try {
            // Publish directly to the queue (empty exchange means direct routing to queue)
            $this->channel->basic_publish($message, '', $queue);

            \Log::info('RabbitMQ: Job pushed successfully', [
                'queue' => $queue,
                'message_delivered' => true
            ]);
        } catch (\Exception $e) {
            \Log::error('RabbitMQ: Failed to push job', [
                'queue' => $queue,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }

        return true;
    }

    /**
     * Push a new job onto the queue after a delay.
     *
     * @param  \DateTimeInterface|\DateInterval|int  $delay
     * @param  string  $job
     * @param  mixed  $data
     * @param  string|null  $queue
     * @return mixed
     */
    public function later($delay, $job, $data = '', $queue = null)
    {
        // RabbitMQ doesn't natively support delayed messages
        // You would need a plugin like rabbitmq-delayed-message-exchange
        // For now, we'll just push immediately
        return $this->push($job, $data, $queue);
    }

    /**
     * Pop the next job off of the queue.
     *
     * @param  string|null  $queue
     * @return \Illuminate\Contracts\Queue\Job|null
     */
    public function pop($queue = null)
    {
        $queue = $queue ?: $this->defaultQueue;

        $this->declareQueue($queue);

        $message = $this->channel->basic_get($queue);

        if ($message) {
            return new RabbitMQJob($this->container, $this->channel, $message, $this->connectionName, $queue);
        }

        return null;
    }

    /**
     * Declare a queue.
     *
     * @param  string  $queue
     * @return void
     */
    protected function declareQueue($queue)
    {
        $this->channel->queue_declare(
            $queue,     // queue name
            false,      // passive
            true,       // durable
            false,      // exclusive
            false       // auto_delete
        );
    }

    /**
     * Declare an exchange.
     *
     * @return void
     */
    protected function declareExchange()
    {
        $this->channel->exchange_declare(
            $this->exchange,
            $this->exchangeType,
            false,      // passive
            true,       // durable
            false       // auto_delete
        );
    }

    /**
     * Get the connection for the queue.
     *
     * @return \PhpAmqpLib\Connection\AMQPStreamConnection
     */
    public function getConnection()
    {
        return $this->connection;
    }

    /**
     * Get the underlying AMQP channel.
     *
     * @return \PhpAmqpLib\Channel\AMQPChannel
     */
    public function getChannel()
    {
        return $this->channel;
    }
}
