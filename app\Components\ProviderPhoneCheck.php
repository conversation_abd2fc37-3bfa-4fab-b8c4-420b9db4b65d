<?php

namespace App\Components;

use App\Base\Provider\BaseProvider;
use App\Constants\CommonConstants;
use App\Models\Log;
use App\Models\User;
use Illuminate\Support\Facades\Http;

class ProviderPhoneCheck extends BaseProvider
{
    public static $providerID = CommonConstants::PROVIDER_PHONE_CHECK;
    public static function placeOrder($orderModel) {
        $validate=self::validateRequest($orderModel);
        if($validate!==true) {
            return $validate;
        }

        $url = self::$providerDetails['api_url'] . "?api_key=".self::$providerDetails['api_key']."&imei=".$orderModel->imei."&service_id=".self::$providerServiceDetails['sid'];
        $response = Http::get($url);
        $result=$response->body();

        /* $curl = curl_init ($url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 40);
        curl_setopt($curl, CURLOPT_TIMEOUT, 60);
        $result = curl_exec($curl);
        curl_close($curl); */

        if($result && $result!="") {
            return self::parseResult($orderModel,$result);
        }

        return self::defaultResult();
    }

    public static function parseResult($orderModel,$result) {
        try {
            if ($result == "" || $result == false || stripos($result, "error code")!==false || stripos($result, "not enough money")!==false) {
                return self::failedResult(self::SERVICE_MAINTENANCE_MESSAGE,$result);
            }

            $finalResult=null;

            $decode_resp = json_decode($result, true);

            if (array_key_exists('status', $decode_resp)) {
                if ($decode_resp['status'] == "success") {
                    if (array_key_exists('result', $decode_resp)) {
                        $finalResult=$decode_resp['result'];
                    }
                }
            }

            if (!$finalResult) {
                return self::failedResult(self::DEFAULT_MESSAGE,$result);
            }

            $sid=self::$providerServiceDetails['sid'];
            switch ($sid) {
                case "43":
                    $new_data = array(
                        'IMEI' => '', 'Manufacturer' => '', 'Model' => '', 'Model Name' => '', 'Blacklist Status' => '', 'Operating System' => '',
                        'WLAN' => '', 'Device Type' => '', 'Date of Checking' => '',
                    );

                    if (array_key_exists('object', $decode_resp)) {
                        if (array_key_exists('marketingName', $decode_resp['object'])) {
                            if ($decode_resp['object']['marketingName'] != "") {
                                $new_data['Model'] = $decode_resp['object']['marketingName'];
                            }
                        }
                        if (array_key_exists('modelName', $decode_resp['object'])) {
                            if ($decode_resp['object']['modelName'] != "") {
                                $new_data['Model Name'] = $decode_resp['object']['modelName'];
                            }
                        }
                        if (array_key_exists('IMEI', $decode_resp['object'])) {
                            if ($decode_resp['object']['IMEI'] != "") {
                                $new_data['IMEI'] = $decode_resp['object']['IMEI'];
                            }
                        }
                        if (array_key_exists('manufacturer', $decode_resp['object'])) {
                            if ($decode_resp['object']['manufacturer'] != "") {
                                $new_data['Manufacturer'] = $decode_resp['object']['manufacturer'];
                            }
                        }

                        if (array_key_exists('OnBlockList', $decode_resp['object'])) {
                            if (!$decode_resp['object']['OnBlockList']) {
                                $new_data['Blacklist Status'] = '<span style="color:green;">CLEAN</span>';
                            } else {
                                $new_data['Blacklist Status'] = '<span style="color:red;">BLACKLISTED</span>';
                            }
                        }

                        if (array_key_exists('operatingSystem', $decode_resp['object'])) {
                            if ($decode_resp['object']['operatingSystem'] != "") {
                                $new_data['Operating System'] = $decode_resp['object']['operatingSystem'];
                            }
                        }
                        if (array_key_exists('wlan', $decode_resp['object'])) {
                            if ($decode_resp['object']['wlan'] != "") {
                                $new_data['WLAN'] = $decode_resp['object']['wlan'];
                            }
                        }
                        if (array_key_exists('deviceType', $decode_resp['object'])) {
                            if ($decode_resp['object']['deviceType'] != "") {
                                $new_data['Device Type'] = $decode_resp['object']['deviceType'];
                            }
                        }
                        if (array_key_exists('dateOfChecking', $decode_resp['object'])) {
                            if ($decode_resp['object']['dateOfChecking'] != "") {
                                $new_data['Date of Checking'] = $decode_resp['object']['dateOfChecking'];
                            }
                        }
                    }

                    $newHtmlResult = "";
                    foreach ($new_data as $key => $val) {
                        if ($val == "") {
                            continue;
                        }
                        $newHtmlResult .= $key . " : " . $val . "<br />";
                    }
                    $finalResult = $newHtmlResult;

                    if($newHtmlResult=="") {
                        return self::failedResult(self::DEFAULT_MESSAGE,$result);
                    }

                    return self::successResult($finalResult,$result);
                    break;
            }

            return self::successResult($finalResult, $result);
        } catch (\Exception $e) {

        }
        return self::defaultResult();
    }
}
