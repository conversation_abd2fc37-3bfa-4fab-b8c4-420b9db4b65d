<?php
use App\Components\Helper;
?>
@extends(\App\Components\Helper::getLayoutForUser())
@section('content')
    <main>
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <h1>{{__('Provider Services')}}</h1>
                    <div class="text-zero top-right-button-container">
                        <a href="{{route('provider-service.create')}}" class="btn btn-primary btn-lg top-right-button mr-1"> <i class="glyph-icon simple-icon-plus"></i> ADD NEW</a>
                    </div>

                    <nav class="breadcrumb-container d-none d-sm-block d-lg-inline-block" aria-label="breadcrumb">
                        <ol class="breadcrumb pt-0">
                            <li class="breadcrumb-item">
                                <a href="{{\App\Components\Helper::dashboardLink()}}">{{__('Dashboard')}}</a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">{{__('Provider Services')}}</li>
                        </ol>
                    </nav>
                    <div class="separator mb-5"></div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-lg-12 col-md-12 mb-4">
                    <div class="card">
                        <div class="card-body table-wrapper">
                            <div class="table-filters-ghost" style="display: none;">
                                <div class="p-b-10 mt-3 searchFiltersContainer">
                                    <div class="p-b-10 searchFilters">
                                        <div class="row">
                                            <div class="col-12 col-md">
                                                <div class="form-group text-left">
                                                    <label for="role">{{ __('Search') }}</label>
                                                    <input type="text" name="q" value="" class="form-control filterField" placeholder="Enter Search">
                                                </div>
                                            </div>
                                            <div class="col-12 col-md">
                                                <div class="form-group text-left">
                                                    <label for="provider_id">{{ __('Provider') }}</label>
                                                    <select name="provider_id" id="provider_id" class="form-control filterField">
                                                        <option value="">All</option>
                                                        @foreach (\App\Models\Provider::query()->orderBy("name")->get() as $provider)
                                                            <option <?=(array_key_exists('provider',$_GET) && $_GET['provider']==$provider->id ? 'selected=""' : '')?> value="{{$provider->id}}">{{$provider->name}}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-12 col-md">
                                                <div class="form-group text-left">
                                                    <label for="is_active">{{ __('Active') }}</label>
                                                    <select name="is_active" id="is_active" class="form-control filterField">
                                                        <option value="">All</option>
                                                        @foreach (\App\Constants\CommonConstants::YES_NO_PROPERTIES as $userStatus => $userStatusData)
                                                            <option value="{{$userStatus}}">{{$userStatusData['text']}}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-12 col-md">
                                                <div class="form-group text-left">
                                                    <label for="is_chinese_result">{{ __('Chinese Result') }}</label>
                                                    <select name="is_chinese_result" id="is_chinese_result" class="form-control filterField">
                                                        <option value="">All</option>
                                                        @foreach (\App\Constants\CommonConstants::YES_NO_PROPERTIES as $userStatus => $userStatusData)
                                                            <option value="{{$userStatus}}">{{$userStatusData['text']}}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-12 col-md">
                                                <div class="form-group text-left">
                                                    <label for="type">{{ __('Type') }}</label>
                                                    <select name="type" id="type" class="form-control filterField">
                                                        <option value="">All</option>
                                                        <option value="{{\App\Constants\CommonConstants::TYPE_BOTH}}">Both (IMEI/SN)</option>
                                                        <option value="{{\App\Constants\CommonConstants::TYPE_IMEI}}">IMEI</option>
                                                        <option value="{{\App\Constants\CommonConstants::TYPE_SN}}">SN</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <table id="recordsTable" class="table table-hover align-middle" style="width:100%">
                                <thead>
                                <tr>
                                    <th>{{__('Time')}}</th>
                                    <th>{{__('Name')}}</th>
                                    <th>{{__('Provider')}}</th>
                                    <th>{{__('Service ID')}}</th>
                                    <th>{{__('Type')}}</th>
                                    <th>{{__('Price')}}</th>
                                    <th>{{__('Active')}}</th>
                                    <th>{{__('Chinese Result')}}</th>
                                    <th>{{__('Actions')}}</th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>



@endsection
@section('pageJs')

    <script>
        $(document).ready(function () {
            let recordsTable = $('#recordsTable');
            let mainTableWrapper = recordsTable.closest(".table-wrapper");
            let filters = mainTableWrapper.find(".table-filters-ghost");
            let dataTableOptions = {!! json_encode(\App\Common\ContentHelper::getDataTableOptions()) !!};
            let recordsTableObj = recordsTable.DataTable({
                ...dataTableOptions,
                processing: false,
                order: [[0, "desc"]],
                ajax: {
                    url: "{{ route('provider-service.dataTable') }}",
                    data: function (data) {
                        data['search_query'] = {};
                        mainTableWrapper.find(".filterField").each(function () {
                            let obj = $(this);
                            data['search_query'][obj.attr("name")] = obj.val();
                        });
                    },
                    dataSrc: function (json) {
                        let data = json.stats;
                        return json.data;
                    }
                },
                columns: [
                    {data: 'created_at', name: 'created_at'},
                    {data: 'name', name: 'name'},
                    {data: 'provider', name: 'provider',orderable: false, searchable: false},
                    {data: 'sid', name: 'sid'},
                    {data: 'type', name: 'type',orderable: false, searchable: false},
                    {data: 'price', name: 'price'},
                    {data: 'is_active', name: 'is_active',orderable: false, searchable: false},
                    {data: 'is_chinese_result', name: 'is_chinese_result',orderable: false, searchable: false},
                    {data: 'actions', name: 'actions',orderable: false, searchable: false},
                ]
            });
            mainTableWrapper.find(".tableFilters").html(filters.html());
            filters.remove();
            $(document).on('keyup change', '.filterField', function () {
                recordsTableObj.draw();
            });
        });


    </script>

@endsection
