<?php

namespace App\Components;

use App\Base\Provider\BaseProvider;
use App\Constants\CommonConstants;
use App\Models\Log;
use App\Models\User;
use Illuminate\Support\Facades\Http;

class ProviderUnlock3 extends BaseProvider
{
    public static $providerID = CommonConstants::PROVIDER_UNLOCK_3;
    public static function placeOrder($orderModel) {
        $validate=self::validateRequest($orderModel);
        if($validate!==true) {
            return $validate;
        }

        $url = self::$providerDetails['api_url'] . "?key=".self::$providerDetails['api_key']."&lan=en&sn=".$orderModel->imei."&act=".self::$providerServiceDetails['sid'];
        $response = Http::get($url);
        $result=$response->body();

        /* $curl = curl_init ($url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 40);
        curl_setopt($curl, CURLOPT_TIMEOUT, 60);
        $result = curl_exec($curl);
        curl_close($curl); */

        if($result && $result!="") {
            return self::parseResult($orderModel,$result);
        }

        return self::defaultResult();
    }

    public static function parseResult($orderModel,$result) {
        try {
            if ($result == "" || $result == false || stripos($result, "error code")!==false) {
                return self::failedResult(self::SERVICE_MAINTENANCE_MESSAGE,$result);
            }

            if (stripos($result, "Error A01") !== false
                || stripos($result, "Error A02") !== false
                || stripos($result, "Error A03") !== false
                || stripos($result, "Error E01") !== false
                || stripos($result, "Error E02") !== false
                || stripos($result, "Error R01") !== false
                || stripos($result, "Error B01") !== false
                || stripos($result, "Error S01") !== false
                || stripos($result, "Error S02") !== false
                || stripos($result, "Error S03") !== false) {
                return self::failedResult(self::DEFAULT_MESSAGE,$result);
            }

            $finalResult=null;
            $new_result = json_decode($result, true);
            if(is_array($new_result)) {
                if (array_key_exists('code', $new_result) && array_key_exists('result', $new_result)) {
                    if ($new_result['code'] == "0") {
                        $finalResult = $new_result['result'];
                    }
                }
            }

            if(!$finalResult) {
                return self::failedResult(self::DEFAULT_MESSAGE,$result);
            }

            $new_csv_result = self::getCsvReport($finalResult, true, false, true);
            if (is_array($new_csv_result) && count($new_csv_result) > 0) {
                $eng_result = [];
                foreach ($new_csv_result as $k => $v) {
                    $key = self::translateChineseWord($k, false);
                    $val = self::translateChineseWord($v, false);
                    $eng_result[] = trim($key) . " : " . trim($val);
                }
                $finalResult = implode("<br>", $eng_result);
            }

            $sid=self::$providerServiceDetails['sid'];
            switch ($sid) {
                case "clean2":
                    if (stripos($finalResult, 'imei') !== false) {
                        $finalResult = preg_replace('/imei/i', 'IMEI/SN', $finalResult);
                    }
                    if (stripos($finalResult, 'Serial') !== false) {
                        $finalResult = preg_replace('/Serial/i', 'IMEI/SN', $finalResult);
                    }
                    break;

                case "fmia":
                    if (stripos($finalResult, 'imei number') !== false) {
                        $finalResult = preg_replace('/imei number/i', 'IMEI/SN', $finalResult);
                    } else if (stripos($finalResult, 'imei') !== false) {
                        $finalResult = preg_replace('/imei/i', 'IMEI/SN', $finalResult);
                    }
                    if (stripos($finalResult, 'Serial') !== false) {
                        $finalResult = preg_replace('/Serial/i', 'IMEI/SN', $finalResult);
                    }

                    if (stripos($finalResult, 'Find My iPhone') !== false) {
                        if (stripos($finalResult, ': on') !== false) {
                            $finalResult = preg_replace('/: ON/i', ": <span style='color:red;'>ON</span>", $finalResult);
                        }
                        if (stripos($finalResult, ': off') !== false) {
                            $finalResult = preg_replace('/: OFF/i', ": <span style='color:green;'>OFF</span>", $finalResult);
                        }
                    }
                    break;

                case "baoxen":
                    $new_data = array(
                        'Model' => '', 'IMEI' => '', 'Serial' => '', 'IMEI/SN' => '', 'Activation Status' => '', 'Purchase Date' => '',
                        'Telephone Technical Support' => '', 'Telephone Technical Support Expiration Date' => '',
                        'Telephone Technical Support Expire In' => '',
                        'Repairs And Service Coverage' => '', 'Repairs And Service Expiration Date' => '',
                        'Repairs And Service Expires In' => '',
                        'Apple Care' => '', 'Refurbished Device' => '', 'Replaced Device' => '',
                        'Registered Device' => '', 'Loaner Device' => '',
                    );

                    $ex = explode("<br>", $finalResult);
                    if (count($ex) > 1) {
                        $resp = array();
                        foreach ($ex as $e) {
                            $data = trim(strip_tags($e));
                            $exx = explode(":", $data);
                            if (count($exx) == 2) {
                                $resp[strtolower(trim($exx[0]))] = trim($exx[1]);
                            }
                        }
                        if (array_key_exists('model', $resp)) {
                            $new_data['Model'] = trim($resp['model']);
                        }
                        if (array_key_exists('imei', $resp)) {
                            $new_data['IMEI'] = trim($resp['imei']);
                        }
                        if (array_key_exists('serial', $resp)) {
                            $new_data['Serial'] = trim($resp['serial']);
                        }
                        if (array_key_exists('replaced by apple', $resp)) {
                            if (strtolower(trim($resp['replaced by apple'])) == "yes") {
                                $new_data['Replaced Device'] = '<span style="color:red;">Yes</span>';
                            }
                        }
                        if (array_key_exists('replaced device', $resp)) {
                            if (strtolower(trim($resp['replaced device'])) == "yes") {
                                $new_data['Replaced Device'] = '<span style="color:red;">Yes</span>';
                            }
                        }
                        if ($new_data['Replaced Device'] == "") {
                            $new_data['Replaced Device'] = '<span style="color:green;">No</span>';
                        }
                        if (array_key_exists('refurbhised device', $resp)) {
                            if (strtolower($resp['refurbhised device']) != "no") {
                                $new_data['Refurbished Device'] = '<span style="color:red;">Yes</span>';
                            } else {
                                $new_data['Refurbished Device'] = '<span style="color:green;">No</span>';
                            }
                        }

                        if (array_key_exists('loaner device', $resp)) {
                            if (strtolower($resp['loaner device']) == "no") {
                                $new_data['Loaner Device'] = '<span style="color:red;">No</span>';;
                            } else {
                                $new_data['Loaner Device'] = '<span style="color:green;">Yes</span>';
                            }
                        }

                        if (array_key_exists('repairs and service coverage', $resp)) {
                            if ($resp['repairs and service coverage'] != "" && strtotime($resp['repairs and service coverage'])) {
                                $new_data['Repairs And Service Coverage'] = '<span style="color:green;">Active</span>';
                                $new_data['Repairs And Service Expiration Date'] = $resp['repairs and service coverage'];

                                if (array_key_exists('repairs and service coverage expires in', $resp)) {
                                    if ($resp['repairs and service coverage expires in'] != "" && $resp['repairs and service coverage expires in'] != null) {
                                        $exp = explode(" ", $resp['repairs and service coverage expires in']);
                                        if (count($exp) > 0) {
                                            $new_data['Repairs And Service Expires In'] = trim($exp[0]) . (trim($exp[0]) > 1 ? ' Days' : ' Day');
                                        }
                                    } else {
                                        $new_data['Repairs And Service Expires In'] = '0';
                                    }
                                }
                            } else {
                                $new_data['Repairs And Service Coverage'] = '<span style="color:red;">Expired</span>';
                                $new_data['Repairs And Service Expires In'] = '0';
                            }
                        }

                        if (array_key_exists('telephone technical support', $resp)) {
                            if ($resp['telephone technical support'] != "" && strtotime($resp['telephone technical support'])) {
                                $new_data['Telephone Technical Support'] = '<span style="color:green;">Active</span>';
                                $new_data['Telephone Technical Support Expiration Date'] = $resp['telephone technical support'];
                                try {
                                    $ex = explode("-", trim($resp['telephone technical support']));
                                    if (count($ex) == 3) {
                                        $end = strtotime($ex['1'] . "/" . $ex[2] . "/" . $ex[0]);
                                        $end = strtotime("+1 day", strtotime("midnight", $end)) - 1;
                                        if ($end > time()) {
                                            $diff = round(($end - time()) / (60 * 60 * 24));
                                            $new_data['Telephone Technical Support Expire In'] = $diff . ($diff > 1 ? ' Days' : ' Day');
                                        }
                                    }
                                } catch (\Exception $e) {

                                }
                            } else {
                                $new_data['Telephone Technical Support'] = '<span style="color:red;">Expired</span>';
                            }
                        }

                        if (array_key_exists('activation date', $resp)) {
                            $new_data['Purchase Date'] = $resp['activation date'];
                        }

                        if (array_key_exists('apple care', $resp)) {
                            if ($resp['apple care'] != null && $resp['apple care'] != "") {
                                if (strtolower($resp['apple care']) == "no") {
                                    $new_data['Apple Care'] = '<span style="color:red;">No</span>';
                                } else if (strtolower($resp['apple care']) == "yes") {
                                    $new_data['Apple Care'] = '<span style="color:green;">Yes</span>';
                                }
                            }
                        }

                        if (array_key_exists('activation status', $resp)) {
                            if (strtolower($resp['activation status']) == "activated" || strtolower($resp['activation status']) == "active") {
                                $new_data['Activation Status'] = '<span style="color:green;">Activated</span>';
                            } else {
                                $new_data['Activation Status'] = '<span style="color:red;">Unactivated</span>';
                                if (array_key_exists('Telephone Technical Support', $new_data)) {
                                    unset($new_data['Telephone Technical Support']);
                                }
                                if (array_key_exists('Repairs And Service Coverage', $new_data)) {
                                    unset($new_data['Repairs And Service Coverage']);
                                }
                                if (array_key_exists('Repairs And Service Expires In', $new_data)) {
                                    unset($new_data['Repairs And Service Expires In']);
                                }
                                if (array_key_exists('Replaced Device', $new_data)) {
                                    unset($new_data['Replaced Device']);
                                }
                                if (array_key_exists('Apple Care', $new_data)) {
                                    unset($new_data['Apple Care']);
                                }
                                if (array_key_exists('Purchase Date', $new_data)) {
                                    unset($new_data['Purchase Date']);
                                }
                                if (array_key_exists('Loaner Device', $new_data)) {
                                    unset($new_data['Loaner Device']);
                                }
                                if (array_key_exists('Refurbished Device', $new_data)) {
                                    unset($new_data['Refurbished Device']);
                                }
                            }
                        }

                        $new_resp = "";

                        foreach ($new_data as $kk => $vv) {
                            if ($vv == "") {
                                unset($new_data[$kk]);
                                continue;
                            }
                            $new_resp .= $kk . ": " . $vv . "<br>";
                        }
                        $new_resp = substr($new_resp, 0, -4);
                        if (count($new_data) > 0) {
                            $finalResult = $new_resp;
                        } else {
                            return self::failedResult(self::DEFAULT_MESSAGE,$result);
                        }
                    }

                    return self::successResult($finalResult,$result);
                    break;

                case "model":
                    $new_result = array();
                    $ex = explode("<br>", $finalResult);
                    if (count($ex) > 2) {
                        if (array_key_exists(1, $ex)) {
                            $exx = explode(":", $ex[1]);
                            if (count($exx) == 2) {
                                $color = trim(end(explode(" ", trim($exx[1]))));
                                if (strlen($color) > 0 && stripos("abcdefghijklmnopqrstuvwxyz", $color[0]) === false) {
                                    $translate_color = self::translateWord($color);
                                } else {
                                    $translate_color = $color;
                                }

                                $new_result[] = $ex[0];
                                $new_result[] = "Model Name: " . str_replace($color, $translate_color, trim($exx[1]));
                                foreach (array('iphone', 'ipod', 'ipad', 'iwatch', 'imac', 'mackbook') as $kk => $vv) {
                                    if (stripos(strtolower(trim($exx[1])), $vv) !== false) {
                                        $new_result[] = "Brand Name: Apple";
                                        $new_result[] = "Manufacturer: Apple Inc";
                                        break;
                                    }
                                }
                            }
                        }
                    }

                    if (count($new_result) > 0) {
                        $finalResult = implode("<br />", $new_result);
                    } else {
                        return self::failedResult(self::DEFAULT_MESSAGE,$result);
                    }

                    return self::successResult($finalResult,$result);
                    break;
            }

            return self::successResult($finalResult, $result);
        } catch (\Exception $e) {

        }
        return self::defaultResult();
    }
}
