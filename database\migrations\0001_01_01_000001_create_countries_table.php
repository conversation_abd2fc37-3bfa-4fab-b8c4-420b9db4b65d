<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('countries', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('iso')->unique();
            $table->string('iso3')->unique()->nullable();
            $table->string('num_code')->nullable();
            $table->string('phone_code')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('countries');
    }
};
