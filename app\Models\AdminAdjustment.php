<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use App\Casts\AmountCast;
use App\Constants\CommonConstants;
use Illuminate\Database\Eloquent\Model;

class AdminAdjustment extends BaseModel
{
    protected $fillable = [
        'user_id',
        'ref_no',
        'amount',
        'is_debit',
        'comments',
        'is_paid',
        'is_invoice',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'amount' => AmountCast::class,
    ];

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function invoice()
    {
        if($this->is_invoice) {
            return UserInvoice::query()
                ->where('user_id','=',$this->user_id)
                ->where('adjustment_id','=',$this->id)
                ->first();
        }
        return null;
    }

    public function onCreating()
    {
        if($this->comments=="") {
            $this->comments=null;
        }
        parent::onCreating();
    }

    public function onCreated()
    {
        $particular="Admin adjustment #".$this->ref_no;
        if(trim($this->comments)!="") {
            $particular.=" - ".$this->comments;
        }
        Transaction::saveTransaction($this->user_id,CommonConstants::TRANSACTION_TYPE_ADMIN_ADJUSTMENT,$this->is_debit,$this->amount,$particular,['admin_adjustment_id'=>$this->id]);
        if($this->is_invoice) {
            $model=new UserInvoice();
            $model->user_id=$this->user_id;
            $model->adjustment_id = $this->id;
            $model->amount=$this->amount;
            $model->is_paid=$this->is_paid;
            $model->particular=$this->comments;
            if(!$model->particular || $model->particular=="") {
                $model->particular=$particular;
            }
            if(!$model->save()) {
                Log::insertLog([
                    'user_id'=>$this->user_id,
                    'type'=>'admin_adjustment_invoice_error',
                    'particulars'=>'Unable to add invoice for admin adjustment #'.$this->id,
                    'data'=>['id'=>$this->id,'message'=>'Unable to generate invoice'],
                ]);
            }
        }
        parent::onCreated(); // TODO: Change the autogenerated stub
    }
}
