<?php

namespace App\Components;

use App\Base\Provider\BaseProvider;
use App\Constants\CommonConstants;
use App\Models\Log;
use App\Models\User;
use Illuminate\Support\Facades\Http;

class ProviderAlphaImeiCheck extends BaseProvider
{
    public static $providerID = CommonConstants::PROVIDER_ALPHA_IMEI_CHECK;
    public static function placeOrder($orderModel) {
        $validate=self::validateRequest($orderModel);
        if($validate!==true) {
            return $validate;
        }

        $url = self::$providerDetails['api_url'] . "?key=".self::$providerDetails['api_key']."&imei=".$orderModel->imei."&service=".self::$providerServiceDetails['sid'];
        $response = Http::get($url);
        $result=$response->body();

        /* $curl = curl_init ($url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 40);
        curl_setopt($curl, CURLOPT_TIMEOUT, 60);
        $result = curl_exec($curl);
        curl_close($curl); */

        if($result && $result!="") {
            return self::parseResult($orderModel,$result);
        }

        return self::defaultResult();
    }

    public static function parseResult($orderModel,$result) {
        try {
            if ($result == "" || $result == false
                || stripos($result, "Checking your browser")!==false || stripos($result, "error code")!==false) {
                return self::failedResult(self::SERVICE_MAINTENANCE_MESSAGE,$result);
            }

            $finalResult=null;
            $decode=json_decode($result,true);
            if(is_array($decode) && array_key_exists('status',$decode)) {
                if($decode['status']=="success") {
                    if(array_key_exists('object',$decode)) {
                        $finalResult=$decode['object'];
                    } else if(array_key_exists('result',$decode)) {
                        $finalResult=$decode['result'];
                    }
                }
            }
            if (!$finalResult) {
                return self::failedResult(self::DEFAULT_MESSAGE,$result);
            }

            $sid=self::$providerServiceDetails['sid'];
            switch ($sid) {
                case "14":
                    $new_data = array(
                        'Model' => '', 'IMEI' => '', 'IMEI 2' => '', 'Serial Number' => '', 'Estimated Purchase Date' => '',
                    );

                    if (is_array($finalResult) && count($finalResult) > 0) {
                        foreach ($finalResult as $key => $val) {
                            if (strtolower($key) == "modeldesc") {
                                $new_data['Model'] = $val;
                            }
                            if (strtolower($key) == "serial") {
                                $new_data['Serial Number'] = $val;
                            }
                            if (strtolower($key) == "imei") {
                                $new_data['IMEI'] = $val;
                            }
                            if (strtolower($key) == "imei2" || strtolower($key) == "imei2 number") {
                                $new_data['IMEI 2'] = $val;
                            }
                            if (strtolower($key) == "estpurchasedate") {
                                $new_data['Estimated Purchase Date'] = $val;
                                try {
                                    if ($val != "") {
                                        $date = strtotime(trim($val));
                                        if ($date) {
                                            $new_data['Estimated Purchase Date'] = date("Y-m-d", $date);
                                        }
                                    }
                                } catch (\Exception $e) {

                                }
                            }
                        }
                    }

                    $newHtmlResult = "";
                    foreach ($new_data as $key => $val) {
                        if ($val == "") {
                            continue;
                        }
                        $newHtmlResult .= $key . " : " . $val . "<br />";
                    }
                    $finalResult = $newHtmlResult;
                    if($finalResult=="" || $new_data['IMEI']=="") {
                        return self::failedResult(self::DEFAULT_MESSAGE,$result);
                    }

                    return self::successResult($finalResult,$result);
                    break;
                default:
                    if(is_array($finalResult)) {
                        $newHtmlResult = "";
                        foreach ($finalResult as $key => $val) {
                            if ($val == "") {
                                continue;
                            }
                            $newHtmlResult .= $key . " : " . $val . "<br />";
                        }
                        $finalResult=$newHtmlResult;
                    }
                    break;
            }

            return self::successResult($finalResult, $result);
        } catch (\Exception $e) {

        }
        return self::defaultResult();
    }
}
