<?php

namespace Database\Seeders;

use App\Constants\CommonConstants;
use App\Constants\UserConstants;
use App\Models\Country;
use App\Models\User;
use Faker\Provider\UserAgent;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        User::create([
            'name' => 'Back Office',
            'email' => '<EMAIL>',
            'username' => 'backoffice',
            'email_verified_at' => date(CommonConstants::PHP_DATE_FORMAT),
            'password' => 'test@123',
            'user_role_id' => UserConstants::ROLE_ADMIN,
            'user_agent' => UserAgent::chrome(),
            'created_ip' => '127.0.0.1',
            'remember_token' => Str::random(10),
            'created_at' => date(CommonConstants::PHP_DATE_FORMAT),
        ]);
    }
}
