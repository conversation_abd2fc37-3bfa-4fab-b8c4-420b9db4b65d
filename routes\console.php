<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;

Artisan::command('generate-system-cache', function () {
    \App\Models\Provider::generateCacheFile();
    \App\Models\Service::generateCacheFile();
    die("Completed");
})->purpose('Generate system cache files');

Artisan::command('generate-yesterday-orders-log', function () {
    \App\Models\UserOrderLog::saveLog();
    die("Completed");
})->purpose('Generate yesterday orders log');

Artisan::command('generate-today-orders-log', function () {
    \App\Models\UserOrderLog::saveLog(date(\App\Constants\CommonConstants::PHP_DATE_FORMAT_SHORT));
    die("Completed");
})->purpose('Generate today orders log');

Artisan::command('testing', function () {
    $serviceID=1;
    $serviceDetails=\App\Models\Service::fetchDetailsFromCache($serviceID);
    if(!$serviceDetails) {
        die("Service details not found");
    }
    $model=new \App\Models\UserOrder();
    $model->service_id=$serviceDetails['id'];
    $model->provider_id=$serviceDetails['provider_id'];
    $model->provider_service_id=$serviceDetails['provider_service_id'];
    //$model->provider_service_id=7;
    $model->imei="358073469204544";
    $result=$model->placeOrder();
    //$result=\App\Components\ProviderSickw::placeOrder($model);
    print_r($result);
})->purpose('Testing output');
