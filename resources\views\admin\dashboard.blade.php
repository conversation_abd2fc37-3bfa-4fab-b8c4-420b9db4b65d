<?php
use App\Components\Helper;
?>
@extends(\App\Components\Helper::getLayoutForUser())
@section('content')
    <main>
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <h1>Dashboard</h1>
                    <nav class="breadcrumb-container d-none d-sm-block d-lg-inline-block" aria-label="breadcrumb">
                        <ol class="breadcrumb pt-0">
                            <li class="breadcrumb-item">
                                <a href="{{\App\Components\Helper::dashboardLink()}}">Home</a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">Dashboard</li>
                        </ol>
                    </nav>
                    <div class="separator mb-5"></div>
                </div>



            </div>
            <div class="row icon-cards-row">
                <div class="col-md-3">
                    <a href="javascript:void(0);" class="card">
                        <div class="card-body text-center"><i class="iconsminds-pantone"></i>
                            <p class="card-text mb-0">Active Services</p>
                            <p class="lead text-center"><?=\App\Models\Service::defaultQuery()->count()?></p>
                        </div>
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="javascript:void(0);" class="card">
                        <div class="card-body text-center"><i class="iconsminds-basket-coins"></i>
                            <p class="card-text mb-0">Total Deposits</p>
                            <p class="lead text-center">N/A</p>
                        </div>
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="javascript:void(0);" class="card">
                        <div class="card-body text-center"><i class="iconsminds-user"></i>
                            <p class="card-text mb-0">Active Customers</p>
                            <p class="lead text-center"><?=\App\Models\User::defaultQuery()->count()?></p>
                        </div>
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="javascript:void(0);" class="card">
                        <div class="card-body text-center"><i class="iconsminds-shopping-basket"></i>
                            <p class="card-text mb-0">Total Orders</p>
                            <p class="lead text-center"><?=\App\Models\UserOrder::count()?></p>
                        </div>
                    </a>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12 col-lg-4 mb-4">
                    <div class="card h-100">
                        <div class="card-body">
                            <h5 class="card-title">Support Tickets</h5>
                            <div class="dashboard-donut-chart chart">
                                <canvas id="ticketsChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-12 col-lg-4 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Recent Transactions</h5>
                            <div class="row">
                                <div class="col-12">
                                    <div class="table-responsive">
                                        <table class="table">
                                            <thead>
                                            <tr>
                                                <th class="border-top-0">Type</th>
                                                <th class="border-top-0">User</th>
                                                <th class="border-top-0">Amount</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <?php
                                            $transactions=\App\Models\Transaction::query()->orderByDesc("id")->limit(10)->get();
                                            if(count($transactions)>0) {
                                                foreach ($transactions as $transaction) {
                                                    ?>
                                                    <tr>
                                                        <td class="text-truncate"><span title="<?=$transaction->type->name?>"><?=$transaction->type->name?></span></td>
                                                        <td class="text-truncate"><?=$transaction->user->displayDetails?></td>
                                                        <td class="text-truncate">
                                                            <?php
                                                            if($transaction->debit_amount) {
                                                                ?>
                                                                <span class="text-danger"><?=Helper::printAmount($transaction->debit_amount)?></span>
                                                                <?php
                                                            } else {
                                                                ?>
                                                                <span class="text-success"><?=Helper::printAmount($transaction->credit_amount)?></span>
                                                                <?php
                                                            }
                                                            ?>
                                                        </td>
                                                    </tr>
                                                    <?php
                                                }
                                            }
                                            ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-12 col-lg-4 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Latest Users</h5>
                            <div class="row">
                                <div class="col-12">
                                    <div class="table-responsive">
                                        <table class="table">
                                            <thead>
                                            <tr>
                                                <th class="border-top-0">User</th>
                                                <th class="border-top-0">Date</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <?php
                                            $users=\App\Models\User::defaultQuery()->orderByDesc("id")->limit(10)->get();
                                            if(count($users)>0) {
                                                foreach ($users as $user) {
                                                    ?>
                                                    <tr>
                                                        <td class="text-truncate"><?=$user->displayDetails?></td>
                                                        <td class="text-truncate"><?=date("d M, Y",strtotime($user->created_at))?></td>
                                                    </tr>
                                                    <?php
                                                }
                                            }
                                            ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-12 col-lg-12 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Recent Orders</h5>
                            <div class="row">
                                <div class="col-12">
                                    <div class="table-responsive">
                                        <table class="table">
                                            <thead>
                                            <tr>
                                                <th class="border-top-0">Date</th>
                                                <th class="border-top-0">Status</th>
                                                <th class="border-top-0">Request</th>
                                                <th class="border-top-0">User</th>
                                                <th class="border-top-0">Service</th>
                                                <th class="border-top-0">IMEI</th>
                                                <th class="border-top-0">Amount</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <?php
                                            $orders=\App\Models\UserOrder::query()->orderByDesc("id")->limit(10)->get();
                                            if(count($orders)>0) {
                                                foreach ($orders as $order) {
                                                    ?>
                                                    <tr>
                                                        <td class="text-truncate"><?=$order->created_at;?></td>
                                                        <td class="text-truncate"><?=$order->printOrderStatus();?></td>
                                                        <td class="text-truncate"><?=$order->order_ref?></td>
                                                        <td class="text-truncate"><?php if($order->user){ print $order->user->displayDetails;} ?></td>
                                                        <td class="text-truncate"><?php if($order->service){ print $order->service->displayDetails;} ?></td>
                                                        <td class="text-truncate"><?=$order->imei?></td>
                                                        <td class="text-truncate"><?=Helper::printAmount($order->price)?></td>
                                                    </tr>
                                                    <?php
                                                }
                                            }
                                            ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>



@endsection
@section('pageJs')
    <script src="js/vendor/Chart.bundle.min.js"></script>
    <script src="js/vendor/chartjs-plugin-datalabels.js"></script>

    <script>
        let rootStyle = getComputedStyle(document.body);
        let primaryColor = rootStyle.getPropertyValue("--primary-color").trim();
        let themeColor1 = rootStyle.getPropertyValue("--theme-color-1").trim();
        let themeColor2 = rootStyle.getPropertyValue("--theme-color-2").trim();
        let themeColor3 = rootStyle.getPropertyValue("--theme-color-3").trim();
        let themeColor4 = rootStyle.getPropertyValue("--theme-color-4").trim();
        let themeColor5 = rootStyle.getPropertyValue("--theme-color-5").trim();
        let themeColor6 = rootStyle.getPropertyValue("--theme-color-6").trim();
        let themeColor1_10 = rootStyle
            .getPropertyValue("--theme-color-1-10")
            .trim();
        let themeColor2_10 = rootStyle
            .getPropertyValue("--theme-color-2-10")
            .trim();
        let themeColor3_10 = rootStyle
            .getPropertyValue("--theme-color-3-10")
            .trim();
        let themeColor4_10 = rootStyle
            .getPropertyValue("--theme-color-4-10")
            .trim();

        let themeColor5_10 = rootStyle
            .getPropertyValue("--theme-color-5-10")
            .trim();
        let themeColor6_10 = rootStyle
            .getPropertyValue("--theme-color-6-10")
            .trim();
        let foregroundColor = rootStyle
            .getPropertyValue("--foreground-color")
            .trim();
        let separatorColor = rootStyle.getPropertyValue("--separator-color").trim();
        let chartTooltip = {
            backgroundColor: foregroundColor,
            titleFontColor: primaryColor,
            borderColor: separatorColor,
            borderWidth: 0.5,
            bodyFontColor: primaryColor,
            bodySpacing: 10,
            xPadding: 15,
            yPadding: 15,
            cornerRadius: 0.15,
            displayColors: false
        };

        if (document.getElementById("ticketsChart")) {
            var polarChartNoShadow = document
                .getElementById("ticketsChart")
                .getContext("2d");
            var myChart = new Chart(polarChartNoShadow, {
                type: "polarArea",
                options: {
                    plugins: {
                        datalabels: {
                            display: false
                        }
                    },
                    responsive: true,
                    maintainAspectRatio: false,
                    scale: {
                        ticks: {
                            display: false
                        }
                    },
                    legend: {
                        position: "bottom",
                        labels: {
                            padding: 30,
                            usePointStyle: true,
                            fontSize: 12
                        }
                    },
                    tooltips: chartTooltip
                },
                data: {
                    datasets: [
                        {
                            label: "Stock",
                            borderWidth: 2,
                            pointBackgroundColor: themeColor1,
                            borderColor: [themeColor1, themeColor2],
                            backgroundColor: [
                                themeColor1_10,
                                themeColor2_10
                            ],
                            data: [1, 1]
                        }
                    ],
                    labels: ["Open", "Closed"]
                }
            });
        }




    </script>


@endsection
